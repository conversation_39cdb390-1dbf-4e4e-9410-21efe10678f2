{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "lib/**", "es/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:unit": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["test-results/**", "playwright-report/**"]}, "test:performance": {"dependsOn": ["build"], "outputs": ["performance-results/**"]}, "test:coverage": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}, "docs:build": {"dependsOn": ["build"], "outputs": ["docs/.vitepress/dist/**"]}, "docs:dev": {"cache": false, "persistent": true}, "docs:preview": {"cache": false, "persistent": true}}}