/**
 * @fileoverview 微前端应用完整生命周期集成测试
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCore } from '@micro-core/core';
import type { AppConfig } from '@micro-core/core/src/types';
import { RouterPlugin } from '@micro-core/plugins/router/src/router-plugin';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('微前端应用完整生命周期集成测试', () => {
    let microCore: MicroCore;
    let routerPlugin: RouterPlugin;
    let mockApps: any[];
    let containers: HTMLElement[];

    beforeEach(async () => {
        // 初始化微前端核心
        microCore = new MicroCore({
            sandbox: true,
            prefetch: true,
            debug: true
        });

        // 初始化路由插件
        routerPlugin = new RouterPlugin({
            mode: 'hash',
            base: '/'
        });
        microCore.use(routerPlugin);

        // 创建模拟应用
        mockApps = [
            global.createMockApp('react-app', 'react'),
            global.createMockApp('vue-app', 'vue'),
            global.createMockApp('angular-app', 'angular')
        ];

        // 创建容器
        containers = [
            global.createMockContainer('react-container'),
            global.createMockContainer('vue-container'),
            global.createMockContainer('angular-container')
        ];

        // 模拟网络请求
        global.fetch = vi.fn().mockImplementation((url: string) => {
            const app = mockApps.find(app => url.startsWith(app.baseUrl));
            if (!app) {
                return Promise.reject(new Error('Not found'));
            }

            if (url.endsWith('/manifest.json')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve(global.createMockManifest(app.name, app.framework))
                });
            }

            if (url.endsWith('/index.js')) {
                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        window['${app.name}'] = {
                            mount: ${app.lifecycle.mount.toString()},
                            unmount: ${app.lifecycle.unmount.toString()},
                            update: ${app.lifecycle.update.toString()}
                        };
                    `)
                });
            }

            return Promise.resolve({
                ok: true,
                text: () => Promise.resolve('')
            });
        });
    });

    afterEach(() => {
        microCore.destroy();
        containers.forEach(container => {
            if (container.parentNode) {
                container.parentNode.removeChild(container);
            }
        });
    });

    describe('应用注册和发现', () => {
        it('应该能够注册多个微前端应用', () => {
            const appConfigs: AppConfig[] = mockApps.map((app, index) => ({
                name: app.name,
                entry: app.entry,
                container: `#${containers[index].id}`,
                activeWhen: `/${app.name}`
            }));

            appConfigs.forEach(config => {
                microCore.registerApp(config);
            });

            const registeredApps = microCore.getApps();
            expect(registeredApps).toHaveLength(3);
            expect(registeredApps.map(app => app.name)).toEqual([
                'react-app', 'vue-app', 'angular-app'
            ]);
        });

        it('应该能够自动发现和注册应用', async () => {
            // 模拟自动发现配置
            microCore.configure({
                autoDiscovery: {
                    enabled: true,
                    patterns: ['http://localhost:300*'],
                    manifestPath: '/manifest.json'
                }
            });

            await microCore.discoverApps();

            const discoveredApps = microCore.getApps();
            expect(discoveredApps.length).toBeGreaterThan(0);
        });
    });

    describe('应用加载和启动', () => {
        beforeEach(() => {
            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`
                });
            });
        });

        it('应该能够加载和启动单个应用', async () => {
            await microCore.startApp('react-app');

            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('MOUNTED');
            expect(mockApps[0].lifecycle.mount).toHaveBeenCalled();
        });

        it('应该能够并行启动多个应用', async () => {
            const startPromises = [
                microCore.startApp('react-app'),
                microCore.startApp('vue-app')
            ];

            await Promise.all(startPromises);

            const reactApp = microCore.getApp('react-app');
            const vueApp = microCore.getApp('vue-app');

            expect(reactApp?.status).toBe('MOUNTED');
            expect(vueApp?.status).toBe('MOUNTED');
        });

        it('应该能够处理应用启动失败', async () => {
            // 模拟启动失败
            mockApps[0].lifecycle.mount.mockRejectedValue(new Error('启动失败'));

            await expect(microCore.startApp('react-app')).rejects.toThrow('启动失败');

            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('MOUNT_ERROR');
        });
    });

    describe('路由驱动的应用切换', () => {
        beforeEach(() => {
            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`
                });
            });
        });

        it('应该能够根据路由自动启动匹配的应用', async () => {
            // 导航到react应用路由
            await routerPlugin.push('/react-app');

            // 等待应用启动
            await new Promise(resolve => setTimeout(resolve, 100));

            const reactApp = microCore.getApp('react-app');
            expect(reactApp?.status).toBe('MOUNTED');
        });

        it('应该能够在路由切换时停止不匹配的应用', async () => {
            // 先启动react应用
            await routerPlugin.push('/react-app');
            await new Promise(resolve => setTimeout(resolve, 100));

            // 切换到vue应用
            await routerPlugin.push('/vue-app');
            await new Promise(resolve => setTimeout(resolve, 100));

            const reactApp = microCore.getApp('react-app');
            const vueApp = microCore.getApp('vue-app');

            expect(reactApp?.status).toBe('UNMOUNTED');
            expect(vueApp?.status).toBe('MOUNTED');
        });

        it('应该能够处理复杂的路由匹配规则', async () => {
            // 注册带有复杂匹配规则的应用
            microCore.registerApp({
                name: 'complex-app',
                entry: 'http://localhost:3006/index.js',
                container: '#complex-container',
                activeWhen: ['/complex', '/complex/*', (location) => {
                    return location.pathname.includes('special');
                }]
            });

            global.createMockContainer('complex-container');

            // 测试不同的路由匹配
            const testRoutes = ['/complex', '/complex/detail', '/special-page'];

            for (const route of testRoutes) {
                await routerPlugin.push(route);
                await new Promise(resolve => setTimeout(resolve, 50));

                const activeApps = microCore.getActiveApps(route);
                expect(activeApps.some(app => app.name === 'complex-app')).toBe(true);
            }
        });
    });

    describe('应用间通信', () => {
        let communicationMock: any;

        beforeEach(async () => {
            communicationMock = global.mockAppCommunication();

            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`
                });
            });

            // 启动两个应用用于通信测试
            await microCore.startApp('react-app');
            await microCore.startApp('vue-app');
        });

        afterEach(() => {
            communicationMock.restore();
        });

        it('应该能够在应用间发送和接收消息', async () => {
            const testMessage = {
                type: 'TEST_MESSAGE',
                from: 'react-app',
                to: 'vue-app',
                data: { value: 'hello' }
            };

            // 发送消息
            microCore.sendMessage('vue-app', testMessage);

            // 等待消息传递
            await new Promise(resolve => setTimeout(resolve, 50));

            expect(communicationMock.messages).toHaveLength(1);
            expect(communicationMock.messages[0].message).toEqual(testMessage);
        });

        it('应该能够广播消息给所有应用', async () => {
            const broadcastMessage = {
                type: 'BROADCAST_MESSAGE',
                data: { announcement: 'system update' }
            };

            microCore.broadcast(broadcastMessage);

            await new Promise(resolve => setTimeout(resolve, 50));

            // 应该发送给所有已启动的应用
            expect(communicationMock.messages.length).toBeGreaterThanOrEqual(2);
        });

        it('应该能够处理消息传递失败', async () => {
            const errorCollector = global.mockErrorCollector();

            // 发送消息给不存在的应用
            microCore.sendMessage('non-existent-app', { type: 'TEST' });

            await new Promise(resolve => setTimeout(resolve, 50));

            expect(errorCollector.errors.length).toBeGreaterThan(0);
            errorCollector.restore();
        });
    });

    describe('全局状态管理', () => {
        beforeEach(async () => {
            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`
                });
            });

            await microCore.startApp('react-app');
            await microCore.startApp('vue-app');
        });

        it('应该能够在应用间共享全局状态', () => {
            // 设置全局状态
            microCore.setState('user', {
                id: 1,
                name: 'Test User',
                role: 'admin'
            });

            microCore.setState('theme', 'dark');

            // 验证状态可以被获取
            const user = microCore.getState('user');
            const theme = microCore.getState('theme');

            expect(user).toEqual({
                id: 1,
                name: 'Test User',
                role: 'admin'
            });
            expect(theme).toBe('dark');
        });

        it('应该能够监听状态变化', () => {
            const stateChangeHandler = vi.fn();
            microCore.on('stateChange', stateChangeHandler);

            microCore.setState('counter', 1);
            microCore.setState('counter', 2);

            expect(stateChangeHandler).toHaveBeenCalledTimes(2);
        });

        it('应该能够在应用卸载时清理相关状态', async () => {
            // 设置应用相关状态
            microCore.setState('react-app:data', { value: 'test' });
            microCore.setState('vue-app:data', { value: 'test' });

            // 卸载react应用
            await microCore.stopApp('react-app');

            // 验证应用相关状态被清理
            const reactAppData = microCore.getState('react-app:data');
            const vueAppData = microCore.getState('vue-app:data');

            expect(reactAppData).toBeUndefined();
            expect(vueAppData).toBeDefined();
        });
    });

    describe('错误处理和恢复', () => {
        let errorCollector: any;

        beforeEach(() => {
            errorCollector = global.mockErrorCollector();

            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`
                });
            });
        });

        afterEach(() => {
            errorCollector.restore();
        });

        it('应该能够处理应用加载失败并进行重试', async () => {
            // 模拟网络错误
            global.fetch = vi.fn().mockRejectedValue(new Error('Network Error'));

            // 配置重试策略
            microCore.configure({
                retry: {
                    maxAttempts: 3,
                    delay: 100
                }
            });

            await expect(microCore.startApp('react-app')).rejects.toThrow('Network Error');

            // 验证重试次数
            expect(global.fetch).toHaveBeenCalledTimes(3);
        });

        it('应该能够隔离应用错误不影响其他应用', async () => {
            await microCore.startApp('react-app');
            await microCore.startApp('vue-app');

            // 模拟react应用出错
            mockApps[0].lifecycle.mount.mockImplementation(() => {
                throw new Error('React App Error');
            });

            // 重新挂载react应用触发错误
            await microCore.stopApp('react-app');
            await expect(microCore.startApp('react-app')).rejects.toThrow('React App Error');

            // 验证vue应用仍然正常
            const vueApp = microCore.getApp('vue-app');
            expect(vueApp?.status).toBe('MOUNTED');
        });

        it('应该能够自动恢复失败的应用', async () => {
            // 配置自动恢复
            microCore.configure({
                autoRecover: {
                    enabled: true,
                    maxAttempts: 2,
                    delay: 200
                }
            });

            // 模拟应用启动失败然后成功
            let attemptCount = 0;
            mockApps[0].lifecycle.mount.mockImplementation(() => {
                attemptCount++;
                if (attemptCount === 1) {
                    throw new Error('First attempt failed');
                }
                return Promise.resolve();
            });

            await microCore.startApp('react-app');

            // 验证应用最终启动成功
            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('MOUNTED');
            expect(attemptCount).toBe(2);
        });
    });

    describe('性能监控和优化', () => {
        let performanceMonitor: any;

        beforeEach(() => {
            performanceMonitor = global.mockPerformanceMonitor();

            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${containers[index].id}`,
                    activeWhen: `/${app.name}`,
                    preload: true
                });
            });
        });

        afterEach(() => {
            performanceMonitor.restore();
        });

        it('应该能够记录应用加载和启动性能指标', async () => {
            await microCore.startApp('react-app');

            const app = microCore.getApp('react-app');
            expect(app?.loadTime).toBeGreaterThan(0);
            expect(app?.mountTime).toBeGreaterThan(0);

            // 验证性能标记被创建
            const marks = performanceMonitor.metrics.filter(m => m.type === 'mark');
            expect(marks.some(m => m.name.includes('react-app'))).toBe(true);
        });

        it('应该能够预加载应用资源', async () => {
            // 启用预加载
            microCore.configure({
                prefetch: {
                    enabled: true,
                    strategy: 'idle'
                }
            });

            await microCore.prefetchApp('react-app');

            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('LOADED');
        });

        it('应该能够监控内存使用情况', async () => {
            const initialMemory = performance.memory?.usedJSHeapSize || 0;

            await microCore.startApp('react-app');
            await microCore.startApp('vue-app');

            const currentMemory = performance.memory?.usedJSHeapSize || 0;
            const memoryUsage = microCore.getMemoryUsage();

            expect(memoryUsage.total).toBeGreaterThan(initialMemory);
            expect(memoryUsage.apps).toBeDefined();
        });
    });
});