/**
 * @fileoverview 插件系统集成测试
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCore } from '@micro-core/core';
import { AuthPlugin } from '@micro-core/plugins/auth/src/auth-plugin';
import { CommunicationPlugin } from '@micro-core/plugins/communication/src/communication-plugin';
import { RouterPlugin } from '@micro-core/plugins/router/src/router-plugin';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('插件系统集成测试', () => {
    let microCore: MicroCore;
    let routerPlugin: RouterPlugin;
    let communicationPlugin: CommunicationPlugin;
    let authPlugin: AuthPlugin;

    beforeEach(() => {
        microCore = new MicroCore({
            debug: true,
            sandbox: true
        });

        routerPlugin = new RouterPlugin({
            mode: 'hash',
            base: '/'
        });

        communicationPlugin = new CommunicationPlugin({
            protocol: 'postMessage',
            timeout: 5000
        });

        authPlugin = new AuthPlugin({
            loginUrl: '/login',
            tokenKey: 'access_token'
        });
    });

    afterEach(() => {
        microCore.destroy();
    });

    describe('插件注册和生命周期', () => {
        it('应该能够注册多个插件', () => {
            microCore.use(routerPlugin);
            microCore.use(communicationPlugin);
            microCore.use(authPlugin);

            const plugins = microCore.getPlugins();
            expect(plugins).toHaveLength(3);
            expect(plugins.map(p => p.name)).toEqual([
                'router', 'communication', 'auth'
            ]);
        });

        it('应该能够按正确顺序初始化插件', async () => {
            const initOrder: string[] = [];

            const mockPlugin1 = {
                name: 'plugin1',
                version: '1.0.0',
                install: vi.fn().mockImplementation(() => {
                    initOrder.push('plugin1');
                }),
                uninstall: vi.fn()
            };

            const mockPlugin2 = {
                name: 'plugin2',
                version: '1.0.0',
                dependencies: ['plugin1'],
                install: vi.fn().mockImplementation(() => {
                    initOrder.push('plugin2');
                }),
                uninstall: vi.fn()
            };

            microCore.use(mockPlugin2);
            microCore.use(mockPlugin1);

            await microCore.initialize();

            expect(initOrder).toEqual(['plugin1', 'plugin2']);
        });

        it('应该能够处理插件依赖关系', () => {
            const pluginWithDeps = {
                name: 'dependent-plugin',
                version: '1.0.0',
                dependencies: ['router', 'communication'],
                install: vi.fn(),
                uninstall: vi.fn()
            };

            microCore.use(routerPlugin);
            microCore.use(communicationPlugin);
            microCore.use(pluginWithDeps);

            expect(() => microCore.validatePluginDependencies()).not.toThrow();
        });

        it('应该能够检测缺失的插件依赖', () => {
            const pluginWithMissingDeps = {
                name: 'dependent-plugin',
                version: '1.0.0',
                dependencies: ['missing-plugin'],
                install: vi.fn(),
                uninstall: vi.fn()
            };

            microCore.use(pluginWithMissingDeps);

            expect(() => microCore.validatePluginDependencies()).toThrow();
        });
    });

    describe('路由插件集成', () => {
        beforeEach(() => {
            microCore.use(routerPlugin);
        });

        it('应该能够监听路由变化并触发应用切换', async () => {
            const mockApp = global.createMockApp('test-app');

            microCore.registerApp({
                name: 'test-app',
                entry: mockApp.entry,
                container: '#test-container',
                activeWhen: '/test'
            });

            global.createMockContainer('test-container');

            // 监听应用状态变化
            const statusChanges: string[] = [];
            microCore.on('appStatusChange', (event) => {
                statusChanges.push(`${event.name}:${event.status}`);
            });

            // 导航到应用路由
            await routerPlugin.push('/test');
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(statusChanges).toContain('test-app:MOUNTED');
        });

        it('应该能够处理路由守卫', async () => {
            const guardFn = vi.fn().mockResolvedValue(true);

            routerPlugin.beforeEach(guardFn);

            await routerPlugin.push('/protected');

            expect(guardFn).toHaveBeenCalledWith(
                expect.objectContaining({ path: '/protected' }),
                expect.any(Object),
                expect.any(Function)
            );
        });

        it('应该能够阻止未授权的路由访问', async () => {
            const guardFn = vi.fn().mockResolvedValue(false);

            routerPlugin.beforeEach(guardFn);

            const currentPath = routerPlugin.getCurrentPath();
            await routerPlugin.push('/protected');

            // 路由应该没有改变
            expect(routerPlugin.getCurrentPath()).toBe(currentPath);
        });
    });

    describe('通信插件集成', () => {
        beforeEach(() => {
            microCore.use(communicationPlugin);
        });

        it('应该能够在应用间发送和接收消息', async () => {
            const mockApp1 = global.createMockApp('app1');
            const mockApp2 = global.createMockApp('app2');

            microCore.registerApp({
                name: 'app1',
                entry: mockApp1.entry,
                container: '#app1-container',
                activeWhen: '/app1'
            });

            microCore.registerApp({
                name: 'app2',
                entry: mockApp2.entry,
                container: '#app2-container',
                activeWhen: '/app2'
            });

            global.createMockContainer('app1-container');
            global.createMockContainer('app2-container');

            await microCore.startApp('app1');
            await microCore.startApp('app2');

            const messageHandler = vi.fn();
            communicationPlugin.on('message', messageHandler);

            const testMessage = {
                type: 'TEST_MESSAGE',
                data: { value: 'hello' }
            };

            communicationPlugin.send('app2', testMessage);

            await new Promise(resolve => setTimeout(resolve, 50));

            expect(messageHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    from: 'app1',
                    to: 'app2',
                    data: testMessage
                })
            );
        });

        it('应该能够处理消息传递超时', async () => {
            const timeoutHandler = vi.fn();
            communicationPlugin.on('timeout', timeoutHandler);

            // 发送消息给不存在的应用
            communicationPlugin.send('non-existent-app', { type: 'TEST' });

            await new Promise(resolve => setTimeout(resolve, 5100));

            expect(timeoutHandler).toHaveBeenCalled();
        });

        it('应该能够广播消息给所有应用', async () => {
            const mockApps = [
                global.createMockApp('app1'),
                global.createMockApp('app2'),
                global.createMockApp('app3')
            ];

            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`
                });
                global.createMockContainer(`${app.name}-container`);
            });

            await Promise.all(mockApps.map(app => microCore.startApp(app.name)));

            const messageHandlers = mockApps.map(() => vi.fn());
            messageHandlers.forEach(handler => {
                communicationPlugin.on('message', handler);
            });

            const broadcastMessage = {
                type: 'BROADCAST',
                data: { announcement: 'system update' }
            };

            communicationPlugin.broadcast(broadcastMessage);

            await new Promise(resolve => setTimeout(resolve, 100));

            messageHandlers.forEach(handler => {
                expect(handler).toHaveBeenCalled();
            });
        });
    });

    describe('认证插件集成', () => {
        beforeEach(() => {
            microCore.use(authPlugin);
            microCore.use(routerPlugin);
        });

        it('应该能够保护需要认证的路由', async () => {
            const mockApp = global.createMockApp('protected-app');

            microCore.registerApp({
                name: 'protected-app',
                entry: mockApp.entry,
                container: '#protected-container',
                activeWhen: '/protected',
                meta: { requiresAuth: true }
            });

            global.createMockContainer('protected-container');

            // 未登录状态下访问受保护路由
            await routerPlugin.push('/protected');

            // 应该被重定向到登录页
            expect(routerPlugin.getCurrentPath()).toBe('/login');
        });

        it('应该能够在登录后访问受保护的路由', async () => {
            const mockApp = global.createMockApp('protected-app');

            microCore.registerApp({
                name: 'protected-app',
                entry: mockApp.entry,
                container: '#protected-container',
                activeWhen: '/protected',
                meta: { requiresAuth: true }
            });

            global.createMockContainer('protected-container');

            // 模拟登录
            authPlugin.login({
                token: 'test-token',
                user: { id: 1, name: 'Test User' }
            });

            await routerPlugin.push('/protected');

            // 应该能够访问受保护路由
            expect(routerPlugin.getCurrentPath()).toBe('/protected');

            const app = microCore.getApp('protected-app');
            expect(app?.status).toBe('MOUNTED');
        });

        it('应该能够在token过期时自动登出', async () => {
            // 模拟过期的token
            authPlugin.login({
                token: 'expired-token',
                user: { id: 1, name: 'Test User' },
                expiresAt: Date.now() - 1000 // 已过期
            });

            const logoutHandler = vi.fn();
            authPlugin.on('logout', logoutHandler);

            // 检查token状态
            await authPlugin.checkTokenValidity();

            expect(logoutHandler).toHaveBeenCalled();
            expect(authPlugin.isAuthenticated()).toBe(false);
        });
    });

    describe('插件间协作', () => {
        beforeEach(() => {
            microCore.use(routerPlugin);
            microCore.use(communicationPlugin);
            microCore.use(authPlugin);
        });

        it('应该能够在路由变化时同步认证状态', async () => {
            const authStatusHandler = vi.fn();
            communicationPlugin.on('authStatusChange', authStatusHandler);

            // 登录
            authPlugin.login({
                token: 'test-token',
                user: { id: 1, name: 'Test User' }
            });

            await routerPlugin.push('/dashboard');

            expect(authStatusHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    isAuthenticated: true,
                    user: expect.objectContaining({ id: 1 })
                })
            );
        });

        it('应该能够在应用间同步用户状态', async () => {
            const mockApps = [
                global.createMockApp('app1'),
                global.createMockApp('app2')
            ];

            mockApps.forEach(app => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`
                });
                global.createMockContainer(`${app.name}-container`);
            });

            await Promise.all(mockApps.map(app => microCore.startApp(app.name)));

            // 在一个应用中登录
            authPlugin.login({
                token: 'test-token',
                user: { id: 1, name: 'Test User' }
            });

            // 验证用户状态被同步到所有应用
            const userState = microCore.getState('user');
            expect(userState).toEqual({
                id: 1,
                name: 'Test User',
                isAuthenticated: true
            });
        });

        it('应该能够处理插件间的事件传递', async () => {
            const eventHandlers = {
                router: vi.fn(),
                communication: vi.fn(),
                auth: vi.fn()
            };

            routerPlugin.on('navigate', eventHandlers.router);
            communicationPlugin.on('message', eventHandlers.communication);
            authPlugin.on('login', eventHandlers.auth);

            // 触发一系列相关事件
            authPlugin.login({
                token: 'test-token',
                user: { id: 1, name: 'Test User' }
            });

            await routerPlugin.push('/dashboard');

            communicationPlugin.broadcast({
                type: 'USER_LOGIN',
                data: { userId: 1 }
            });

            await new Promise(resolve => setTimeout(resolve, 100));

            expect(eventHandlers.auth).toHaveBeenCalled();
            expect(eventHandlers.router).toHaveBeenCalled();
            expect(eventHandlers.communication).toHaveBeenCalled();
        });
    });

    describe('插件错误处理', () => {
        it('应该能够处理插件初始化失败', async () => {
            const faultyPlugin = {
                name: 'faulty-plugin',
                version: '1.0.0',
                install: vi.fn().mockRejectedValue(new Error('初始化失败')),
                uninstall: vi.fn()
            };

            microCore.use(faultyPlugin);

            await expect(microCore.initialize()).rejects.toThrow('初始化失败');

            // 其他插件应该仍然正常工作
            microCore.use(routerPlugin);
            await expect(microCore.initialize()).resolves.not.toThrow();
        });

        it('应该能够隔离插件错误不影响核心功能', async () => {
            const faultyPlugin = {
                name: 'faulty-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn(),
                onAppMount: vi.fn().mockImplementation(() => {
                    throw new Error('插件处理错误');
                })
            };

            microCore.use(faultyPlugin);
            microCore.use(routerPlugin);

            const mockApp = global.createMockApp('test-app');
            microCore.registerApp({
                name: 'test-app',
                entry: mockApp.entry,
                container: '#test-container',
                activeWhen: '/test'
            });

            global.createMockContainer('test-container');

            // 应用启动应该成功，即使插件出错
            await expect(microCore.startApp('test-app')).resolves.not.toThrow();

            const app = microCore.getApp('test-app');
            expect(app?.status).toBe('MOUNTED');
        });

        it('应该能够在插件卸载时清理资源', async () => {
            const cleanupFn = vi.fn();
            const testPlugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: cleanupFn
            };

            microCore.use(testPlugin);
            await microCore.initialize();

            microCore.unuse('test-plugin');

            expect(cleanupFn).toHaveBeenCalled();
        });
    });
});