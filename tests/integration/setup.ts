/**
 * @fileoverview 集成测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { vi } from 'vitest';

// 继承所有包的测试设置
import '../../packages/adapters/test/setup';
import '../../packages/core/test/setup';
import '../../packages/plugins/test/setup';
import '../../packages/shared/test/setup';

// 模拟网络请求
global.fetch = vi.fn();

// 模拟服务器端口
const mockPorts = new Set([3001, 3002, 3003, 3004, 3005]);
let currentPort = 3001;

// 模拟HTTP服务器
global.createMockServer = (port?: number) => {
    const serverPort = port || currentPort++;
    mockPorts.add(serverPort);

    return {
        port: serverPort,
        url: `http://localhost:${serverPort}`,
        listen: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined),
        use: vi.fn(),
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        delete: vi.fn()
    };
};

// 模拟应用资源
global.createMockApp = (name: string, framework: string = 'react') => {
    const port = currentPort++;
    const baseUrl = `http://localhost:${port}`;

    return {
        name,
        framework,
        port,
        baseUrl,
        entry: `${baseUrl}/index.js`,
        manifest: `${baseUrl}/manifest.json`,
        assets: {
            js: [`${baseUrl}/index.js`, `${baseUrl}/vendor.js`],
            css: [`${baseUrl}/index.css`],
            html: `${baseUrl}/index.html`
        },
        lifecycle: {
            mount: vi.fn().mockResolvedValue(undefined),
            unmount: vi.fn().mockResolvedValue(undefined),
            update: vi.fn().mockResolvedValue(undefined),
            bootstrap: vi.fn().mockResolvedValue(undefined)
        },
        server: global.createMockServer(port)
    };
};

// 模拟应用清单文件
global.createMockManifest = (appName: string, framework: string = 'react') => {
    return {
        name: appName,
        version: '1.0.0',
        framework,
        entry: './index.js',
        assets: {
            js: ['./index.js', './vendor.js'],
            css: ['./index.css']
        },
        dependencies: {
            react: '^18.0.0',
            'react-dom': '^18.0.0'
        },
        microCore: {
            version: '^1.0.0',
            plugins: ['router', 'communication'],
            sandbox: true,
            isolation: {
                style: true,
                script: true
            }
        }
    };
};

// 模拟DOM容器
global.createMockContainer = (id: string) => {
    const container = document.createElement('div');
    container.id = id;
    container.style.width = '100%';
    container.style.height = '100%';
    document.body.appendChild(container);
    return container;
};

// 模拟路由变化
global.mockRouteChange = (path: string, state?: any) => {
    const event = new PopStateEvent('popstate', { state });
    Object.defineProperty(window, 'location', {
        value: {
            ...window.location,
            pathname: path,
            href: `${window.location.origin}${path}`,
            search: '',
            hash: ''
        },
        writable: true
    });
    window.dispatchEvent(event);
};

// 模拟应用间通信
global.mockAppCommunication = () => {
    const messages: any[] = [];

    const originalPostMessage = window.postMessage;
    window.postMessage = vi.fn((message, targetOrigin) => {
        messages.push({ message, targetOrigin, timestamp: Date.now() });

        // 模拟异步消息传递
        setTimeout(() => {
            const event = new MessageEvent('message', {
                data: message,
                origin: window.location.origin,
                source: window
            });
            window.dispatchEvent(event);
        }, 0);
    });

    return {
        messages,
        clear: () => messages.length = 0,
        restore: () => {
            window.postMessage = originalPostMessage;
        }
    };
};

// 模拟性能监控
global.mockPerformanceMonitor = () => {
    const metrics: any[] = [];

    const originalMark = performance.mark;
    const originalMeasure = performance.measure;

    performance.mark = vi.fn((name: string) => {
        metrics.push({
            type: 'mark',
            name,
            timestamp: performance.now()
        });
        return originalMark.call(performance, name);
    });

    performance.measure = vi.fn((name: string, startMark?: string, endMark?: string) => {
        const startTime = startMark ?
            metrics.find(m => m.name === startMark)?.timestamp || 0 : 0;
        const endTime = endMark ?
            metrics.find(m => m.name === endMark)?.timestamp || performance.now() : performance.now();

        metrics.push({
            type: 'measure',
            name,
            duration: endTime - startTime,
            startTime,
            endTime
        });

        return originalMeasure.call(performance, name, startMark, endMark);
    });

    return {
        metrics,
        clear: () => metrics.length = 0,
        restore: () => {
            performance.mark = originalMark;
            performance.measure = originalMeasure;
        }
    };
};

// 模拟错误收集
global.mockErrorCollector = () => {
    const errors: any[] = [];

    const originalErrorHandler = window.onerror;
    const originalUnhandledRejection = window.onunhandledrejection;

    window.onerror = (message, source, lineno, colno, error) => {
        errors.push({
            type: 'error',
            message,
            source,
            lineno,
            colno,
            error,
            timestamp: Date.now()
        });
        return false;
    };

    window.onunhandledrejection = (event) => {
        errors.push({
            type: 'unhandledrejection',
            reason: event.reason,
            promise: event.promise,
            timestamp: Date.now()
        });
    };

    return {
        errors,
        clear: () => errors.length = 0,
        restore: () => {
            window.onerror = originalErrorHandler;
            window.onunhandledrejection = originalUnhandledRejection;
        }
    };
};

// 全局清理函数
global.cleanupIntegrationTest = () => {
    // 清理DOM
    document.body.innerHTML = '';

    // 清理事件监听器
    window.removeEventListener('popstate', () => { });
    window.removeEventListener('message', () => { });

    // 重置全局状态
    if (window.__MICRO_CORE__) {
        window.__MICRO_CORE__.apps.clear();
        window.__MICRO_CORE__.plugins.clear();
    }

    // 清理模拟
    vi.clearAllMocks();
};

// 测试前后自动清理
beforeEach(() => {
    global.cleanupIntegrationTest();
});

afterEach(() => {
    global.cleanupIntegrationTest();
});