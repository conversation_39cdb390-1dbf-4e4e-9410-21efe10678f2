import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./setup.ts'],
        testTimeout: 30000,
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            exclude: [
                'node_modules/',
                'tests/',
                'dist/',
                '**/*.d.ts',
                '**/*.config.*'
            ]
        }
    },
    resolve: {
        alias: {
            '@micro-core/shared': resolve(__dirname, '../../packages/shared/src'),
            '@micro-core/core': resolve(__dirname, '../../packages/core/src'),
            '@micro-core/plugins': resolve(__dirname, '../../packages/plugins/src'),
            '@micro-core/adapters': resolve(__dirname, '../../packages/adapters/src'),
            '@micro-core/builders': resolve(__dirname, '../../packages/builders/src'),
            '@micro-core/sidecar': resolve(__dirname, '../../packages/sidecar/src')
        }
    }
});