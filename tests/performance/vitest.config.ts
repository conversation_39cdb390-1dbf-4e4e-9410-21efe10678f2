/**
 * @fileoverview 性能基准测试配置
 * <AUTHOR> <<EMAIL>>
 */

import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        name: 'performance',
        root: resolve(__dirname),
        environment: 'jsdom',
        setupFiles: ['./setup.ts'],
        testTimeout: 30000, // 性能测试需要更长时间
        hookTimeout: 10000,
        globals: true,
        reporters: [
            'default',
            ['json', { outputFile: './reports/performance-results.json' }],
            ['html', { outputFile: './reports/performance-report.html' }]
        ],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            reportsDirectory: './coverage',
            include: [
                '../../packages/*/src/**/*.ts',
                '../../apps/*/src/**/*.{ts,tsx}'
            ],
            exclude: [
                '**/*.test.ts',
                '**/*.spec.ts',
                '**/node_modules/**',
                '**/dist/**'
            ]
        }
    },
    resolve: {
        alias: {
            '@micro-core/shared': resolve(__dirname, '../../packages/shared/src'),
            '@micro-core/core': resolve(__dirname, '../../packages/core/src'),
            '@micro-core/plugins': resolve(__dirname, '../../packages/plugins/src'),
            '@micro-core/adapters': resolve(__dirname, '../../packages/adapters/src'),
            '@micro-core/builders': resolve(__dirname, '../../packages/builders/src'),
            '@micro-core/sidecar': resolve(__dirname, '../../packages/sidecar/src')
        }
    }
});