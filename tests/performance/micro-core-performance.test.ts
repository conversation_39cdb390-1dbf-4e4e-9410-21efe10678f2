/**
 * @fileoverview 微前端核心性能基准测试
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCore } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { createLoadTest, createMemoryTest, createPerformanceTest, measurePerformance } from './setup';

describe('微前端核心性能基准测试', () => {
    let microCore: MicroCore;

    beforeEach(() => {
        microCore = new MicroCore({
            debug: false, // 关闭调试模式以获得更准确的性能数据
            sandbox: true
        });
    });

    afterEach(() => {
        microCore.destroy();
    });

    describe('初始化性能测试', () => {
        it('微前端核心初始化应在50ms内完成', createPerformanceTest(
            'micro-core-initialization',
            50,
            () => {
                new MicroCore({
                    debug: false,
                    sandbox: true,
                    prefetch: true
                });
            }
        ));

        it('大量配置初始化应在100ms内完成', createPerformanceTest(
            'complex-initialization',
            100,
            () => {
                new MicroCore({
                    debug: false,
                    sandbox: true,
                    prefetch: true,
                    plugins: [
                        { name: 'router', config: { mode: 'history' } },
                        { name: 'communication', config: { timeout: 5000 } },
                        { name: 'auth', config: { tokenKey: 'token' } }
                    ],
                    globalState: {
                        user: { id: 1, name: 'Test' },
                        theme: 'dark',
                        permissions: ['read', 'write']
                    }
                });
            }
        ));

        it('初始化内存使用应小于10MB', createMemoryTest(
            'initialization-memory',
            10,
            () => {
                const cores = Array.from({ length: 10 }, () =>
                    new MicroCore({ debug: false, sandbox: true })
                );
                cores.forEach(core => core.destroy());
            }
        ));
    });

    describe('应用注册性能测试', () => {
        it('单个应用注册应在5ms内完成', createPerformanceTest(
            'single-app-registration',
            5,
            () => {
                microCore.registerApp({
                    name: 'test-app',
                    entry: 'http://localhost:3001/index.js',
                    container: '#test-container',
                    activeWhen: '/test'
                });
            }
        ));

        it('批量应用注册性能测试', createLoadTest(
            'batch-app-registration',
            100, // 注册100个应用
            200, // 平均每个应用注册时间不超过2ms
            () => {
                const appName = `app-${Date.now()}-${Math.random()}`;
                microCore.registerApp({
                    name: appName,
                    entry: `http://localhost:3001/${appName}.js`,
                    container: `#${appName}-container`,
                    activeWhen: `/${appName}`
                });
            }
        ));

        it('大量应用注册内存测试', createMemoryTest(
            'mass-app-registration-memory',
            50,
            () => {
                // 注册1000个应用
                for (let i = 0; i < 1000; i++) {
                    microCore.registerApp({
                        name: `mass-app-${i}`,
                        entry: `http://localhost:3001/app-${i}.js`,
                        container: `#container-${i}`,
                        activeWhen: `/app-${i}`
                    });
                }
            }
        ));
    });

    describe('应用启动性能测试', () => {
        beforeEach(() => {
            // 模拟应用资源
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['test-app'] = {
                        mount: function(props) {
                            const div = document.createElement('div');
                            div.textContent = 'Test App';
                            props.container.appendChild(div);
                            return Promise.resolve();
                        },
                        unmount: function(props) {
                            props.container.innerHTML = '';
                            return Promise.resolve();
                        }
                    };
                `)
            });

            // 创建容器元素
            const container = document.createElement('div');
            container.id = 'test-container';
            document.body.appendChild(container);
        });

        afterEach(() => {
            const container = document.getElementById('test-container');
            if (container) {
                document.body.removeChild(container);
            }
        });

        it('应用启动应在200ms内完成', async () => {
            microCore.registerApp({
                name: 'test-app',
                entry: 'http://localhost:3001/test.js',
                container: '#test-container',
                activeWhen: '/test'
            });

            const { metrics } = await measurePerformance(
                'app-startup',
                () => microCore.startApp('test-app'),
                200
            );

            expect(metrics.duration).toBeLessThan(200);
        });

        it('应用冷启动vs热启动性能对比', async () => {
            microCore.registerApp({
                name: 'test-app',
                entry: 'http://localhost:3001/test.js',
                container: '#test-container',
                activeWhen: '/test'
            });

            // 冷启动
            const { metrics: coldStart } = await measurePerformance(
                'cold-start',
                () => microCore.startApp('test-app')
            );

            await microCore.stopApp('test-app');

            // 热启动（资源已缓存）
            const { metrics: hotStart } = await measurePerformance(
                'hot-start',
                () => microCore.startApp('test-app')
            );

            // 热启动应该比冷启动快至少50%
            expect(hotStart.duration).toBeLessThan(coldStart.duration * 0.5);
        });

        it('并发应用启动性能测试', async () => {
            const appCount = 5;
            const apps = Array.from({ length: appCount }, (_, i) => ({
                name: `concurrent-app-${i}`,
                entry: `http://localhost:3001/app-${i}.js`,
                container: `#container-${i}`,
                activeWhen: `/app-${i}`
            }));

            // 注册所有应用
            apps.forEach(app => {
                microCore.registerApp(app);
                const container = document.createElement('div');
                container.id = app.container.slice(1);
                document.body.appendChild(container);
            });

            // 并发启动测试
            const { metrics } = await measurePerformance(
                'concurrent-startup',
                () => Promise.all(apps.map(app => microCore.startApp(app.name))),
                500 // 5个应用并发启动应在500ms内完成
            );

            expect(metrics.duration).toBeLessThan(500);

            // 清理容器
            apps.forEach(app => {
                const container = document.getElementById(app.container.slice(1));
                if (container) {
                    document.body.removeChild(container);
                }
            });
        });
    });

    describe('路由性能测试', () => {
        beforeEach(() => {
            // 注册多个应用用于路由测试
            const apps = [
                { name: 'home-app', activeWhen: '/' },
                { name: 'user-app', activeWhen: '/users' },
                { name: 'order-app', activeWhen: '/orders' },
                { name: 'product-app', activeWhen: '/products' },
                { name: 'admin-app', activeWhen: '/admin' }
            ];

            apps.forEach(app => {
                microCore.registerApp({
                    name: app.name,
                    entry: `http://localhost:3001/${app.name}.js`,
                    container: `#${app.name}-container`,
                    activeWhen: app.activeWhen
                });
            });
        });

        it('路由匹配应在1ms内完成', createPerformanceTest(
            'route-matching',
            1,
            () => {
                microCore.matchRoute('/users/123/profile');
            }
        ));

        it('复杂路由匹配性能测试', createLoadTest(
            'complex-route-matching',
            1000, // 1000次路由匹配
            1, // 平均每次匹配不超过1ms
            () => {
                const routes = [
                    '/',
                    '/users',
                    '/users/123',
                    '/users/123/profile',
                    '/orders',
                    '/orders/456',
                    '/products',
                    '/products/category/electronics',
                    '/admin',
                    '/admin/settings'
                ];
                const randomRoute = routes[Math.floor(Math.random() * routes.length)];
                microCore.matchRoute(randomRoute);
            }
        ));

        it('路由切换性能测试', async () => {
            const routes = ['/', '/users', '/orders', '/products', '/admin'];
            const switchTimes: number[] = [];

            for (const route of routes) {
                const { metrics } = await measurePerformance(
                    `route-switch-${route}`,
                    () => microCore.navigateTo(route)
                );
                switchTimes.push(metrics.duration);
            }

            const averageSwitchTime = switchTimes.reduce((sum, time) => sum + time, 0) / switchTimes.length;

            // 平均路由切换时间应小于50ms
            expect(averageSwitchTime).toBeLessThan(50);
        });
    });

    describe('通信性能测试', () => {
        it('应用间消息传递应在5ms内完成', createPerformanceTest(
            'inter-app-communication',
            5,
            () => {
                microCore.sendMessage('test-app', {
                    type: 'TEST_MESSAGE',
                    data: { id: 1, message: 'Hello' }
                });
            }
        ));

        it('广播消息性能测试', createPerformanceTest(
            'broadcast-message',
            10,
            () => {
                microCore.broadcast({
                    type: 'GLOBAL_UPDATE',
                    data: { timestamp: Date.now() }
                });
            }
        ));

        it('大量消息传递性能测试', createLoadTest(
            'mass-messaging',
            1000, // 发送1000条消息
            5, // 平均每条消息处理时间不超过5ms
            () => {
                microCore.sendMessage('test-app', {
                    type: 'PERFORMANCE_TEST',
                    data: {
                        id: Math.random(),
                        timestamp: Date.now(),
                        payload: 'x'.repeat(100) // 100字符负载
                    }
                });
            }
        ));

        it('消息序列化性能测试', createPerformanceTest(
            'message-serialization',
            2,
            () => {
                const largeData = {
                    users: Array.from({ length: 1000 }, (_, i) => ({
                        id: i,
                        name: `User ${i}`,
                        email: `user${i}@example.com`,
                        profile: {
                            age: 20 + (i % 50),
                            city: `City ${i % 100}`,
                            interests: [`hobby${i % 10}`, `sport${i % 5}`]
                        }
                    }))
                };

                microCore.sendMessage('test-app', {
                    type: 'LARGE_DATA',
                    data: largeData
                });
            }
        ));
    });

    describe('状态管理性能测试', () => {
        it('状态更新应在2ms内完成', createPerformanceTest(
            'state-update',
            2,
            () => {
                microCore.setState('user', {
                    id: 1,
                    name: 'Updated User',
                    timestamp: Date.now()
                });
            }
        ));

        it('大量状态更新性能测试', createLoadTest(
            'mass-state-updates',
            1000, // 1000次状态更新
            2, // 平均每次更新不超过2ms
            () => {
                const key = `state-${Math.floor(Math.random() * 100)}`;
                microCore.setState(key, {
                    value: Math.random(),
                    timestamp: Date.now()
                });
            }
        ));

        it('状态订阅性能测试', createPerformanceTest(
            'state-subscription',
            5,
            () => {
                // 创建100个状态订阅
                for (let i = 0; i < 100; i++) {
                    microCore.subscribe(`state-${i}`, (value) => {
                        // 模拟状态处理
                        return value;
                    });
                }
            }
        ));

        it('状态通知性能测试', async () => {
            // 创建多个订阅者
            const subscriptions = Array.from({ length: 100 }, (_, i) => {
                return microCore.subscribe(`test-state`, (value) => {
                    return value;
                });
            });

            // 测试状态更新通知性能
            const { metrics } = await measurePerformance(
                'state-notification',
                () => {
                    microCore.setState('test-state', {
                        id: Date.now(),
                        data: 'test data'
                    });
                },
                20 // 通知100个订阅者应在20ms内完成
            );

            expect(metrics.duration).toBeLessThan(20);

            // 清理订阅
            subscriptions.forEach(unsubscribe => unsubscribe());
        });
    });

    describe('内存管理性能测试', () => {
        it('应用卸载后应释放内存', async () => {
            // 创建容器
            const container = document.createElement('div');
            container.id = 'memory-test-container';
            document.body.appendChild(container);

            // 模拟应用
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['memory-test-app'] = {
                        mount: function(props) {
                            // 创建大量DOM元素模拟内存使用
                            for (let i = 0; i < 1000; i++) {
                                const div = document.createElement('div');
                                div.textContent = 'Memory test element ' + i;
                                props.container.appendChild(div);
                            }
                            return Promise.resolve();
                        },
                        unmount: function(props) {
                            props.container.innerHTML = '';
                            return Promise.resolve();
                        }
                    };
                `)
            });

            microCore.registerApp({
                name: 'memory-test-app',
                entry: 'http://localhost:3001/memory-test.js',
                container: '#memory-test-container',
                activeWhen: '/memory-test'
            });

            // 启动应用
            await microCore.startApp('memory-test-app');

            // 记录启动后内存
            const memoryAfterMount = process.memoryUsage?.() || { heapUsed: 0 };

            // 停止应用
            await microCore.stopApp('memory-test-app');

            // 强制垃圾回收（如果可用）
            if (global.gc) {
                global.gc();
            }

            // 记录卸载后内存
            const memoryAfterUnmount = process.memoryUsage?.() || { heapUsed: 0 };

            // 内存应该有所释放（允许一定误差）
            const memoryDiff = memoryAfterMount.heapUsed - memoryAfterUnmount.heapUsed;
            expect(memoryDiff).toBeGreaterThan(-1024 * 1024); // 允许1MB的误差

            // 清理
            document.body.removeChild(container);
        });

        it('长时间运行内存稳定性测试', async () => {
            const initialMemory = process.memoryUsage?.()?.heapUsed || 0;

            // 模拟长时间运行场景
            for (let i = 0; i < 100; i++) {
                // 注册应用
                microCore.registerApp({
                    name: `stability-app-${i}`,
                    entry: `http://localhost:3001/app-${i}.js`,
                    container: `#container-${i}`,
                    activeWhen: `/app-${i}`
                });

                // 更新状态
                microCore.setState(`state-${i}`, { value: i });

                // 发送消息
                microCore.broadcast({ type: 'TEST', data: i });

                // 每10次循环清理一次
                if (i % 10 === 0) {
                    // 清理一些应用
                    const appsToClean = Math.min(5, i);
                    for (let j = 0; j < appsToClean; j++) {
                        const appName = `stability-app-${i - j - 1}`;
                        try {
                            microCore.unregisterApp(appName);
                        } catch (error) {
                            // 忽略清理错误
                        }
                    }

                    // 强制垃圾回收
                    if (global.gc) {
                        global.gc();
                    }
                }
            }

            const finalMemory = process.memoryUsage?.()?.heapUsed || 0;
            const memoryGrowth = finalMemory - initialMemory;
            const memoryGrowthMB = memoryGrowth / 1024 / 1024;

            // 内存增长应该控制在合理范围内（50MB）
            expect(memoryGrowthMB).toBeLessThan(50);
        });
    });

    describe('并发性能测试', () => {
        it('高并发应用注册性能测试', async () => {
            const concurrentCount = 50;
            const { metrics } = await measurePerformance(
                'concurrent-app-registration',
                () => {
                    const promises = Array.from({ length: concurrentCount }, (_, i) => {
                        return new Promise<void>((resolve) => {
                            microCore.registerApp({
                                name: `concurrent-app-${i}`,
                                entry: `http://localhost:3001/app-${i}.js`,
                                container: `#container-${i}`,
                                activeWhen: `/app-${i}`
                            });
                            resolve();
                        });
                    });
                    return Promise.all(promises);
                },
                1000 // 50个应用并发注册应在1秒内完成
            );

            expect(metrics.duration).toBeLessThan(1000);
        });

        it('高并发状态更新性能测试', async () => {
            const concurrentCount = 100;
            const { metrics } = await measurePerformance(
                'concurrent-state-updates',
                () => {
                    const promises = Array.from({ length: concurrentCount }, (_, i) => {
                        return new Promise<void>((resolve) => {
                            microCore.setState(`concurrent-state-${i}`, {
                                id: i,
                                value: Math.random(),
                                timestamp: Date.now()
                            });
                            resolve();
                        });
                    });
                    return Promise.all(promises);
                },
                500 // 100个并发状态更新应在500ms内完成
            );

            expect(metrics.duration).toBeLessThan(500);
        });

        it('高并发消息传递性能测试', async () => {
            const concurrentCount = 200;
            const { metrics } = await measurePerformance(
                'concurrent-messaging',
                () => {
                    const promises = Array.from({ length: concurrentCount }, (_, i) => {
                        return new Promise<void>((resolve) => {
                            microCore.broadcast({
                                type: 'CONCURRENT_TEST',
                                data: {
                                    id: i,
                                    timestamp: Date.now(),
                                    payload: `Message ${i}`
                                }
                            });
                            resolve();
                        });
                    });
                    return Promise.all(promises);
                },
                800 // 200个并发消息应在800ms内完成
            );

            expect(metrics.duration).toBeLessThan(800);
        });
    });

    describe('压力测试', () => {
        it('大量应用管理压力测试', async () => {
            const appCount = 500;

            // 注册大量应用
            const { metrics: registrationMetrics } = await measurePerformance(
                'mass-app-registration',
                () => {
                    for (let i = 0; i < appCount; i++) {
                        microCore.registerApp({
                            name: `stress-app-${i}`,
                            entry: `http://localhost:3001/stress-${i}.js`,
                            container: `#stress-container-${i}`,
                            activeWhen: `/stress-${i}`
                        });
                    }
                },
                2000 // 500个应用注册应在2秒内完成
            );

            expect(registrationMetrics.duration).toBeLessThan(2000);

            // 批量查询应用信息
            const { metrics: queryMetrics } = await measurePerformance(
                'mass-app-query',
                () => {
                    for (let i = 0; i < appCount; i++) {
                        microCore.getApp(`stress-app-${i}`);
                    }
                },
                500 // 500个应用查询应在500ms内完成
            );

            expect(queryMetrics.duration).toBeLessThan(500);

            // 批量卸载应用
            const { metrics: unregistrationMetrics } = await measurePerformance(
                'mass-app-unregistration',
                () => {
                    for (let i = 0; i < appCount; i++) {
                        microCore.unregisterApp(`stress-app-${i}`);
                    }
                },
                1000 // 500个应用卸载应在1秒内完成
            );

            expect(unregistrationMetrics.duration).toBeLessThan(1000);
        });

        it('大量状态管理压力测试', async () => {
            const stateCount = 1000;

            // 创建大量状态
            const { metrics: creationMetrics } = await measurePerformance(
                'mass-state-creation',
                () => {
                    for (let i = 0; i < stateCount; i++) {
                        microCore.setState(`stress-state-${i}`, {
                            id: i,
                            data: `State data ${i}`,
                            nested: {
                                value: Math.random(),
                                array: Array.from({ length: 10 }, (_, j) => j)
                            }
                        });
                    }
                },
                1500 // 1000个状态创建应在1.5秒内完成
            );

            expect(creationMetrics.duration).toBeLessThan(1500);

            // 批量读取状态
            const { metrics: readMetrics } = await measurePerformance(
                'mass-state-read',
                () => {
                    for (let i = 0; i < stateCount; i++) {
                        microCore.getState(`stress-state-${i}`);
                    }
                },
                300 // 1000个状态读取应在300ms内完成
            );

            expect(readMetrics.duration).toBeLessThan(300);

            // 批量更新状态
            const { metrics: updateMetrics } = await measurePerformance(
                'mass-state-update',
                () => {
                    for (let i = 0; i < stateCount; i++) {
                        microCore.setState(`stress-state-${i}`, {
                            id: i,
                            data: `Updated state data ${i}`,
                            timestamp: Date.now()
                        });
                    }
                },
                1000 // 1000个状态更新应在1秒内完成
            );

            expect(updateMetrics.duration).toBeLessThan(1000);
        });

        it('复杂场景综合压力测试', async () => {
            const { metrics } = await measurePerformance(
                'complex-stress-test',
                async () => {
                    // 同时进行多种操作
                    const operations = [
                        // 应用管理操作
                        ...Array.from({ length: 50 }, (_, i) => () => {
                            microCore.registerApp({
                                name: `complex-app-${i}`,
                                entry: `http://localhost:3001/complex-${i}.js`,
                                container: `#complex-container-${i}`,
                                activeWhen: `/complex-${i}`
                            });
                        }),

                        // 状态管理操作
                        ...Array.from({ length: 100 }, (_, i) => () => {
                            microCore.setState(`complex-state-${i}`, {
                                value: Math.random(),
                                timestamp: Date.now()
                            });
                        }),

                        // 消息传递操作
                        ...Array.from({ length: 200 }, (_, i) => () => {
                            microCore.broadcast({
                                type: 'COMPLEX_TEST',
                                data: { id: i, message: `Complex message ${i}` }
                            });
                        }),

                        // 路由操作
                        ...Array.from({ length: 30 }, (_, i) => () => {
                            microCore.matchRoute(`/complex-${i}/sub-route`);
                        })
                    ];

                    // 随机执行所有操作
                    const shuffledOperations = operations.sort(() => Math.random() - 0.5);

                    // 分批执行以避免过度并发
                    const batchSize = 20;
                    for (let i = 0; i < shuffledOperations.length; i += batchSize) {
                        const batch = shuffledOperations.slice(i, i + batchSize);
                        await Promise.all(batch.map(op => Promise.resolve(op())));
                    }
                },
                3000 // 复杂综合测试应在3秒内完成
            );

            expect(metrics.duration).toBeLessThan(3000);
        });
    });

    describe('性能回归测试', () => {
        it('版本间性能对比基准测试', async () => {
            // 这个测试用于建立性能基准，用于版本间对比
            const benchmarks = {
                initialization: 0,
                appRegistration: 0,
                stateUpdate: 0,
                messaging: 0,
                routeMatching: 0
            };

            // 初始化基准
            const { metrics: initMetrics } = await measurePerformance(
                'benchmark-initialization',
                () => new MicroCore({ debug: false, sandbox: true })
            );
            benchmarks.initialization = initMetrics.duration;

            // 应用注册基准
            const { metrics: regMetrics } = await measurePerformance(
                'benchmark-app-registration',
                () => {
                    microCore.registerApp({
                        name: 'benchmark-app',
                        entry: 'http://localhost:3001/benchmark.js',
                        container: '#benchmark-container',
                        activeWhen: '/benchmark'
                    });
                }
            );
            benchmarks.appRegistration = regMetrics.duration;

            // 状态更新基准
            const { metrics: stateMetrics } = await measurePerformance(
                'benchmark-state-update',
                () => {
                    microCore.setState('benchmark-state', {
                        value: Math.random(),
                        timestamp: Date.now()
                    });
                }
            );
            benchmarks.stateUpdate = stateMetrics.duration;

            // 消息传递基准
            const { metrics: msgMetrics } = await measurePerformance(
                'benchmark-messaging',
                () => {
                    microCore.broadcast({
                        type: 'BENCHMARK_MESSAGE',
                        data: { test: true }
                    });
                }
            );
            benchmarks.messaging = msgMetrics.duration;

            // 路由匹配基准
            const { metrics: routeMetrics } = await measurePerformance(
                'benchmark-route-matching',
                () => {
                    microCore.matchRoute('/benchmark/test/route');
                }
            );
            benchmarks.routeMatching = routeMetrics.duration;

            // 输出基准数据（可用于CI/CD中的性能监控）
            console.log('性能基准数据:', JSON.stringify(benchmarks, null, 2));

            // 基本的性能断言
            expect(benchmarks.initialization).toBeLessThan(100);
            expect(benchmarks.appRegistration).toBeLessThan(10);
            expect(benchmarks.stateUpdate).toBeLessThan(5);
            expect(benchmarks.messaging).toBeLessThan(5);
            expect(benchmarks.routeMatching).toBeLessThan(2);
        });
    });
});
