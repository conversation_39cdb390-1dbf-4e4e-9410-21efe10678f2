/**
 * @fileoverview 微前端应用生命周期E2E测试
 * <AUTHOR> <<EMAIL>>
 */

import { expect, test } from '@playwright/test';
import { PlaygroundPage } from '../pages/playground.page';

test.describe('微前端应用生命周期E2E测试', () => {
    let playgroundPage: PlaygroundPage;

    test.beforeEach(async ({ page }) => {
        playgroundPage = new PlaygroundPage(page);
        await playgroundPage.goto();
    });

    test.describe('页面基础功能', () => {
        test('应该能够正常加载Playground页面', async () => {
            // 验证页面标题
            await expect(playgroundPage.page).toHaveTitle(/Micro-Core Playground/);

            // 验证主要页面元素存在
            await expect(playgroundPage.header).toBeVisible();
            await expect(playgroundPage.navigation).toBeVisible();
            await expect(playgroundPage.mainContent).toBeVisible();

            // 截图记录
            await playgroundPage.takeScreenshot('playground-loaded');
        });

        test('应该能够在不同功能模块间导航', async () => {
            // 测试导航到各个模块
            const modules = [
                { tab: 'navigateToOverview', name: '概览' },
                { tab: 'navigateToApps', name: '应用管理' },
                { tab: 'navigateToPlugins', name: '插件系统' },
                { tab: 'navigateToAdapters', name: '适配器' },
                { tab: 'navigateToBuilders', name: '构建工具' },
                { tab: 'navigateToSidecar', name: '边车模式' },
                { tab: 'navigateToDebug', name: '调试工具' },
                { tab: 'navigateToSettings', name: '系统设置' }
            ];

            for (const module of modules) {
                await (playgroundPage as any)[module.tab]();

                // 验证URL变化
                expect(playgroundPage.page.url()).toContain(module.tab.replace('navigateTo', '').toLowerCase());

                // 验证页面内容加载
                await expect(playgroundPage.mainContent).toBeVisible();

                // 截图记录
                await playgroundPage.takeScreenshot(`navigation-${module.name}`);
            }
        });

        test('应该能够显示系统状态信息', async () => {
            await playgroundPage.navigateToOverview();

            const systemStatus = await playgroundPage.getSystemStatus();

            // 验证系统状态
            expect(systemStatus.coreStatus).toBeTruthy();
            expect(systemStatus.pluginCount).toBeGreaterThanOrEqual(0);
            expect(systemStatus.appCount).toBeGreaterThanOrEqual(0);
            expect(systemStatus.adapterCount).toBeGreaterThanOrEqual(0);
        });
    });

    test.describe('微前端应用管理', () => {
        test.beforeEach(async () => {
            await playgroundPage.navigateToApps();
        });

        test('应该能够查看应用列表', async () => {
            const appList = await playgroundPage.getAppList();

            // 验证应用列表结构
            expect(Array.isArray(appList)).toBe(true);

            // 如果有预配置的应用，验证其属性
            if (appList.length > 0) {
                const firstApp = appList[0];
                expect(firstApp).toHaveProperty('name');
                expect(firstApp).toHaveProperty('status');
                expect(firstApp).toHaveProperty('framework');
            }

            await playgroundPage.takeScreenshot('app-list');
        });

        test('应该能够添加新的微前端应用', async () => {
            const newAppConfig = {
                name: 'test-react-app',
                entry: 'http://localhost:3001/index.js',
                container: '#test-container',
                activeWhen: '/test-app',
                framework: 'react'
            };

            await playgroundPage.addApp(newAppConfig);

            // 验证应用添加成功
            const successMessage = await playgroundPage.waitForSuccess();
            expect(successMessage).toContain('添加成功');

            // 验证应用出现在列表中
            const appList = await playgroundPage.getAppList();
            const addedApp = appList.find(app => app.name === newAppConfig.name);
            expect(addedApp).toBeTruthy();

            await playgroundPage.takeScreenshot('app-added');
        });

        test('应该能够启动和停止微前端应用', async () => {
            // 首先添加一个测试应用
            const testAppConfig = {
                name: 'lifecycle-test-app',
                entry: 'http://localhost:3001/index.js',
                container: '#lifecycle-container',
                activeWhen: '/lifecycle-test',
                framework: 'react'
            };

            await playgroundPage.addApp(testAppConfig);
            await playgroundPage.waitForSuccess();

            // 启动应用
            await playgroundPage.startApp(testAppConfig.name);
            await playgroundPage.takeScreenshot('app-started');

            // 验证应用状态
            await playgroundPage.waitForAppStatus(testAppConfig.name, 'MOUNTED');

            // 停止应用
            await playgroundPage.stopApp(testAppConfig.name);
            await playgroundPage.takeScreenshot('app-stopped');

            // 验证应用状态
            await playgroundPage.waitForAppStatus(testAppConfig.name, 'UNMOUNTED');
        });

        test('应该能够重启微前端应用', async () => {
            const testAppConfig = {
                name: 'restart-test-app',
                entry: 'http://localhost:3001/index.js',
                container: '#restart-container',
                activeWhen: '/restart-test',
                framework: 'react'
            };

            await playgroundPage.addApp(testAppConfig);
            await playgroundPage.waitForSuccess();

            // 先启动应用
            await playgroundPage.startApp(testAppConfig.name);
            await playgroundPage.waitForAppStatus(testAppConfig.name, 'MOUNTED');

            // 重启应用
            await playgroundPage.restartApp(testAppConfig.name);
            await playgroundPage.takeScreenshot('app-restarted');

            // 验证应用重新启动
            await playgroundPage.waitForAppStatus(testAppConfig.name, 'MOUNTED');
        });

        test('应该能够删除微前端应用', async () => {
            const testAppConfig = {
                name: 'delete-test-app',
                entry: 'http://localhost:3001/index.js',
                container: '#delete-container',
                activeWhen: '/delete-test',
                framework: 'react'
            };

            await playgroundPage.addApp(testAppConfig);
            await playgroundPage.waitForSuccess();

            // 删除应用
            await playgroundPage.removeApp(testAppConfig.name);

            // 验证应用从列表中消失
            const appList = await playgroundPage.getAppList();
            const deletedApp = appList.find(app => app.name === testAppConfig.name);
            expect(deletedApp).toBeFalsy();

            await playgroundPage.takeScreenshot('app-deleted');
        });
    });

    test.describe('插件系统管理', () => {
        test.beforeEach(async () => {
            await playgroundPage.navigateToPlugins();
        });

        test('应该能够查看插件列表和状态', async () => {
            // 验证插件页面加载
            await expect(playgroundPage.mainContent).toBeVisible();

            // 查找插件列表元素
            const pluginList = playgroundPage.page.locator('[data-testid="plugin-list"]');
            await expect(pluginList).toBeVisible();

            // 验证核心插件存在
            const corePlugins = ['router', 'communication', 'auth'];

            for (const pluginName of corePlugins) {
                const pluginItem = pluginList.locator(`[data-plugin-name="${pluginName}"]`);
                await expect(pluginItem).toBeVisible();

                // 验证插件状态
                const pluginStatus = pluginItem.locator('[data-testid="plugin-status"]');
                await expect(pluginStatus).toBeVisible();
            }

            await playgroundPage.takeScreenshot('plugin-list');
        });

        test('应该能够启用和禁用插件', async () => {
            const pluginList = playgroundPage.page.locator('[data-testid="plugin-list"]');
            const routerPlugin = pluginList.locator('[data-plugin-name="router"]');

            // 获取当前状态
            const statusElement = routerPlugin.locator('[data-testid="plugin-status"]');
            const currentStatus = await statusElement.textContent();

            // 切换插件状态
            const toggleButton = routerPlugin.locator('[data-testid="plugin-toggle"]');
            await toggleButton.click();

            // 等待状态变化
            await playgroundPage.page.waitForTimeout(1000);

            // 验证状态已改变
            const newStatus = await statusElement.textContent();
            expect(newStatus).not.toBe(currentStatus);

            await playgroundPage.takeScreenshot('plugin-toggled');
        });
    });

    test.describe('适配器系统管理', () => {
        test.beforeEach(async () => {
            await playgroundPage.navigateToAdapters();
        });

        test('应该能够查看适配器列表', async () => {
            // 验证适配器页面加载
            await expect(playgroundPage.mainContent).toBeVisible();

            // 查找适配器列表
            const adapterList = playgroundPage.page.locator('[data-testid="adapter-list"]');
            await expect(adapterList).toBeVisible();

            // 验证核心适配器存在
            const coreAdapters = ['react', 'vue', 'angular'];

            for (const adapterName of coreAdapters) {
                const adapterItem = adapterList.locator(`[data-adapter-name="${adapterName}"]`);
                await expect(adapterItem).toBeVisible();

                // 验证适配器信息
                const adapterInfo = adapterItem.locator('[data-testid="adapter-info"]');
                await expect(adapterInfo).toBeVisible();
            }

            await playgroundPage.takeScreenshot('adapter-list');
        });

        test('应该能够查看适配器详细信息', async () => {
            const adapterList = playgroundPage.page.locator('[data-testid="adapter-list"]');
            const reactAdapter = adapterList.locator('[data-adapter-name="react"]');

            // 点击查看详情
            const detailButton = reactAdapter.locator('[data-testid="adapter-detail"]');
            await detailButton.click();

            // 验证详情弹窗
            const detailModal = playgroundPage.page.locator('[data-testid="adapter-detail-modal"]');
            await expect(detailModal).toBeVisible();

            // 验证详情内容
            const adapterName = detailModal.locator('[data-testid="adapter-name"]');
            const adapterVersion = detailModal.locator('[data-testid="adapter-version"]');
            const adapterFeatures = detailModal.locator('[data-testid="adapter-features"]');

            await expect(adapterName).toBeVisible();
            await expect(adapterVersion).toBeVisible();
            await expect(adapterFeatures).toBeVisible();

            await playgroundPage.takeScreenshot('adapter-detail');

            // 关闭弹窗
            const closeButton = detailModal.locator('[data-testid="close-modal"]');
            await closeButton.click();

            await expect(detailModal).not.toBeVisible();
        });
    });

    test.describe('调试工具', () => {
        test.beforeEach(async () => {
            await playgroundPage.navigateToDebug();
        });

        test('应该能够查看系统调试信息', async () => {
            // 验证调试页面加载
            await expect(playgroundPage.mainContent).toBeVisible();

            // 获取调试信息
            const debugInfo = await playgroundPage.getDebugInfo();

            // 验证调试信息结构
            expect(debugInfo).toHaveProperty('core');
            expect(debugInfo).toHaveProperty('apps');
            expect(debugInfo).toHaveProperty('plugins');
            expect(debugInfo).toHaveProperty('adapters');

            await playgroundPage.takeScreenshot('debug-info');
        });

        test('应该能够启用调试模式', async () => {
            await playgroundPage.enableDebugMode();

            // 验证调试模式已启用
            const successMessage = await playgroundPage.waitForSuccess();
            expect(successMessage).toContain('调试模式已启用');

            await playgroundPage.takeScreenshot('debug-mode-enabled');
        });

        test('应该能够查看控制台日志', async () => {
            // 监听控制台消息
            const consoleMessages: string[] = [];
            playgroundPage.page.on('console', msg => {
                consoleMessages.push(`${msg.type()}: ${msg.text()}`);
            });

            // 触发一些操作产生日志
            await playgroundPage.navigateToApps();
            await playgroundPage.navigateToPlugins();

            // 等待日志产生
            await playgroundPage.page.waitForTimeout(2000);

            // 验证有日志产生
            expect(consoleMessages.length).toBeGreaterThan(0);

            // 查找微前端相关日志
            const microCoreMessages = consoleMessages.filter(msg =>
                msg.includes('micro-core') || msg.includes('MicroCore')
            );
            expect(microCoreMessages.length).toBeGreaterThan(0);
        });
    });

    test.describe('错误处理', () => {
        test('应该能够处理网络错误', async () => {
            // 模拟网络错误
            await playgroundPage.page.route('**/api/**', route => {
                route.abort('failed');
            });

            await playgroundPage.navigateToApps();

            // 尝试添加应用（会失败）
            const failAppConfig = {
                name: 'fail-test-app',
                entry: 'http://invalid-url/index.js',
                container: '#fail-container',
                activeWhen: '/fail-test',
                framework: 'react'
            };

            await playgroundPage.addApp(failAppConfig);

            // 验证错误消息
            const errorMessage = await playgroundPage.waitForError();
            expect(errorMessage).toContain('网络错误');

            await playgroundPage.takeScreenshot('network-error');
        });

        test('应该能够处理应用加载失败', async () => {
            await playgroundPage.navigateToApps();

            // 添加一个无效的应用
            const invalidAppConfig = {
                name: 'invalid-app',
                entry: 'http://localhost:9999/nonexistent.js',
                container: '#invalid-container',
                activeWhen: '/invalid',
                framework: 'react'
            };

            await playgroundPage.addApp(invalidAppConfig);
            await playgroundPage.waitForSuccess();

            // 尝试启动应用
            await playgroundPage.startApp(invalidAppConfig.name);

            // 验证应用状态为错误
            await playgroundPage.waitForAppStatus(invalidAppConfig.name, 'LOAD_ERROR', 15000);

            await playgroundPage.takeScreenshot('app-load-error');
        });

        test('应该能够从错误中恢复', async () => {
            await playgroundPage.navigateToApps();

            // 添加一个会失败的应用
            const recoverAppConfig = {
                name: 'recover-test-app',
                entry: 'http://localhost:9999/recover.js',
                container: '#recover-container',
                activeWhen: '/recover',
                framework: 'react'
            };

            await playgroundPage.addApp(recoverAppConfig);
            await playgroundPage.waitForSuccess();

            // 启动应用（失败）
            await playgroundPage.startApp(recoverAppConfig.name);
            await playgroundPage.waitForAppStatus(recoverAppConfig.name, 'LOAD_ERROR', 15000);

            // 修复应用配置
            await playgroundPage.removeApp(recoverAppConfig.name);

            const fixedAppConfig = {
                ...recoverAppConfig,
                entry: 'http://localhost:3001/index.js'
            };

            await playgroundPage.addApp(fixedAppConfig);
            await playgroundPage.waitForSuccess();

            // 重新启动应用（成功）
            await playgroundPage.startApp(fixedAppConfig.name);
            await playgroundPage.waitForAppStatus(fixedAppConfig.name, 'MOUNTED');

            await playgroundPage.takeScreenshot('app-recovered');
        });
    });

    test.describe('性能测试', () => {
        test('页面加载性能应该在合理范围内', async ({ page }) => {
            const startTime = Date.now();

            await page.goto('/');
            await page.waitForLoadState('networkidle');

            const loadTime = Date.now() - startTime;

            // 页面加载时间应该小于5秒
            expect(loadTime).toBeLessThan(5000);

            console.log(`页面加载时间: ${loadTime}ms`);
        });

        test('应用启动性能应该在合理范围内', async () => {
            await playgroundPage.navigateToApps();

            const perfAppConfig = {
                name: 'perf-test-app',
                entry: 'http://localhost:3001/index.js',
                container: '#perf-container',
                activeWhen: '/perf-test',
                framework: 'react'
            };

            await playgroundPage.addApp(perfAppConfig);
            await playgroundPage.waitForSuccess();

            const startTime = Date.now();
            await playgroundPage.startApp(perfAppConfig.name);
            await playgroundPage.waitForAppStatus(perfAppConfig.name, 'MOUNTED');
            const mountTime = Date.now() - startTime;

            // 应用启动时间应该小于3秒
            expect(mountTime).toBeLessThan(3000);

            console.log(`应用启动时间: ${mountTime}ms`);

            await playgroundPage.takeScreenshot('performance-test');
        });
    });
});