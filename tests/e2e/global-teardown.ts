/**
 * @fileoverview E2E测试全局清理
 * <AUTHOR> <<EMAIL>>
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function globalTeardown() {
    console.log('🧹 开始E2E测试全局清理...');

    try {
        // 停止所有开发服务器
        console.log('🛑 停止开发服务器...');

        // 查找并终止相关进程
        try {
            await execAsync('pkill -f "vite.*3000"');
            await execAsync('pkill -f "vite.*3001"');
            await execAsync('pkill -f "vite.*3002"');
        } catch (error) {
            // 忽略进程不存在的错误
            console.log('ℹ️ 部分进程可能已经停止');
        }

        // 清理测试数据
        console.log('🗑️ 清理测试数据...');
        await cleanupTestData();

        // 清理临时文件
        console.log('🧽 清理临时文件...');
        await cleanupTempFiles();

        console.log('✅ E2E测试全局清理完成');

    } catch (error) {
        console.error('❌ E2E测试全局清理失败:', error);
        // 不抛出错误，避免影响测试结果
    }
}

async function cleanupTestData() {
    const fs = require('fs');
    const path = require('path');

    try {
        const testDataPath = path.join(process.cwd(), 'tests/e2e/test-data');
        if (fs.existsSync(testDataPath)) {
            fs.rmSync(testDataPath, { recursive: true, force: true });
        }
        console.log('📊 测试数据清理完成');
    } catch (error) {
        console.error('❌ 测试数据清理失败:', error);
    }
}

async function cleanupTempFiles() {
    const fs = require('fs');
    const path = require('path');

    try {
        // 清理测试结果目录中的临时文件
        const tempPaths = [
            'test-results/temp',
            'playwright-report/temp',
            '.playwright/temp'
        ];

        for (const tempPath of tempPaths) {
            const fullPath = path.join(process.cwd(), tempPath);
            if (fs.existsSync(fullPath)) {
                fs.rmSync(fullPath, { recursive: true, force: true });
            }
        }

        console.log('🧽 临时文件清理完成');
    } catch (error) {
        console.error('❌ 临时文件清理失败:', error);
    }
}

export default globalTeardown;