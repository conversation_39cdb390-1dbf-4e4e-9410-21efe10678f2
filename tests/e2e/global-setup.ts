/**
 * @fileoverview E2E测试全局设置
 * <AUTHOR> <<EMAIL>>
 */

import { chromium, FullConfig } from '@playwright/test';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function globalSetup(config: FullConfig) {
    console.log('🚀 开始E2E测试全局设置...');

    try {
        // 构建所有包
        console.log('📦 构建项目包...');
        await execAsync('pnpm build', { cwd: process.cwd() });

        // 启动示例应用服务器
        console.log('🌐 启动示例应用服务器...');

        // 启动React示例应用
        const reactProcess = exec('pnpm dev', {
            cwd: 'apps/examples/react-app'
        });

        // 启动Vue示例应用
        const vueProcess = exec('pnpm dev', {
            cwd: 'apps/examples/vue-app'
        });

        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 10000));

        // 验证服务器是否正常运行
        const browser = await chromium.launch();
        const page = await browser.newPage();

        try {
            // 检查playground是否可访问
            await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
            console.log('✅ Playground服务器运行正常');

            // 检查React示例应用是否可访问
            await page.goto('http://localhost:3001', { waitUntil: 'networkidle' });
            console.log('✅ React示例应用运行正常');

            // 检查Vue示例应用是否可访问
            await page.goto('http://localhost:3002', { waitUntil: 'networkidle' });
            console.log('✅ Vue示例应用运行正常');

        } catch (error) {
            console.error('❌ 服务器健康检查失败:', error);
            throw error;
        } finally {
            await browser.close();
        }

        // 设置测试数据
        console.log('📊 准备测试数据...');
        await setupTestData();

        console.log('✅ E2E测试全局设置完成');

    } catch (error) {
        console.error('❌ E2E测试全局设置失败:', error);
        throw error;
    }
}

async function setupTestData() {
    // 创建测试用的微前端应用配置
    const testApps = [
        {
            name: 'test-react-app',
            entry: 'http://localhost:3001/index.js',
            container: '#react-container',
            activeWhen: '/react-app',
            framework: 'react'
        },
        {
            name: 'test-vue-app',
            entry: 'http://localhost:3002/index.js',
            container: '#vue-container',
            activeWhen: '/vue-app',
            framework: 'vue'
        }
    ];

    // 将测试配置写入临时文件
    const fs = require('fs');
    const path = require('path');

    const testDataPath = path.join(process.cwd(), 'tests/e2e/test-data');
    if (!fs.existsSync(testDataPath)) {
        fs.mkdirSync(testDataPath, { recursive: true });
    }

    fs.writeFileSync(
        path.join(testDataPath, 'apps.json'),
        JSON.stringify(testApps, null, 2)
    );

    // 创建测试用户数据
    const testUsers = [
        {
            id: 1,
            username: 'testuser',
            password: 'password123',
            role: 'admin',
            permissions: ['read', 'write', 'admin']
        },
        {
            id: 2,
            username: 'normaluser',
            password: 'password123',
            role: 'user',
            permissions: ['read']
        }
    ];

    fs.writeFileSync(
        path.join(testDataPath, 'users.json'),
        JSON.stringify(testUsers, null, 2)
    );

    console.log('📊 测试数据准备完成');
}

export default globalSetup;