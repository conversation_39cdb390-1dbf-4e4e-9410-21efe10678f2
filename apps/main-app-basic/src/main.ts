/**
 * Micro-Core 示例主应用
 */

import { ReactAdapter } from '@micro-core/adapter-react';
import { Vue3Adapter } from '@micro-core/adapter-vue3';
import { MicroCoreKernel } from '@micro-core/core';
import { CommunicationPlugin } from '@micro-core/plugin-communication';
import { RouterPlugin } from '@micro-core/plugin-router';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

// 创建微前端内核
const kernel = new MicroCoreKernel();

// 安装插件
kernel.use(new RouterPlugin());
kernel.use(new CommunicationPlugin());
kernel.use(new ProxySandboxPlugin());
kernel.use(new ReactAdapter());
kernel.use(new Vue3Adapter());

// 注册 React 应用
kernel.registerApplication({
    name: 'react-app',
    entry: 'http://localhost:3001', // 假设的 React 应用地址
    container: '#react-container',
    activeWhen: '/react',
    customProps: {
        title: 'React 微应用'
    }
});

// 注册 Vue 应用
kernel.registerApplication({
    name: 'vue-app',
    entry: 'http://localhost:3002', // 假设的 Vue 应用地址
    container: '#vue-container',
    activeWhen: '/vue',
    customProps: {
        title: 'Vue 微应用'
    }
});

// 启动微前端
kernel.start().then(() => {
    console.log('Micro-Core 微前端架构已启动');
    updateStatus();
});

// 全局函数
(window as any).loadReactApp = async () => {
    try {
        await kernel.loadApp('react-app');
        await kernel.mountApp('react-app');
        updateStatus();
    } catch (error) {
        console.error('加载 React 应用失败:', error);
    }
};

(window as any).loadVueApp = async () => {
    try {
        await kernel.loadApp('vue-app');
        await kernel.mountApp('vue-app');
        updateStatus();
    } catch (error) {
        console.error('加载 Vue 应用失败:', error);
    }
};

(window as any).unloadAll = async () => {
    try {
        await kernel.unmountApp('react-app');
        await kernel.unmountApp('vue-app');
        updateStatus();
    } catch (error) {
        console.error('卸载应用失败:', error);
    }
};

// 更新状态显示
function updateStatus() {
    const statusEl = document.getElementById('app-status');
    if (statusEl) {
        const status = kernel.getStatus();
        statusEl.innerHTML = `
      <p>已启动: ${status.started ? '是' : '否'}</p>
      <p>应用数量: ${status.applications}</p>
      <p>插件数量: ${status.plugins}</p>
      <p>沙箱提供者: ${status.sandboxProviders}</p>
    `;
    }
}

// 监听路由变化
kernel.on('router:change', (event) => {
    console.log('路由变化:', event);
    updateStatus();
});

// 监听应用状态变化
kernel.on('app:mounted', (app) => {
    console.log('应用已挂载:', app.name);
    updateStatus();
});

kernel.on('app:unmounted', (app) => {
    console.log('应用已卸载:', app.name);
    updateStatus();
});