{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "module": "ESNext",
        "skipLibCheck": true,
        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "preserve",
        "jsxImportSource": "solid-js",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noImplicitReturns": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "forceConsistentCasingInFileNames": true,
        /* Path mapping */
        "baseUrl": ".",
        "paths": {
            "@/*": [
                "src/*"
            ],
            "@micro-core/core": [
                "../../packages/core/src"
            ],
            "@micro-core/adapter-solid": [
                "../../packages/adapters/adapter-solid/src"
            ],
            "@micro-core/builder-vite": [
                "../../packages/builders/builder-vite/src"
            ],
            "@micro-core/shared": [
                "../../packages/shared/src"
            ]
        }
    },
    "include": [
        "src/**/*",
        "vite.config.ts"
    ],
    "exclude": [
        "dist",
        "node_modules",
        "**/*.test.ts",
        "**/*.test.tsx",
        "**/*.spec.ts",
        "**/*.spec.tsx"
    ]
}