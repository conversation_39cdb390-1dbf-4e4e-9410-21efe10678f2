/**
 * @fileoverview Solid.js 错误边界工具
 * <AUTHOR> <<EMAIL>>
 */

import { createContext, createSignal, JSX, ParentComponent, useContext } from 'solid-js'

export interface ErrorInfo {
    message: string
    stack?: string
    componentStack?: string
    timestamp: number
}

export interface ErrorBoundaryState {
    hasError: boolean
    error: ErrorInfo | null
}

// 错误上下文
const ErrorContext = createContext<{
    captureError: (error: Error, context?: string) => void
}>()

// 全局错误处理器
export class GlobalErrorHandler {
    private static instance: GlobalErrorHandler
    private errorHandlers: Array<(error: ErrorInfo) => void> = []

    static getInstance(): GlobalErrorHandler {
        if (!GlobalErrorHandler.instance) {
            GlobalErrorHandler.instance = new GlobalErrorHandler()
        }
        return GlobalErrorHandler.instance
    }

    private constructor() {
        this.setupGlobalErrorHandling()
    }

    private setupGlobalErrorHandling(): void {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.handleError({
                message: event.message,
                stack: event.error?.stack,
                timestamp: Date.now()
            })
        })

        // 捕获未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: Date.now()
            })
        })
    }

    public addErrorHandler(handler: (error: ErrorInfo) => void): void {
        this.errorHandlers.push(handler)
    }

    public removeErrorHandler(handler: (error: ErrorInfo) => void): void {
        const index = this.errorHandlers.indexOf(handler)
        if (index > -1) {
            this.errorHandlers.splice(index, 1)
        }
    }

    private handleError(error: ErrorInfo): void {
        console.error('Solid.js 应用错误:', error)

        this.errorHandlers.forEach(handler => {
            try {
                handler(error)
            } catch (handlerError) {
                console.error('错误处理器执行失败:', handlerError)
            }
        })
    }

    public captureError(error: Error, context?: string): void {
        this.handleError({
            message: error.message,
            stack: error.stack,
            componentStack: context,
            timestamp: Date.now()
        })
    }
}

export const globalErrorHandler = GlobalErrorHandler.getInstance()

// 默认错误回退组件
const DefaultErrorFallback = (props: { error: ErrorInfo; reset: () => void }) => {
    const errorDivStyle = {
        padding: '20px',
        border: '1px solid #ff6b6b',
        'border-radius': '8px',
        'background-color': '#ffe0e0',
        color: '#d63031',
        margin: '20px 0'
    }

    const buttonStyle = {
        padding: '8px 16px',
        'background-color': '#d63031',
        color: 'white',
        border: 'none',
        'border-radius': '4px',
        cursor: 'pointer'
    }

    const detailsStyle = {
        'margin-top': '10px'
    }

    const preStyle = {
        'white-space': 'pre-wrap' as const,
        'font-size': '12px'
    }

    return (
        <div style={errorDivStyle}>
            <h3>应用出现错误</h3>
            <p>{props.error.message}</p>
            <button onClick={props.reset} style={buttonStyle}>
                重试
            </button>
            <details style={detailsStyle}>
                <summary>错误详情</summary>
                <pre style={preStyle}>
                    {props.error.stack}
                </pre>
            </details>
        </div>
    )
}

// 错误边界组件
export const ErrorBoundary: ParentComponent<{
    fallback?: (error: ErrorInfo, reset: () => void) => JSX.Element
    onError?: (error: ErrorInfo) => void
}> = (props) => {
    const [state, setState] = createSignal<ErrorBoundaryState>({
        hasError: false,
        error: null
    })

    const captureError = (error: Error, context?: string) => {
        const errorInfo: ErrorInfo = {
            message: error.message,
            stack: error.stack,
            componentStack: context,
            timestamp: Date.now()
        }

        setState({
            hasError: true,
            error: errorInfo
        })

        props.onError?.(errorInfo)
        globalErrorHandler.captureError(error, context)
    }

    const reset = () => {
        setState({
            hasError: false,
            error: null
        })
    }

    const contextValue = {
        captureError
    }

    return (
        <ErrorContext.Provider value={contextValue}>
            {state().hasError ? (
                props.fallback ? (
                    props.fallback(state().error!, reset)
                ) : (
                    <DefaultErrorFallback error={state().error!} reset={reset} />
                )
            ) : (
                props.children
            )}
        </ErrorContext.Provider>
    )
}

// Hook 用于在组件中捕获错误
export function useErrorHandler() {
    const context = useContext(ErrorContext)
    if (!context) {
        throw new Error('useErrorHandler must be used within an ErrorBoundary')
    }
    return context.captureError
}

// 高阶组件，为组件添加错误边界
export function withErrorBoundary<T extends Record<string, any>>(
    Component: (props: T) => JSX.Element,
    errorBoundaryProps?: {
        fallback?: (error: ErrorInfo, reset: () => void) => JSX.Element
        onError?: (error: ErrorInfo) => void
    }
) {
    return (props: T) => (
        <ErrorBoundary {...errorBoundaryProps}>
            <Component {...props} />
        </ErrorBoundary>
    )
}

// 异步错误捕获工具
export function catchAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R | null> {
    return async (...args: T) => {
        try {
            return await fn(...args)
        } catch (error) {
            globalErrorHandler.captureError(error as Error, 'async-function')
            return null
        }
    }
}