/**
 * @fileoverview Solid.js 测试环境设置
 */

import { vi } from 'vitest'

// Mock Solid.js render function
vi.mock('solid-js/web', async (importOriginal) => {
    const actual = await importOriginal()
    return {
        ...actual,
        render: vi.fn((component, container) => {
            // Mock render function
            if (container) {
                container.innerHTML = '<div class="solid-app">Mock Solid App</div>'
            }
            return () => {
                // Mock dispose function
                if (container) {
                    container.innerHTML = ''
                }
            }
        })
    }
})

// Mock Solid.js App component
vi.mock('../App', () => ({
    default: () => '<div class="solid-app">Mock Solid App</div>'
}))

// Mock performance API if not available
if (!global.performance) {
    global.performance = {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn()
    } as any
}

// Mock window.postMessage
global.window.postMessage = vi.fn()

// Mock DOM methods
Object.defineProperty(window, 'requestIdleCallback', {
    writable: true,
    value: vi.fn((cb: Function) => setTimeout(cb, 0))
})

Object.defineProperty(window, 'cancelIdleCallback', {
    writable: true,
    value: vi.fn()
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}))

// Setup DOM
document.body.innerHTML = '<div id="root"></div>'