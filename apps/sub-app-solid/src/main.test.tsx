/**
 * @fileoverview Solid.js 微前端生命周期测试
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { bootstrap, getAppStatus, mount, unmount, update } from './main'

// Mock DOM elements
const mockContainer = document.createElement('div')
mockContainer.id = 'test-container'
document.body.appendChild(mockContainer)

describe('Solid.js 微前端生命周期', () => {
    beforeEach(() => {
        // 清理容器
        mockContainer.innerHTML = ''
        // 重置全局状态
        vi.clearAllMocks()
    })

    it('应该正确执行 bootstrap 生命周期', async () => {
        const props = { theme: 'light' }

        await expect(bootstrap(props)).resolves.toBeUndefined()

        const status = getAppStatus()
        expect(status.isBootstrapped).toBe(true)
        expect(status.name).toBe('sub-app-solid')
        expect(status.framework).toBe('Solid.js')
    })

    it('应该正确执行 mount 生命周期', async () => {
        const props = {
            container: mockContainer,
            theme: 'light'
        }

        await expect(mount(props)).resolves.toBeUndefined()

        const status = getAppStatus()
        expect(status.isMounted).toBe(true)
    })

    it('应该正确执行 unmount 生命周期', async () => {
        const props = {
            container: mockContainer,
            theme: 'light'
        }

        // 先挂载
        await mount(props)
        expect(getAppStatus().isMounted).toBe(true)

        // 再卸载
        await expect(unmount(props)).resolves.toBeUndefined()

        const status = getAppStatus()
        expect(status.isMounted).toBe(false)
        expect(mockContainer.innerHTML).toBe('')
    })

    it('应该正确执行 update 生命周期', async () => {
        const props = {
            container: mockContainer,
            theme: 'light'
        }

        // 先挂载
        await mount(props)

        // 更新属性
        const updateProps = { ...props, theme: 'dark' }
        await expect(update(updateProps)).resolves.toBeUndefined()
    })

    it('应该处理错误情况', async () => {
        // 测试无效容器选择器
        const invalidProps = { container: '#non-existent-container' }

        await expect(mount(invalidProps)).rejects.toThrow('找不到挂载容器')
    })

    it('应该防止重复启动', async () => {
        const props = { theme: 'light' }

        await bootstrap(props)
        expect(getAppStatus().isBootstrapped).toBe(true)

        // 重复启动应该被跳过
        await bootstrap(props)
        expect(getAppStatus().isBootstrapped).toBe(true)
    })

    it('应该防止重复挂载', async () => {
        const props = {
            container: mockContainer,
            theme: 'light'
        }

        await mount(props)
        expect(getAppStatus().isMounted).toBe(true)

        // 重复挂载应该先卸载再挂载
        await mount(props)
        expect(getAppStatus().isMounted).toBe(true)
    })
})