# Micro-Core Playground

🎮 **微前端架构开发调试环境**

这是一个完整的微前端架构开发调试环境，用于测试和演示 micro-core 的各种功能。

## ✨ 功能特性

### 🚀 核心功能
- **微前端运行时** - 完整的微前端应用生命周期管理
- **插件系统** - 路由、通信、认证、沙箱等可扩展插件
- **多框架适配** - 支持 React、Vue、Angular、HTML 等框架
- **构建工具集成** - 集成 Webpack、Vite、Rollup、ESBuild
- **共享资源管理** - 依赖共享、资源缓存、版本管理
- **边车模式** - 零配置的自动发现和管理

### 🛠️ 开发工具
- **实时调试** - 浏览器控制台调试API
- **应用管理** - 微前端应用的启动、停止、重启
- **性能监控** - 应用性能指标和错误日志
- **配置管理** - 动态配置更新和管理

### 🎯 用户界面
- **现代化设计** - 基于 Ant Design 的美观界面
- **响应式布局** - 支持桌面和移动设备
- **实时状态** - 应用状态实时更新显示
- **错误处理** - 完善的错误边界和异常处理

## 🚀 快速开始

### 安装依赖

```bash
# 在项目根目录安装所有依赖
pnpm install

# 或者只安装 playground 依赖
cd apps/playground
pnpm install
```

### 启动开发服务器

```bash
# 在 playground 目录下启动
pnpm dev

# 或者在项目根目录启动
pnpm dev:playground
```

### 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
apps/playground/
├── public/                 # 静态资源
│   └── favicon.svg        # 应用图标
├── src/                   # 源代码
│   ├── components/        # React 组件
│   │   └── ErrorBoundary.tsx  # 错误边界组件
│   ├── core/             # 核心配置
│   │   └── micro-core-setup.ts  # 微前端核心初始化
│   ├── styles/           # 样式文件
│   │   └── global.css    # 全局样式
│   ├── App.tsx           # 主应用组件
│   └── main.tsx          # 应用入口
├── index.html            # HTML 模板
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
├── vite.config.ts        # Vite 构建配置
└── README.md            # 项目文档
```

## 🎮 使用指南

### 1. 概览页面
- 查看微前端架构的各个组件状态
- 了解系统功能和特性
- 快速开始指南

### 2. 微前端应用
- 查看已注册的微前端应用列表
- 管理应用的启动、停止状态
- 查看应用详细信息和配置

### 3. 插件系统
- 测试路由插件功能
- 验证应用间通信
- 管理认证和权限
- 配置沙箱隔离

### 4. 适配器管理
- 查看框架适配器状态
- 测试多框架兼容性
- 配置适配器参数

### 5. 构建工具
- 管理构建工具配置
- 查看构建状态和日志
- 优化构建性能

### 6. 边车模式
- 配置自动发现规则
- 管理代理服务器
- 监控应用通信

### 7. 调试工具
- 查看应用运行状态
- 分析性能指标
- 查看错误日志和堆栈

### 8. 系统设置
- 配置全局参数
- 管理主题和样式
- 导入导出配置

## 🔧 开发调试

### 浏览器控制台调试

在浏览器控制台中输入以下命令进行调试：

```javascript
// 查看微前端核心实例
window.__MICRO_CORE__

// 获取已注册的应用列表
window.__MICRO_CORE__.getApps()

// 查看应用状态
window.__MICRO_CORE__.getAppStatus('app-name')

// 启动应用
window.__MICRO_CORE__.startApp('app-name')

// 停止应用
window.__MICRO_CORE__.stopApp('app-name')

// 获取系统统计信息
window.__MICRO_CORE__.debug.getStats()

// 启用详细日志
window.__MICRO_CORE__.debug.enableLogging()
```

### 热重载支持

开发模式下支持热重载：
- 修改组件代码自动更新
- 修改配置文件自动重启
- 保持应用状态不丢失

### 错误处理

完善的错误处理机制：
- React 错误边界捕获组件错误
- 全局错误处理捕获运行时错误
- 友好的错误提示和恢复建议

## 🌐 微前端应用配置

### 预定义应用

Playground 预配置了以下微前端应用：

```typescript
const PREDEFINED_APPS = [
    {
        name: 'react-app',
        displayName: 'React 应用',
        entry: 'http://localhost:3001',
        container: '#react-app-container',
        activeWhen: '/react-app',
        framework: 'react'
    },
    {
        name: 'vue-app',
        displayName: 'Vue 应用',
        entry: 'http://localhost:3002',
        container: '#vue-app-container',
        activeWhen: '/vue-app',
        framework: 'vue'
    },
    // ... 更多应用配置
];
```

### 自定义应用

可以通过以下方式注册自定义应用：

```javascript
// 在控制台中注册新应用
window.__MICRO_CORE__.registerApp({
    name: 'my-app',
    displayName: '我的应用',
    entry: 'http://localhost:3005',
    container: '#my-app-container',
    activeWhen: '/my-app',
    framework: 'react',
    props: {
        title: '自定义应用',
        theme: 'dark'
    }
});
```

## 🔗 相关链接

- [Micro-Core 核心文档](../../packages/core/README.md)
- [插件系统文档](../../packages/plugins/README.md)
- [适配器文档](../../packages/adapters/README.md)
- [构建工具文档](../../packages/builders/README.md)
- [边车模式文档](../../packages/sidecar/README.md)

## 🤝 贡献指南

欢迎贡献代码和建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件

---

**Micro-Core Playground** - 让微前端开发更简单！ 🚀