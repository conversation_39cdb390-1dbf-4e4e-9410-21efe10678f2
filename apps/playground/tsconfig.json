{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@micro-core/core": ["../../packages/core/src"], "@micro-core/shared": ["../../packages/shared/src"], "@micro-core/adapters": ["../../packages/adapters/src"], "@micro-core/plugins": ["../../packages/plugins/src"], "@micro-core/builders": ["../../packages/builders/src"], "@micro-core/sidecar": ["../../packages/sidecar/src"]}}, "include": ["src", "vite.config.ts"], "exclude": ["dist", "node_modules"], "references": [{"path": "../../packages/core"}, {"path": "../../packages/shared"}, {"path": "../../packages/adapters"}, {"path": "../../packages/plugins"}, {"path": "../../packages/builders"}, {"path": "../../packages/sidecar"}]}