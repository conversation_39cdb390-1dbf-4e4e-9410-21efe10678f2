/**
 * @fileoverview React 错误边界组件
 * <AUTHOR> <<EMAIL>>
 */

import { Button, Result } from 'antd';
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
    children: ReactNode;
    onError?: (error: Error, errorInfo?: ErrorInfo) => void;
    fallback?: ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
    errorInfo?: ErrorInfo;
}

/**
 * React 错误边界组件
 */
export class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): State {
        // 更新 state 使下一次渲染能够显示降级后的 UI
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // 记录错误信息
        console.error('ErrorBoundary 捕获到错误:', error, errorInfo);

        // 更新状态
        this.setState({
            error,
            errorInfo
        });

        // 调用外部错误处理器
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
    }

    handleReload = () => {
        window.location.reload();
    };

    handleReset = () => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    };

    render() {
        if (this.state.hasError) {
            // 如果有自定义的 fallback UI，则使用它
            if (this.props.fallback) {
                return this.props.fallback;
            }

            // 默认的错误 UI
            return (
                <div style={{
                    padding: '50px',
                    minHeight: '100vh',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: '#f5f5f5'
                }}>
                    <Result
                        status="error"
                        title="应用运行时错误"
                        subTitle={
                            <div>
                                <p>抱歉，应用在运行过程中遇到了错误。</p>
                                {this.state.error && (
                                    <details style={{
                                        marginTop: '16px',
                                        textAlign: 'left',
                                        background: '#fff',
                                        padding: '16px',
                                        borderRadius: '6px',
                                        border: '1px solid #d9d9d9'
                                    }}>
                                        <summary style={{
                                            cursor: 'pointer',
                                            fontWeight: 'bold',
                                            marginBottom: '8px'
                                        }}>
                                            查看错误详情
                                        </summary>
                                        <div style={{
                                            fontSize: '12px',
                                            color: '#666',
                                            fontFamily: 'Monaco, Consolas, monospace'
                                        }}>
                                            <div style={{ marginBottom: '8px' }}>
                                                <strong>错误信息:</strong>
                                                <pre style={{
                                                    background: '#f6f8fa',
                                                    padding: '8px',
                                                    borderRadius: '4px',
                                                    overflow: 'auto',
                                                    margin: '4px 0'
                                                }}>
                                                    {this.state.error.message}
                                                </pre>
                                            </div>
                                            {this.state.error.stack && (
                                                <div>
                                                    <strong>错误堆栈:</strong>
                                                    <pre style={{
                                                        background: '#f6f8fa',
                                                        padding: '8px',
                                                        borderRadius: '4px',
                                                        overflow: 'auto',
                                                        margin: '4px 0',
                                                        maxHeight: '200px'
                                                    }}>
                                                        {this.state.error.stack}
                                                    </pre>
                                                </div>
                                            )}
                                            {this.state.errorInfo?.componentStack && (
                                                <div>
                                                    <strong>组件堆栈:</strong>
                                                    <pre style={{
                                                        background: '#f6f8fa',
                                                        padding: '8px',
                                                        borderRadius: '4px',
                                                        overflow: 'auto',
                                                        margin: '4px 0',
                                                        maxHeight: '200px'
                                                    }}>
                                                        {this.state.errorInfo.componentStack}
                                                    </pre>
                                                </div>
                                            )}
                                        </div>
                                    </details>
                                )}
                            </div>
                        }
                        extra={[
                            <Button key="reset" onClick={this.handleReset}>
                                重试
                            </Button>,
                            <Button key="reload" type="primary" onClick={this.handleReload}>
                                刷新页面
                            </Button>
                        ]}
                    />
                </div>
            );
        }

        return this.props.children;
    }
}

/**
 * 函数式错误边界 Hook（React 18+）
 */
export function useErrorHandler() {
    return (error: Error, errorInfo?: ErrorInfo) => {
        console.error('useErrorHandler 捕获到错误:', error, errorInfo);

        // 可以在这里添加错误上报逻辑
        if (window.__MICRO_CORE_PLAYGROUND__?.showError) {
            window.__MICRO_CORE_PLAYGROUND__.showError(
                `React 组件错误: ${error.message}`
            );
        }
    };
}

/**
 * 高阶组件：为组件添加错误边界
 */
export function withErrorBoundary<P extends object>(
    WrappedComponent: React.ComponentType<P>,
    fallback?: ReactNode
) {
    const WithErrorBoundaryComponent = (props: P) => {
        return (
            <ErrorBoundary fallback={fallback}>
                <WrappedComponent {...props} />
            </ErrorBoundary>
        );
    };

    WithErrorBoundaryComponent.displayName =
        `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithErrorBoundaryComponent;
}

export default ErrorBoundary;