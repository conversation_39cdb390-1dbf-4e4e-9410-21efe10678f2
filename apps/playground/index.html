<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Micro-Core Playground - 微前端架构开发调试环境</title>
    <meta name="description" content="微前端架构开发调试环境，用于测试和演示micro-core的各种功能" />
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 加载动画 */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-container.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .loading-text {
            color: white;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 30px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 错误页面样式 */
        .error-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }

        .error-container.show {
            display: flex;
        }

        .error-icon {
            font-size: 64px;
            color: #ff4d4f;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 24px;
            color: #262626;
            margin-bottom: 10px;
        }

        .error-message {
            font-size: 16px;
            color: #8c8c8c;
            text-align: center;
            max-width: 500px;
            line-height: 1.5;
            margin-bottom: 30px;
        }

        .error-actions {
            display: flex;
            gap: 16px;
        }

        .error-button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            color: #262626;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .error-button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .error-button.primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .error-button.primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
    </style>
</head>

<body>
    <!-- 加载页面 -->
    <div id="loading" class="loading-container">
        <div class="loading-logo">
            <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50" cy="50" r="45" stroke="white" stroke-width="3" fill="none" opacity="0.3" />
                <path d="M20 50 L35 35 L50 50 L65 35 L80 50" stroke="white" stroke-width="3" fill="none"
                    stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="35" cy="35" r="3" fill="white" />
                <circle cx="50" cy="50" r="3" fill="white" />
                <circle cx="65" cy="35" r="3" fill="white" />
            </svg>
        </div>
        <div class="loading-text">正在加载 Micro-Core Playground...</div>
        <div class="loading-spinner"></div>
    </div>

    <!-- 错误页面 -->
    <div id="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">应用加载失败</div>
        <div class="error-message" id="error-message">
            抱歉，应用在加载过程中遇到了问题。请检查网络连接或刷新页面重试。
        </div>
        <div class="error-actions">
            <button class="error-button" onclick="window.location.reload()">刷新页面</button>
            <button class="error-button primary" onclick="showConsole()">查看控制台</button>
        </div>
    </div>

    <!-- 应用根节点 -->
    <div id="root"></div>

    <!-- 微前端应用容器 -->
    <div id="micro-app-container" style="display: none;"></div>

    <script>
        // 全局错误处理
        window.addEventListener('error', function (event) {
            console.error('全局错误:', event.error);
            showError('JavaScript 执行错误: ' + event.error.message);
        });

        window.addEventListener('unhandledrejection', function (event) {
            console.error('未处理的 Promise 拒绝:', event.reason);
            showError('异步操作失败: ' + (event.reason?.message || event.reason));
        });

        // 显示错误页面
        function showError(message) {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const errorMessage = document.getElementById('error-message');

            if (loading) loading.classList.add('hidden');
            if (error) error.classList.add('show');
            if (errorMessage) errorMessage.textContent = message;
        }

        // 显示控制台
        function showConsole() {
            if (window.chrome && window.chrome.devtools) {
                // Chrome DevTools
                console.log('%c请查看控制台中的错误信息', 'color: #ff4d4f; font-size: 16px; font-weight: bold;');
            } else {
                alert('请按 F12 打开开发者工具查看详细错误信息');
            }
        }

        // 隐藏加载页面
        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) {
                setTimeout(() => {
                    loading.classList.add('hidden');
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 500);
                }, 1000);
            }
        }

        // 应用加载超时检测
        setTimeout(() => {
            const loading = document.getElementById('loading');
            if (loading && !loading.classList.contains('hidden')) {
                showError('应用加载超时，请检查网络连接或刷新页面重试。');
            }
        }, 30000); // 30秒超时

        // 暴露全局方法供应用使用
        window.__MICRO_CORE_PLAYGROUND__ = {
            hideLoading,
            showError,
            showConsole
        };
    </script>

    <script type="module" src="/src/main.tsx"></script>
</body>

</html>