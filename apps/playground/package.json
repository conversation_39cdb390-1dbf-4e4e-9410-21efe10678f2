{"name": "@micro-core/playground", "version": "1.0.0", "description": "微前端架构开发调试环境 - 用于测试和演示micro-core的各种功能", "keywords": ["microfrontend", "playground", "development", "testing", "demo"], "author": "Echo <<EMAIL>>", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "test": "vitest", "test:watch": "vitest --watch", "start:main": "npm run dev", "start:react-app": "cd micro-apps/react-app && npm run dev", "start:vue-app": "cd micro-apps/vue-app && npm run dev", "start:angular-app": "cd micro-apps/angular-app && npm run dev", "start:all": "concurrently \"npm run start:main\" \"npm run start:react-app\" \"npm run start:vue-app\" \"npm run start:angular-app\"", "install:all": "npm install && cd micro-apps/react-app && npm install && cd ../vue-app && npm install && cd ../angular-app && npm install"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "@micro-core/adapter-react": "workspace:*", "@micro-core/adapter-vue3": "workspace:*", "@micro-core/adapter-angular": "workspace:*", "@micro-core/adapter-html": "workspace:*", "@micro-core/sidecar": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "styled-components": "^6.1.0", "axios": "^1.6.0", "dayjs": "^1.11.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^5.0.0", "vite": "^7.0.4", "typescript": "^5.3.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "vitest": "^3.2.5", "@vitest/coverage-v8": "^3.2.5", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "jsdom": "^23.0.0", "rimraf": "^5.0.0", "concurrently": "^8.2.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}