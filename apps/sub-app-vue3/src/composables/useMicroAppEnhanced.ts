/**
 * @fileoverview Enhanced Vue 3 微前端应用 Composable with Plugin Integration
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 * 
 * 集成了优化后的插件系统，提供企业级微前端功能：
 * - AuthPlugin 集成：完整的认证和权限管理
 * - CommunicationPlugin 集成：高性能事件总线通信
 * - RouterPlugin 集成：带守卫的路由管理
 * - ProxySandboxPlugin 集成：安全的沙箱隔离
 */

import { computed, inject, ref, onMounted, onUnmounted, reactive } from 'vue'
import type { AuthUser, Role, Permission } from '@micro-core/plugin-auth'
import type { EventBusMessage, GlobalState } from '@micro-core/plugin-communication'
import type { RouteGuardConfig, NavigationOptions } from '@micro-core/plugin-router'

// 增强的微前端应用属性接口
export interface EnhancedMicroAppProps {
    name?: string
    basename?: string
    theme?: 'light' | 'dark' | 'auto'
    user?: AuthUser
    container?: HTMLElement | string
    sandbox?: boolean
    permissions?: Permission[]
    roles?: Role[]
    routeGuards?: RouteGuardConfig[]
    globalState?: Record<string, any>
    [key: string]: any
}

// 增强的微前端应用信息接口
export interface EnhancedMicroAppInfo {
    name: string
    framework: string
    version: string
    basename: string
    isMicroFrontend: boolean
    sandboxEnabled: boolean
    pluginsLoaded: string[]
    securityLevel: 'low' | 'medium' | 'high'
    performance: {
        loadTime: number
        memoryUsage: number
        lastUpdate: number
    }
}

// 微前端内核接口
interface MicroCoreKernel {
    getPlugin: (name: string) => any
    getEventBus: () => any
    getGlobalState: () => any
    executeHook: (hookName: string, ...args: any[]) => Promise<any>
}

/**
 * 增强的微前端应用 Composable
 * 提供与插件系统深度集成的功能
 */
export function useEnhancedMicroApp() {
    const microProps = inject<EnhancedMicroAppProps>('microProps', {})
    const microKernel = inject<MicroCoreKernel>('microKernel')
    
    // 响应式状态
    const isInitialized = ref(false)
    const pluginStatus = reactive<Record<string, boolean>>({})
    const performanceMetrics = reactive({
        loadTime: 0,
        memoryUsage: 0,
        lastUpdate: Date.now()
    })

    // 初始化插件连接
    const initializePlugins = async () => {
        if (!microKernel) {
            console.warn('[useMicroApp] MicroCore kernel not available')
            return
        }

        try {
            // 检查插件可用性
            const authPlugin = microKernel.getPlugin('auth')
            const commPlugin = microKernel.getPlugin('communication')
            const routerPlugin = microKernel.getPlugin('router')
            const sandboxPlugin = microKernel.getPlugin('sandbox-proxy')

            pluginStatus.auth = !!authPlugin
            pluginStatus.communication = !!commPlugin
            pluginStatus.router = !!routerPlugin
            pluginStatus.sandbox = !!sandboxPlugin

            // 执行初始化钩子
            await microKernel.executeHook('microAppInit', {
                name: microProps.name || 'sub-app-vue3',
                framework: 'Vue3',
                version: '3.4.0'
            })

            isInitialized.value = true
            console.log('[useMicroApp] Enhanced micro-app initialized successfully')
        } catch (error) {
            console.error('[useMicroApp] Failed to initialize plugins:', error)
        }
    }

    // 生命周期管理
    onMounted(() => {
        initializePlugins()
        performanceMetrics.loadTime = performance.now()
    })

    onUnmounted(() => {
        // 清理资源
        if (microKernel) {
            microKernel.executeHook('microAppDestroy', {
                name: microProps.name || 'sub-app-vue3'
            })
        }
    })

    return {
        // 基础属性
        props: computed(() => microProps),
        name: computed(() => microProps.name || 'sub-app-vue3'),
        basename: computed(() => microProps.basename || '/vue3'),
        theme: computed(() => microProps.theme || 'light'),
        container: computed(() => microProps.container),
        
        // 增强功能
        isInitialized: computed(() => isInitialized.value),
        pluginStatus: computed(() => pluginStatus),
        performanceMetrics: computed(() => performanceMetrics),
        kernel: microKernel,
        
        // 工具方法
        refreshMetrics: () => {
            performanceMetrics.memoryUsage = (performance as any).memory?.usedJSHeapSize || 0
            performanceMetrics.lastUpdate = Date.now()
        }
    }
}

/**
 * 增强的认证管理 Composable
 * 集成 AuthPlugin 提供完整的认证和权限管理
 */
export function useEnhancedAuth() {
    const { kernel, pluginStatus } = useEnhancedMicroApp()
    const authPlugin = kernel?.getPlugin('auth')
    
    // 响应式认证状态
    const authState = reactive({
        isAuthenticated: false,
        user: null as AuthUser | null,
        permissions: [] as Permission[],
        roles: [] as Role[],
        token: null as string | null
    })

    // 更新认证状态
    const updateAuthState = () => {
        if (!authPlugin) return

        authState.isAuthenticated = authPlugin.isAuthenticated()
        authState.user = authPlugin.getCurrentUser()
        authState.permissions = authPlugin.getUserPermissions()
        authState.roles = authPlugin.getUserRoles()
        authState.token = authPlugin.getToken()
    }

    // 监听认证事件
    onMounted(() => {
        if (authPlugin && kernel) {
            const eventBus = kernel.getEventBus()
            
            eventBus.on('auth:login:success', updateAuthState)
            eventBus.on('auth:logout:success', updateAuthState)
            eventBus.on('auth:token:refresh', updateAuthState)
            
            // 初始状态更新
            updateAuthState()
        }
    })

    return {
        // 状态
        isAuthenticated: computed(() => authState.isAuthenticated),
        user: computed(() => authState.user),
        permissions: computed(() => authState.permissions),
        roles: computed(() => authState.roles),
        token: computed(() => authState.token),
        isPluginAvailable: computed(() => pluginStatus.auth),
        
        // 方法
        login: async (credentials: any) => {
            if (!authPlugin) throw new Error('AuthPlugin not available')
            return await authPlugin.login(credentials)
        },
        
        logout: async () => {
            if (!authPlugin) throw new Error('AuthPlugin not available')
            return await authPlugin.logout()
        },
        
        hasPermission: (permission: Permission | string) => {
            if (!authPlugin) return false
            return authPlugin.hasPermission(permission)
        },
        
        hasRole: (role: Role | string) => {
            if (!authPlugin) return false
            return authPlugin.hasRole(role)
        },
        
        refreshToken: async () => {
            if (!authPlugin) throw new Error('AuthPlugin not available')
            return await authPlugin.refreshToken()
        }
    }
}

/**
 * 增强的通信管理 Composable
 * 集成 CommunicationPlugin 提供高性能事件总线通信
 */
export function useEnhancedCommunication() {
    const { kernel, pluginStatus } = useEnhancedMicroApp()
    const commPlugin = kernel?.getPlugin('communication')
    
    // 事件监听器管理
    const eventListeners = new Map<string, Function[]>()
    
    // 清理函数
    const cleanup = () => {
        eventListeners.forEach((listeners, event) => {
            listeners.forEach(listener => {
                commPlugin?.off(event, listener)
            })
        })
        eventListeners.clear()
    }

    onUnmounted(cleanup)

    return {
        isPluginAvailable: computed(() => pluginStatus.communication),
        
        // 事件总线方法
        emit: (event: string, data: any) => {
            if (!commPlugin) {
                console.warn('[useCommunication] CommunicationPlugin not available')
                return
            }
            commPlugin.emit(event, data)
        },
        
        on: (event: string, listener: Function) => {
            if (!commPlugin) {
                console.warn('[useCommunication] CommunicationPlugin not available')
                return () => {}
            }
            
            commPlugin.on(event, listener)
            
            // 记录监听器以便清理
            if (!eventListeners.has(event)) {
                eventListeners.set(event, [])
            }
            eventListeners.get(event)!.push(listener)
            
            // 返回取消监听函数
            return () => {
                commPlugin.off(event, listener)
                const listeners = eventListeners.get(event)
                if (listeners) {
                    const index = listeners.indexOf(listener)
                    if (index > -1) listeners.splice(index, 1)
                }
            }
        },
        
        off: (event: string, listener?: Function) => {
            if (!commPlugin) return
            commPlugin.off(event, listener)
        },
        
        // 全局状态管理
        getGlobalState: (key?: string) => {
            if (!commPlugin) return undefined
            return commPlugin.getGlobalState(key)
        },
        
        setGlobalState: (key: string, value: any) => {
            if (!commPlugin) return
            commPlugin.setGlobalState(key, value)
        },
        
        // 消息通道
        createMessageChannel: (channelName: string) => {
            if (!commPlugin) return null
            return commPlugin.createMessageChannel(channelName)
        },
        
        // 广播消息
        broadcast: (message: EventBusMessage) => {
            if (!commPlugin) return
            commPlugin.broadcast(message)
        }
    }
}

/**
 * 增强的路由管理 Composable
 * 集成 RouterPlugin 提供带守卫的路由管理
 */
export function useEnhancedRouter() {
    const { kernel, pluginStatus, basename } = useEnhancedMicroApp()
    const routerPlugin = kernel?.getPlugin('router')
    
    return {
        basename,
        isPluginAvailable: computed(() => pluginStatus.router),
        
        // 路由导航
        navigate: async (path: string, options?: NavigationOptions) => {
            if (!routerPlugin) {
                console.warn('[useRouter] RouterPlugin not available, falling back to basic navigation')
                window.history.pushState({}, '', path)
                return
            }
            return await routerPlugin.navigate(path, options)
        },
        
        // 路由守卫
        addGuard: (guard: RouteGuardConfig) => {
            if (!routerPlugin) return
            routerPlugin.addGuard(guard)
        },
        
        removeGuard: (guardName: string) => {
            if (!routerPlugin) return
            routerPlugin.removeGuard(guardName)
        },
        
        // 路径工具
        getFullPath: (path: string) => {
            const base = basename.value.endsWith('/') ? basename.value.slice(0, -1) : basename.value
            const cleanPath = path.startsWith('/') ? path : `/${path}`
            return `${base}${cleanPath}`
        },
        
        getRelativePath: (fullPath: string) => {
            const base = basename.value.endsWith('/') ? basename.value.slice(0, -1) : basename.value
            return fullPath.startsWith(base) ? fullPath.slice(base.length) || '/' : fullPath
        },
        
        // 当前路由信息
        getCurrentRoute: () => {
            if (!routerPlugin) return null
            return routerPlugin.getCurrentRoute()
        }
    }
}

/**
 * 增强的主题管理 Composable
 * 提供高级主题功能和系统集成
 */
export function useEnhancedTheme() {
    const { theme } = useEnhancedMicroApp()
    const { emit } = useEnhancedCommunication()
    
    // 系统主题检测
    const systemTheme = ref<'light' | 'dark'>('light')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')
    
    const updateSystemTheme = () => {
        systemTheme.value = prefersDark.matches ? 'dark' : 'light'
    }
    
    onMounted(() => {
        updateSystemTheme()
        prefersDark.addEventListener('change', updateSystemTheme)
    })
    
    onUnmounted(() => {
        prefersDark.removeEventListener('change', updateSystemTheme)
    })
    
    // 计算实际主题
    const actualTheme = computed(() => {
        if (theme.value === 'auto') {
            return systemTheme.value
        }
        return theme.value || 'light'
    })
    
    return {
        theme: actualTheme,
        systemTheme: computed(() => systemTheme.value),
        isDark: computed(() => actualTheme.value === 'dark'),
        isLight: computed(() => actualTheme.value === 'light'),
        isAuto: computed(() => theme.value === 'auto'),
        
        // 主题切换
        setTheme: (newTheme: 'light' | 'dark' | 'auto') => {
            document.documentElement.setAttribute('data-theme', newTheme === 'auto' ? systemTheme.value : newTheme)
            
            // 广播主题变更事件
            emit('theme:change', {
                theme: newTheme,
                actualTheme: newTheme === 'auto' ? systemTheme.value : newTheme,
                source: 'sub-app-vue3'
            })
        },
        
        toggleTheme: () => {
            const newTheme = actualTheme.value === 'dark' ? 'light' : 'dark'
            document.documentElement.setAttribute('data-theme', newTheme)
            
            emit('theme:toggle', {
                from: actualTheme.value,
                to: newTheme,
                source: 'sub-app-vue3'
            })
        }
    }
}

/**
 * 性能监控 Composable
 * 提供微前端应用性能监控和优化建议
 */
export function usePerformanceMonitor() {
    const { performanceMetrics, refreshMetrics } = useEnhancedMicroApp()
    const { emit } = useEnhancedCommunication()
    
    const performanceData = reactive({
        fps: 0,
        renderTime: 0,
        bundleSize: 0,
        networkLatency: 0
    })
    
    // FPS 监控
    let frameCount = 0
    let lastTime = performance.now()
    
    const measureFPS = () => {
        frameCount++
        const currentTime = performance.now()
        
        if (currentTime - lastTime >= 1000) {
            performanceData.fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
            frameCount = 0
            lastTime = currentTime
            
            // 发送性能数据
            emit('performance:update', {
                source: 'sub-app-vue3',
                metrics: {
                    ...performanceMetrics.value,
                    ...performanceData
                }
            })
        }
        
        requestAnimationFrame(measureFPS)
    }
    
    onMounted(() => {
        measureFPS()
    })
    
    return {
        metrics: computed(() => ({
            ...performanceMetrics.value,
            ...performanceData
        })),
        
        refresh: refreshMetrics,
        
        // 性能建议
        getOptimizationSuggestions: () => {
            const suggestions = []
            
            if (performanceData.fps < 30) {
                suggestions.push('Consider reducing DOM updates or using virtual scrolling')
            }
            
            if (performanceMetrics.value.memoryUsage > 50 * 1024 * 1024) {
                suggestions.push('Memory usage is high, consider implementing cleanup routines')
            }
            
            return suggestions
        }
    }
}

// 导出所有增强的 composables
export {
    useEnhancedMicroApp as useMicroApp,
    useEnhancedAuth as useAuth,
    useEnhancedCommunication as useCommunication,
    useEnhancedRouter as useRouter,
    useEnhancedTheme as useTheme,
    usePerformanceMonitor
}

// 向后兼容的导出
export const useMicroAppInfo = () => {
    const { name, pluginStatus, performanceMetrics } = useEnhancedMicroApp()
    
    return {
        info: computed(() => ({
            name: name.value,
            framework: 'Vue3',
            version: '3.4.0',
            basename: '/vue3',
            isMicroFrontend: true,
            sandboxEnabled: pluginStatus.sandbox,
            pluginsLoaded: Object.keys(pluginStatus).filter(key => pluginStatus[key]),
            securityLevel: 'high' as const,
            performance: performanceMetrics.value
        })),
        name,
        framework: computed(() => 'Vue3'),
        version: computed(() => '3.4.0'),
        basename: computed(() => '/vue3'),
        isMicroFrontend: computed(() => true),
        isStandalone: computed(() => false)
    }
}

// 类型导出
export type {
    EnhancedMicroAppProps,
    EnhancedMicroAppInfo,
    AuthUser,
    Role,
    Permission,
    EventBusMessage,
    RouteGuardConfig,
    NavigationOptions
}
