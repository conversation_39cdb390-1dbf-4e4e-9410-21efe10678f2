<template>
  <div id="vue3-app" class="vue3-app">
    <header class="app-header">
      <h2>Vue 3 子应用</h2>
      <nav class="app-nav">
        <router-link to="/" class="nav-link">产品管理</router-link>
        <router-link to="/orders" class="nav-link">订单管理</router-link>
      </nav>
    </header>
    
    <main class="app-main">
      <router-view />
    </main>
    
    <footer class="app-footer">
      <p>Vue 3 + Composition API + Pinia</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const theme = ref('light')
const message = ref('')

// 生命周期钩子
onMounted(() => {
  console.log('Vue3 应用已挂载')
  
  // 监听主应用消息
  window.addEventListener('micro-core:theme-change', handleThemeChange)
  window.addEventListener('micro-core:message', handleMessage)
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('micro-core:theme-change', handleThemeChange)
  window.removeEventListener('micro-core:message', handleMessage)
})

// 事件处理函数
const handleThemeChange = (event: CustomEvent) => {
  theme.value = event.detail.theme
}

const handleMessage = (event: CustomEvent) => {
  message.value = event.detail.message
}
</script>

<style scoped>
.vue3-app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-header h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.app-nav {
  display: flex;
  gap: 1rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.app-main {
  flex: 1;
  padding: 2rem;
  background-color: #f5f5f5;
}

.app-footer {
  background-color: #35495e;
  color: white;
  text-align: center;
  padding: 1rem;
}

.app-footer p {
  margin: 0;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }
  
  .app-nav {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .app-main {
    padding: 1rem;
  }
}
</style>