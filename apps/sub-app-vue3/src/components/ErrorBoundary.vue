<!--
  @fileoverview Vue 3 错误边界组件
  <AUTHOR> <<EMAIL>>
-->

<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <h2>😵 应用出现错误</h2>
      <p>很抱歉，应用遇到了一个错误。</p>
      
      <details v-if="isDevelopment && error" class="error-details">
        <summary>错误详情</summary>
        <pre class="error-stack">{{ error.stack }}</pre>
      </details>
      
      <button @click="handleRetry" class="retry-button">
        重试
      </button>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, onMounted } from 'vue'

// Props
interface Props {
  fallback?: boolean
  onError?: (error: Error, info: string) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallback: true
})

// 响应式数据
const hasError = ref(false)
const error = ref<Error | null>(null)
const isDevelopment = ref(process.env.NODE_ENV === 'development')

// 错误捕获
onErrorCaptured((err: Error, instance, info: string) => {
  console.error('Vue3 错误边界捕获到错误:', err, info)
  
  // 更新错误状态
  hasError.value = true
  error.value = err
  
  // 调用外部错误处理函数
  if (props.onError) {
    props.onError(err, info)
  }
  
  // 报告错误
  reportError(err, info)
  
  // 阻止错误继续传播
  return false
})

// 组件挂载时设置全局错误处理
onMounted(() => {
  // 监听未捕获的 Promise 拒绝
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
})

/**
 * 报告错误到监控系统
 */
function reportError(error: Error, info: string) {
  const errorReport = {
    message: error.message,
    stack: error.stack,
    componentInfo: info,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    framework: 'Vue3'
  }
  
  // 发送到错误监控服务
  console.log('错误报告:', errorReport)
  
  // 这里可以集成 Sentry 等错误监控服务
  // Sentry.captureException(error, { extra: errorReport })
}

/**
 * 处理未捕获的 Promise 拒绝
 */
function handleUnhandledRejection(event: PromiseRejectionEvent) {
  console.error('Vue3 应用未捕获的 Promise 拒绝:', event.reason)
  
  // 如果是 Error 对象，按错误处理
  if (event.reason instanceof Error) {
    hasError.value = true
    error.value = event.reason
    reportError(event.reason, 'Unhandled Promise Rejection')
  }
}

/**
 * 重试处理
 */
function handleRetry() {
  hasError.value = false
  error.value = null
}
</script>

<style scoped>
.error-boundary {
  padding: 20px;
  border: 1px solid #ff4d4f;
  border-radius: 6px;
  background-color: #fff2f0;
  color: #a8071a;
  text-align: center;
  margin: 20px;
}

.error-content h2 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.error-content p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

.error-details {
  margin: 10px 0;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  font-weight: bold;
  margin-bottom: 10px;
}

.error-stack {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  max-height: 200px;
}

.retry-button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-button:hover {
  background-color: #40a9ff;
}

.retry-button:active {
  background-color: #096dd9;
}
</style>