<template>
  <div class="product-management">
    <div class="page-header">
      <h3>产品管理</h3>
      <button @click="addProduct" class="add-btn">添加产品</button>
    </div>

    <div class="product-stats">
      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <div class="stat-number">{{ products.length }}</div>
          <div class="stat-label">总产品数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ activeProducts }}</div>
          <div class="stat-label">在售产品</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <div class="stat-number">¥{{ totalValue.toLocaleString() }}</div>
          <div class="stat-label">总价值</div>
        </div>
      </div>
    </div>

    <div class="product-table">
      <table>
        <thead>
          <tr>
            <th>产品名称</th>
            <th>价格</th>
            <th>库存</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="product in products" :key="product.id">
            <td>{{ product.name }}</td>
            <td>¥{{ product.price.toLocaleString() }}</td>
            <td>{{ product.stock }}</td>
            <td>
              <span :class="['status', product.status]">
                {{ product.status === 'active' ? '在售' : '下架' }}
              </span>
            </td>
            <td>
              <button @click="editProduct(product)" class="edit-btn">编辑</button>
              <button @click="deleteProduct(product.id)" class="delete-btn">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Product {
  id: number
  name: string
  price: number
  stock: number
  status: 'active' | 'inactive'
}

const products = ref<Product[]>([
  { id: 1, name: 'iPhone 15 Pro', price: 8999, stock: 50, status: 'active' },
  { id: 2, name: 'MacBook Pro', price: 15999, stock: 30, status: 'active' },
  { id: 3, name: 'iPad Air', price: 4599, stock: 80, status: 'active' },
  { id: 4, name: 'Apple Watch', price: 2999, stock: 0, status: 'inactive' },
  { id: 5, name: 'AirPods Pro', price: 1899, stock: 120, status: 'active' }
])

const activeProducts = computed(() => {
  return products.value.filter(p => p.status === 'active').length
})

const totalValue = computed(() => {
  return products.value.reduce((total, product) => {
    return total + (product.price * product.stock)
  }, 0)
})

const addProduct = () => {
  const newProduct: Product = {
    id: Date.now(),
    name: `新产品 ${products.value.length + 1}`,
    price: Math.floor(Math.random() * 10000) + 1000,
    stock: Math.floor(Math.random() * 100) + 10,
    status: 'active'
  }
  products.value.push(newProduct)
}

const editProduct = (product: Product) => {
  const newName = prompt('请输入新的产品名称:', product.name)
  if (newName) {
    product.name = newName
  }
}

const deleteProduct = (id: number) => {
  if (confirm('确定要删除这个产品吗？')) {
    const index = products.value.findIndex(p => p.id === id)
    if (index > -1) {
      products.value.splice(index, 1)
    }
  }
}

onMounted(() => {
  console.log('产品管理页面已加载')
})
</script>

<style scoped>
.product-management {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

.add-btn {
  background: #42b883;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.add-btn:hover {
  background: #369870;
}

.product-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

.product-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

tbody tr:hover {
  background: #f8f9fa;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.edit-btn, .delete-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-right: 0.5rem;
  transition: background-color 0.3s;
}

.edit-btn {
  background: #007bff;
  color: white;
}

.edit-btn:hover {
  background: #0056b3;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .product-stats {
    grid-template-columns: 1fr;
  }
  
  .product-table {
    overflow-x: auto;
  }
  
  table {
    min-width: 600px;
  }
}
</style>