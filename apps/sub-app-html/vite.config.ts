import { defineConfig } from 'vite'

export default defineConfig({
    server: {
        port: 3004,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/main.ts',
            name: 'SubAppHtml',
            fileName: 'sub-app-html',
            formats: ['umd']
        },
        rollupOptions: {
            external: [],
            output: {
                globals: {}
            }
        }
    },

    define: {
        __MICRO_APP_NAME__: JSON.stringify('sub-app-html'),
        __POWERED_BY_MICRO_CORE__: true,
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    }
})