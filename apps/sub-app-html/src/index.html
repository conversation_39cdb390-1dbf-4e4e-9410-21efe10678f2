<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 微前端子应用</title>
    <link rel="stylesheet" href="./style.css">
</head>
<body>
    <div id="html-app" class="html-app">
        <header class="app-header">
            <h1>HTML 微前端子应用</h1>
            <p>这是一个基于原生 HTML/JavaScript/CSS 的微前端子应用示例</p>
        </header>
        
        <main class="app-main">
            <section class="feature-section">
                <h2>功能特性</h2>
                <ul>
                    <li>✅ 原生 HTML/JavaScript/CSS 实现</li>
                    <li>✅ 无框架依赖，轻量级</li>
                    <li>✅ 完整的微前端生命周期管理</li>
                    <li>✅ 支持独立运行和微前端模式</li>
                </ul>
            </section>
            
            <section class="demo-section">
                <h2>演示功能</h2>
                <div class="demo-content">
                    <p>当前时间: <span id="current-time"></span></p>
                    <button id="demo-button" class="demo-button">
                        点击测试 HTML 功能
                    </button>
                    <div id="click-counter" class="counter">
                        点击次数: <span id="count">0</span>
                    </div>
                </div>
            </section>
        </main>
        
        <footer class="app-footer">
            <p>Powered by Micro-Core 微前端框架</p>
        </footer>
    </div>
    
    <script src="./main.js"></script>
</body>
</html>