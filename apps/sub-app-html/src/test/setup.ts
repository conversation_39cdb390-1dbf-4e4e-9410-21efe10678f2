/**
 * @fileoverview HTML 测试环境设置
 */

import { vi } from 'vitest'

// Mock performance API if not available
if (!global.performance) {
    global.performance = {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn(),
        memory: {
            usedJSHeapSize: 1000000,
            totalJSHeapSize: 2000000,
            jsHeapSizeLimit: 4000000
        }
    } as any
}

// Mock window.postMessage
global.window.postMessage = vi.fn()

// Mock DOM methods
Object.defineProperty(window, 'requestIdleCallback', {
    writable: true,
    value: vi.fn((cb: Function) => setTimeout(cb, 0))
})

Object.defineProperty(window, 'cancelIdleCallback', {
    writable: true,
    value: vi.fn()
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}))

// Mock setInterval and clearInterval
global.setInterval = vi.fn((fn, delay) => {
    return setTimeout(fn, delay) as any
})

global.clearInterval = vi.fn((id) => {
    clearTimeout(id as any)
})

// Setup DOM
document.body.innerHTML = '<div id="app"></div>'