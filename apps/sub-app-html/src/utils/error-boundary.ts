/**
 * @fileoverview HTML 应用错误边界工具
 * <AUTHOR> <<EMAIL>>
 */

export interface ErrorInfo {
    message: string
    stack?: string
    componentStack?: string
    timestamp: number
}

export class ErrorBoundary {
    private static instance: ErrorBoundary
    private errorHandlers: Array<(error: ErrorInfo) => void> = []
    private fallbackElement: HTMLElement | null = null

    static getInstance(): ErrorBoundary {
        if (!ErrorBoundary.instance) {
            ErrorBoundary.instance = new ErrorBoundary()
        }
        return ErrorBoundary.instance
    }

    private constructor() {
        this.setupGlobalErrorHandling()
    }

    private setupGlobalErrorHandling(): void {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.handleError({
                message: event.message,
                stack: event.error?.stack,
                timestamp: Date.now()
            })
        })

        // 捕获未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: Date.now()
            })
        })
    }

    public addErrorHandler(handler: (error: ErrorInfo) => void): void {
        this.errorHandlers.push(handler)
    }

    public removeErrorHandler(handler: (error: ErrorInfo) => void): void {
        const index = this.errorHandlers.indexOf(handler)
        if (index > -1) {
            this.errorHandlers.splice(index, 1)
        }
    }

    private handleError(error: ErrorInfo): void {
        console.error('HTML 应用错误:', error)

        // 显示错误边界 UI
        this.showErrorBoundary(error)

        this.errorHandlers.forEach(handler => {
            try {
                handler(error)
            } catch (handlerError) {
                console.error('错误处理器执行失败:', handlerError)
            }
        })
    }

    public captureError(error: Error, context?: string): void {
        this.handleError({
            message: error.message,
            stack: error.stack,
            componentStack: context,
            timestamp: Date.now()
        })
    }

    private showErrorBoundary(error: ErrorInfo): void {
        // 创建错误边界 UI
        if (!this.fallbackElement) {
            this.fallbackElement = document.createElement('div')
            this.fallbackElement.className = 'error-boundary'
            this.fallbackElement.innerHTML = `
                <div style="
                    padding: 20px;
                    border: 1px solid #ff6b6b;
                    border-radius: 8px;
                    background-color: #fff5f5;
                    color: #c92a2a;
                    margin: 20px 0;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                ">
                    <h3 style="margin-top: 0;">应用出现错误</h3>
                    <p id="error-message">${error.message}</p>
                    <button id="error-retry" style="
                        padding: 8px 16px;
                        background-color: #ff6b6b;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-right: 10px;
                    ">重试</button>
                    <button id="error-details" style="
                        padding: 8px 16px;
                        background-color: #868e96;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    ">查看详情</button>
                    <div id="error-stack" style="
                        display: none;
                        margin-top: 15px;
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                        font-family: monospace;
                        font-size: 12px;
                        white-space: pre-wrap;
                        max-height: 200px;
                        overflow-y: auto;
                    ">${error.stack || '无堆栈信息'}</div>
                </div>
            `

            // 绑定事件
            const retryButton = this.fallbackElement.querySelector('#error-retry')
            const detailsButton = this.fallbackElement.querySelector('#error-details')
            const stackDiv = this.fallbackElement.querySelector('#error-stack')

            retryButton?.addEventListener('click', () => {
                this.hideErrorBoundary()
                window.location.reload()
            })

            detailsButton?.addEventListener('click', () => {
                if (stackDiv) {
                    stackDiv.style.display = stackDiv.style.display === 'none' ? 'block' : 'none'
                }
            })
        } else {
            // 更新错误信息
            const messageElement = this.fallbackElement.querySelector('#error-message')
            const stackElement = this.fallbackElement.querySelector('#error-stack')
            if (messageElement) messageElement.textContent = error.message
            if (stackElement) stackElement.textContent = error.stack || '无堆栈信息'
        }

        // 显示错误边界
        const container = document.querySelector('.html-app') || document.body
        if (container && !container.contains(this.fallbackElement)) {
            container.appendChild(this.fallbackElement)
        }
    }

    public hideErrorBoundary(): void {
        if (this.fallbackElement && this.fallbackElement.parentNode) {
            this.fallbackElement.parentNode.removeChild(this.fallbackElement)
        }
    }

    public wrapFunction<T extends (...args: any[]) => any>(
        fn: T,
        context?: string
    ): T {
        return ((...args: any[]) => {
            try {
                return fn(...args)
            } catch (error) {
                this.captureError(error as Error, context)
                throw error
            }
        }) as T
    }

    public wrapAsyncFunction<T extends (...args: any[]) => Promise<any>>(
        fn: T,
        context?: string
    ): T {
        return (async (...args: any[]) => {
            try {
                return await fn(...args)
            } catch (error) {
                this.captureError(error as Error, context)
                throw error
            }
        }) as T
    }
}

export const errorBoundary = ErrorBoundary.getInstance()