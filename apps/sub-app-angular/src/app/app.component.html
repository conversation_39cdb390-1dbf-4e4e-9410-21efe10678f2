<div class="angular-app">
    <header class="app-header">
        <h1>{{ title }}</h1>
        <p>这是一个基于 Angular 16+ 的微前端子应用示例</p>
    </header>

    <main class="app-main">
        <section class="feature-section">
            <h2>功能特性</h2>
            <ul>
                <li>✅ 支持 Angular 16+ 版本</li>
                <li>✅ 完整的微前端生命周期管理</li>
                <li>✅ 独立运行和微前端模式双重支持</li>
                <li>✅ TypeScript 严格模式</li>
            </ul>
        </section>

        <section class="demo-section">
            <h2>演示功能</h2>
            <div class="demo-content">
                <p>当前时间: {{ getCurrentTime() }}</p>
                <button (click)="handleClick()" class="demo-button">
                    点击测试 Angular 功能
                </button>
            </div>
        </section>
    </main>

    <footer class="app-footer">
        <p>Powered by Micro-Core 微前端框架</p>
    </footer>
</div>