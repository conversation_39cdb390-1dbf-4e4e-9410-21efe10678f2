/**
 * @fileoverview Angular 微前端应用服务
 * <AUTHOR> <<EMAIL>>
 */

import { Inject, Injectable, Optional } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'

// 微前端应用属性接口
export interface MicroAppProps {
    name?: string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    container?: HTMLElement | string
    [key: string]: any
}

// 微前端应用信息接口
export interface MicroAppInfo {
    name: string
    framework: string
    version: string
    basename: string
    isMicroFrontend: boolean
}

@Injectable({
    providedIn: 'root'
})
export class MicroAppService {
    private propsSubject = new BehaviorSubject<MicroAppProps>({})
    private infoSubject = new BehaviorSubject<MicroAppInfo>({
        name: 'sub-app-angular',
        framework: 'Angular',
        version: '16.0.0',
        basename: '/angular',
        isMicroFrontend: false
    })

    constructor(
        @Optional() @Inject('MICRO_APP_PROPS') private microProps: MicroAppProps,
        @Optional() @Inject('MICRO_APP_INFO') private microInfo: MicroAppInfo
    ) {
        if (microProps) {
            this.propsSubject.next(microProps)
        }
        if (microInfo) {
            this.infoSubject.next(microInfo)
        }
    }

    /**
     * 获取微前端应用属性
     */
    getProps(): Observable<MicroAppProps> {
        return this.propsSubject.asObservable()
    }

    /**
     * 获取当前属性值
     */
    getCurrentProps(): MicroAppProps {
        return this.propsSubject.value
    }

    /**
     * 更新属性
     */
    updateProps(props: Partial<MicroAppProps>): void {
        const currentProps = this.propsSubject.value
        this.propsSubject.next({ ...currentProps, ...props })
    }

    /**
     * 获取微前端应用信息
     */
    getInfo(): Observable<MicroAppInfo> {
        return this.infoSubject.asObservable()
    }

    /**
     * 获取当前应用信息
     */
    getCurrentInfo(): MicroAppInfo {
        return this.infoSubject.value
    }

    /**
     * 获取应用名称
     */
    getAppName(): string {
        return this.getCurrentProps().name || this.getCurrentInfo().name
    }

    /**
     * 获取基础路径
     */
    getBasename(): string {
        return this.getCurrentProps().basename || this.getCurrentInfo().basename
    }

    /**
     * 获取主题
     */
    getTheme(): 'light' | 'dark' {
        return this.getCurrentProps().theme || 'light'
    }

    /**
     * 是否为暗色主题
     */
    isDarkTheme(): boolean {
        return this.getTheme() === 'dark'
    }

    /**
     * 获取用户信息
     */
    getUser(): any {
        return this.getCurrentProps().user
    }

    /**
     * 是否已登录
     */
    isLoggedIn(): boolean {
        return !!this.getUser()
    }

    /**
     * 是否为微前端模式
     */
    isMicroFrontend(): boolean {
        return this.getCurrentInfo().isMicroFrontend
    }

    /**
     * 是否为独立模式
     */
    isStandalone(): boolean {
        return !this.isMicroFrontend()
    }

    /**
     * 发送消息到主应用
     */
    sendToMain(type: string, data: any): void {
        window.postMessage({
            type: 'MICRO_APP_MESSAGE',
            source: 'sub-app-angular',
            target: 'main',
            data: { type, data, timestamp: Date.now() }
        }, '*')
    }

    /**
     * 广播消息到所有应用
     */
    broadcast(type: string, data: any): void {
        window.postMessage({
            type: 'MICRO_APP_BROADCAST',
            source: 'sub-app-angular',
            data: { type, data, timestamp: Date.now() }
        }, '*')
    }

    /**
     * 监听消息
     */
    onMessage(): Observable<MessageEvent> {
        return new Observable(observer => {
            const handler = (event: MessageEvent) => {
                if (event.data?.type === 'MICRO_APP_MESSAGE' || event.data?.type === 'MICRO_APP_BROADCAST') {
                    observer.next(event)
                }
            }

            window.addEventListener('message', handler)

            return () => {
                window.removeEventListener('message', handler)
            }
        })
    }

    /**
     * 获取完整路径
     */
    getFullPath(path: string): string {
        const basename = this.getBasename()
        const base = basename.endsWith('/') ? basename.slice(0, -1) : basename
        const cleanPath = path.startsWith('/') ? path : `/${path}`
        return `${base}${cleanPath}`
    }

    /**
     * 获取相对路径
     */
    getRelativePath(fullPath: string): string {
        const basename = this.getBasename()
        const base = basename.endsWith('/') ? basename.slice(0, -1) : basename
        return fullPath.startsWith(base) ? fullPath.slice(base.length) || '/' : fullPath
    }
}