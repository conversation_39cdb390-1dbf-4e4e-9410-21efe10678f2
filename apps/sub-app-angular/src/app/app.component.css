.angular-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

.app-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #dd0031, #c3002f);
    color: white;
    border-radius: 8px;
}

.app-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
}

.app-header p {
    margin: 0;
    opacity: 0.9;
}

.app-main {
    margin-bottom: 30px;
}

.feature-section,
.demo-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #dd0031;
}

.feature-section h2,
.demo-section h2 {
    color: #dd0031;
    margin-top: 0;
}

.feature-section ul {
    list-style: none;
    padding: 0;
}

.feature-section li {
    padding: 8px 0;
    font-size: 1.1em;
}

.demo-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.demo-button {
    background: #dd0031;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    align-self: flex-start;
}

.demo-button:hover {
    background: #c3002f;
}

.app-footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid #eee;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .angular-app {
        padding: 10px;
    }

    .app-header h1 {
        font-size: 2em;
    }

    .feature-section,
    .demo-section {
        padding: 15px;
    }
}