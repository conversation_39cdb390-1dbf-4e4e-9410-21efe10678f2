import { Component, OnDestroy, OnInit } from '@angular/core'
import { Subscription } from 'rxjs'
import { MicroAppInfo, MicroAppProps, MicroAppService } from './services/micro-app.service'

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit, OnDestroy {
    title = 'Angular 微前端子应用'
    props: MicroAppProps = {}
    info: MicroAppInfo = {
        name: 'sub-app-angular',
        framework: 'Angular',
        version: '16.0.0',
        basename: '/angular',
        isMicroFrontend: false
    }

    private subscriptions: Subscription[] = []

    constructor(private microAppService: MicroAppService) {
        console.log('Angular 子应用组件初始化')
    }

    ngOnInit(): void {
        // 订阅微前端属性变化
        const propsSubscription = this.microAppService.getProps().subscribe(props => {
            this.props = props
            console.log('微前端属性更新:', props)
        })

        // 订阅微前端应用信息变化
        const infoSubscription = this.microAppService.getInfo().subscribe(info => {
            this.info = info
            console.log('微前端应用信息更新:', info)
        })

        // 监听应用间消息
        const messageSubscription = this.microAppService.onMessage().subscribe(event => {
            console.log('收到微前端消息:', event.data)
            this.handleMicroMessage(event)
        })

        this.subscriptions.push(propsSubscription, infoSubscription, messageSubscription)
    }

    ngOnDestroy(): void {
        // 清理订阅
        this.subscriptions.forEach(sub => sub.unsubscribe())
    }

    getCurrentTime(): string {
        return new Date().toLocaleString('zh-CN')
    }

    handleClick(): void {
        alert('Angular 子应用功能测试成功！')
        console.log('Angular 子应用按钮点击事件触发')

        // 发送消息到主应用
        this.microAppService.sendToMain('button-clicked', {
            message: 'Angular 子应用按钮被点击',
            timestamp: Date.now()
        })
    }

    sendBroadcast(): void {
        this.microAppService.broadcast('angular-broadcast', {
            message: '来自 Angular 应用的广播消息',
            timestamp: Date.now()
        })
    }

    toggleTheme(): void {
        const currentTheme = this.microAppService.getTheme()
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark'

        this.microAppService.updateProps({ theme: newTheme })
        document.documentElement.setAttribute('data-theme', newTheme)
    }

    private handleMicroMessage(event: MessageEvent): void {
        const { type, data } = event.data.data || {}

        switch (type) {
            case 'theme-changed':
                document.documentElement.setAttribute('data-theme', data.theme)
                break
            case 'user-updated':
                this.microAppService.updateProps({ user: data.user })
                break
            default:
                console.log('未处理的消息类型:', type)
        }
    }

    get appName(): string {
        return this.microAppService.getAppName()
    }

    get basename(): string {
        return this.microAppService.getBasename()
    }

    get theme(): string {
        return this.microAppService.getTheme()
    }

    get isMicroFrontend(): boolean {
        return this.microAppService.isMicroFrontend()
    }

    get isLoggedIn(): boolean {
        return this.microAppService.isLoggedIn()
    }

    get user(): any {
        return this.microAppService.getUser()
    }
}
