import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core'
import { BrowserModule } from '@angular/platform-browser'
import { RouterModule, Routes } from '@angular/router'

import { AppComponent } from './app.component'
import { MicroAppErrorHandler } from './services/error-handler.service'
import { MicroAppService } from './services/micro-app.service'

// 路由配置
const routes: Routes = [
    {
        path: '',
        component: AppComponent
    },
    {
        path: 'home',
        component: AppComponent
    },
    {
        path: 'about',
        loadChildren: () => import('./modules/about/about.module').then(m => m.AboutModule)
    },
    {
        path: '**',
        redirectTo: ''
    }
]

@NgModule({
    declarations: [
        AppComponent
    ],
    imports: [
        BrowserModule,
        RouterModule.forRoot(routes, {
            useHash: false,
            enableTracing: false
        })
    ],
    providers: [
        MicroAppService,
        {
            provide: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
            useClass: MicroAppError<PERSON><PERSON><PERSON>
        }
    ],
    bootstrap: [AppComponent]
})
export class AppModule { }