/**
 * @fileoverview Angular 应用测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { vi } from 'vitest'

// 模拟全局对象
Object.defineProperty(window, '__POWERED_BY_MICRO_CORE__', {
    writable: true,
    value: true
})

Object.defineProperty(window, '__MICRO_APP_NAME__', {
    writable: true,
    value: 'sub-app-angular'
})

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16))
global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id))

// 模拟 Zone.js 相关
global.Zone = {
    current: {
        get: vi.fn(),
        fork: vi.fn(),
        run: vi.fn((fn) => fn()),
        runGuarded: vi.fn((fn) => fn()),
    }
} as any

// 设置测试环境变量
process.env.NODE_ENV = 'test'