{
    "compileOnSave": false,
    "compilerOptions": {
        "target": "ES2020",
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "module": "ES2022",
        "skipLibCheck": true,
        /* Angular specific */
        "baseUrl": "./",
        "outDir": "./dist/out-tsc",
        "moduleResolution": "node",
        "importHelpers": true,
        "useDefineForClassFields": false,
        "experimentalDecorators": true,
        "downlevelIteration": true,
        "sourceMap": true,
        "declaration": false,
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noImplicitReturns": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "forceConsistentCasingInFileNames": true,
        /* Path mapping */
        "paths": {
            "@/*": [
                "src/*"
            ],
            "@micro-core/core": [
                "../../packages/core/src"
            ],
            "@micro-core/adapter-angular": [
                "../../packages/adapters/adapter-angular/src"
            ],
            "@micro-core/shared": [
                "../../packages/shared/src"
            ]
        }
    },
    "include": [
        "src/**/*"
    ],
    "exclude": [
        "dist",
        "node_modules",
        "**/*.test.ts",
        "**/*.spec.ts"
    ],
    "angularCompilerOptions": {
        "enableI18nLegacyMessageIdFormat": false,
        "strictInjectionParameters": true,
        "strictInputAccessModifiers": true,
        "strictTemplates": true
    }
}