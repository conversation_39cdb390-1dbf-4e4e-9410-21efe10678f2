/**
 * @fileoverview Svelte 测试环境设置
 */

import { vi } from 'vitest'

// Mock Svelte App component
vi.mock('../App.svelte', () => ({
    default: class MockApp {
        constructor(options: any) {
            // Mock Svelte component constructor
            const target = options.target
            if (target) {
                target.innerHTML = '<div class="svelte-app">Mock Svelte App</div>'
            }
        }

        $destroy() {
            // Mock destroy method
        }

        $set(props: any) {
            // Mock set method
        }
    }
}))

// Mock performance API if not available
if (!global.performance) {
    global.performance = {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn()
    } as any
}

// Mock window.postMessage
global.window.postMessage = vi.fn()

// Mock DOM methods
Object.defineProperty(window, 'requestIdleCallback', {
    writable: true,
    value: vi.fn((cb: Function) => setTimeout(cb, 0))
})

Object.defineProperty(window, 'cancelIdleCallback', {
    writable: true,
    value: vi.fn()
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}))

// Setup DOM
document.body.innerHTML = '<div id="app"></div>'