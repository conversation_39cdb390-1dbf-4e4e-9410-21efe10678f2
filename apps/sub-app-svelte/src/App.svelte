<script>
    import { onMount } from 'svelte';
    
    let count = 0;
    let currentTime = '';
    let timeInterval;

    onMount(() => {
        console.log('Svelte 子应用组件挂载');
        updateTime();
        timeInterval = setInterval(updateTime, 1000);
        
        return () => {
            if (timeInterval) {
                clearInterval(timeInterval);
            }
        };
    });

    function updateTime() {
        currentTime = new Date().toLocaleString('zh-CN');
    }

    function handleClick() {
        count += 1;
        console.log('Svelte 子应用按钮点击，当前计数:', count);
        alert(`Svelte 子应用功能测试成功！点击次数: ${count}`);
    }
</script>

<div class="svelte-app">
    <header class="app-header">
        <h1>Svelte 微前端子应用</h1>
        <p>这是一个基于 Svelte 4.0+ 的微前端子应用示例</p>
    </header>

    <main class="app-main">
        <section class="feature-section">
            <h2>功能特性</h2>
            <ul>
                <li>✅ 基于 Svelte 4.0+ 版本</li>
                <li>✅ 编译时优化，运行时轻量</li>
                <li>✅ 完整的微前端生命周期管理</li>
                <li>✅ 响应式数据绑定</li>
                <li>✅ TypeScript 支持</li>
            </ul>
        </section>

        <section class="demo-section">
            <h2>演示功能</h2>
            <div class="demo-content">
                <p>当前时间: <span class="time-display">{currentTime}</span></p>
                <button on:click={handleClick} class="demo-button">
                    点击测试 Svelte 功能
                </button>
                <div class="counter">
                    点击次数: <span class="count-display">{count}</span>
                </div>
            </div>
        </section>
    </main>

    <footer class="app-footer">
        <p>Powered by Micro-Core 微前端框架</p>
    </footer>
</div>

<style>
    /* Svelte 子应用样式 */
    .svelte-app {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
        color: #333;
    }

    .app-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #ff3e00, #ff8a00);
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .app-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.5em;
        font-weight: 300;
    }

    .app-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1.1em;
    }

    .app-main {
        margin-bottom: 30px;
    }

    .feature-section,
    .demo-section {
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #ff3e00;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .feature-section h2,
    .demo-section h2 {
        color: #ff3e00;
        margin-top: 0;
        font-size: 1.5em;
    }

    .feature-section ul {
        list-style: none;
        padding: 0;
    }

    .feature-section li {
        padding: 8px 0;
        font-size: 1.1em;
        position: relative;
        padding-left: 20px;
    }

    .feature-section li::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        background: #ff3e00;
        border-radius: 50%;
    }

    .demo-content {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .demo-content p {
        font-size: 1.1em;
        margin: 0;
    }

    .time-display {
        font-weight: bold;
        color: #ff3e00;
    }

    .demo-button {
        background: linear-gradient(135deg, #ff3e00, #ff8a00);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 1em;
        transition: all 0.3s ease;
        align-self: flex-start;
        box-shadow: 0 2px 4px rgba(255, 62, 0, 0.3);
    }

    .demo-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 62, 0, 0.4);
    }

    .demo-button:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(255, 62, 0, 0.3);
    }

    .counter {
        padding: 10px 15px;
        background: #fff3f0;
        border-radius: 6px;
        font-size: 1.1em;
        align-self: flex-start;
    }

    .count-display {
        font-weight: bold;
        color: #ff3e00;
        font-size: 1.2em;
        transition: all 0.3s ease;
    }

    .count-display:hover {
        transform: scale(1.1);
        color: #ff8a00;
    }

    .app-footer {
        text-align: center;
        padding: 20px;
        color: #666;
        border-top: 1px solid #eee;
        margin-top: 30px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .svelte-app {
            padding: 10px;
        }

        .app-header h1 {
            font-size: 2em;
        }

        .feature-section,
        .demo-section {
            padding: 15px;
        }

        .demo-content {
            align-items: stretch;
        }

        .demo-button,
        .counter {
            align-self: stretch;
            text-align: center;
        }
    }

    /* 动画效果 */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .svelte-app {
        animation: fadeIn 0.5s ease-out;
    }
</style>