/**
 * @fileoverview Svelte 错误边界工具
 * <AUTHOR> <<EMAIL>>
 */

export interface ErrorInfo {
    message: string
    stack?: string
    componentStack?: string
    timestamp: number
}

export class ErrorBoundary {
    private static instance: ErrorBoundary
    private errorHandlers: Array<(error: ErrorInfo) => void> = []

    static getInstance(): ErrorBoundary {
        if (!ErrorBoundary.instance) {
            ErrorBoundary.instance = new ErrorBoundary()
        }
        return ErrorBoundary.instance
    }

    private constructor() {
        this.setupGlobalErrorHandling()
    }

    private setupGlobalErrorHandling(): void {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.handleError({
                message: event.message,
                stack: event.error?.stack,
                timestamp: Date.now()
            })
        })

        // 捕获未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                message: event.reason?.message || 'Unhandled Promise Rejection',
                stack: event.reason?.stack,
                timestamp: Date.now()
            })
        })
    }

    public addErrorHandler(handler: (error: ErrorInfo) => void): void {
        this.errorHandlers.push(handler)
    }

    public removeErrorHandler(handler: (error: ErrorInfo) => void): void {
        const index = this.errorHandlers.indexOf(handler)
        if (index > -1) {
            this.errorHandlers.splice(index, 1)
        }
    }

    private handleError(error: ErrorInfo): void {
        console.error('Svelte 应用错误:', error)

        this.errorHandlers.forEach(handler => {
            try {
                handler(error)
            } catch (handlerError) {
                console.error('错误处理器执行失败:', handlerError)
            }
        })
    }

    public captureError(error: Error, context?: string): void {
        this.handleError({
            message: error.message,
            stack: error.stack,
            componentStack: context,
            timestamp: Date.now()
        })
    }
}

export const errorBoundary = ErrorBoundary.getInstance()