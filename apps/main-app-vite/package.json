{"name": "main-app-vite", "version": "0.1.0", "description": "Micro-Core主应用示例 - 基于Vite构建", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/sidecar": "workspace:*", "@micro-core/builder-vite": "workspace:*", "vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^5.3.0", "vite": "^7.0.4", "vitest": "^3.2.5", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "vue-tsc": "^1.8.0"}, "keywords": ["micro-frontend", "main-app", "vite", "vue", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}