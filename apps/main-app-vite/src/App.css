/* 主应用样式 */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 头部样式 */
.app-header {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.logo-icon {
    margin-right: 0.5rem;
    font-size: 2rem;
}

.logo-text {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
}

.github-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.github-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 主内容区域 */
.app-main {
    flex: 1;
    background-color: #f5f5f5;
}

/* 首页样式 */
.home-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-section {
    text-align: center;
    padding: 4rem 0;
    background: white;
    border-radius: 12px;
    margin-bottom: 3rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #1976d2, #1565c0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1976d2;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* 信息区域样式 */
.info-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-section h2 {
    color: #1976d2;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
}

.info-card h3 {
    color: #1976d2;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.status {
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status.active {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status.inactive {
    background-color: #ffebee;
    color: #c62828;
}

.app-count {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 1rem;
}

.app-list {
    list-style: none;
    padding: 0;
}

.app-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.app-item:last-child {
    border-bottom: none;
}

.app-name {
    font-weight: 500;
}

.app-status {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: bold;
}

.app-status.active {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.app-status.inactive {
    background-color: #fff3e0;
    color: #f57c00;
}

.app-status.error {
    background-color: #ffebee;
    color: #c62828;
}

.config-info p {
    margin: 0.5rem 0;
    color: #666;
}

/* 演示区域样式 */
.demo-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
    color: #1976d2;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.demo-section p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.demo-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.demo-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.demo-link:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.demo-link.react {
    border-color: #61dafb;
}

.demo-link.react:hover {
    background-color: #f0fdff;
    border-color: #61dafb;
}

.demo-link.vue {
    border-color: #4fc08d;
}

.demo-link.vue:hover {
    background-color: #f0fff4;
    border-color: #4fc08d;
}

.demo-link.angular {
    border-color: #dd0031;
}

.demo-link.angular:hover {
    background-color: #fff5f5;
    border-color: #dd0031;
}

.demo-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.demo-text {
    font-size: 1.2rem;
    font-weight: 500;
}

/* 微应用容器样式 */
.micro-app-container {
    min-height: 500px;
    background: white;
    border-radius: 8px;
    margin: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.micro-app-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.micro-app-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #c62828;
    text-align: center;
    padding: 2rem;
}

.micro-app-error h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.micro-app-error button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.micro-app-error button:hover {
    background-color: #1565c0;
}

/* 404页面样式 */
.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
    color: #666;
    padding: 2rem;
}

.not-found h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #1976d2;
}

.not-found a {
    margin-top: 1rem;
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;
}

.not-found a:hover {
    text-decoration: underline;
}

/* 底部样式 */
.app-footer {
    background-color: #333;
    color: white;
    padding: 1rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-app {
    color: #1976d2;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .demo-links {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .home-page {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-menu {
        flex-direction: column;
        gap: 0.5rem;
    }

    .hero-section {
        padding: 2rem 1rem;
    }

    .feature-card {
        padding: 1rem;
    }
}