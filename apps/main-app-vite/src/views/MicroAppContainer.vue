<template>
  <div class="micro-app-container">
    <div class="app-header">
      <div class="app-info">
        <h3>{{ appTitle }}</h3>
        <span class="app-url" v-if="appConfig">{{ appConfig.entry }}</span>
      </div>
      <div class="app-controls">
        <div class="app-status">
          <span :class="['status-indicator', statusClass]"></span>
          {{ statusText }}
        </div>
        <div class="app-actions">
          <button 
            @click="reloadApp" 
            :disabled="loading"
            class="action-btn reload-btn"
            title="重新加载应用"
          >
            🔄
          </button>
          <button 
            @click="toggleDebugInfo" 
            class="action-btn debug-btn"
            title="切换调试信息"
          >
            🐛
          </button>
        </div>
      </div>
    </div>

    <!-- 调试信息面板 -->
    <div v-if="showDebugInfo" class="debug-panel">
      <div class="debug-section">
        <h4>应用信息</h4>
        <div class="debug-item">
          <span class="debug-label">名称:</span>
          <span class="debug-value">{{ props.appName }}</span>
        </div>
        <div class="debug-item">
          <span class="debug-label">状态:</span>
          <span class="debug-value">{{ appStatus }}</span>
        </div>
        <div class="debug-item" v-if="loadTime">
          <span class="debug-label">加载时间:</span>
          <span class="debug-value">{{ loadTime }}ms</span>
        </div>
        <div class="debug-item" v-if="errorInfo">
          <span class="debug-label">错误信息:</span>
          <span class="debug-value error">{{ errorInfo }}</span>
        </div>
      </div>
    </div>
    
    <div class="app-content">
      <!-- 错误状态 -->
      <div v-if="appStatus === 'error'" class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>应用加载失败</h3>
        <p>{{ errorMessage || '未知错误，请检查应用是否正常运行' }}</p>
        <div class="error-actions">
          <button @click="reloadApp" class="retry-btn">重试</button>
          <button @click="showDebugInfo = true" class="debug-btn">查看详情</button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载 {{ appTitle }}...</p>
        <div class="loading-progress">
          <div class="progress-bar" :style="{ width: `${loadingProgress}%` }"></div>
        </div>
      </div>

      <!-- 应用容器 -->
      <div 
        v-else
        :id="containerId" 
        class="micro-app-wrapper"
        :class="{ 'app-mounted': appStatus === 'mounted' }"
      >
        <!-- 微应用将在这里渲染 -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { getMicroCore, getMicroCoreManager, loadApp, unloadApp } from '../micro-config'

interface Props {
  appName: string
}

const props = defineProps<Props>()

const loading = ref(true)
const appStatus = ref('loading')
const errorMessage = ref('')
const errorInfo = ref('')
const loadTime = ref(0)
const showDebugInfo = ref(false)
const loadingProgress = ref(0)
const appConfig = ref<any>(null)

const appTitle = computed(() => {
  const titles: Record<string, string> = {
    'react-app': 'React 应用',
    'vue3-app': 'Vue3 应用',
    'vue2-app': 'Vue2 应用',
    'angular-app': 'Angular 应用',
    'svelte-app': 'Svelte 应用',
    'solid-app': 'Solid 应用',
    'html-app': 'HTML 应用'
  }
  return titles[props.appName] || '未知应用'
})

const containerId = computed(() => `${props.appName}-container`)

const statusClass = computed(() => {
  switch (appStatus.value) {
    case 'mounted': return 'status-success'
    case 'loading': return 'status-loading'
    case 'error': return 'status-error'
    case 'timeout': return 'status-warning'
    default: return 'status-default'
  }
})

const statusText = computed(() => {
  switch (appStatus.value) {
    case 'mounted': return '运行中'
    case 'loading': return '加载中'
    case 'error': return '加载失败'
    case 'timeout': return '加载超时'
    default: return '未知状态'
  }
})

// 切换调试信息
const toggleDebugInfo = () => {
  showDebugInfo.value = !showDebugInfo.value
}

// 重新加载应用
const reloadApp = async () => {
  try {
    loading.value = true
    appStatus.value = 'loading'
    errorMessage.value = ''
    errorInfo.value = ''
    loadingProgress.value = 0

    // 先卸载应用
    await unloadApp(props.appName)
    
    // 等待一下再重新加载
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 重新加载应用
    await loadAppWithProgress()
    
  } catch (error) {
    console.error('重新加载应用失败:', error)
    handleAppError(error as Error)
  }
}

// 带进度的应用加载
const loadAppWithProgress = async () => {
  const startTime = Date.now()
  
  // 模拟加载进度
  const progressInterval = setInterval(() => {
    if (loadingProgress.value < 90) {
      loadingProgress.value += Math.random() * 10
    }
  }, 200)

  try {
    await loadApp(props.appName)
    loadTime.value = Date.now() - startTime
    loadingProgress.value = 100
    
    setTimeout(() => {
      loading.value = false
      appStatus.value = 'mounted'
    }, 300)
    
  } catch (error) {
    throw error
  } finally {
    clearInterval(progressInterval)
  }
}

// 处理应用错误
const handleAppError = (error: Error) => {
  appStatus.value = 'error'
  loading.value = false
  errorMessage.value = error.message
  errorInfo.value = error.stack || ''
  
  // 发送错误到父应用
  window.parent.postMessage({
    type: 'MICRO_APP_ERROR',
    data: {
      appName: props.appName,
      error: {
        message: error.message,
        stack: error.stack
      }
    }
  }, '*')
}

// 设置事件监听器
const setupEventListeners = () => {
  const manager = getMicroCoreManager()
  if (!manager) return

  const kernel = manager.getKernel()
  if (!kernel) return

  // 监听应用状态变化
  kernel.on('app-loading', (data: any) => {
    if (data.name === props.appName) {
      appStatus.value = 'loading'
      loading.value = true
    }
  })

  kernel.on('app-mounted', (data: any) => {
    if (data.name === props.appName) {
      appStatus.value = 'mounted'
      loading.value = false
      loadingProgress.value = 100
      
      // 发送挂载消息到父应用
      window.parent.postMessage({
        type: 'MICRO_APP_MOUNTED',
        data: { appName: props.appName }
      }, '*')
    }
  })

  kernel.on('app-unmounted', (data: any) => {
    if (data.name === props.appName) {
      appStatus.value = 'loading'
      
      // 发送卸载消息到父应用
      window.parent.postMessage({
        type: 'MICRO_APP_UNMOUNTED',
        data: { appName: props.appName }
      }, '*')
    }
  })

  kernel.on('app-error', (data: any) => {
    if (data.name === props.appName) {
      handleAppError(data.error)
    }
  })
}

// 获取应用配置
const getAppConfig = () => {
  const manager = getMicroCoreManager()
  if (manager) {
    appConfig.value = manager.getApplicationRegistry().get(props.appName)
  }
}

// 监听应用名称变化
watch(() => props.appName, async (newAppName, oldAppName) => {
  if (oldAppName) {
    try {
      await unloadApp(oldAppName)
    } catch (error) {
      console.warn('Failed to unload previous app:', error)
    }
  }
  
  if (newAppName) {
    await loadAppWithProgress()
  }
})

onMounted(async () => {
  try {
    // 获取应用配置
    getAppConfig()
    
    // 设置事件监听器
    setupEventListeners()
    
    // 加载应用
    await loadAppWithProgress()
    
  } catch (error) {
    console.error('加载微应用失败:', error)
    handleAppError(error as Error)
  }
})

onUnmounted(async () => {
  try {
    await unloadApp(props.appName)
  } catch (error) {
    console.warn('Failed to unload app on unmount:', error)
  }
})
</script>

<style scoped>
.micro-app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--container-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.app-info {
  flex: 1;
}

.app-info h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
}

.app-url {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

.app-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.app-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-success {
  background-color: #28a745;
}

.status-loading {
  background-color: #ffc107;
  animation: pulse 1.5s infinite;
}

.status-error {
  background-color: #dc3545;
}

.status-warning {
  background-color: #fd7e14;
}

.status-default {
  background-color: #6c757d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.app-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reload-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.debug-btn:hover {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.debug-panel {
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
}

.debug-section h4 {
  margin: 0 0 0.75rem 0;
  color: var(--text-color);
  font-size: 1rem;
}

.debug-item {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.debug-label {
  min-width: 80px;
  color: var(--text-secondary);
  font-weight: 500;
}

.debug-value {
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

.debug-value.error {
  color: #dc3545;
}

.app-content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  padding: 2rem;
  text-align: center;
  background: var(--bg-color);
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.error-state h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
}

.error-state p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  max-width: 400px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
}

.retry-btn, .debug-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.retry-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.debug-btn:hover {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  padding: 2rem;
  text-align: center;
  background: var(--bg-color);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.micro-app-wrapper {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: var(--bg-color);
  transition: opacity 0.3s ease;
}

.micro-app-wrapper.app-mounted {
  opacity: 1;
}

@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }
  
  .app-controls {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .debug-panel {
    padding: 1rem;
  }
  
  .debug-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .debug-label {
    min-width: auto;
    font-weight: bold;
  }
  
  .error-actions {
    flex-direction: column;
    width: 100%;
    max-width: 200px;
  }
  
  .loading-progress {
    width: 150px;
  }
}
</style>