/**
 * @fileoverview Vue 3 主应用测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { cleanup } from '@vue/test-utils'
import { afterEach, vi } from 'vitest'

// 每个测试后清理
afterEach(() => {
    cleanup()
})

// 模拟全局对象
Object.defineProperty(window, '__POWERED_BY_MICRO_CORE__', {
    writable: true,
    value: true
})

Object.defineProperty(window, '__MICRO_APP_NAME__', {
    writable: true,
    value: 'main-app-vite'
})

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16))
global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id))

// 模拟 URL 构造函数
global.URL.createObjectURL = vi.fn(() => 'mock-url')
global.URL.revokeObjectURL = vi.fn()

// 设置测试环境变量
process.env.NODE_ENV = 'test'