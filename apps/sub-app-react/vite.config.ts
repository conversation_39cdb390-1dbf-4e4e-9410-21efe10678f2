import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig({
    plugins: [react()],

    server: {
        port: 3001,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/index.tsx',
            name: 'SubAppReact',
            fileName: 'sub-app-react',
            formats: ['umd']
        },
        rollupOptions: {
            external: ['react', 'react-dom'],
            output: {
                globals: {
                    react: 'React',
                    'react-dom': 'ReactDOM'
                }
            }
        }
    },

    define: {
        __MICRO_APP_NAME__: JSON.stringify('sub-app-react'),
        __POWERED_BY_MICRO_CORE__: true
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    },

    optimizeDeps: {
        include: ['react', 'react-dom', 'react-router-dom']
    }
})