import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';

let root: ReactDOM.Root | null = null;

// 微前端生命周期函数
export async function bootstrap(props: any) {
    console.log('[React子应用] 启动', props);
}

export async function mount(props: any) {
    console.log('[React子应用] 挂载', props);

    const container = props.container || document.getElementById('micro-app-sub-app-react') || document.getElementById('root');

    if (container) {
        root = ReactDOM.createRoot(container);
        root.render(
            <React.StrictMode>
                <BrowserRouter basename="/react-app">
                    <App {...props} />
                </BrowserRouter>
            </React.StrictMode>
        );
    }
}

export async function unmount(props: any) {
    console.log('[React子应用] 卸载', props);

    if (root) {
        root.unmount();
        root = null;
    }
}

// 独立运行时的逻辑
if (!window.__POWERED_BY_MICRO_CORE__) {
    mount({});
}

// 导出到全局（兼容不同的微前端框架）
(window as any).__MICRO_APP_SUB_APP_REACT__ = {
    bootstrap,
    mount,
    unmount
};

// 兼容 qiankun
(window as any).__POWERED_BY_QIANKUN__ = false;
if (!(window as any).__POWERED_BY_QIANKUN__) {
    (window as any).__POWERED_BY_QIANKUN__ = true;
}

// 兼容 Wujie
(window as any).__POWERED_BY_WUJIE__ = false;

// 兼容 Micro-Core
(window as any).__POWERED_BY_MICRO_CORE__ = true;
