/**
 * @fileoverview React 应用组件测试
 * <AUTHOR> <<EMAIL>>
 */

import { render, screen } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { describe, expect, it } from 'vitest'
import App from './App'

// 测试辅助函数
const renderApp = (props = {}) => {
    return render(
        <BrowserRouter>
            <App {...props} />
        </BrowserRouter>
    )
}

describe('App Component', () => {
    it('should render without crashing', () => {
        renderApp()
        expect(screen.getByText('⚛️ React 子应用')).toBeInTheDocument()
    })

    it('should display app name from props', () => {
        const props = { name: 'test-react-app' }
        renderApp(props)
        expect(screen.getByText('test-react-app')).toBeInTheDocument()
    })

    it('should show micro-frontend mode when powered by micro-core', () => {
        renderApp()
        expect(screen.getByText('微前端模式')).toBeInTheDocument()
    })

    it('should render navigation menu', () => {
        renderApp()
        expect(screen.getByText('首页')).toBeInTheDocument()
        expect(screen.getByText('关于')).toBeInTheDocument()
        expect(screen.getByText('演示')).toBeInTheDocument()
    })

    it('should apply theme from props', () => {
        const props = { theme: 'dark' }
        renderApp(props)
        expect(screen.getByText('主题: dark')).toBeInTheDocument()
    })

    it('should handle basename from props', () => {
        const props = { basename: '/custom-path' }
        renderApp(props)
        expect(screen.getByText(/基础路径: \/custom-path/)).toBeInTheDocument()
    })
})