/**
 * @fileoverview React 错误边界组件
 * <AUTHOR> <<EMAIL>>
 */

import { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
    children: ReactNode
    fallback?: ReactNode
    onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
    hasError: boolean
    error?: Error
    errorInfo?: ErrorInfo
}

/**
 * React 错误边界组件
 * 捕获子组件中的 JavaScript 错误，记录错误并显示备用 UI
 */
export class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): State {
        // 更新 state 使下一次渲染能够显示降级后的 UI
        return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // 记录错误信息
        console.error('React 错误边界捕获到错误:', error, errorInfo)

        // 更新状态
        this.setState({ error, errorInfo })

        // 调用外部错误处理函数
        if (this.props.onError) {
            this.props.onError(error, errorInfo)
        }

        // 发送错误到监控系统
        this.reportError(error, errorInfo)
    }

    /**
     * 报告错误到监控系统
     */
    private reportError(error: Error, errorInfo: ErrorInfo) {
        // 这里可以集成错误监控服务
        const errorReport = {
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        }

        // 发送到错误监控服务
        console.log('错误报告:', errorReport)
    }

    /**
     * 重试渲染
     */
    private handleRetry = () => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined })
    }

    render() {
        if (this.state.hasError) {
            // 自定义降级 UI
            if (this.props.fallback) {
                return this.props.fallback
            }

            // 默认错误 UI
            return (
                <div style={{
                    padding: '20px',
                    border: '1px solid #ff4d4f',
                    borderRadius: '6px',
                    backgroundColor: '#fff2f0',
                    color: '#a8071a',
                    textAlign: 'center'
                }}>
                    <h2>😵 应用出现错误</h2>
                    <p>很抱歉，应用遇到了一个错误。</p>

                    {process.env.NODE_ENV === 'development' && this.state.error && (
                        <details style={{ marginTop: '10px', textAlign: 'left' }}>
                            <summary>错误详情</summary>
                            <pre style={{
                                backgroundColor: '#f5f5f5',
                                padding: '10px',
                                borderRadius: '4px',
                                overflow: 'auto',
                                fontSize: '12px'
                            }}>
                                {this.state.error.stack}
                            </pre>
                        </details>
                    )}

                    <button
                        onClick={this.handleRetry}
                        style={{
                            marginTop: '10px',
                            padding: '8px 16px',
                            backgroundColor: '#1890ff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                        }}
                    >
                        重试
                    </button>
                </div>
            )
        }

        return this.props.children
    }
}

export default ErrorBoundary