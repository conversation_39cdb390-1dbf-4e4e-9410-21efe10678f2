<!--
  @fileoverview Vue 2 首页组件
  <AUTHOR> <<EMAIL>>
-->

<template>
  <div class="vue2-home">
    <div class="hero-section">
      <h1>🔥 Vue 2 微前端子应用</h1>
      <p class="subtitle">{{ message }}</p>

      <div class="counter-section">
        <h2>计数器演示</h2>
        <div class="counter">
          <button @click="decrement">-</button>
          <span class="count">{{ count }}</span>
          <button @click="increment">+</button>
        </div>
        <button class="reset-btn" @click="reset">重置</button>
      </div>

      <div class="features-section">
        <h2>Vue 2 特性展示</h2>
        <div class="features-grid">
          <div class="feature-card">
            <h3>📊 响应式数据</h3>
            <p>Vue 2 的响应式系统基于 Object.defineProperty</p>
          </div>
          <div class="feature-card">
            <h3>🧭 路由系统</h3>
            <p>Vue Router 实现单页面应用路由</p>
          </div>
          <div class="feature-card">
            <h3>🎨 模板语法</h3>
            <p>直观的模板语法和指令系统</p>
          </div>
          <div class="feature-card">
            <h3>⚡ 组件系统</h3>
            <p>可复用的组件化开发</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      message: '欢迎使用 Vue 2 微前端子应用！',
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    },
    decrement() {
      this.count--
    },
    reset() {
      this.count = 0
    }
  },
  mounted() {
    console.log('Vue 2 首页组件已挂载')
  }
}
</script>

<style scoped>
.vue2-home {
  padding: 20px;
}

.hero-section {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-section h1 {
  color: #4fc08d;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
}

.counter-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin: 15px 0;
}

.counter button {
  width: 40px;
  height: 40px;
  border: 1px solid #4fc08d;
  background: white;
  color: #4fc08d;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
}

.counter button:hover {
  background: #4fc08d;
  color: white;
}

.count {
  font-size: 24px;
  font-weight: bold;
  color: #4fc08d;
  min-width: 60px;
}

.reset-btn {
  padding: 8px 16px;
  background: #4fc08d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.reset-btn:hover {
  background: #42a085;
}

.features-section {
  margin-top: 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.feature-card {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-align: left;
}

.feature-card h3 {
  color: #4fc08d;
  margin-bottom: 10px;
}

.feature-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}
</style>