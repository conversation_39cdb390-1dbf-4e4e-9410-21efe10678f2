<template>
    <div class="vue2-app">
        <header class="app-header">
            <h1>Vue 2.7 微前端子应用</h1>
            <p>这是一个基于 Vue 2.7+ 的微前端子应用示例</p>
        </header>

        <main class="app-main">
            <section class="feature-section">
                <h2>功能特性</h2>
                <ul>
                    <li>✅ 基于 Vue 2.7+ 版本</li>
                    <li>✅ 支持 Composition API</li>
                    <li>✅ 完整的微前端生命周期管理</li>
                    <li>✅ 响应式数据绑定</li>
                    <li>✅ 组件化开发</li>
                </ul>
            </section>

            <section class="demo-section">
                <h2>演示功能</h2>
                <div class="demo-content">
                    <p>当前时间: <span class="time-display">{{ currentTime }}</span></p>
                    <button @click="handleClick" class="demo-button">
                        点击测试 Vue2 功能
                    </button>
                    <div class="counter">
                        点击次数: <span class="count-display">{{ count }}</span>
                    </div>
                </div>
            </section>
        </main>

        <footer class="app-footer">
            <p>Powered by Micro-Core 微前端框架</p>
        </footer>
    </div>
</template>

<script>
export default {
    name: 'App',
    data() {
        return {
            count: 0,
            currentTime: '',
            timeInterval: null
        };
    },
    mounted() {
        console.log('Vue2 子应用组件挂载');
        this.updateTime();
        this.timeInterval = setInterval(this.updateTime, 1000);
    },
    beforeDestroy() {
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
            this.timeInterval = null;
        }
    },
    methods: {
        updateTime() {
            this.currentTime = new Date().toLocaleString('zh-CN');
        },
        handleClick() {
            this.count++;
            console.log('Vue2 子应用按钮点击，当前计数:', this.count);
            alert(`Vue2 子应用功能测试成功！点击次数: ${this.count}`);
        }
    }
};
</script>

<style scoped>
/* Vue2 子应用样式 */
.vue2-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    color: #333;
}

.app-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #42b883, #35495e);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 300;
}

.app-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.app-main {
    margin-bottom: 30px;
}

.feature-section,
.demo-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #42b883;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.feature-section h2,
.demo-section h2 {
    color: #42b883;
    margin-top: 0;
    font-size: 1.5em;
}

.feature-section ul {
    list-style: none;
    padding: 0;
}

.feature-section li {
    padding: 8px 0;
    font-size: 1.1em;
    position: relative;
    padding-left: 20px;
}

.feature-section li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #42b883;
    border-radius: 50%;
}

.demo-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.demo-content p {
    font-size: 1.1em;
    margin: 0;
}

.time-display {
    font-weight: bold;
    color: #42b883;
}

.demo-button {
    background: linear-gradient(135deg, #42b883, #35495e);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
    align-self: flex-start;
    box-shadow: 0 2px 4px rgba(66, 184, 131, 0.3);
}

.demo-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(66, 184, 131, 0.4);
}

.demo-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(66, 184, 131, 0.3);
}

.counter {
    padding: 10px 15px;
    background: #f0f9ff;
    border-radius: 6px;
    font-size: 1.1em;
    align-self: flex-start;
}

.count-display {
    font-weight: bold;
    color: #42b883;
    font-size: 1.2em;
    transition: all 0.3s ease;
}

.count-display:hover {
    transform: scale(1.1);
    color: #35495e;
}

.app-footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid #eee;
    margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vue2-app {
        padding: 10px;
    }

    .app-header h1 {
        font-size: 2em;
    }

    .feature-section,
    .demo-section {
        padding: 15px;
    }

    .demo-content {
        align-items: stretch;
    }

    .demo-button,
    .counter {
        align-self: stretch;
        text-align: center;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vue2-app {
    animation: fadeIn 0.5s ease-out;
}
</style>