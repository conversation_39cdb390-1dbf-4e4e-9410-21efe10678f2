/**
 * @fileoverview Vitest configuration for Micro-Core projects
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

const { resolve } = require('path');

/**
 * Base Vitest configuration
 */
const baseConfig = {
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'test/',
        'dist/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/index.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(process.cwd(), 'src'),
      '@micro-core': resolve(process.cwd(), 'packages')
    }
  }
};

/**
 * Node.js specific configuration
 */
const nodeConfig = {
  ...baseConfig,
  test: {
    ...baseConfig.test,
    environment: 'node',
    setupFiles: undefined
  }
};

/**
 * React specific configuration
 */
const reactConfig = {
  ...baseConfig,
  test: {
    ...baseConfig.test,
    setupFiles: [
      ...baseConfig.test.setupFiles,
      '@testing-library/jest-dom'
    ]
  }
};

/**
 * Library specific configuration
 */
const libraryConfig = {
  ...baseConfig,
  test: {
    ...baseConfig.test,
    environment: 'node',
    coverage: {
      ...baseConfig.test.coverage,
      exclude: [
        ...baseConfig.test.coverage.exclude,
        '**/*.test.ts',
        '**/*.spec.ts'
      ]
    }
  }
};

module.exports = {
  base: baseConfig,
  node: nodeConfig,
  react: reactConfig,
  library: libraryConfig
};
