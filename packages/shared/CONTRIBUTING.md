# Contributing to @micro-core/shared

Thank you for your interest in contributing to the Micro-Core shared package! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/micro-core.git
   cd micro-core
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Navigate to shared package**
   ```bash
   cd packages/shared
   ```

4. **Run tests**
   ```bash
   pnpm test
   ```

5. **Start development**
   ```bash
   pnpm dev
   ```

## 📁 Project Structure

```
packages/shared/
├── constants/          # Shared constants
│   ├── src/index.ts   # Main constants export
│   └── __tests__/     # Constants tests
├── types/             # TypeScript type definitions
│   ├── src/index.ts   # Main types export
│   └── __tests__/     # Type tests
├── utils/             # Utility functions
│   ├── src/           # Source files by category
│   │   ├── index.ts   # Main utils export
│   │   ├── type-check.ts
│   │   ├── object.ts
│   │   ├── string.ts
│   │   └── ...
│   └── __tests__/     # Utils tests
├── helpers/           # Business logic helpers
│   ├── src/index.ts   # Main helpers export
│   └── __tests__/     # Helpers tests
└── test/              # Test configuration
    └── setup.ts       # Test environment setup
```

## 🛠️ Development Guidelines

### Code Style

We use ESLint and Prettier for consistent code formatting:

```bash
# Check code style
pnpm lint

# Fix code style issues
pnpm lint:fix

# Format code
pnpm format
```

### TypeScript Guidelines

1. **Use strict TypeScript**
   - Enable strict mode in tsconfig.json
   - Avoid `any` type unless absolutely necessary
   - Provide proper type annotations for public APIs

2. **Type definitions**
   - Export all public types from `types/src/index.ts`
   - Use descriptive interface names with proper JSDoc comments
   - Prefer interfaces over type aliases for object shapes

3. **Generic types**
   - Use meaningful constraint names: `T extends Record<string, any>`
   - Provide default type parameters where appropriate
   - Document generic parameters in JSDoc

### Function Guidelines

1. **Pure functions preferred**
   ```typescript
   // ✅ Good: Pure function
   export function add(a: number, b: number): number {
     return a + b;
   }

   // ❌ Avoid: Side effects
   let counter = 0;
   export function increment(): number {
     return ++counter;
   }
   ```

2. **Error handling**
   ```typescript
   // ✅ Good: Explicit error handling
   export function parseJson<T>(json: string): Result<T, Error> {
     try {
       const data = JSON.parse(json);
       return { success: true, data };
     } catch (error) {
       return { success: false, error: error as Error };
     }
   }
   ```

3. **Documentation**
   ```typescript
   /**
    * Merges multiple objects deeply
    * @param objects - Objects to merge
    * @returns Merged object
    * @example
    * ```typescript
    * const result = deepMerge({ a: 1 }, { b: 2 });
    * // Result: { a: 1, b: 2 }
    * ```
    */
   export function deepMerge<T>(...objects: Partial<T>[]): T {
     // Implementation
   }
   ```

### Testing Guidelines

1. **Test structure**
   ```typescript
   import { describe, it, expect } from 'vitest';
   import { functionToTest } from '../src/module';

   describe('functionToTest', () => {
     it('should handle normal case', () => {
       const result = functionToTest('input');
       expect(result).toBe('expected');
     });

     it('should handle edge case', () => {
       const result = functionToTest('');
       expect(result).toBe('default');
     });

     it('should throw on invalid input', () => {
       expect(() => functionToTest(null)).toThrow();
     });
   });
   ```

2. **Test coverage**
   - Aim for 90%+ test coverage
   - Test both happy path and error cases
   - Include edge cases and boundary conditions
   - Test async functions properly

3. **Test naming**
   - Use descriptive test names
   - Follow pattern: "should [expected behavior] when [condition]"
   - Group related tests in describe blocks

## 📝 Adding New Features

### 1. Adding Utility Functions

1. **Create the function file**
   ```typescript
   // packages/shared/utils/src/new-feature.ts
   /**
    * Description of the new feature
    * @param param1 - Parameter description
    * @returns Return value description
    */
   export function newFeature(param1: string): boolean {
     // Implementation
     return true;
   }
   ```

2. **Export from main index**
   ```typescript
   // packages/shared/utils/src/index.ts
   export * from './new-feature';
   ```

3. **Add to utils object**
   ```typescript
   // packages/shared/utils/src/index.ts
   import { newFeature } from './new-feature';

   export const utils = {
     // ... existing utils
     newFeature
   };
   ```

4. **Write tests**
   ```typescript
   // packages/shared/utils/__tests__/new-feature.test.ts
   import { describe, it, expect } from 'vitest';
   import { newFeature } from '../src/new-feature';

   describe('newFeature', () => {
     it('should work correctly', () => {
       expect(newFeature('test')).toBe(true);
     });
   });
   ```

### 2. Adding Constants

1. **Add to constants file**
   ```typescript
   // packages/shared/constants/src/index.ts
   export enum NewConstants {
     VALUE_ONE = 'VALUE_ONE',
     VALUE_TWO = 'VALUE_TWO'
   }
   ```

2. **Add to constants object**
   ```typescript
   export const constants = {
     // ... existing constants
     NewConstants
   };
   ```

### 3. Adding Types

1. **Define the type**
   ```typescript
   // packages/shared/types/src/index.ts
   /**
    * Description of the new type
    */
   export interface NewType {
     /** Property description */
     property: string;
     /** Optional property description */
     optionalProperty?: number;
   }
   ```

2. **Add to types object if needed**
   ```typescript
   export const types = {
     // Runtime type checking functions if needed
   };
   ```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run specific test file
pnpm test type-check.test.ts
```

### Test Environment

Tests run in a jsdom environment with mocked browser APIs:
- DOM APIs (document, window)
- Storage APIs (localStorage, sessionStorage)
- Network APIs (fetch)
- Performance APIs
- Crypto APIs

### Writing Tests

1. **Use descriptive test names**
2. **Test both success and failure cases**
3. **Mock external dependencies**
4. **Clean up after tests**
5. **Use appropriate matchers**

## 📋 Pull Request Process

### Before Submitting

1. **Run the full test suite**
   ```bash
   pnpm test
   ```

2. **Check code style**
   ```bash
   pnpm lint
   pnpm format
   ```

3. **Build the package**
   ```bash
   pnpm build
   ```

4. **Update documentation if needed**

### PR Guidelines

1. **Title**: Use conventional commit format
   - `feat: add new utility function`
   - `fix: resolve memory leak in event bus`
   - `docs: update API documentation`

2. **Description**: Include
   - What changes were made
   - Why the changes were necessary
   - Any breaking changes
   - Testing instructions

3. **Checklist**
   - [ ] Tests pass
   - [ ] Code follows style guidelines
   - [ ] Documentation updated
   - [ ] No breaking changes (or properly documented)
   - [ ] Changelog updated if needed

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment information**
   - Node.js version
   - Package version
   - Operating system

2. **Steps to reproduce**
   - Minimal code example
   - Expected behavior
   - Actual behavior

3. **Additional context**
   - Error messages
   - Stack traces
   - Related issues

## 💡 Feature Requests

For new features, please:

1. **Check existing issues** to avoid duplicates
2. **Describe the use case** and motivation
3. **Provide examples** of how it would be used
4. **Consider backwards compatibility**

## 📞 Getting Help

- **Documentation**: Check the [README](./README.md) and [API docs](./docs/api.md)
- **Issues**: Search existing [GitHub issues](../../issues)
- **Discussions**: Use [GitHub Discussions](../../discussions) for questions

## 🏆 Recognition

Contributors will be recognized in:
- CHANGELOG.md for significant contributions
- README.md contributors section
- Release notes for major features

Thank you for contributing to Micro-Core! 🎉
