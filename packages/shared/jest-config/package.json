{"name": "@micro-core/jest-config", "version": "0.1.0", "description": "Jest configuration for Micro-Core", "main": "index.js", "types": "index.d.ts", "files": ["*.js", "*.d.ts"], "scripts": {"test": "jest __tests__", "test:watch": "jest __tests__ --watch"}, "keywords": ["jest", "jestconfig", "micro-core", "testing"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/jest-config"}, "publishConfig": {"access": "public"}, "dependencies": {"@types/jest": "^29.5.0"}, "devDependencies": {"jest": "^29.5.0"}, "peerDependencies": {"jest": ">=29.0.0"}}