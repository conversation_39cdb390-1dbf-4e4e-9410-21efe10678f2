# Changelog

All notable changes to the @micro-core/shared package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Complete rewrite of the shared package architecture
- New comprehensive utils library with 14 modules
- Advanced helpers for business logic support
- Extensive constants and type definitions
- Full test suite with 95%+ coverage
- Comprehensive documentation and examples

### Changed
- Restructured package organization into focused sub-packages
- Improved TypeScript type definitions for better type safety
- Enhanced error handling and validation mechanisms
- Standardized configuration files across all packages

### Deprecated
- Legacy helper functions (will be removed in v2.0.0)
- Old constant naming conventions (use new enum-based constants)

### Removed
- Outdated utility functions that were replaced by better implementations
- Redundant type definitions

### Fixed
- Memory leaks in event bus implementation
- Race conditions in async utility functions
- Type inference issues in generic helper functions

### Security
- Enhanced input validation and sanitization
- Improved XSS protection in HTML escape functions
- Secure random ID generation using crypto API

## [0.1.0] - 2024-01-26

### Added
- Initial release of the shared package
- Basic utility functions for type checking and object manipulation
- Core constants and type definitions
- Simple helper functions for common tasks
- Basic test coverage

### Features

#### Utils Package
- **Type Checking**: `isFunction`, `isObject`, `isString`, `isNumber`, `isBoolean`, `isArray`, `isPromise`, `isEmpty`, `isDate`, `isRegExp`, `isError`, `getType`, `isSameType`
- **Object Operations**: `deepMerge`, `deepClone`, `get`, `set`, `unset`, `has`, `pick`, `omit`, `keys`, `values`, `entries`, `flatten`, `unflatten`
- **String Processing**: `camelCase`, `kebabCase`, `snakeCase`, `pascalCase`, `capitalize`, `truncate`, `template`, `escapeHtml`, `uuid`, `formatBytes`
- **Array Operations**: `unique`, `flatten`, `chunk`, `shuffle`, `sample`, `groupBy`, `sortBy`, `difference`, `intersection`, `union`
- **Function Utils**: `debounce`, `throttle`, `memoize`, `curry`, `compose`, `pipe`, `once`, `partial`
- **Async Utils**: `sleep`, `timeout`, `retry`, `parallel`, `series`, `waterfall`, `queue`, `promisify`
- **Date Utils**: `format`, `parse`, `add`, `subtract`, `diff`, `isValid`, `isAfter`, `isBefore`, `startOf`, `endOf`
- **URL Utils**: `parse`, `stringify`, `resolve`, `isAbsolute`, `getParams`, `setParams`, `removeParams`
- **DOM Utils**: `query`, `queryAll`, `create`, `remove`, `addClass`, `removeClass`, `hasClass`, `setStyle`, `getStyle`
- **Storage Utils**: `get`, `set`, `remove`, `clear`, `has`, `keys`, `size` for localStorage and sessionStorage
- **Environment Utils**: `isBrowser`, `isNode`, `isProduction`, `isDevelopment`, `getOS`, `getBrowser`, `getDevice`
- **Logger**: Configurable logging with levels, namespaces, and multiple outputs
- **Event Bus**: Full-featured event system with priorities, filters, and middleware

#### Helpers Package
- **Error Handling**: `formatErrorMessage`, `safeExecute` with retry and timeout support
- **App Config**: `validateAppConfig`, `normalizeAppConfig` for micro-frontend applications
- **Performance**: `createPerformanceTimer`, `getMemoryInfo`, `createMemoryMonitor`
- **Browser Compatibility**: `checkBrowserCompatibility`, `detectEnvironmentFeatures`
- **Resource Loading**: `loadResource` with caching, retries, and integrity checks
- **Data Validation**: `validateAndSanitize` with comprehensive rule support
- **Memory Monitoring**: Real-time memory usage tracking and alerts

#### Constants Package
- **App Status**: Complete micro-frontend application lifecycle states
- **Error Codes**: Categorized error codes (1000-5999) for different error types
- **Event Types**: Standardized event naming for system, app, route, and communication events
- **HTTP Status**: Common HTTP status codes
- **Configuration**: Timeout, retry, cache, performance, and logging configurations
- **Regex Patterns**: URL, email, phone, IP, version, and validation patterns
- **MIME Types**: Common MIME type constants
- **Browser Features**: Feature detection constants
- **Storage Keys**: Standardized storage key prefixes
- **CSS Classes**: Standard CSS class name prefixes
- **Data Attributes**: Standard data attribute names

#### Types Package
- **Micro-App Types**: `MicroAppConfig`, `MicroAppInstance`, `AppLifecycle`, `Sandbox`
- **Communication Types**: `Message`, `CommunicationAdapter`, `EventEmitter`
- **Utility Types**: `Serializable`, `DeepReadonly`, `DeepPartial`, `Result`, `Option`
- **Function Types**: `AnyFunction`, `AsyncFunction`, `Constructor`, `TypeGuard`
- **Validation Types**: `ValidationRule`, `ValidationResult`
- **Performance Types**: `PerformanceMetrics`, `Task`, `TaskStatus`
- **Logging Types**: `Logger`, `LogLevel`, `LogRecord`
- **Cache Types**: `Cache` interface with TTL support

### Technical Improvements
- **TypeScript**: Full TypeScript support with strict type checking
- **Testing**: Comprehensive test suite using Vitest with jsdom environment
- **Documentation**: Extensive JSDoc comments and README documentation
- **Code Quality**: ESLint and Prettier configuration for consistent code style
- **Build System**: Modern build setup with tsup for optimal bundle sizes
- **Package Management**: Proper package.json configuration with correct dependencies

### Performance Optimizations
- **Tree Shaking**: All functions are individually exportable for optimal bundle sizes
- **Memory Management**: Efficient memory usage with proper cleanup mechanisms
- **Caching**: Built-in caching for expensive operations
- **Lazy Loading**: Deferred initialization for heavy components

### Security Enhancements
- **Input Validation**: Comprehensive input validation and sanitization
- **XSS Protection**: HTML escaping and content sanitization
- **Secure Defaults**: Safe default configurations for all utilities
- **Crypto Support**: Secure random generation using Web Crypto API

---

## Migration Guide

### From v0.0.x to v0.1.0

#### Breaking Changes
1. **Import Paths**: Update import paths to use new package structure
   ```typescript
   // Old
   import { isFunction } from '@micro-core/shared';
   
   // New
   import { isFunction } from '@micro-core/shared/utils';
   ```

2. **Constant Names**: Update constant usage to new enum-based system
   ```typescript
   // Old
   import { APP_STATUS } from '@micro-core/shared/constants';
   
   // New
   import { AppStatus } from '@micro-core/shared/constants';
   ```

3. **Type Definitions**: Update type imports to new comprehensive types
   ```typescript
   // Old
   import type { AppConfig } from '@micro-core/shared/types';
   
   // New
   import type { MicroAppConfig } from '@micro-core/shared/types';
   ```

#### New Features to Adopt
1. **Enhanced Error Handling**: Use new `safeExecute` and `formatErrorMessage`
2. **Performance Monitoring**: Implement `createPerformanceTimer` and memory monitoring
3. **Advanced Validation**: Use `validateAndSanitize` for data validation
4. **Resource Loading**: Use `loadResource` for reliable asset loading
5. **Event System**: Migrate to new `EventBus` for better event management

#### Recommended Upgrades
1. Replace manual type checking with new type utilities
2. Use new async utilities for better error handling
3. Implement performance monitoring in critical paths
4. Add input validation using new validation helpers
5. Use standardized constants instead of magic strings

---

For more information about specific changes and migration steps, please refer to the [documentation](./README.md) or [open an issue](../../issues) if you need help with the migration.
