{"name": "@micro-core/shared-constants", "version": "0.1.0", "description": "Micro-Core shared constants", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["micro-frontend", "constants", "shared"], "author": "Echo <<EMAIL>>", "license": "MIT", "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "rimraf": "^5.0.0", "tsup": "^8.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}