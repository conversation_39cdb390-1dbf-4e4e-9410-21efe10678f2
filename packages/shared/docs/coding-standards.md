# Coding Standards

This document outlines the coding standards and best practices for the @micro-core/shared package.

## 📋 General Principles

### 1. Code Quality
- **Readability**: Code should be self-documenting and easy to understand
- **Maintainability**: Write code that is easy to modify and extend
- **Testability**: Design functions to be easily testable
- **Performance**: Consider performance implications, but prioritize clarity
- **Security**: Follow secure coding practices

### 2. Consistency
- Follow established patterns within the codebase
- Use consistent naming conventions
- Maintain consistent file structure
- Apply consistent formatting rules

## 🎯 TypeScript Standards

### Type Definitions

#### ✅ Good Practices
```typescript
// Use descriptive interface names
interface UserProfile {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// Use generic constraints appropriately
function processItems<T extends { id: string }>(items: T[]): T[] {
  return items.filter(item => item.id);
}

// Prefer union types for known values
type Theme = 'light' | 'dark' | 'auto';

// Use readonly for immutable data
interface ReadonlyConfig {
  readonly apiUrl: string;
  readonly features: readonly string[];
}
```

#### ❌ Avoid
```typescript
// Don't use any
function process(data: any): any {
  return data;
}

// Don't use overly generic names
interface Data {
  stuff: any;
}

// Don't use function types for simple callbacks
interface Options {
  callback: Function; // Use specific signature instead
}
```

### Function Design

#### ✅ Good Practices
```typescript
// Pure functions with clear inputs/outputs
export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount);
}

// Use Result type for operations that can fail
export function parseJson<T>(json: string): Result<T, Error> {
  try {
    const data = JSON.parse(json);
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}

// Provide overloads for different use cases
export function createElement(tag: string): HTMLElement;
export function createElement(tag: string, attributes: Record<string, string>): HTMLElement;
export function createElement(
  tag: string,
  attributes?: Record<string, string>
): HTMLElement {
  const element = document.createElement(tag);
  if (attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
  }
  return element;
}
```

#### ❌ Avoid
```typescript
// Don't mutate input parameters
function sortItems(items: any[]): any[] {
  return items.sort(); // Mutates original array
}

// Don't use unclear return types
function getData(): any {
  return fetch('/api/data');
}

// Don't mix sync and async patterns
function processData(data: any, callback?: Function): any | Promise<any> {
  // Confusing return type
}
```

## 📝 Documentation Standards

### JSDoc Comments

#### Required for Public APIs
```typescript
/**
 * Merges multiple objects deeply, creating a new object
 * @param target - The target object to merge into
 * @param sources - Source objects to merge from
 * @returns A new object with merged properties
 * @throws {TypeError} When target is not an object
 * @example
 * ```typescript
 * const result = deepMerge({ a: 1 }, { b: 2 }, { c: 3 });
 * console.log(result); // { a: 1, b: 2, c: 3 }
 * ```
 * @since 0.1.0
 */
export function deepMerge<T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  // Implementation
}
```

#### Interface Documentation
```typescript
/**
 * Configuration for micro-frontend applications
 * @public
 */
export interface MicroAppConfig {
  /** Unique application name */
  name: string;
  
  /** Application entry point URL or configuration */
  entry: string | AppEntry;
  
  /** 
   * Container selector or element where the app will be mounted
   * @defaultValue Generated from app name: `#${name}`
   */
  container?: string | HTMLElement;
  
  /**
   * Rule to determine when the app should be active
   * @example
   * ```typescript
   * // String pattern
   * activeRule: '/my-app'
   * 
   * // Function
   * activeRule: (location) => location.pathname.startsWith('/my-app')
   * ```
   */
  activeRule?: string | string[] | ((location: Location) => boolean);
}
```

### Code Comments

#### ✅ Good Comments
```typescript
// Calculate the optimal chunk size based on available memory
const chunkSize = Math.min(maxChunkSize, availableMemory / 4);

// TODO: Implement caching mechanism for better performance
// FIXME: Handle edge case when array is empty
// NOTE: This workaround is needed for Safari compatibility

/**
 * Complex algorithm explanation:
 * 1. First, we normalize the input data
 * 2. Then we apply the transformation matrix
 * 3. Finally, we validate the output
 */
```

#### ❌ Avoid
```typescript
// Increment counter (obvious from code)
counter++;

// This function adds two numbers (redundant)
function add(a: number, b: number): number {
  return a + b;
}
```

## 🧪 Testing Standards

### Test Structure
```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { functionToTest } from '../src/module';

describe('functionToTest', () => {
  // Group related tests
  describe('when input is valid', () => {
    it('should return expected result', () => {
      const result = functionToTest('valid-input');
      expect(result).toBe('expected-output');
    });

    it('should handle edge cases', () => {
      expect(functionToTest('')).toBe('');
      expect(functionToTest('single')).toBe('single');
    });
  });

  describe('when input is invalid', () => {
    it('should throw TypeError for null input', () => {
      expect(() => functionToTest(null)).toThrow(TypeError);
    });

    it('should return default for undefined input', () => {
      expect(functionToTest(undefined)).toBe('default');
    });
  });

  // Test async functions properly
  describe('async operations', () => {
    it('should resolve with correct data', async () => {
      const result = await asyncFunction('input');
      expect(result).toEqual({ success: true, data: 'output' });
    });

    it('should reject with error on failure', async () => {
      await expect(asyncFunction('invalid')).rejects.toThrow('Invalid input');
    });
  });
});
```

### Test Naming Conventions
- **Describe blocks**: Use the function/class name being tested
- **Test cases**: Use "should [expected behavior] when [condition]"
- **Be specific**: Include the exact scenario being tested

### Test Coverage Requirements
- **Minimum 90%** overall coverage
- **100%** for critical utility functions
- **All branches** should be tested
- **Error paths** must be covered

## 🏗️ Architecture Patterns

### Module Organization
```typescript
// Each module should have a clear, single responsibility
// utils/src/string.ts - String manipulation utilities
// utils/src/object.ts - Object manipulation utilities
// utils/src/array.ts - Array manipulation utilities

// Export pattern
export { functionA, functionB } from './module-a';
export { functionC, functionD } from './module-b';

// Grouped exports for convenience
export const stringUtils = {
  camelCase,
  kebabCase,
  snakeCase,
  // ...
};
```

### Error Handling Patterns
```typescript
// Use Result type for operations that can fail
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

// Consistent error handling
export function safeOperation<T>(operation: () => T): Result<T> {
  try {
    const data = operation();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}

// Use custom error types for specific domains
export class ValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public value: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}
```

### Performance Considerations
```typescript
// Use memoization for expensive computations
const memoizedFunction = memoize((input: string) => {
  // Expensive computation
  return expensiveOperation(input);
});

// Lazy initialization for heavy objects
let heavyObject: HeavyObject | undefined;
export function getHeavyObject(): HeavyObject {
  if (!heavyObject) {
    heavyObject = new HeavyObject();
  }
  return heavyObject;
}

// Use generators for large datasets
export function* processLargeDataset(data: any[]): Generator<ProcessedItem> {
  for (const item of data) {
    yield processItem(item);
  }
}
```

## 🔒 Security Guidelines

### Input Validation
```typescript
// Always validate and sanitize inputs
export function sanitizeHtml(html: string): string {
  return html
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

// Use type guards for runtime validation
export function isValidEmail(email: unknown): email is string {
  return typeof email === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}
```

### Safe Defaults
```typescript
// Provide safe defaults
export function createConfig(options: Partial<Config> = {}): Config {
  return {
    timeout: 30000,
    retries: 3,
    secure: true,
    ...options
  };
}
```

## 📊 Performance Guidelines

### Memory Management
```typescript
// Clean up event listeners
export function createEventManager() {
  const listeners = new Map();
  
  return {
    on(event: string, listener: Function) {
      // Add listener
    },
    
    destroy() {
      listeners.clear(); // Clean up
    }
  };
}

// Use WeakMap for object associations
const objectMetadata = new WeakMap();
```

### Optimization Techniques
```typescript
// Use object pooling for frequently created objects
class ObjectPool<T> {
  private pool: T[] = [];
  
  constructor(private factory: () => T) {}
  
  get(): T {
    return this.pool.pop() || this.factory();
  }
  
  release(obj: T): void {
    this.pool.push(obj);
  }
}

// Debounce expensive operations
export const debouncedSave = debounce(saveData, 1000);
```

## 🔄 Versioning and Compatibility

### Semantic Versioning
- **MAJOR**: Breaking changes
- **MINOR**: New features (backwards compatible)
- **PATCH**: Bug fixes (backwards compatible)

### Deprecation Process
```typescript
/**
 * @deprecated Use newFunction instead. Will be removed in v2.0.0
 */
export function oldFunction(): void {
  console.warn('oldFunction is deprecated. Use newFunction instead.');
  return newFunction();
}
```

### Breaking Changes
- Document in CHANGELOG.md
- Provide migration guide
- Use deprecation warnings first
- Follow semantic versioning

---

These standards ensure code quality, maintainability, and consistency across the @micro-core/shared package. All contributors should follow these guidelines to maintain the high quality of the codebase.
