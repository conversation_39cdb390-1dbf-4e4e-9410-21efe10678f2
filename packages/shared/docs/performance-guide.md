# Performance Guide

This guide covers performance best practices and optimization techniques for the @micro-core/shared package.

## 🎯 Performance Principles

### 1. Measure First
Always measure performance before optimizing:

```typescript
import { createPerformanceTimer } from '@micro-core/shared/helpers';

const timer = createPerformanceTimer('operation');
timer.start();
// Your operation here
const duration = timer.end();
console.log(`Operation took ${duration.toFixed(2)}ms`);
```

### 2. Optimize for Common Cases
Focus on optimizing the most frequently used code paths:

```typescript
// Fast path for common case
export function fastStringCheck(str: string): boolean {
  // Quick check for empty string (common case)
  if (str.length === 0) return false;
  
  // More expensive validation for non-empty strings
  return /^[a-zA-Z0-9]+$/.test(str);
}
```

### 3. Lazy Loading
Load resources only when needed:

```typescript
// Lazy initialization
let expensiveResource: ExpensiveResource | undefined;

export function getExpensiveResource(): ExpensiveResource {
  if (!expensiveResource) {
    expensiveResource = new ExpensiveResource();
  }
  return expensiveResource;
}
```

## 🚀 Memory Optimization

### Memory Monitoring
Use built-in memory monitoring tools:

```typescript
import { getMemoryInfo, createMemoryMonitor } from '@micro-core/shared/helpers';

// Check current memory usage
const memInfo = getMemoryInfo();
if (memInfo && memInfo.warning) {
  console.warn(`High memory usage: ${memInfo.percentage}%`);
}

// Set up continuous monitoring
const monitor = createMemoryMonitor({
  threshold: 80,
  onWarning: (info) => {
    console.warn('Memory warning:', info);
    // Trigger cleanup
    performCleanup();
  }
});
monitor.start();
```

### Memory Leak Prevention

#### 1. Event Listener Cleanup
```typescript
export class ComponentManager {
  private listeners = new Map<string, Function>();
  
  addEventListener(element: Element, event: string, handler: Function) {
    element.addEventListener(event, handler);
    this.listeners.set(`${event}-${Date.now()}`, () => {
      element.removeEventListener(event, handler);
    });
  }
  
  destroy() {
    // Clean up all listeners
    this.listeners.forEach(cleanup => cleanup());
    this.listeners.clear();
  }
}
```

#### 2. WeakMap for Object Associations
```typescript
// Use WeakMap to avoid memory leaks
const elementMetadata = new WeakMap<Element, ElementData>();

export function setElementData(element: Element, data: ElementData) {
  elementMetadata.set(element, data);
}

export function getElementData(element: Element): ElementData | undefined {
  return elementMetadata.get(element);
}
```

#### 3. Object Pooling
```typescript
class ObjectPool<T> {
  private pool: T[] = [];
  private maxSize: number;
  
  constructor(
    private factory: () => T,
    private reset: (obj: T) => void,
    maxSize = 100
  ) {
    this.maxSize = maxSize;
  }
  
  acquire(): T {
    const obj = this.pool.pop();
    return obj || this.factory();
  }
  
  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.reset(obj);
      this.pool.push(obj);
    }
  }
}

// Usage example
const stringBuilderPool = new ObjectPool(
  () => [],
  (arr) => arr.length = 0,
  50
);
```

## ⚡ Function Optimization

### Memoization
Cache expensive function results:

```typescript
import { memoize } from '@micro-core/shared/utils';

// Expensive computation
const expensiveCalculation = memoize((input: number) => {
  // Complex calculation
  let result = 0;
  for (let i = 0; i < input * 1000; i++) {
    result += Math.sqrt(i);
  }
  return result;
});

// First call: computed
const result1 = expensiveCalculation(100); // ~50ms

// Second call: cached
const result2 = expensiveCalculation(100); // ~0.1ms
```

### Debouncing and Throttling
Control function execution frequency:

```typescript
import { debounce, throttle } from '@micro-core/shared/utils';

// Debounce: Execute only after calls stop
const debouncedSave = debounce((data: any) => {
  saveToServer(data);
}, 1000);

// Throttle: Execute at most once per interval
const throttledScroll = throttle((event: Event) => {
  updateScrollPosition(event);
}, 16); // ~60fps
```

### Early Returns
Minimize unnecessary computations:

```typescript
export function processItems<T>(
  items: T[],
  predicate: (item: T) => boolean
): T[] {
  // Early return for empty arrays
  if (items.length === 0) return [];
  
  // Early return if no predicate
  if (!predicate) return items.slice();
  
  // Process only when necessary
  return items.filter(predicate);
}
```

## 🔄 Async Optimization

### Parallel Processing
Execute independent operations concurrently:

```typescript
import { parallel } from '@micro-core/shared/utils';

// Sequential (slow)
const result1 = await fetchUserData(userId);
const result2 = await fetchUserPreferences(userId);
const result3 = await fetchUserSettings(userId);

// Parallel (fast)
const [userData, preferences, settings] = await parallel([
  () => fetchUserData(userId),
  () => fetchUserPreferences(userId),
  () => fetchUserSettings(userId)
]);
```

### Request Batching
Combine multiple requests:

```typescript
class RequestBatcher {
  private batch: Array<{ id: string; resolve: Function; reject: Function }> = [];
  private timeoutId: NodeJS.Timeout | null = null;
  
  request<T>(id: string): Promise<T> {
    return new Promise((resolve, reject) => {
      this.batch.push({ id, resolve, reject });
      
      if (!this.timeoutId) {
        this.timeoutId = setTimeout(() => this.flush(), 10);
      }
    });
  }
  
  private async flush() {
    const currentBatch = this.batch.splice(0);
    this.timeoutId = null;
    
    try {
      const ids = currentBatch.map(item => item.id);
      const results = await fetchMultiple(ids);
      
      currentBatch.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      currentBatch.forEach(item => item.reject(error));
    }
  }
}
```

### Resource Loading Optimization
```typescript
import { loadResource } from '@micro-core/shared/helpers';

// Preload critical resources
const criticalResources = [
  'https://cdn.example.com/critical.js',
  'https://cdn.example.com/critical.css'
];

// Load in parallel with caching
const loadPromises = criticalResources.map(url => 
  loadResource(url, url.endsWith('.js') ? 'script' : 'style', {
    cache: true,
    retries: 2
  })
);

await Promise.all(loadPromises);
```

## 📊 Data Structure Optimization

### Choose Appropriate Data Structures

#### Arrays vs Sets
```typescript
// For membership testing, use Set
const allowedIds = new Set(['id1', 'id2', 'id3']);
const isAllowed = allowedIds.has(userId); // O(1)

// Instead of array
const allowedIdsArray = ['id1', 'id2', 'id3'];
const isAllowedSlow = allowedIdsArray.includes(userId); // O(n)
```

#### Maps vs Objects
```typescript
// For dynamic keys, use Map
const userCache = new Map<string, User>();
userCache.set(userId, userData);
const user = userCache.get(userId);

// Objects are better for known, static keys
interface Config {
  apiUrl: string;
  timeout: number;
  retries: number;
}
```

### Efficient Array Operations
```typescript
// Use appropriate array methods
const numbers = [1, 2, 3, 4, 5];

// Find first match (stops at first result)
const firstEven = numbers.find(n => n % 2 === 0); // 2

// Check if any match (stops at first match)
const hasEven = numbers.some(n => n % 2 === 0); // true

// Transform and filter in one pass
const doubledEvens = numbers.reduce<number[]>((acc, n) => {
  if (n % 2 === 0) {
    acc.push(n * 2);
  }
  return acc;
}, []);
```

## 🎨 DOM Optimization

### Batch DOM Operations
```typescript
import { createElement } from '@micro-core/shared/utils';

// Inefficient: Multiple DOM manipulations
function addItemsSlow(container: Element, items: string[]) {
  items.forEach(item => {
    const element = createElement('div', { textContent: item });
    container.appendChild(element); // Triggers reflow each time
  });
}

// Efficient: Batch operations
function addItemsFast(container: Element, items: string[]) {
  const fragment = document.createDocumentFragment();
  
  items.forEach(item => {
    const element = createElement('div', { textContent: item });
    fragment.appendChild(element);
  });
  
  container.appendChild(fragment); // Single reflow
}
```

### Virtual Scrolling for Large Lists
```typescript
class VirtualList {
  private itemHeight: number;
  private visibleCount: number;
  private scrollTop = 0;
  
  constructor(
    private container: Element,
    private items: any[],
    itemHeight: number
  ) {
    this.itemHeight = itemHeight;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2;
    this.setupScrolling();
  }
  
  private render() {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, this.items.length);
    
    // Only render visible items
    const visibleItems = this.items.slice(startIndex, endIndex);
    this.renderItems(visibleItems, startIndex);
  }
  
  private setupScrolling() {
    this.container.addEventListener('scroll', 
      throttle(() => {
        this.scrollTop = this.container.scrollTop;
        this.render();
      }, 16)
    );
  }
}
```

## 📈 Performance Monitoring

### Built-in Performance Tracking
```typescript
import { createPerformanceTimer } from '@micro-core/shared/helpers';

class PerformanceTracker {
  private metrics = new Map<string, number[]>();
  
  track<T>(operation: string, fn: () => T): T {
    const timer = createPerformanceTimer();
    timer.start();
    
    try {
      const result = fn();
      const duration = timer.end();
      this.recordMetric(operation, duration);
      return result;
    } catch (error) {
      timer.end();
      throw error;
    }
  }
  
  private recordMetric(operation: string, duration: number) {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const durations = this.metrics.get(operation)!;
    durations.push(duration);
    
    // Keep only last 100 measurements
    if (durations.length > 100) {
      durations.shift();
    }
  }
  
  getStats(operation: string) {
    const durations = this.metrics.get(operation) || [];
    if (durations.length === 0) return null;
    
    const sorted = durations.slice().sort((a, b) => a - b);
    return {
      count: durations.length,
      avg: durations.reduce((a, b) => a + b) / durations.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)]
    };
  }
}

// Usage
const tracker = new PerformanceTracker();

const result = tracker.track('data-processing', () => {
  return processLargeDataset(data);
});

console.log(tracker.getStats('data-processing'));
```

### Performance Budget
```typescript
const PERFORMANCE_BUDGETS = {
  'critical-path': 100, // 100ms
  'user-interaction': 16, // 16ms (60fps)
  'background-task': 1000 // 1s
};

function checkPerformanceBudget(operation: string, duration: number) {
  const budget = PERFORMANCE_BUDGETS[operation];
  if (budget && duration > budget) {
    console.warn(
      `Performance budget exceeded for ${operation}: ${duration}ms > ${budget}ms`
    );
  }
}
```

## 🔧 Build Optimization

### Tree Shaking
Ensure functions are tree-shakeable:

```typescript
// ✅ Good: Individual exports
export function functionA() { /* ... */ }
export function functionB() { /* ... */ }

// ❌ Bad: Default export with object
export default {
  functionA() { /* ... */ },
  functionB() { /* ... */ }
};
```

### Bundle Analysis
```bash
# Analyze bundle size
pnpm build:analyze

# Check for duplicate dependencies
pnpm ls --depth=0
```

## 📋 Performance Checklist

### Development
- [ ] Use performance timers for critical operations
- [ ] Implement memory monitoring for long-running processes
- [ ] Use appropriate data structures (Set, Map, WeakMap)
- [ ] Implement lazy loading for expensive resources
- [ ] Use memoization for expensive computations
- [ ] Debounce/throttle high-frequency operations

### Testing
- [ ] Write performance tests for critical functions
- [ ] Test with large datasets
- [ ] Monitor memory usage during tests
- [ ] Verify no memory leaks in long-running operations

### Production
- [ ] Monitor real-world performance metrics
- [ ] Set up performance budgets
- [ ] Use CDN for static resources
- [ ] Enable compression (gzip/brotli)
- [ ] Implement proper caching strategies

---

Following these performance guidelines will help ensure the @micro-core/shared package remains fast and efficient across all use cases.
