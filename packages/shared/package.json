{"name": "@micro-core/shared", "version": "0.1.0", "description": "Micro-Core 共享工具包 - 提供通用工具函数、类型定义和配置管理", "keywords": ["micro-core", "shared", "utils", "types", "config"], "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared"}, "license": "MIT", "author": {"name": "Echo", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./constants": {"types": "./dist/constants/index.d.ts", "import": "./dist/constants/index.js", "require": "./dist/constants/index.cjs"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.cjs"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsup", "build:watch": "tsup --watch", "clean": "rm -rf dist", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "type-check": "tsc --noEmit"}, "dependencies": {"eventemitter3": "^5.0.1"}, "devDependencies": {"@micro-core/eslint-config": "workspace:*", "@micro-core/ts-config": "workspace:*", "@types/node": "^20.10.6", "@vitest/coverage-v8": "^1.1.3", "@vitest/ui": "^1.1.3", "eslint": "^8.56.0", "jsdom": "^23.0.1", "tsup": "^8.0.1", "typescript": "^5.3.3", "vitest": "^1.1.3"}, "publishConfig": {"access": "public"}}