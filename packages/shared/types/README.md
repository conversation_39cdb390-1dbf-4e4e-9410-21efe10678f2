# @micro-core/types

TypeScript type definitions for Micro-Core projects.

## Installation

```bash
npm install @micro-core/types
```

## Usage

```typescript
import type {
  // Core types
  MicroAppConfig,
  MicroAppInstance,
  AppLifecycle,
  
  // Communication types
  Message,
  EventEmitter,
  CommunicationAdapter,
  
  // Utility types
  DeepReadonly,
  DeepPartial,
  Optional,
  Result,
  
  // Function types
  AnyFunction,
  AsyncFunction,
  TypeGuard
} from '@micro-core/types';

// Define app configuration
const appConfig: MicroAppConfig = {
  name: 'my-app',
  entry: 'https://example.com/app.js',
  container: '#app-container',
  activeRule: '/my-app'
};

// Use utility types
type ReadonlyConfig = DeepReadonly<MicroAppConfig>;
type PartialConfig = DeepPartial<MicroAppConfig>;

// Result type for error handling
const loadApp = async (): Promise<Result<MicroAppInstance, Error>> => {
  try {
    const app = await loadMicroApp(appConfig);
    return { success: true, data: app };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
```

## API Reference

### Core Types

#### MicroAppConfig
Configuration for micro-frontend applications:
```typescript
interface MicroAppConfig {
  name: string;
  entry: string | AppEntry;
  container?: string | HTMLElement;
  activeRule?: string | ((location: Location) => boolean);
  loader?: LoaderConfig;
  sandbox?: SandboxConfig;
  props?: Record<string, any>;
}
```

#### MicroAppInstance
Instance of a loaded micro-frontend application:
```typescript
interface MicroAppInstance {
  name: string;
  status: AppStatus;
  mount: (props?: Record<string, any>) => Promise<void>;
  unmount: () => Promise<void>;
  update?: (props: Record<string, any>) => Promise<void>;
  getStatus: () => AppStatus;
}
```

#### AppLifecycle
Application lifecycle hooks:
```typescript
interface AppLifecycle {
  bootstrap?: (props?: Record<string, any>) => Promise<void>;
  mount: (props?: Record<string, any>) => Promise<void>;
  unmount: (props?: Record<string, any>) => Promise<void>;
  update?: (props?: Record<string, any>) => Promise<void>;
}
```

### Communication Types

#### Message
Inter-application message structure:
```typescript
interface Message<T = any> {
  type: string;
  payload: T;
  source: string;
  target?: string;
  timestamp: number;
}
```

#### EventEmitter
Event emitter interface:
```typescript
interface EventEmitter<T extends EventMap = EventMap> {
  on<K extends keyof T>(event: K, listener: EventHandler<T[K]>): void;
  off<K extends keyof T>(event: K, listener: EventHandler<T[K]>): void;
  emit<K extends keyof T>(event: K, ...args: T[K]): void;
}
```

### Utility Types

#### DeepReadonly<T>
Makes all properties of T readonly recursively:
```typescript
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};
```

#### DeepPartial<T>
Makes all properties of T optional recursively:
```typescript
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
```

#### Result<T, E>
Result type for error handling:
```typescript
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };
```

#### Optional<T, K>
Makes specific properties optional:
```typescript
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
```

### Function Types

#### AnyFunction
Any function type:
```typescript
type AnyFunction = (...args: any[]) => any;
```

#### AsyncFunction<T>
Async function type:
```typescript
type AsyncFunction<T = any> = (...args: any[]) => Promise<T>;
```

#### TypeGuard<T>
Type guard function:
```typescript
type TypeGuard<T> = (value: any) => value is T;
```

### Configuration Types

#### LoaderConfig
Loader configuration:
```typescript
interface LoaderConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  credentials?: RequestCredentials;
}
```

#### SandboxConfig
Sandbox configuration:
```typescript
interface SandboxConfig {
  strictStyleIsolation?: boolean;
  experimentalStyleIsolation?: boolean;
  excludeAssetFilter?: (assetUrl: string) => boolean;
}
```

## Features

- ✅ Comprehensive type coverage
- ✅ Well-documented interfaces
- ✅ Utility types for common patterns
- ✅ Generic type support
- ✅ Strict TypeScript compatibility
- ✅ Zero runtime overhead

## License

MIT
