/**
 * Base Micro-Core error
 */
export class MicroCoreError extends Error {
    code: string;
    details?: any;

    constructor(message: string, code?: string, details?: any) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code || 'UNKNOWN_ERROR';
        this.details = details;
    }
}

/**
 * Application error
 */
export class ApplicationError extends MicroCoreError {
    appName: string;

    constructor(appName: string, message: string, details?: any) {
        super(message, 'APPLICATION_ERROR', details);
        this.name = 'ApplicationError';
        this.appName = appName;
    }
}

/**
 * Plugin error
 */
export class PluginError extends MicroCoreError {
    pluginName: string;

    constructor(pluginName: string, message: string, details?: any) {
        super(message, 'PLUGIN_ERROR', details);
        this.name = 'PluginError';
        this.pluginName = pluginName;
    }
}

/**
 * Sandbox error
 */
export class SandboxError extends MicroCoreError {
    sandboxName: string;

    constructor(sandboxName: string, message: string, details?: any) {
        super(message, 'SANDBOX_ERROR', details);
        this.name = 'SandboxError';
        this.sandboxName = sandboxName;
    }
}

/**
 * Resource error
 */
export class ResourceError extends MicroCoreError {
    url: string;

    constructor(url: string, message: string, details?: any) {
        super(message, 'RESOURCE_ERROR', details);
        this.name = 'ResourceError';
        this.url = url;
    }
}