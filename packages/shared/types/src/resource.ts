/**
 * Resource type
 */
export type ResourceType = 'js' | 'css' | 'html' | 'json' | 'wasm';

/**
 * Resource status
 */
export type ResourceStatus = 'NOT_LOADED' | 'LOADING' | 'LOADED' | 'ERROR';

/**
 * Resource options
 */
export interface ResourceOptions {
    cache?: boolean;
    timeout?: number;
    retries?: number;
    prefetch?: boolean;
}

/**
 * Resource definition
 */
export interface Resource {
    url: string;
    type: ResourceType;
    content?: string | ArrayBuffer;
    status: ResourceStatus;
    error?: Error;
    headers?: Record<string, string>;
    size?: number;
    loadTime?: number;
    lastAccessed?: Date;
    etag?: string;
}

/**
 * Resource manager interface
 */
export interface ResourceManager {
    loadResource(url: string, type: ResourceType, options?: ResourceOptions): Promise<Resource>;
    loadResources(resources: Array<{ url: string, type: ResourceType }>, options?: ResourceOptions): Promise<Resource[]>;
    prefetchResource(url: string, type: ResourceType): void;
    prefetchResources(resources: Array<{ url: string, type: ResourceType }>): void;
    getCachedResource(url: string): Resource | undefined;
    clearCache(): void;
}