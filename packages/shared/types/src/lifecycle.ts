/**
 * 生命周期相关类型定义
 */

/**
 * 生命周期阶段枚举
 */
export enum LifecyclePhase {
    LOAD = 'LOAD',
    BOOTSTRAP = 'BOOTSTRAP',
    MOUNT = 'MOUNT',
    UNMOUNT = 'UNMOUNT',
    UNLOAD = 'UNLOAD',
    UPDATE = 'UPDATE'
}

/**
 * 生命周期钩子函数类型
 */
export type LifecycleHook<T = any> = (props: T) => Promise<any> | any;

/**
 * 生命周期管理器接口
 */
export interface LifecycleManager {
    executeLifecycle(phase: LifecyclePhase, appName: string, props?: any): Promise<void>;
    registerHook(phase: LifecyclePhase, appName: string, hook: LifecycleHook): void;
    unregisterHook(phase: LifecyclePhase, appName: string, hook: LifecycleHook): void;
    getHooks(phase: LifecyclePhase, appName: string): LifecycleHook[];
}

/**
 * 生命周期事件接口
 */
export interface LifecycleEvent {
    phase: LifecyclePhase;
    appName: string;
    props?: any;
    timestamp: number;
    success: boolean;
    error?: Error;
    duration?: number;
}