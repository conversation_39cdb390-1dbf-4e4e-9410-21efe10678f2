/**
 * 通信相关类型定义
 */

/**
 * 事件总线接口
 */
export interface EventBus {
    on(event: string, callback: (...args: any[]) => void): void;
    off(event: string, callback?: (...args: any[]) => void): void;
    emit(event: string, ...args: any[]): void;
    once(event: string, callback: (...args: any[]) => void): void;
    clear(): void;
}

/**
 * 全局状态管理接口
 */
export interface GlobalState {
    get<T = any>(key: string): T | undefined;
    set<T = any>(key: string, value: T): void;
    remove(key: string): void;
    clear(): void;
    watch<T = any>(key: string, callback: (newValue: T, oldValue: T) => void): () => void;
    getAll(): Record<string, any>;
}

/**
 * 通信消息接口
 */
export interface CommunicationMessage {
    type: string;
    data: any;
    from: string;
    to?: string;
    timestamp: number;
    id: string;
}

/**
 * 通信管理器接口
 */
export interface CommunicationManager {
    eventBus: EventBus;
    globalState: GlobalState;
    sendMessage(message: CommunicationMessage): void;
    onMessage(callback: (message: CommunicationMessage) => void): () => void;
}