/**
 * 工具类型定义
 */

/**
 * 深度部分类型
 */
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度必需类型
 */
export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * 可选键类型
 */
export type OptionalKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

/**
 * 必需键类型
 */
export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

/**
 * 函数类型
 */
export type Fn<T = any, R = T> = (...args: T[]) => R;

/**
 * 异步函数类型
 */
export type AsyncFn<T = any, R = T> = (...args: T[]) => Promise<R>;

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * 抽象构造函数类型
 */
export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T;

/**
 * 键值对类型
 */
export type KeyValuePair<K = string, V = any> = {
    key: K;
    value: V;
};

/**
 * 可为空类型
 */
export type Nullable<T> = T | null;

/**
 * 可为未定义类型
 */
export type Optional<T> = T | undefined;

/**
 * 可为空或未定义类型
 */
export type Maybe<T> = T | null | undefined;

/**
 * 数组或单个元素类型
 */
export type ArrayOrSingle<T> = T | T[];

/**
 * 字符串键对象类型
 */
export type StringKeyObject<T = any> = Record<string, T>;

/**
 * 数字键对象类型
 */
export type NumberKeyObject<T = any> = Record<number, T>;

/**
 * 符号键对象类型
 */
export type SymbolKeyObject<T = any> = Record<symbol, T>;

/**
 * 任意键对象类型
 */
export type AnyKeyObject<T = any> = Record<string | number | symbol, T>;