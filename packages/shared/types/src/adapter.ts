/**
 * Adapter options
 */
export interface AdapterOptions {
    // Common options for all adapters
}

/**
 * React adapter options
 */
export interface ReactAdapterOptions extends AdapterOptions {
    reactVersion?: '16' | '17' | '18';
    useLegacyRoot?: boolean;
}

/**
 * Vue adapter options
 */
export interface VueAdapterOptions extends AdapterOptions {
    vueVersion?: '2' | '3';
}

/**
 * Angular adapter options
 */
export interface AngularAdapterOptions extends AdapterOptions {
    angularVersion?: string;
    enableZoneJs?: boolean;
}

/**
 * Adapter interface
 */
export interface Adapter {
    mount(container: HTMLElement, component: any, props?: any): void;
    unmount(container: HTMLElement): void;
    update(container: HTMLElement, component: any, props?: any): void;
}