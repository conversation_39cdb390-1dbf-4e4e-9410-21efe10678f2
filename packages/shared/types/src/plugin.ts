/**
 * Plugin interface
 */
export interface Plugin {
    name: string;
    version?: string;
    dependencies?: string[];
    install: (kernel: any, options?: any) => void;
    uninstall?: (kernel: any) => void;
    status?: 'INSTALLED' | 'ERROR';
    error?: Error;
    options?: any;
}

/**
 * Plugin hooks
 */
export interface PluginHooks {
    [hookName: string]: Function[];
}

/**
 * Plugin system interface
 */
export interface PluginSystem {
    register(plugin: Plugin, options?: any): void;
    unregister(pluginName: string): void;
    getPlugin(pluginName: string): Plugin | undefined;
    addHook(hookName: string, hook: Function): void;
    removeHook(hookName: string, hook: Function): void;
    applyHooks(hookName: string, ...args: any[]): Promise<any[]>;
}