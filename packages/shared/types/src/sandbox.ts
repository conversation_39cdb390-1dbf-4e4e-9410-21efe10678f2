/**
 * 沙箱相关类型定义
 */

/**
 * 沙箱类型枚举
 */
export type SandboxType =
    | 'proxy'
    | 'defineProperty'
    | 'webComponent'
    | 'iframe'
    | 'namespace'
    | 'federation';

/**
 * 沙箱配置选项
 */
export interface SandboxOptions {
    type: SandboxType;
    strict?: boolean;
    excludeAssetFilter?: (url: string) => boolean;
    globalVariables?: string[];
    multiMode?: boolean;
    speedy?: boolean;
}

/**
 * 沙箱配置接口
 */
export interface SandboxConfig extends SandboxOptions {
    name: string;
    container?: HTMLElement;
    props?: Record<string, any>;
}

/**
 * Proxy 沙箱选项
 */
export interface ProxySandboxOptions extends SandboxOptions {
    type: 'proxy';
    fakeWindow?: boolean;
    patchDocument?: boolean;
}

/**
 * Iframe 沙箱选项
 */
export interface IframeSandboxOptions extends SandboxOptions {
    type: 'iframe';
    src?: string;
    attrs?: Record<string, string>;
    replace?: boolean;
}

/**
 * WebComponent 沙箱选项
 */
export interface WebComponentSandboxOptions extends SandboxOptions {
    type: 'webComponent';
    shadowMode?: 'open' | 'closed';
    delegatesFocus?: boolean;
}

/**
 * 沙箱实例接口
 */
export interface SandboxInstance {
    name: string;
    type: SandboxType;
    active: boolean;
    proxy?: any;
    iframe?: HTMLIFrameElement;
    webComponent?: HTMLElement;
    start(): Promise<void>;
    stop(): Promise<void>;
    destroy(): Promise<void>;
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
    create(config: SandboxConfig): Promise<SandboxInstance>;
    get(name: string): SandboxInstance | undefined;
    destroy(name: string): Promise<void>;
    destroyAll(): Promise<void>;
}