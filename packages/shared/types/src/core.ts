/**
 * Core kernel configuration options
 */
export interface MicroCoreKernelOptions {
    plugins?: Plugin[];
    sandbox?: SandboxStrategy;
    router?: RouterOptions;
    debug?: boolean;
    maxApps?: number;
    timeout?: number;
}

/**
 * Plugin interface
 */
export interface Plugin {
    name: string;
    version?: string;
    dependencies?: string[];
    install: (kernel: any, options?: any) => void;
    uninstall?: (kernel: any) => void;
    status?: 'INSTALLED' | 'ERROR';
    error?: Error;
    options?: any;
}

/**
 * Sandbox strategy types
 */
export type SandboxStrategy =
    | 'proxy'
    | 'defineProperty'
    | 'webComponent'
    | 'iframe'
    | 'namespace'
    | 'federation';

/**
 * Router configuration options
 */
export interface RouterOptions {
    mode: 'history' | 'hash' | 'memory';
    base?: string;
    routes?: Route[];
}

/**
 * Route definition
 */
export interface Route {
    path: string;
    appName: string;
    props?: Record<string, any>;
}