/**
 * 路由相关类型定义
 */

/**
 * 路由记录接口
 */
export interface Route {
    path: string;
    appName: string;
    props?: Record<string, any>;
    meta?: Record<string, any>;
}

/**
 * 路由配置选项
 */
export interface RouterOptions {
    mode: 'history' | 'hash' | 'memory';
    base?: string;
    routes?: Route[];
    fallback?: boolean;
    scrollBehavior?: (to: RouteLocation, from: RouteLocation, savedPosition: any) => any;
}

/**
 * 路由配置接口
 */
export interface RouterConfig extends RouterOptions {
    strict?: boolean;
    sensitive?: boolean;
    end?: boolean;
}

/**
 * 路由位置接口
 */
export interface RouteLocation {
    path: string;
    name?: string;
    params: RouteParams;
    query: RouteQuery;
    hash: string;
    fullPath: string;
    matched: RouteRecord[];
    meta: Record<string, any>;
    redirectedFrom?: RouteLocation;
}

/**
 * 路由参数类型
 */
export type RouteParams = Record<string, string | string[]>;

/**
 * 路由查询参数类型
 */
export type RouteQuery = Record<string, string | string[] | null>;

/**
 * 路由记录接口
 */
export interface RouteRecord {
    path: string;
    name?: string;
    component?: any;
    components?: Record<string, any>;
    redirect?: string | RouteLocation | ((to: RouteLocation) => string | RouteLocation);
    alias?: string | string[];
    children?: RouteRecord[];
    meta?: Record<string, any>;
    beforeEnter?: NavigationGuard | NavigationGuard[];
    props?: boolean | Record<string, any> | ((route: RouteLocation) => Record<string, any>);
    caseSensitive?: boolean;
    pathToRegexpOptions?: any;
}

/**
 * 导航守卫类型
 */
export type NavigationGuard = (
    to: RouteLocation,
    from: RouteLocation,
    next: (to?: string | false | RouteLocation | Error) => void
) => any;

/**
 * 路由守卫接口
 */
export interface RouterGuard {
    beforeEach?: NavigationGuard;
    beforeResolve?: NavigationGuard;
    afterEach?: (to: RouteLocation, from: RouteLocation) => void;
}