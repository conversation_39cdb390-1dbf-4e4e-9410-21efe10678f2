/**
 * 应用相关类型定义
 */

/**
 * 应用状态枚举
 */
export enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',
  NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED',
  BOOTSTRAPPING = 'BOOTSTRAPPING',
  NOT_MOUNTED = 'NOT_MOUNTED',
  MOUNTING = 'MOUNTING',
  MOUNTED = 'MOUNTED',
  UNMOUNTING = 'UNMOUNTING',
  UNLOADING = 'UNLOADING',
  LOAD_ERROR = 'LOAD_ERROR',
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}

/**
 * 应用配置接口
 */
export interface AppConfig {
  name: string;
  entry: string | (() => Promise<any>);
  container: string | HTMLElement;
  activeWhen?: string | string[] | ((location: Location) => boolean);
  customProps?: Record<string, any>;
  loader?: {
    loading?: HTMLElement | string;
    error?: HTMLElement | string;
  };
  sandbox?: boolean | Record<string, any>;
  singular?: boolean;
  fetch?: (url: string, options?: RequestInit) => Promise<Response>;
}

/**
 * 应用实例接口
 */
export interface AppInstance {
  name: string;
  status: AppStatus;
  config: AppConfig;
  loadPromise?: Promise<any>;
  bootstrapPromise?: Promise<any>;
  mountPromise?: Promise<any>;
  unmountPromise?: Promise<any>;
  unloadPromise?: Promise<any>;
  parcels?: any[];
  customProps?: Record<string, any>;
  container?: HTMLElement;
  error?: Error;
}

/**
 * 应用生命周期函数类型
 */
export type LifecycleFunction<T = any> = (props: T) => Promise<any> | any;
export type BootstrapFunction<T = any> = LifecycleFunction<T>;
export type MountFunction<T = any> = LifecycleFunction<T>;
export type UnmountFunction<T = any> = LifecycleFunction<T>;
export type UpdateFunction<T = any> = LifecycleFunction<T>;

/**
 * 应用生命周期接口
 */
export interface AppLifecycles {
  bootstrap?: BootstrapFunction | BootstrapFunction[];
  mount?: MountFunction | MountFunction[];
  unmount?: UnmountFunction | UnmountFunction[];
  update?: UpdateFunction | UpdateFunction[];
}