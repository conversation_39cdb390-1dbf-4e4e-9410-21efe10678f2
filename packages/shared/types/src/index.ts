/**
 * @fileoverview 共享类型定义 - 微前端架构核心类型
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { AppStatus } from '../../constants/src';

/**
 * 微前端应用配置接口
 */
export interface MicroAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口地址 */
    entry: string | AppEntry;
    /** 容器选择器或元素 */
    container?: string | HTMLElement;
    /** 激活规则 */
    activeRule?: string | string[] | ((location: Location) => boolean);
    /** 应用属性 */
    props?: Record<string, any>;
    /** 加载器配置 */
    loader?: LoaderConfig;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 样式隔离配置 */
    styleIsolation?: StyleIsolationConfig;
    /** 生命周期钩子 */
    hooks?: AppLifecycleHooks;
    /** 应用元数据 */
    meta?: AppMetadata;
}

/**
 * 应用入口配置
 */
export interface AppEntry {
    /** 脚本地址 */
    scripts?: string[];
    /** 样式地址 */
    styles?: string[];
    /** HTML 地址 */
    html?: string;
}

/**
 * 加载器配置
 */
export interface LoaderConfig {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 是否缓存 */
    cache?: boolean;
    /** 跨域配置 */
    crossOrigin?: boolean | string;
    /** 完整性校验 */
    integrity?: string;
    /** 自定义获取器 */
    fetch?: (url: string) => Promise<string>;
}

/**
 * 沙箱配置
 */
export interface SandboxConfig {
    /** 是否启用沙箱 */
    enabled?: boolean;
    /** 沙箱类型 */
    type?: 'proxy' | 'snapshot' | 'iframe';
    /** 严格模式 */
    strict?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
}

/**
 * 样式隔离配置
 */
export interface StyleIsolationConfig {
    /** 是否启用样式隔离 */
    enabled?: boolean;
    /** 隔离类型 */
    type?: 'scoped' | 'shadow' | 'namespace';
    /** 命名空间前缀 */
    prefix?: string;
    /** 是否隔离全局样式 */
    isolateGlobalStyles?: boolean;
}

/**
 * 应用生命周期钩子
 */
export interface AppLifecycleHooks {
    /** 加载前 */
    beforeLoad?: (app: MicroAppInstance) => Promise<void> | void;
    /** 加载后 */
    afterLoad?: (app: MicroAppInstance) => Promise<void> | void;
    /** 启动前 */
    beforeBootstrap?: (app: MicroAppInstance) => Promise<void> | void;
    /** 启动后 */
    afterBootstrap?: (app: MicroAppInstance) => Promise<void> | void;
    /** 挂载前 */
    beforeMount?: (app: MicroAppInstance) => Promise<void> | void;
    /** 挂载后 */
    afterMount?: (app: MicroAppInstance) => Promise<void> | void;
    /** 卸载前 */
    beforeUnmount?: (app: MicroAppInstance) => Promise<void> | void;
    /** 卸载后 */
    afterUnmount?: (app: MicroAppInstance) => Promise<void> | void;
    /** 更新前 */
    beforeUpdate?: (app: MicroAppInstance) => Promise<void> | void;
    /** 更新后 */
    afterUpdate?: (app: MicroAppInstance) => Promise<void> | void;
    /** 错误处理 */
    onError?: (error: Error, app: MicroAppInstance) => void;
}

/**
 * 应用元数据
 */
export interface AppMetadata {
    /** 版本号 */
    version?: string;
    /** 描述 */
    description?: string;
    /** 作者 */
    author?: string;
    /** 主页 */
    homepage?: string;
    /** 仓库地址 */
    repository?: string;
    /** 许可证 */
    license?: string;
    /** 关键词 */
    keywords?: string[];
    /** 依赖项 */
    dependencies?: Record<string, string>;
    /** 构建时间 */
    buildTime?: string;
    /** 自定义元数据 */
    custom?: Record<string, any>;
}

/**
 * 微前端应用实例接口
 */
export interface MicroAppInstance {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: AppStatus;
    /** 应用配置 */
    config: MicroAppConfig;
    /** 应用容器 */
    container?: HTMLElement;
    /** 沙箱实例 */
    sandbox?: Sandbox;
    /** 生命周期函数 */
    lifecycle?: AppLifecycle;
    /** 应用属性 */
    props?: Record<string, any>;
    /** 错误信息 */
    error?: Error;
    /** 创建时间 */
    createdAt: Date;
    /** 最后更新时间 */
    updatedAt: Date;
}

/**
 * 应用生命周期函数接口
 */
export interface AppLifecycle {
    /** 启动函数 */
    bootstrap?: (props?: Record<string, any>) => Promise<void>;
    /** 挂载函数 */
    mount?: (props?: Record<string, any>) => Promise<void>;
    /** 卸载函数 */
    unmount?: (props?: Record<string, any>) => Promise<void>;
    /** 更新函数 */
    update?: (props?: Record<string, any>) => Promise<void>;
}

/**
 * 沙箱接口
 */
export interface Sandbox {
    /** 沙箱名称 */
    name: string;
    /** 是否激活 */
    active: boolean;
    /** 代理对象 */
    proxy?: WindowProxy;
    /** 激活沙箱 */
    activate(): void;
    /** 停用沙箱 */
    deactivate(): void;
    /** 销毁沙箱 */
    destroy(): void;
}

/**
 * 路由信息接口
 */
export interface RouteInfo {
    /** 路径 */
    path: string;
    /** 查询参数 */
    query?: Record<string, string>;
    /** 哈希 */
    hash?: string;
    /** 状态 */
    state?: any;
}

/**
 * 通信消息接口
 */
export interface Message<T = any> {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 发送者 */
    from: string;
    /** 接收者 */
    to: string;
    /** 消息数据 */
    data: T;
    /** 时间戳 */
    timestamp: number;
    /** 是否需要回复 */
    needReply?: boolean;
}

/**
 * 通信适配器接口
 */
export interface CommunicationAdapter {
    /** 发送消息 */
    send<T = any>(message: Message<T>): Promise<void>;
    /** 监听消息 */
    listen<T = any>(handler: (message: Message<T>) => void): () => void;
    /** 销毁适配器 */
    destroy(): void;
}

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件发射器接口
 */
export interface EventEmitter {
    /** 监听事件 */
    on<T = any>(event: string, listener: EventListener<T>): () => void;
    /** 监听事件一次 */
    once<T = any>(event: string, listener: EventListener<T>): () => void;
    /** 取消监听 */
    off(event: string, listener?: EventListener): void;
    /** 发射事件 */
    emit(event: string, data?: any): void;
}

/**
 * 基础配置接口
 */
export interface BaseConfig {
    /** 名称 */
    name: string;
    /** 版本 */
    version?: string;
    /** 描述 */
    description?: string;
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 生命周期接口
 */
export interface Lifecycle {
    /** 初始化 */
    init?(): Promise<void> | void;
    /** 启动 */
    start?(): Promise<void> | void;
    /** 停止 */
    stop?(): Promise<void> | void;
    /** 销毁 */
    destroy?(): Promise<void> | void;
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SILENT = 4
}

/**
 * 日志记录接口
 */
export interface LogRecord {
    /** 日志级别 */
    level: LogLevel;
    /** 日志消息 */
    message: string;
    /** 附加数据 */
    data?: any[];
    /** 时间戳 */
    timestamp: Date;
    /** 来源 */
    source?: string;
}

/**
 * 日志器接口
 */
export interface Logger {
    /** 调试日志 */
    debug(message: string, ...args: any[]): void;
    /** 信息日志 */
    info(message: string, ...args: any[]): void;
    /** 警告日志 */
    warn(message: string, ...args: any[]): void;
    /** 错误日志 */
    error(message: string, ...args: any[]): void;
    /** 设置日志级别 */
    setLevel(level: LogLevel): void;
    /** 创建子日志器 */
    child(namespace: string): Logger;
}

/**
 * 缓存接口
 */
export interface Cache<K = string, V = any> {
    /** 获取缓存 */
    get(key: K): V | undefined;
    /** 设置缓存 */
    set(key: K, value: V, ttl?: number): void;
    /** 删除缓存 */
    delete(key: K): boolean;
    /** 清空缓存 */
    clear(): void;
    /** 检查是否存在 */
    has(key: K): boolean;
    /** 获取大小 */
    size(): number;
    /** 获取所有键 */
    keys(): K[];
    /** 获取所有值 */
    values(): V[];
}

/**
 * 异步任务状态
 */
export enum TaskStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    COMPLETED = 'completed',
    FAILED = 'failed',
    CANCELLED = 'cancelled'
}

/**
 * 异步任务接口
 */
export interface Task<T = any> {
    /** 任务ID */
    id: string;
    /** 任务名称 */
    name: string;
    /** 任务状态 */
    status: TaskStatus;
    /** 任务结果 */
    result?: T;
    /** 错误信息 */
    error?: Error;
    /** 进度 (0-100) */
    progress?: number;
    /** 创建时间 */
    createdAt: Date;
    /** 开始时间 */
    startedAt?: Date;
    /** 完成时间 */
    completedAt?: Date;
    /** 取消任务 */
    cancel?(): void;
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
    /** 内存使用情况 */
    memory?: {
        used: number;
        total: number;
        percentage: number;
    };
    /** 加载时间 */
    loadTime?: number;
    /** 渲染时间 */
    renderTime?: number;
    /** 交互时间 */
    interactiveTime?: number;
    /** 错误数量 */
    errorCount?: number;
    /** 自定义指标 */
    custom?: Record<string, number>;
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
    /** 错误代码 */
    code: string | number;
    /** 错误消息 */
    message: string;
    /** 错误堆栈 */
    stack?: string;
    /** 错误上下文 */
    context?: Record<string, any>;
    /** 时间戳 */
    timestamp: Date;
    /** 来源 */
    source?: string;
}

/**
 * 插件接口
 */
export interface Plugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version?: string;
    /** 插件安装 */
    install(app: any, options?: any): void;
    /** 插件卸载 */
    uninstall?(): void;
}

/**
 * 中间件接口
 */
export interface Middleware<T = any> {
    /** 中间件名称 */
    name: string;
    /** 中间件处理函数 */
    handler: (context: T, next: () => Promise<void>) => Promise<void>;
}

/**
 * 验证规则接口
 */
export interface ValidationRule<T = any> {
    /** 是否必需 */
    required?: boolean;
    /** 数据类型 */
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
    /** 最小长度/值 */
    min?: number;
    /** 最大长度/值 */
    max?: number;
    /** 正则表达式 */
    pattern?: RegExp;
    /** 自定义验证函数 */
    validator?: (value: T) => boolean | string;
    /** 错误消息 */
    message?: string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    /** 是否有效 */
    valid: boolean;
    /** 错误列表 */
    errors: string[];
    /** 清理后的数据 */
    data?: any;
}

// ============================================================================
// 工具类型定义
// ============================================================================

/**
 * 可序列化的对象类型
 */
export type Serializable =
    | string
    | number
    | boolean
    | null
    | undefined
    | Serializable[]
    | { [key: string]: Serializable };

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度必需类型
 */
export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * 可空类型
 */
export type Nullable<T> = T | null;

/**
 * 可选类型
 */
export type Optional<T> = T | undefined;

/**
 * 可空可选类型
 */
export type Maybe<T> = T | null | undefined;

/**
 * 函数类型
 */
export type AnyFunction = (...args: any[]) => any;

/**
 * 异步函数类型
 */
export type AsyncFunction<T = any> = (...args: any[]) => Promise<T>;

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * 抽象构造函数类型
 */
export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T;

/**
 * 键值对类型
 */
export type KeyValuePair<K = string, V = any> = {
    key: K;
    value: V;
};

/**
 * 结果类型（成功或失败）
 */
export type Result<T, E = Error> =
    | { success: true; data: T }
    | { success: false; error: E };

/**
 * 选项类型
 */
export type Option<T> = T | null;

/**
 * 联合转交集类型
 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

/**
 * 获取函数参数类型
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

/**
 * 获取函数返回类型
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any;

/**
 * 获取 Promise 解析类型
 */
export type Awaited<T> = T extends PromiseLike<infer U> ? U : T;

/**
 * 排除类型
 */
export type Exclude<T, U> = T extends U ? never : T;

/**
 * 提取类型
 */
export type Extract<T, U> = T extends U ? T : never;

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * 选择属性类型
 */
export type Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

/**
 * 排除属性类型
 */
export type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>;

/**
 * 记录类型
 */
export type Record<K extends keyof any, T> = {
    [P in K]: T;
};

/**
 * 条件类型
 */
export type If<C extends boolean, T, F> = C extends true ? T : F;

/**
 * 数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * 对象值类型
 */
export type ValueOf<T> = T[keyof T];

/**
 * 字符串字面量类型
 */
export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never;

/**
 * 数字字面量类型
 */
export type NumberLiteral<T> = T extends number ? (number extends T ? never : T) : never;

/**
 * 布尔字面量类型
 */
export type BooleanLiteral<T> = T extends boolean ? (boolean extends T ? never : T) : never;

/**
 * 类型守卫函数类型
 */
export type TypeGuard<T> = (value: any) => value is T;

/**
 * 类型断言函数类型
 */
export type TypeAssertion<T> = (value: any) => asserts value is T;

/**
 * 事件映射类型
 */
export type EventMap = Record<string, any>;

/**
 * 事件键类型
 */
export type EventKey<T extends EventMap> = string & keyof T;

/**
 * 事件处理器类型
 */
export type EventHandler<T extends EventMap, K extends EventKey<T>> = (event: T[K]) => void;

/**
 * 类型集合导出
 */
export const types = {
    // 这里可以添加运行时类型检查函数
} as const;