/**
 * Builder options
 */
export interface BuilderOptions {
    // Common options for all builders
}

/**
 * Vite builder options
 */
export interface ViteBuilderOptions extends BuilderOptions {
    viteVersion?: string;
    mode?: 'development' | 'production';
}

/**
 * Webpack builder options
 */
export interface WebpackBuilderOptions extends BuilderOptions {
    webpackVersion?: string;
    enableModuleFederation?: boolean;
}

/**
 * Builder interface
 */
export interface Builder {
    generateConfig(baseConfig?: any): any;
    build(options?: any): Promise<void>;
}