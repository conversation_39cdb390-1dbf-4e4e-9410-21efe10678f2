/**
 * 事件相关类型定义
 */

/**
 * 事件类型枚举
 */
export enum EventType {
    APP_LOADED = 'APP_LOADED',
    APP_BOOTSTRAPPED = 'APP_BOOTSTRAPPED',
    APP_MOUNTED = 'APP_MOUNTED',
    APP_UNMOUNTED = 'APP_UNMOUNTED',
    APP_UNLOADED = 'APP_UNLOADED',
    APP_ERROR = 'APP_ERROR',
    ROUTE_CHANGED = 'ROUTE_CHANGED',
    PLUGIN_LOADED = 'PLUGIN_LOADED',
    PLUGIN_ERROR = 'PLUGIN_ERROR'
}

/**
 * 基础事件接口
 */
export interface BaseEvent {
    type: EventType | string;
    timestamp: number;
    source: string;
    data?: any;
}

/**
 * 应用事件接口
 */
export interface AppEvent extends BaseEvent {
    appName: string;
    status?: string;
    error?: Error;
}

/**
 * 路由事件接口
 */
export interface RouteEvent extends BaseEvent {
    from: string;
    to: string;
    params?: Record<string, any>;
    query?: Record<string, any>;
}

/**
 * 插件事件接口
 */
export interface PluginEvent extends BaseEvent {
    pluginName: string;
    version?: string;
    error?: Error;
}

/**
 * 事件监听器类型
 */
export type EventListener<T extends BaseEvent = BaseEvent> = (event: T) => void;

/**
 * 事件发射器接口
 */
export interface EventEmitter {
    on<T extends BaseEvent = BaseEvent>(type: string, listener: EventListener<T>): void;
    off<T extends BaseEvent = BaseEvent>(type: string, listener?: EventListener<T>): void;
    emit<T extends BaseEvent = BaseEvent>(event: T): void;
    once<T extends BaseEvent = BaseEvent>(type: string, listener: EventListener<T>): void;
}