{"name": "@micro-core/shared-types", "version": "0.1.0", "description": "Micro-Core shared TypeScript types", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["micro-frontend", "types", "typescript", "shared"], "author": "Echo <<EMAIL>>", "license": "MIT", "devDependencies": {"tsup": "^8.0.0", "typescript": "^5.3.0"}}