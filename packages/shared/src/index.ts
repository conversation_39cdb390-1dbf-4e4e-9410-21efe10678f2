/**
 * @fileoverview Micro-Core Shared Package - Main Entry Point
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// Re-export all constants
export * from '../constants/src';

// Re-export types (excluding conflicting ones)
export type {
    AbstractConstructor, AnyFunction, AppEntry, AppLifecycle, AppLifecycleHooks,
    AppMetadata, AsyncFunction,
    // Base types
    BaseConfig, Cache, CommunicationAdapter, Constructor, DeepPartial, DeepReadonly, DeepRequired, ErrorInfo, EventEmitter, EventHandler, EventKey, EventListener, EventMap, KeyValuePair, Lifecycle, LoaderConfig, LogRecord, Maybe, Message,
    // Core types
    MicroAppConfig,
    MicroAppInstance, Middleware, Nullable, Option, Optional, PerformanceMetrics, Plugin, Result,
    // Communication types
    RouteInfo, Sandbox, SandboxConfig,
    // Utility types
    Serializable, StyleIsolationConfig, Task, TypeAssertion, TypeGuard, ValidationResult, ValidationRule
} from '../types/src';

// Re-export enums (use types version)
export { LogLevel, TaskStatus } from '../types/src';

// Re-export utils (excluding conflicting ones)
export * from '../utils/src';

// Re-export helpers (excluding conflicting exports)
export {
    checkBrowserCompatibility, createMemoryMonitor, createPerformanceTimer, detectEnvironmentFeatures, formatErrorMessage, getMemoryInfo, helpers, normalizeAppConfig, validateAppConfig
} from '../helpers/src';

