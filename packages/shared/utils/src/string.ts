/**
 * @fileoverview 字符串处理工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 驼峰命名转换
 */
export function camelCase(str: string): string {
    return str
        .toLowerCase()
        .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
}

/**
 * 短横线命名转换
 */
export function kebabCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

/**
 * 下划线命名转换
 */
export function snakeCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
}

/**
 * 帕斯卡命名转换
 */
export function pascalCase(str: string): string {
    return camelCase(str).replace(/^[a-z]/, char => char.toUpperCase());
}

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 首字母小写
 */
export function uncapitalize(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1);
}

/**
 * 字符串截断
 */
export function truncate(str: string, length: number, suffix = '...'): string {
    if (str.length <= length) return str;
    return str.slice(0, length - suffix.length) + suffix;
}

/**
 * 字符串填充
 */
export function pad(str: string, length: number, char = ' '): string {
    const padLength = Math.max(0, length - str.length);
    const padLeft = Math.floor(padLength / 2);
    const padRight = padLength - padLeft;
    return char.repeat(padLeft) + str + char.repeat(padRight);
}

/**
 * 左填充
 */
export function padStart(str: string, length: number, char = ' '): string {
    return str.padStart(length, char);
}

/**
 * 右填充
 */
export function padEnd(str: string, length: number, char = ' '): string {
    return str.padEnd(length, char);
}

/**
 * 模板字符串替换
 */
export function template(str: string, data: Record<string, any>): string {
    return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] !== undefined ? String(data[key]) : match;
    });
}

/**
 * 字符串反转
 */
export function reverse(str: string): string {
    return str.split('').reverse().join('');
}

/**
 * 重复字符串
 */
export function repeat(str: string, count: number): string {
    return str.repeat(Math.max(0, count));
}

/**
 * 移除字符串两端的空白字符
 */
export function trim(str: string, chars?: string): string {
    if (!chars) return str.trim();

    const pattern = new RegExp(`^[${escapeRegExp(chars)}]+|[${escapeRegExp(chars)}]+$`, 'g');
    return str.replace(pattern, '');
}

/**
 * 移除字符串左端的空白字符
 */
export function trimStart(str: string, chars?: string): string {
    if (!chars) return str.trimStart();

    const pattern = new RegExp(`^[${escapeRegExp(chars)}]+`, 'g');
    return str.replace(pattern, '');
}

/**
 * 移除字符串右端的空白字符
 */
export function trimEnd(str: string, chars?: string): string {
    if (!chars) return str.trimEnd();

    const pattern = new RegExp(`[${escapeRegExp(chars)}]+$`, 'g');
    return str.replace(pattern, '');
}

/**
 * 转义正则表达式特殊字符
 */
export function escapeRegExp(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 转义HTML特殊字符
 */
export function escapeHtml(str: string): string {
    const htmlEscapes: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
    };

    return str.replace(/[&<>"']/g, char => htmlEscapes[char]);
}

/**
 * 反转义HTML特殊字符
 */
export function unescapeHtml(str: string): string {
    const htmlUnescapes: Record<string, string> = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'"
    };

    return str.replace(/&(?:amp|lt|gt|quot|#39);/g, entity => htmlUnescapes[entity]);
}

/**
 * 字符串分割（支持多个分隔符）
 */
export function split(str: string, separators: string | string[], limit?: number): string[] {
    if (typeof separators === 'string') {
        return str.split(separators, limit);
    }

    const pattern = new RegExp(`[${separators.map(escapeRegExp).join('')}]`);
    return str.split(pattern, limit);
}

/**
 * 单词边界分割
 */
export function words(str: string): string[] {
    return str.match(/\b\w+\b/g) || [];
}

/**
 * 字符串包含检查（忽略大小写）
 */
export function includesIgnoreCase(str: string, searchString: string): boolean {
    return str.toLowerCase().includes(searchString.toLowerCase());
}

/**
 * 字符串开始检查（忽略大小写）
 */
export function startsWithIgnoreCase(str: string, searchString: string): boolean {
    return str.toLowerCase().startsWith(searchString.toLowerCase());
}

/**
 * 字符串结束检查（忽略大小写）
 */
export function endsWithIgnoreCase(str: string, searchString: string): boolean {
    return str.toLowerCase().endsWith(searchString.toLowerCase());
}

/**
 * 计算字符串的字节长度
 */
export function byteLength(str: string): number {
    return new Blob([str]).size;
}

/**
 * 字符串相似度计算（Levenshtein距离）
 */
export function similarity(str1: string, str2: string): number {
    const matrix: number[][] = [];
    const len1 = str1.length;
    const len2 = str2.length;

    // 如果两个字符串都为空，相似度为1
    if (len1 === 0 && len2 === 0) return 1;
    // 如果其中一个为空，相似度为0
    if (len1 === 0 || len2 === 0) return 0;

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
        matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
        matrix[0][j] = j;
    }

    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,      // 删除
                matrix[i][j - 1] + 1,      // 插入
                matrix[i - 1][j - 1] + cost // 替换
            );
        }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
}

/**
 * 生成随机字符串
 */
export function random(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 生成UUID
 */
export function uuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 格式化字节数
 */
export function formatBytes(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化日期
 */
export function formatDate(date: Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', String(year))
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 字符串工具集合
 */
export const stringUtils = {
    camelCase,
    kebabCase,
    snakeCase,
    pascalCase,
    capitalize,
    uncapitalize,
    truncate,
    pad,
    padStart,
    padEnd,
    template,
    reverse,
    repeat,
    trim,
    trimStart,
    trimEnd,
    escapeRegExp,
    escapeHtml,
    unescapeHtml,
    split,
    words,
    includesIgnoreCase,
    startsWithIgnoreCase,
    endsWithIgnoreCase,
    byteLength,
    similarity,
    random,
    uuid,
    formatBytes,
    formatDate
};
