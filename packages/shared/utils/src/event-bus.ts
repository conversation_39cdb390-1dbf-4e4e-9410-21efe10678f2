/**
 * @fileoverview 事件总线工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件监听器包装器
 */
interface EventListenerWrapper<T = any> {
    listener: EventListener<T>;
    once: boolean;
    priority: number;
}

/**
 * 事件总线类
 */
export class EventBus {
    private events = new Map<string, EventListenerWrapper[]>();
    private maxListeners = 100;

    /**
     * 设置最大监听器数量
     */
    setMaxListeners(max: number): void {
        this.maxListeners = max;
    }

    /**
     * 获取最大监听器数量
     */
    getMaxListeners(): number {
        return this.maxListeners;
    }

    /**
     * 添加事件监听器
     */
    on<T = any>(
        event: string,
        listener: EventListener<T>,
        options: { once?: boolean; priority?: number } = {}
    ): () => void {
        const { once = false, priority = 0 } = options;

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listeners = this.events.get(event)!;

        // 检查监听器数量限制
        if (listeners.length >= this.maxListeners) {
            console.warn(`EventBus: Maximum listeners (${this.maxListeners}) exceeded for event "${event}"`);
        }

        const wrapper: EventListenerWrapper<T> = {
            listener,
            once,
            priority
        };

        // 按优先级插入
        const insertIndex = listeners.findIndex(l => l.priority < priority);
        if (insertIndex === -1) {
            listeners.push(wrapper);
        } else {
            listeners.splice(insertIndex, 0, wrapper);
        }

        // 返回取消监听的函数
        return () => this.off(event, listener);
    }

    /**
     * 添加一次性事件监听器
     */
    once<T = any>(event: string, listener: EventListener<T>, priority = 0): () => void {
        return this.on(event, listener, { once: true, priority });
    }

    /**
     * 移除事件监听器
     */
    off<T = any>(event: string, listener?: EventListener<T>): void {
        if (!this.events.has(event)) return;

        const listeners = this.events.get(event)!;

        if (!listener) {
            // 移除所有监听器
            this.events.delete(event);
            return;
        }

        // 移除指定监听器
        const index = listeners.findIndex(wrapper => wrapper.listener === listener);
        if (index !== -1) {
            listeners.splice(index, 1);
            
            // 如果没有监听器了，删除事件
            if (listeners.length === 0) {
                this.events.delete(event);
            }
        }
    }

    /**
     * 发射事件
     */
    async emit<T = any>(event: string, data?: T): Promise<void> {
        if (!this.events.has(event)) return;

        const listeners = this.events.get(event)!;
        const toRemove: EventListenerWrapper[] = [];

        // 执行所有监听器
        const promises = listeners.map(async wrapper => {
            try {
                await wrapper.listener(data);
                
                // 标记一次性监听器待移除
                if (wrapper.once) {
                    toRemove.push(wrapper);
                }
            } catch (error) {
                console.error(`EventBus: Error in listener for event "${event}":`, error);
            }
        });

        await Promise.all(promises);

        // 移除一次性监听器
        toRemove.forEach(wrapper => {
            const index = listeners.indexOf(wrapper);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        });

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.events.delete(event);
        }
    }

    /**
     * 同步发射事件
     */
    emitSync<T = any>(event: string, data?: T): void {
        if (!this.events.has(event)) return;

        const listeners = this.events.get(event)!;
        const toRemove: EventListenerWrapper[] = [];

        // 执行所有监听器
        listeners.forEach(wrapper => {
            try {
                const result = wrapper.listener(data);
                
                // 如果返回Promise，警告用户应该使用emit
                if (result && typeof result.then === 'function') {
                    console.warn(`EventBus: Async listener detected for event "${event}". Consider using emit() instead of emitSync().`);
                }
                
                // 标记一次性监听器待移除
                if (wrapper.once) {
                    toRemove.push(wrapper);
                }
            } catch (error) {
                console.error(`EventBus: Error in listener for event "${event}":`, error);
            }
        });

        // 移除一次性监听器
        toRemove.forEach(wrapper => {
            const index = listeners.indexOf(wrapper);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        });

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.events.delete(event);
        }
    }

    /**
     * 获取事件的监听器数量
     */
    listenerCount(event: string): number {
        return this.events.get(event)?.length || 0;
    }

    /**
     * 获取所有事件名称
     */
    eventNames(): string[] {
        return Array.from(this.events.keys());
    }

    /**
     * 获取事件的所有监听器
     */
    listeners<T = any>(event: string): EventListener<T>[] {
        const wrappers = this.events.get(event) || [];
        return wrappers.map(wrapper => wrapper.listener);
    }

    /**
     * 移除所有监听器
     */
    removeAllListeners(event?: string): void {
        if (event) {
            this.events.delete(event);
        } else {
            this.events.clear();
        }
    }

    /**
     * 在指定监听器之前添加监听器
     */
    prependListener<T = any>(event: string, listener: EventListener<T>): () => void {
        return this.on(event, listener, { priority: Number.MAX_SAFE_INTEGER });
    }

    /**
     * 在指定监听器之前添加一次性监听器
     */
    prependOnceListener<T = any>(event: string, listener: EventListener<T>): () => void {
        return this.once(event, listener, Number.MAX_SAFE_INTEGER);
    }

    /**
     * 等待事件发生
     */
    waitFor<T = any>(event: string, timeout?: number): Promise<T> {
        return new Promise((resolve, reject) => {
            let timeoutId: NodeJS.Timeout | undefined;

            const cleanup = this.once(event, (data: T) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                resolve(data);
            });

            if (timeout) {
                timeoutId = setTimeout(() => {
                    cleanup();
                    reject(new Error(`EventBus: Timeout waiting for event "${event}"`));
                }, timeout);
            }
        });
    }

    /**
     * 创建事件过滤器
     */
    filter<T = any>(
        event: string,
        predicate: (data: T) => boolean
    ): EventBus {
        const filteredBus = new EventBus();
        
        this.on(event, (data: T) => {
            if (predicate(data)) {
                filteredBus.emit(event, data);
            }
        });

        return filteredBus;
    }

    /**
     * 创建事件映射器
     */
    map<T = any, R = any>(
        event: string,
        mapper: (data: T) => R
    ): EventBus {
        const mappedBus = new EventBus();
        
        this.on(event, (data: T) => {
            const mappedData = mapper(data);
            mappedBus.emit(event, mappedData);
        });

        return mappedBus;
    }

    /**
     * 创建事件节流器
     */
    throttle<T = any>(event: string, delay: number): EventBus {
        const throttledBus = new EventBus();
        let lastEmit = 0;
        
        this.on(event, (data: T) => {
            const now = Date.now();
            if (now - lastEmit >= delay) {
                lastEmit = now;
                throttledBus.emit(event, data);
            }
        });

        return throttledBus;
    }

    /**
     * 创建事件防抖器
     */
    debounce<T = any>(event: string, delay: number): EventBus {
        const debouncedBus = new EventBus();
        let timeoutId: NodeJS.Timeout | undefined;
        
        this.on(event, (data: T) => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            
            timeoutId = setTimeout(() => {
                debouncedBus.emit(event, data);
            }, delay);
        });

        return debouncedBus;
    }

    /**
     * 获取调试信息
     */
    debug(): {
        eventCount: number;
        listenerCount: number;
        events: Record<string, number>;
    } {
        const events: Record<string, number> = {};
        let totalListeners = 0;

        this.events.forEach((listeners, event) => {
            events[event] = listeners.length;
            totalListeners += listeners.length;
        });

        return {
            eventCount: this.events.size,
            listenerCount: totalListeners,
            events
        };
    }
}

/**
 * 全局事件总线实例
 */
export const globalEventBus = new EventBus();

/**
 * 创建新的事件总线实例
 */
export function createEventBus(): EventBus {
    return new EventBus();
}

/**
 * 事件总线工具集合
 */
export const eventBusUtils = {
    EventBus,
    globalEventBus,
    createEventBus
};
