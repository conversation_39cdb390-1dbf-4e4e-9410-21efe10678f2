/**
 * @fileoverview 日期工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 格式化日期
 */
export function format(date: Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return format
        .replace('YYYY', String(year))
        .replace('YY', String(year).slice(-2))
        .replace('MM', month)
        .replace('M', String(date.getMonth() + 1))
        .replace('DD', day)
        .replace('D', String(date.getDate()))
        .replace('HH', hours)
        .replace('H', String(date.getHours()))
        .replace('mm', minutes)
        .replace('m', String(date.getMinutes()))
        .replace('ss', seconds)
        .replace('s', String(date.getSeconds()))
        .replace('SSS', milliseconds);
}

/**
 * 解析日期字符串
 */
export function parse(dateString: string): Date {
    return new Date(dateString);
}

/**
 * 检查是否为有效日期
 */
export function isValid(date: Date): boolean {
    return date instanceof Date && !isNaN(date.getTime());
}

/**
 * 获取相对时间
 */
export function relative(date: Date, baseDate = new Date()): string {
    const diff = baseDate.getTime() - date.getTime();
    const seconds = Math.floor(Math.abs(diff) / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    const isFuture = diff < 0;
    const suffix = isFuture ? '后' : '前';

    if (years > 0) return `${years}年${suffix}`;
    if (months > 0) return `${months}个月${suffix}`;
    if (days > 0) return `${days}天${suffix}`;
    if (hours > 0) return `${hours}小时${suffix}`;
    if (minutes > 0) return `${minutes}分钟${suffix}`;
    return `${seconds}秒${suffix}`;
}

/**
 * 添加时间
 */
export function add(date: Date, amount: number, unit: 'years' | 'months' | 'days' | 'hours' | 'minutes' | 'seconds' | 'milliseconds'): Date {
    const result = new Date(date);
    
    switch (unit) {
        case 'years':
            result.setFullYear(result.getFullYear() + amount);
            break;
        case 'months':
            result.setMonth(result.getMonth() + amount);
            break;
        case 'days':
            result.setDate(result.getDate() + amount);
            break;
        case 'hours':
            result.setHours(result.getHours() + amount);
            break;
        case 'minutes':
            result.setMinutes(result.getMinutes() + amount);
            break;
        case 'seconds':
            result.setSeconds(result.getSeconds() + amount);
            break;
        case 'milliseconds':
            result.setMilliseconds(result.getMilliseconds() + amount);
            break;
    }
    
    return result;
}

/**
 * 减去时间
 */
export function subtract(date: Date, amount: number, unit: 'years' | 'months' | 'days' | 'hours' | 'minutes' | 'seconds' | 'milliseconds'): Date {
    return add(date, -amount, unit);
}

/**
 * 获取时间差
 */
export function diff(date1: Date, date2: Date, unit: 'years' | 'months' | 'days' | 'hours' | 'minutes' | 'seconds' | 'milliseconds' = 'milliseconds'): number {
    const diffMs = date1.getTime() - date2.getTime();
    
    switch (unit) {
        case 'years':
            return diffMs / (1000 * 60 * 60 * 24 * 365);
        case 'months':
            return diffMs / (1000 * 60 * 60 * 24 * 30);
        case 'days':
            return diffMs / (1000 * 60 * 60 * 24);
        case 'hours':
            return diffMs / (1000 * 60 * 60);
        case 'minutes':
            return diffMs / (1000 * 60);
        case 'seconds':
            return diffMs / 1000;
        case 'milliseconds':
        default:
            return diffMs;
    }
}

/**
 * 检查是否为今天
 */
export function isToday(date: Date): boolean {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

/**
 * 检查是否为昨天
 */
export function isYesterday(date: Date): boolean {
    const yesterday = subtract(new Date(), 1, 'days');
    return date.toDateString() === yesterday.toDateString();
}

/**
 * 检查是否为明天
 */
export function isTomorrow(date: Date): boolean {
    const tomorrow = add(new Date(), 1, 'days');
    return date.toDateString() === tomorrow.toDateString();
}

/**
 * 检查是否为本周
 */
export function isThisWeek(date: Date): boolean {
    const now = new Date();
    const currentDay = now.getDay();
    const startOfWeek = subtract(now, currentDay, 'days');
    const endOfWeek = add(startOfWeek, 6, 'days');
    
    return date >= startOfWeek && date <= endOfWeek;
}

/**
 * 检查是否为本月
 */
export function isThisMonth(date: Date): boolean {
    const now = new Date();
    return date.getFullYear() === now.getFullYear() && date.getMonth() === now.getMonth();
}

/**
 * 检查是否为本年
 */
export function isThisYear(date: Date): boolean {
    const now = new Date();
    return date.getFullYear() === now.getFullYear();
}

/**
 * 获取一天的开始时间
 */
export function startOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
}

/**
 * 获取一天的结束时间
 */
export function endOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
}

/**
 * 获取一周的开始时间
 */
export function startOfWeek(date: Date, weekStartsOn = 0): Date {
    const day = date.getDay();
    const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
    return startOfDay(subtract(date, diff, 'days'));
}

/**
 * 获取一周的结束时间
 */
export function endOfWeek(date: Date, weekStartsOn = 0): Date {
    return endOfDay(add(startOfWeek(date, weekStartsOn), 6, 'days'));
}

/**
 * 获取一月的开始时间
 */
export function startOfMonth(date: Date): Date {
    const result = new Date(date);
    result.setDate(1);
    return startOfDay(result);
}

/**
 * 获取一月的结束时间
 */
export function endOfMonth(date: Date): Date {
    const result = new Date(date);
    result.setMonth(result.getMonth() + 1, 0);
    return endOfDay(result);
}

/**
 * 获取一年的开始时间
 */
export function startOfYear(date: Date): Date {
    const result = new Date(date);
    result.setMonth(0, 1);
    return startOfDay(result);
}

/**
 * 获取一年的结束时间
 */
export function endOfYear(date: Date): Date {
    const result = new Date(date);
    result.setMonth(11, 31);
    return endOfDay(result);
}

/**
 * 获取月份的天数
 */
export function getDaysInMonth(date: Date): number {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
}

/**
 * 获取年份的天数
 */
export function getDaysInYear(date: Date): number {
    const year = date.getFullYear();
    return isLeapYear(year) ? 366 : 365;
}

/**
 * 检查是否为闰年
 */
export function isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * 获取星期几的名称
 */
export function getWeekdayName(date: Date, locale = 'zh-CN'): string {
    return date.toLocaleDateString(locale, { weekday: 'long' });
}

/**
 * 获取月份的名称
 */
export function getMonthName(date: Date, locale = 'zh-CN'): string {
    return date.toLocaleDateString(locale, { month: 'long' });
}

/**
 * 获取时区偏移
 */
export function getTimezoneOffset(date: Date): number {
    return date.getTimezoneOffset();
}

/**
 * 转换为UTC时间
 */
export function toUTC(date: Date): Date {
    return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
}

/**
 * 从UTC时间转换
 */
export function fromUTC(date: Date): Date {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000);
}

/**
 * 比较两个日期
 */
export function compare(date1: Date, date2: Date): -1 | 0 | 1 {
    const time1 = date1.getTime();
    const time2 = date2.getTime();
    
    if (time1 < time2) return -1;
    if (time1 > time2) return 1;
    return 0;
}

/**
 * 检查日期是否在范围内
 */
export function isBetween(date: Date, start: Date, end: Date, inclusive = true): boolean {
    const time = date.getTime();
    const startTime = start.getTime();
    const endTime = end.getTime();
    
    if (inclusive) {
        return time >= startTime && time <= endTime;
    } else {
        return time > startTime && time < endTime;
    }
}

/**
 * 获取最大日期
 */
export function max(...dates: Date[]): Date {
    return new Date(Math.max(...dates.map(d => d.getTime())));
}

/**
 * 获取最小日期
 */
export function min(...dates: Date[]): Date {
    return new Date(Math.min(...dates.map(d => d.getTime())));
}

/**
 * 日期工具集合
 */
export const dateUtils = {
    format,
    parse,
    isValid,
    relative,
    add,
    subtract,
    diff,
    isToday,
    isYesterday,
    isTomorrow,
    isThisWeek,
    isThisMonth,
    isThisYear,
    startOfDay,
    endOfDay,
    startOfWeek,
    endOfWeek,
    startOfMonth,
    endOfMonth,
    startOfYear,
    endOfYear,
    getDaysInMonth,
    getDaysInYear,
    isLeapYear,
    getWeekdayName,
    getMonthName,
    getTimezoneOffset,
    toUTC,
    fromUTC,
    compare,
    isBetween,
    max,
    min
};
