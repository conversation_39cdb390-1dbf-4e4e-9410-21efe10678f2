/**
 * @fileoverview DOM操作工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 检查是否为浏览器环境
 */
export function isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * 动态加载脚本
 */
export function loadScript(src: string, options: {
    async?: boolean;
    defer?: boolean;
    crossOrigin?: string;
    integrity?: string;
    onLoad?: () => void;
    onError?: (error: Event) => void;
} = {}): Promise<void> {
    return new Promise((resolve, reject) => {
        if (!isBrowser()) {
            reject(new Error('loadScript can only be used in browser environment'));
            return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.async = options.async ?? true;
        script.defer = options.defer ?? false;
        
        if (options.crossOrigin) {
            script.crossOrigin = options.crossOrigin;
        }
        
        if (options.integrity) {
            script.integrity = options.integrity;
        }

        script.onload = () => {
            options.onLoad?.();
            resolve();
        };

        script.onerror = (error) => {
            options.onError?.(error);
            reject(new Error(`Failed to load script: ${src}`));
        };

        document.head.appendChild(script);
    });
}

/**
 * 动态加载样式
 */
export function loadStyle(href: string, options: {
    media?: string;
    crossOrigin?: string;
    integrity?: string;
    onLoad?: () => void;
    onError?: (error: Event) => void;
} = {}): Promise<void> {
    return new Promise((resolve, reject) => {
        if (!isBrowser()) {
            reject(new Error('loadStyle can only be used in browser environment'));
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.type = 'text/css';
        
        if (options.media) {
            link.media = options.media;
        }
        
        if (options.crossOrigin) {
            link.crossOrigin = options.crossOrigin;
        }
        
        if (options.integrity) {
            link.integrity = options.integrity;
        }

        link.onload = () => {
            options.onLoad?.();
            resolve();
        };

        link.onerror = (error) => {
            options.onError?.(error);
            reject(new Error(`Failed to load style: ${href}`));
        };

        document.head.appendChild(link);
    });
}

/**
 * 获取元素
 */
export function $(selector: string, context: Document | Element = document): Element | null {
    if (!isBrowser()) return null;
    return context.querySelector(selector);
}

/**
 * 获取所有匹配的元素
 */
export function $$(selector: string, context: Document | Element = document): Element[] {
    if (!isBrowser()) return [];
    return Array.from(context.querySelectorAll(selector));
}

/**
 * 创建元素
 */
export function createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    attributes?: Record<string, string | number | boolean>,
    children?: (string | Element)[]
): HTMLElementTagNameMap[K] | null {
    if (!isBrowser()) return null;

    const element = document.createElement(tagName);

    if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
            if (typeof value === 'boolean') {
                if (value) {
                    element.setAttribute(key, '');
                }
            } else {
                element.setAttribute(key, String(value));
            }
        });
    }

    if (children) {
        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });
    }

    return element;
}

/**
 * 添加类名
 */
export function addClass(element: Element, ...classNames: string[]): void {
    element.classList.add(...classNames);
}

/**
 * 移除类名
 */
export function removeClass(element: Element, ...classNames: string[]): void {
    element.classList.remove(...classNames);
}

/**
 * 切换类名
 */
export function toggleClass(element: Element, className: string, force?: boolean): boolean {
    return element.classList.toggle(className, force);
}

/**
 * 检查是否有类名
 */
export function hasClass(element: Element, className: string): boolean {
    return element.classList.contains(className);
}

/**
 * 设置样式
 */
export function setStyle(element: HTMLElement, styles: Record<string, string | number>): void {
    Object.entries(styles).forEach(([property, value]) => {
        element.style.setProperty(property, String(value));
    });
}

/**
 * 获取样式
 */
export function getStyle(element: Element, property: string): string {
    if (!isBrowser()) return '';
    return window.getComputedStyle(element).getPropertyValue(property);
}

/**
 * 设置属性
 */
export function setAttributes(element: Element, attributes: Record<string, string | number | boolean>): void {
    Object.entries(attributes).forEach(([key, value]) => {
        if (typeof value === 'boolean') {
            if (value) {
                element.setAttribute(key, '');
            } else {
                element.removeAttribute(key);
            }
        } else {
            element.setAttribute(key, String(value));
        }
    });
}

/**
 * 获取属性
 */
export function getAttribute(element: Element, name: string): string | null {
    return element.getAttribute(name);
}

/**
 * 移除属性
 */
export function removeAttribute(element: Element, name: string): void {
    element.removeAttribute(name);
}

/**
 * 添加事件监听器
 */
export function on<K extends keyof HTMLElementEventMap>(
    element: Element,
    type: K,
    listener: (event: HTMLElementEventMap[K]) => void,
    options?: boolean | AddEventListenerOptions
): void {
    element.addEventListener(type, listener as EventListener, options);
}

/**
 * 移除事件监听器
 */
export function off<K extends keyof HTMLElementEventMap>(
    element: Element,
    type: K,
    listener: (event: HTMLElementEventMap[K]) => void,
    options?: boolean | EventListenerOptions
): void {
    element.removeEventListener(type, listener as EventListener, options);
}

/**
 * 触发事件
 */
export function trigger(element: Element, type: string, detail?: any): void {
    const event = new CustomEvent(type, { detail });
    element.dispatchEvent(event);
}

/**
 * 获取元素位置
 */
export function getOffset(element: Element): { top: number; left: number; width: number; height: number } {
    const rect = element.getBoundingClientRect();
    return {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
        height: rect.height
    };
}

/**
 * 获取元素相对于视口的位置
 */
export function getBoundingRect(element: Element): DOMRect {
    return element.getBoundingClientRect();
}

/**
 * 检查元素是否在视口中
 */
export function isInViewport(element: Element, threshold = 0): boolean {
    if (!isBrowser()) return false;
    
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;
    
    return (
        rect.top >= -threshold &&
        rect.left >= -threshold &&
        rect.bottom <= windowHeight + threshold &&
        rect.right <= windowWidth + threshold
    );
}

/**
 * 滚动到元素
 */
export function scrollToElement(element: Element, options: ScrollIntoViewOptions = {}): void {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
        ...options
    });
}

/**
 * 获取滚动位置
 */
export function getScrollPosition(): { x: number; y: number } {
    if (!isBrowser()) return { x: 0, y: 0 };
    
    return {
        x: window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0,
        y: window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
    };
}

/**
 * 设置滚动位置
 */
export function setScrollPosition(x: number, y: number, behavior: ScrollBehavior = 'auto'): void {
    if (!isBrowser()) return;
    
    window.scrollTo({
        left: x,
        top: y,
        behavior
    });
}

/**
 * 获取元素的文本内容
 */
export function getText(element: Element): string {
    return element.textContent || '';
}

/**
 * 设置元素的文本内容
 */
export function setText(element: Element, text: string): void {
    element.textContent = text;
}

/**
 * 获取元素的HTML内容
 */
export function getHtml(element: Element): string {
    return element.innerHTML;
}

/**
 * 设置元素的HTML内容
 */
export function setHtml(element: Element, html: string): void {
    element.innerHTML = html;
}

/**
 * 清空元素内容
 */
export function empty(element: Element): void {
    element.innerHTML = '';
}

/**
 * 移除元素
 */
export function remove(element: Element): void {
    element.parentNode?.removeChild(element);
}

/**
 * 克隆元素
 */
export function clone(element: Element, deep = true): Element {
    return element.cloneNode(deep) as Element;
}

/**
 * DOM工具集合
 */
export const domUtils = {
    isBrowser,
    loadScript,
    loadStyle,
    $,
    $$,
    createElement,
    addClass,
    removeClass,
    toggleClass,
    hasClass,
    setStyle,
    getStyle,
    setAttributes,
    getAttribute,
    removeAttribute,
    on,
    off,
    trigger,
    getOffset,
    getBoundingRect,
    isInViewport,
    scrollToElement,
    getScrollPosition,
    setScrollPosition,
    getText,
    setText,
    getHtml,
    setHtml,
    empty,
    remove,
    clone
};
