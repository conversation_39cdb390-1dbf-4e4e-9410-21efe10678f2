/**
 * @fileoverview 对象操作工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { isObject, isArray, isDate, isRegExp } from './type-check';

/**
 * 深度合并对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();

    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} });
                deepMerge(target[key], source[key] as any);
            } else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }

    return deepMerge(target, ...sources);
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T, visited = new WeakMap()): T {
    // 处理原始类型和null
    if (obj === null || typeof obj !== 'object') return obj;
    
    // 处理循环引用
    if (visited.has(obj as any)) {
        return visited.get(obj as any);
    }
    
    // 处理日期对象
    if (isDate(obj)) {
        return new Date(obj.getTime()) as any;
    }
    
    // 处理正则表达式
    if (isRegExp(obj)) {
        return new RegExp(obj.source, obj.flags) as any;
    }
    
    // 处理数组
    if (isArray(obj)) {
        const cloned: any[] = [];
        visited.set(obj as any, cloned);
        for (let i = 0; i < obj.length; i++) {
            cloned[i] = deepClone(obj[i], visited);
        }
        return cloned as any;
    }
    
    // 处理Map
    if (obj instanceof Map) {
        const cloned = new Map();
        visited.set(obj as any, cloned);
        obj.forEach((value, key) => {
            cloned.set(deepClone(key, visited), deepClone(value, visited));
        });
        return cloned as any;
    }
    
    // 处理Set
    if (obj instanceof Set) {
        const cloned = new Set();
        visited.set(obj as any, cloned);
        obj.forEach(value => {
            cloned.add(deepClone(value, visited));
        });
        return cloned as any;
    }
    
    // 处理普通对象
    if (isObject(obj)) {
        const cloned = {} as any;
        visited.set(obj as any, cloned);
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key], visited);
            }
        }
        return cloned;
    }
    
    return obj;
}

/**
 * 获取对象属性值（支持路径）
 */
export function get(obj: any, path: string | string[], defaultValue?: any): any {
    const keys = Array.isArray(path) ? path : path.split('.');
    let result = obj;

    for (const key of keys) {
        if (result === null || result === undefined) {
            return defaultValue;
        }
        result = result[key];
    }

    return result !== undefined ? result : defaultValue;
}

/**
 * 设置对象属性值（支持路径）
 */
export function set(obj: any, path: string | string[], value: any): void {
    const keys = Array.isArray(path) ? path : path.split('.');
    const lastKey = keys.pop()!;
    let current = obj;

    for (const key of keys) {
        if (!current[key] || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }

    current[lastKey] = value;
}

/**
 * 删除对象属性（支持路径）
 */
export function unset(obj: any, path: string | string[]): boolean {
    const keys = Array.isArray(path) ? path : path.split('.');
    const lastKey = keys.pop()!;
    let current = obj;

    for (const key of keys) {
        if (!current[key]) {
            return false;
        }
        current = current[key];
    }

    if (current.hasOwnProperty(lastKey)) {
        delete current[lastKey];
        return true;
    }

    return false;
}

/**
 * 检查对象是否有属性（支持路径）
 */
export function has(obj: any, path: string | string[]): boolean {
    const keys = Array.isArray(path) ? path : path.split('.');
    let current = obj;

    for (const key of keys) {
        if (!current || !current.hasOwnProperty(key)) {
            return false;
        }
        current = current[key];
    }

    return true;
}

/**
 * 选择对象的指定属性
 */
export function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
    const result = {} as Pick<T, K>;
    for (const key of keys) {
        if (key in obj) {
            result[key] = obj[key];
        }
    }
    return result;
}

/**
 * 排除对象的指定属性
 */
export function omit<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
    const result = { ...obj } as any;
    for (const key of keys) {
        delete result[key];
    }
    return result;
}

/**
 * 获取对象的所有键
 */
export function keys<T extends Record<string, any>>(obj: T): Array<keyof T> {
    return Object.keys(obj);
}

/**
 * 获取对象的所有值
 */
export function values<T extends Record<string, any>>(obj: T): Array<T[keyof T]> {
    return Object.values(obj);
}

/**
 * 获取对象的所有键值对
 */
export function entries<T extends Record<string, any>>(obj: T): Array<[keyof T, T[keyof T]]> {
    return Object.entries(obj) as Array<[keyof T, T[keyof T]]>;
}

/**
 * 从键值对数组创建对象
 */
export function fromEntries<K extends string | number | symbol, V>(entries: Array<[K, V]>): Record<K, V> {
    return Object.fromEntries(entries) as Record<K, V>;
}

/**
 * 反转对象的键值
 */
export function invert<T extends Record<string, string | number>>(obj: T): Record<string, keyof T> {
    const result: Record<string, keyof T> = {};
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            result[String(obj[key])] = key;
        }
    }
    return result;
}

/**
 * 映射对象的值
 */
export function mapValues<T extends Record<string, any>, R>(
    obj: T,
    mapper: (value: T[keyof T], key: keyof T) => R
): Record<keyof T, R> {
    const result = {} as Record<keyof T, R>;
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            result[key] = mapper(obj[key], key);
        }
    }
    return result;
}

/**
 * 映射对象的键
 */
export function mapKeys<T extends Record<string, any>, K extends string>(
    obj: T,
    mapper: (key: keyof T, value: T[keyof T]) => K
): Record<K, T[keyof T]> {
    const result = {} as Record<K, T[keyof T]>;
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const newKey = mapper(key, obj[key]);
            result[newKey] = obj[key];
        }
    }
    return result;
}

/**
 * 过滤对象的属性
 */
export function filter<T extends Record<string, any>>(
    obj: T,
    predicate: (value: T[keyof T], key: keyof T) => boolean
): Partial<T> {
    const result = {} as Partial<T>;
    for (const key in obj) {
        if (obj.hasOwnProperty(key) && predicate(obj[key], key)) {
            result[key] = obj[key];
        }
    }
    return result;
}

/**
 * 扁平化对象
 */
export function flatten(obj: Record<string, any>, prefix = '', separator = '.'): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const newKey = prefix ? `${prefix}${separator}${key}` : key;
            const value = obj[key];
            
            if (isObject(value) && !isArray(value) && !isDate(value) && !isRegExp(value)) {
                Object.assign(result, flatten(value, newKey, separator));
            } else {
                result[newKey] = value;
            }
        }
    }
    
    return result;
}

/**
 * 展开扁平化的对象
 */
export function unflatten(obj: Record<string, any>, separator = '.'): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            set(result, key.split(separator), obj[key]);
        }
    }
    
    return result;
}

/**
 * 对象工具集合
 */
export const objectUtils = {
    deepMerge,
    deepClone,
    get,
    set,
    unset,
    has,
    pick,
    omit,
    keys,
    values,
    entries,
    fromEntries,
    invert,
    mapValues,
    mapKeys,
    filter,
    flatten,
    unflatten
};
