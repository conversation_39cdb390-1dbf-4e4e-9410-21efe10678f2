/**
 * @fileoverview 日志工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { envUtils } from './env';

/**
 * 日志级别
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SILENT = 4
}

/**
 * 日志级别名称映射
 */
const LOG_LEVEL_NAMES: Record<LogLevel, string> = {
    [LogLevel.DEBUG]: 'DEBUG',
    [LogLevel.INFO]: 'INFO',
    [LogLevel.WARN]: 'WARN',
    [LogLevel.ERROR]: 'ERROR',
    [LogLevel.SILENT]: 'SILENT'
};

/**
 * 日志级别颜色映射
 */
const LOG_LEVEL_COLORS: Record<LogLevel, string> = {
    [LogLevel.DEBUG]: '#6B7280',
    [LogLevel.INFO]: '#3B82F6',
    [LogLevel.WARN]: '#F59E0B',
    [LogLevel.ERROR]: '#EF4444',
    [LogLevel.SILENT]: '#000000'
};

/**
 * 日志输出接口
 */
export interface LogOutput {
    log(level: LogLevel, message: string, data?: any[]): void;
}

/**
 * 控制台日志输出
 */
export class ConsoleOutput implements LogOutput {
    log(level: LogLevel, message: string, data: any[] = []): void {
        if (!envUtils.isBrowser() && !envUtils.isNode()) return;

        const timestamp = new Date().toISOString();
        const levelName = LOG_LEVEL_NAMES[level];
        const color = LOG_LEVEL_COLORS[level];

        if (envUtils.isBrowser()) {
            const style = `color: ${color}; font-weight: bold;`;
            console.log(`%c[${timestamp}] ${levelName}:`, style, message, ...data);
        } else {
            console.log(`[${timestamp}] ${levelName}: ${message}`, ...data);
        }
    }
}

/**
 * 内存日志输出
 */
export class MemoryOutput implements LogOutput {
    private logs: Array<{
        level: LogLevel;
        message: string;
        data: any[];
        timestamp: Date;
    }> = [];
    
    private maxLogs: number;

    constructor(maxLogs = 1000) {
        this.maxLogs = maxLogs;
    }

    log(level: LogLevel, message: string, data: any[] = []): void {
        this.logs.push({
            level,
            message,
            data,
            timestamp: new Date()
        });

        // 保持日志数量在限制内
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
    }

    getLogs(): Array<{
        level: LogLevel;
        message: string;
        data: any[];
        timestamp: Date;
    }> {
        return [...this.logs];
    }

    clear(): void {
        this.logs = [];
    }
}

/**
 * 日志器类
 */
export class Logger {
    private namespace: string;
    private level: LogLevel;
    private outputs: LogOutput[];

    constructor(
        namespace: string,
        level: LogLevel = LogLevel.INFO,
        outputs: LogOutput[] = [new ConsoleOutput()]
    ) {
        this.namespace = namespace;
        this.level = level;
        this.outputs = outputs;
    }

    /**
     * 设置日志级别
     */
    setLevel(level: LogLevel): void {
        this.level = level;
    }

    /**
     * 获取日志级别
     */
    getLevel(): LogLevel {
        return this.level;
    }

    /**
     * 添加输出
     */
    addOutput(output: LogOutput): void {
        this.outputs.push(output);
    }

    /**
     * 移除输出
     */
    removeOutput(output: LogOutput): void {
        const index = this.outputs.indexOf(output);
        if (index !== -1) {
            this.outputs.splice(index, 1);
        }
    }

    /**
     * 记录日志
     */
    private log(level: LogLevel, message: string, ...data: any[]): void {
        if (level < this.level) return;

        const fullMessage = `[${this.namespace}] ${message}`;
        this.outputs.forEach(output => {
            output.log(level, fullMessage, data);
        });
    }

    /**
     * 调试日志
     */
    debug(message: string, ...data: any[]): void {
        this.log(LogLevel.DEBUG, message, ...data);
    }

    /**
     * 信息日志
     */
    info(message: string, ...data: any[]): void {
        this.log(LogLevel.INFO, message, ...data);
    }

    /**
     * 警告日志
     */
    warn(message: string, ...data: any[]): void {
        this.log(LogLevel.WARN, message, ...data);
    }

    /**
     * 错误日志
     */
    error(message: string, ...data: any[]): void {
        this.log(LogLevel.ERROR, message, ...data);
    }

    /**
     * 创建子日志器
     */
    child(namespace: string): Logger {
        return new Logger(
            `${this.namespace}:${namespace}`,
            this.level,
            this.outputs
        );
    }

    /**
     * 性能计时开始
     */
    time(label: string): void {
        if (envUtils.isBrowser() && performance.mark) {
            performance.mark(`${this.namespace}-${label}-start`);
        }
        this.debug(`Timer started: ${label}`);
    }

    /**
     * 性能计时结束
     */
    timeEnd(label: string): number {
        let duration = 0;
        
        if (envUtils.isBrowser() && performance.mark && performance.measure) {
            const startMark = `${this.namespace}-${label}-start`;
            const endMark = `${this.namespace}-${label}-end`;
            
            performance.mark(endMark);
            performance.measure(`${this.namespace}-${label}`, startMark, endMark);
            
            const entries = performance.getEntriesByName(`${this.namespace}-${label}`);
            if (entries.length > 0) {
                duration = entries[0].duration;
            }
        }
        
        this.debug(`Timer ended: ${label} (${duration.toFixed(2)}ms)`);
        return duration;
    }

    /**
     * 分组开始
     */
    group(label: string): void {
        if (envUtils.isBrowser() && console.group) {
            console.group(`[${this.namespace}] ${label}`);
        } else {
            this.info(`--- ${label} ---`);
        }
    }

    /**
     * 分组结束
     */
    groupEnd(): void {
        if (envUtils.isBrowser() && console.groupEnd) {
            console.groupEnd();
        }
    }

    /**
     * 表格输出
     */
    table(data: any): void {
        if (envUtils.isBrowser() && console.table) {
            console.table(data);
        } else {
            this.info('Table data:', data);
        }
    }

    /**
     * 断言
     */
    assert(condition: boolean, message: string, ...data: any[]): void {
        if (!condition) {
            this.error(`Assertion failed: ${message}`, ...data);
        }
    }

    /**
     * 计数
     */
    count(label = 'default'): void {
        if (envUtils.isBrowser() && console.count) {
            console.count(`[${this.namespace}] ${label}`);
        } else {
            this.info(`Count: ${label}`);
        }
    }

    /**
     * 重置计数
     */
    countReset(label = 'default'): void {
        if (envUtils.isBrowser() && console.countReset) {
            console.countReset(`[${this.namespace}] ${label}`);
        }
    }

    /**
     * 堆栈跟踪
     */
    trace(message?: string): void {
        if (envUtils.isBrowser() && console.trace) {
            console.trace(`[${this.namespace}] ${message || 'Trace'}`);
        } else {
            this.debug(message || 'Trace', new Error().stack);
        }
    }
}

/**
 * 全局日志器管理
 */
class LoggerManager {
    private loggers = new Map<string, Logger>();
    private globalLevel = LogLevel.INFO;
    private globalOutputs: LogOutput[] = [new ConsoleOutput()];

    /**
     * 获取或创建日志器
     */
    getLogger(namespace: string): Logger {
        if (!this.loggers.has(namespace)) {
            const logger = new Logger(namespace, this.globalLevel, [...this.globalOutputs]);
            this.loggers.set(namespace, logger);
        }
        return this.loggers.get(namespace)!;
    }

    /**
     * 设置全局日志级别
     */
    setGlobalLevel(level: LogLevel): void {
        this.globalLevel = level;
        this.loggers.forEach(logger => {
            logger.setLevel(level);
        });
    }

    /**
     * 添加全局输出
     */
    addGlobalOutput(output: LogOutput): void {
        this.globalOutputs.push(output);
        this.loggers.forEach(logger => {
            logger.addOutput(output);
        });
    }

    /**
     * 移除全局输出
     */
    removeGlobalOutput(output: LogOutput): void {
        const index = this.globalOutputs.indexOf(output);
        if (index !== -1) {
            this.globalOutputs.splice(index, 1);
            this.loggers.forEach(logger => {
                logger.removeOutput(output);
            });
        }
    }

    /**
     * 获取所有日志器
     */
    getAllLoggers(): Logger[] {
        return Array.from(this.loggers.values());
    }

    /**
     * 清除所有日志器
     */
    clear(): void {
        this.loggers.clear();
    }
}

/**
 * 全局日志器管理实例
 */
const loggerManager = new LoggerManager();

/**
 * 创建日志器
 */
export function createLogger(namespace: string): Logger {
    return loggerManager.getLogger(namespace);
}

/**
 * 设置全局日志级别
 */
export function setGlobalLogLevel(level: LogLevel): void {
    loggerManager.setGlobalLevel(level);
}

/**
 * 添加全局日志输出
 */
export function addGlobalLogOutput(output: LogOutput): void {
    loggerManager.addGlobalOutput(output);
}

/**
 * 移除全局日志输出
 */
export function removeGlobalLogOutput(output: LogOutput): void {
    loggerManager.removeGlobalOutput(output);
}

/**
 * 默认日志器
 */
export const logger = createLogger('micro-core');

/**
 * 日志工具集合
 */
export const loggerUtils = {
    LogLevel,
    Logger,
    ConsoleOutput,
    MemoryOutput,
    createLogger,
    setGlobalLogLevel,
    addGlobalLogOutput,
    removeGlobalLogOutput,
    logger
};
