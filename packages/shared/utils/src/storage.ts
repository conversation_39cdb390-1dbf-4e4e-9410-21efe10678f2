/**
 * @fileoverview 存储工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 检查是否支持存储
 */
function isStorageSupported(storage: Storage): boolean {
    try {
        const testKey = '__storage_test__';
        storage.setItem(testKey, 'test');
        storage.removeItem(testKey);
        return true;
    } catch {
        return false;
    }
}

/**
 * 安全的存储操作
 */
function safeStorageOperation<T>(
    operation: () => T,
    fallback: T,
    onError?: (error: Error) => void
): T {
    try {
        return operation();
    } catch (error) {
        if (onError) {
            onError(error as Error);
        }
        return fallback;
    }
}

/**
 * localStorage 工具
 */
export const local = {
    /**
     * 检查是否支持 localStorage
     */
    isSupported(): boolean {
        return typeof localStorage !== 'undefined' && isStorageSupported(localStorage);
    },

    /**
     * 设置值
     */
    set(key: string, value: any, options?: { expires?: number }): boolean {
        return safeStorageOperation(() => {
            const data = {
                value,
                timestamp: Date.now(),
                expires: options?.expires
            };
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        }, false);
    },

    /**
     * 获取值
     */
    get<T = any>(key: string, defaultValue?: T): T | undefined {
        return safeStorageOperation(() => {
            const item = localStorage.getItem(key);
            if (!item) return defaultValue;

            const data = JSON.parse(item);
            
            // 检查是否过期
            if (data.expires && Date.now() > data.timestamp + data.expires) {
                localStorage.removeItem(key);
                return defaultValue;
            }

            return data.value;
        }, defaultValue);
    },

    /**
     * 移除值
     */
    remove(key: string): boolean {
        return safeStorageOperation(() => {
            localStorage.removeItem(key);
            return true;
        }, false);
    },

    /**
     * 清空所有值
     */
    clear(): boolean {
        return safeStorageOperation(() => {
            localStorage.clear();
            return true;
        }, false);
    },

    /**
     * 获取所有键
     */
    keys(): string[] {
        return safeStorageOperation(() => {
            return Object.keys(localStorage);
        }, []);
    },

    /**
     * 获取存储大小
     */
    size(): number {
        return safeStorageOperation(() => {
            return Object.keys(localStorage).length;
        }, 0);
    },

    /**
     * 检查键是否存在
     */
    has(key: string): boolean {
        return safeStorageOperation(() => {
            return localStorage.getItem(key) !== null;
        }, false);
    },

    /**
     * 获取已使用的存储空间（字节）
     */
    getUsedSpace(): number {
        return safeStorageOperation(() => {
            let total = 0;
            for (const key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    total += localStorage[key].length + key.length;
                }
            }
            return total;
        }, 0);
    }
};

/**
 * sessionStorage 工具
 */
export const session = {
    /**
     * 检查是否支持 sessionStorage
     */
    isSupported(): boolean {
        return typeof sessionStorage !== 'undefined' && isStorageSupported(sessionStorage);
    },

    /**
     * 设置值
     */
    set(key: string, value: any): boolean {
        return safeStorageOperation(() => {
            const data = {
                value,
                timestamp: Date.now()
            };
            sessionStorage.setItem(key, JSON.stringify(data));
            return true;
        }, false);
    },

    /**
     * 获取值
     */
    get<T = any>(key: string, defaultValue?: T): T | undefined {
        return safeStorageOperation(() => {
            const item = sessionStorage.getItem(key);
            if (!item) return defaultValue;

            const data = JSON.parse(item);
            return data.value;
        }, defaultValue);
    },

    /**
     * 移除值
     */
    remove(key: string): boolean {
        return safeStorageOperation(() => {
            sessionStorage.removeItem(key);
            return true;
        }, false);
    },

    /**
     * 清空所有值
     */
    clear(): boolean {
        return safeStorageOperation(() => {
            sessionStorage.clear();
            return true;
        }, false);
    },

    /**
     * 获取所有键
     */
    keys(): string[] {
        return safeStorageOperation(() => {
            return Object.keys(sessionStorage);
        }, []);
    },

    /**
     * 获取存储大小
     */
    size(): number {
        return safeStorageOperation(() => {
            return Object.keys(sessionStorage).length;
        }, 0);
    },

    /**
     * 检查键是否存在
     */
    has(key: string): boolean {
        return safeStorageOperation(() => {
            return sessionStorage.getItem(key) !== null;
        }, false);
    }
};

/**
 * 内存存储（用于不支持 Web Storage 的环境）
 */
class MemoryStorage {
    private data = new Map<string, any>();

    set(key: string, value: any, options?: { expires?: number }): boolean {
        const data = {
            value,
            timestamp: Date.now(),
            expires: options?.expires
        };
        this.data.set(key, data);
        return true;
    }

    get<T = any>(key: string, defaultValue?: T): T | undefined {
        const data = this.data.get(key);
        if (!data) return defaultValue;

        // 检查是否过期
        if (data.expires && Date.now() > data.timestamp + data.expires) {
            this.data.delete(key);
            return defaultValue;
        }

        return data.value;
    }

    remove(key: string): boolean {
        return this.data.delete(key);
    }

    clear(): boolean {
        this.data.clear();
        return true;
    }

    keys(): string[] {
        return Array.from(this.data.keys());
    }

    size(): number {
        return this.data.size;
    }

    has(key: string): boolean {
        return this.data.has(key);
    }
}

/**
 * 内存存储实例
 */
export const memory = new MemoryStorage();

/**
 * 通用存储接口
 */
export interface StorageAdapter {
    set(key: string, value: any, options?: { expires?: number }): boolean;
    get<T = any>(key: string, defaultValue?: T): T | undefined;
    remove(key: string): boolean;
    clear(): boolean;
    keys(): string[];
    size(): number;
    has(key: string): boolean;
}

/**
 * 存储工厂
 */
export function createStorage(type: 'local' | 'session' | 'memory' = 'local'): StorageAdapter {
    switch (type) {
        case 'local':
            return local.isSupported() ? local : memory;
        case 'session':
            return session.isSupported() ? session : memory;
        case 'memory':
        default:
            return memory;
    }
}

/**
 * 带前缀的存储
 */
export function createPrefixedStorage(prefix: string, storage: StorageAdapter = local): StorageAdapter {
    const prefixedKey = (key: string) => `${prefix}:${key}`;
    const unprefixedKey = (key: string) => key.replace(new RegExp(`^${prefix}:`), '');

    return {
        set(key: string, value: any, options?: { expires?: number }): boolean {
            return storage.set(prefixedKey(key), value, options);
        },

        get<T = any>(key: string, defaultValue?: T): T | undefined {
            return storage.get(prefixedKey(key), defaultValue);
        },

        remove(key: string): boolean {
            return storage.remove(prefixedKey(key));
        },

        clear(): boolean {
            const keys = this.keys();
            keys.forEach(key => storage.remove(prefixedKey(key)));
            return true;
        },

        keys(): string[] {
            return storage.keys()
                .filter(key => key.startsWith(`${prefix}:`))
                .map(unprefixedKey);
        },

        size(): number {
            return this.keys().length;
        },

        has(key: string): boolean {
            return storage.has(prefixedKey(key));
        }
    };
}

/**
 * 带版本的存储
 */
export function createVersionedStorage(version: string, storage: StorageAdapter = local): StorageAdapter {
    const versionKey = '__storage_version__';
    const currentVersion = storage.get(versionKey);

    // 如果版本不匹配，清空存储
    if (currentVersion !== version) {
        storage.clear();
        storage.set(versionKey, version);
    }

    return storage;
}

/**
 * 存储工具集合
 */
export const storageUtils = {
    local,
    session,
    memory,
    createStorage,
    createPrefixedStorage,
    createVersionedStorage
};
