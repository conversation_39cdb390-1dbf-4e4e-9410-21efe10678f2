/**
 * @fileoverview 异步工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 超时控制
 */
export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('Timeout')), ms);
        })
    ]);
}

/**
 * 重试执行
 */
export async function retry<T>(
    fn: () => T | Promise<T>,
    maxAttempts = 3,
    delayMs = 1000
): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;
            if (attempt < maxAttempts) {
                await delay(delayMs * attempt);
            }
        }
    }

    throw lastError!;
}

/**
 * 并发控制
 */
export async function concurrent<T>(
    tasks: Array<() => Promise<T>>,
    limit = 5
): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (const task of tasks) {
        const promise = task().then(result => {
            results.push(result);
        });

        executing.push(promise);

        if (executing.length >= limit) {
            await Promise.race(executing);
            executing.splice(executing.findIndex(p => p === promise), 1);
        }
    }

    await Promise.all(executing);
    return results;
}

/**
 * 安全执行
 */
export async function safeExecute<T>(
    fn: () => T | Promise<T>,
    fallback?: T,
    onError?: (error: Error) => void
): Promise<T | undefined> {
    try {
        return await fn();
    } catch (error) {
        if (onError) {
            onError(error as Error);
        }
        return fallback;
    }
}

/**
 * 批量执行
 */
export async function batch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    batchSize = 10
): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchResults = await Promise.all(batch.map(processor));
        results.push(...batchResults);
    }
    
    return results;
}

/**
 * 队列执行
 */
export class AsyncQueue<T = any> {
    private queue: Array<() => Promise<T>> = [];
    private running = false;
    private concurrency: number;
    private results: T[] = [];

    constructor(concurrency = 1) {
        this.concurrency = concurrency;
    }

    add(task: () => Promise<T>): void {
        this.queue.push(task);
        this.process();
    }

    private async process(): Promise<void> {
        if (this.running || this.queue.length === 0) {
            return;
        }

        this.running = true;
        const executing: Promise<void>[] = [];

        while (this.queue.length > 0 || executing.length > 0) {
            while (executing.length < this.concurrency && this.queue.length > 0) {
                const task = this.queue.shift()!;
                const promise = task().then(result => {
                    this.results.push(result);
                });
                executing.push(promise);
            }

            if (executing.length > 0) {
                await Promise.race(executing);
                const completedIndex = executing.findIndex(p => p === promise);
                if (completedIndex !== -1) {
                    executing.splice(completedIndex, 1);
                }
            }
        }

        this.running = false;
    }

    async waitForAll(): Promise<T[]> {
        while (this.running || this.queue.length > 0) {
            await delay(10);
        }
        return [...this.results];
    }

    clear(): void {
        this.queue = [];
        this.results = [];
    }

    size(): number {
        return this.queue.length;
    }
}

/**
 * 异步迭代器工具
 */
export async function* asyncMap<T, R>(
    iterable: AsyncIterable<T>,
    mapper: (item: T) => Promise<R>
): AsyncGenerator<R> {
    for await (const item of iterable) {
        yield await mapper(item);
    }
}

/**
 * 异步过滤器
 */
export async function* asyncFilter<T>(
    iterable: AsyncIterable<T>,
    predicate: (item: T) => Promise<boolean>
): AsyncGenerator<T> {
    for await (const item of iterable) {
        if (await predicate(item)) {
            yield item;
        }
    }
}

/**
 * 异步归约
 */
export async function asyncReduce<T, R>(
    iterable: AsyncIterable<T>,
    reducer: (acc: R, item: T) => Promise<R>,
    initialValue: R
): Promise<R> {
    let accumulator = initialValue;
    for await (const item of iterable) {
        accumulator = await reducer(accumulator, item);
    }
    return accumulator;
}

/**
 * 异步查找
 */
export async function asyncFind<T>(
    iterable: AsyncIterable<T>,
    predicate: (item: T) => Promise<boolean>
): Promise<T | undefined> {
    for await (const item of iterable) {
        if (await predicate(item)) {
            return item;
        }
    }
    return undefined;
}

/**
 * 异步转数组
 */
export async function asyncToArray<T>(iterable: AsyncIterable<T>): Promise<T[]> {
    const result: T[] = [];
    for await (const item of iterable) {
        result.push(item);
    }
    return result;
}

/**
 * 创建可取消的Promise
 */
export function cancellable<T>(
    executor: (resolve: (value: T) => void, reject: (reason?: any) => void) => void
): { promise: Promise<T>; cancel: () => void } {
    let cancelled = false;
    let cancel: () => void;

    const promise = new Promise<T>((resolve, reject) => {
        cancel = () => {
            cancelled = true;
            reject(new Error('Cancelled'));
        };

        executor(
            (value) => {
                if (!cancelled) resolve(value);
            },
            (reason) => {
                if (!cancelled) reject(reason);
            }
        );
    });

    return { promise, cancel: cancel! };
}

/**
 * Promise状态检查
 */
export async function promiseState<T>(promise: Promise<T>): Promise<'pending' | 'fulfilled' | 'rejected'> {
    const pending = Symbol('pending');
    
    try {
        const result = await Promise.race([promise, Promise.resolve(pending)]);
        return result === pending ? 'pending' : 'fulfilled';
    } catch {
        return 'rejected';
    }
}

/**
 * 条件等待
 */
export async function waitUntil(
    condition: () => boolean | Promise<boolean>,
    timeout = 5000,
    interval = 100
): Promise<void> {
    const start = Date.now();
    
    while (Date.now() - start < timeout) {
        if (await condition()) {
            return;
        }
        await delay(interval);
    }
    
    throw new Error('Condition not met within timeout');
}

/**
 * 异步工具集合
 */
export const asyncUtils = {
    delay,
    timeout,
    retry,
    concurrent,
    safeExecute,
    batch,
    AsyncQueue,
    asyncMap,
    asyncFilter,
    asyncReduce,
    asyncFind,
    asyncToArray,
    cancellable,
    promiseState,
    waitUntil
};
