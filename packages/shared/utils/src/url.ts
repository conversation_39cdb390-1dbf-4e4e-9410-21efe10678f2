/**
 * @fileoverview URL处理工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 解析查询字符串
 */
export function parseQuery(query: string): Record<string, string> {
    const params: Record<string, string> = {};
    const searchParams = new URLSearchParams(query.startsWith('?') ? query.slice(1) : query);
    
    searchParams.forEach((value, key) => {
        params[key] = value;
    });
    
    return params;
}

/**
 * 序列化查询对象
 */
export function stringifyQuery(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
                value.forEach(item => searchParams.append(key, String(item)));
            } else {
                searchParams.append(key, String(value));
            }
        }
    });
    
    return searchParams.toString();
}

/**
 * 获取当前页面URL参数
 */
export function getUrlParams(): Record<string, string> {
    if (typeof window === 'undefined') return {};
    return parseQuery(window.location.search);
}

/**
 * 解析URL参数
 */
export function parseParams(url: string): Record<string, string> {
    try {
        const urlObj = new URL(url, typeof window !== 'undefined' ? window.location.origin : 'http://localhost');
        const params: Record<string, string> = {};
        
        urlObj.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        
        return params;
    } catch {
        return {};
    }
}

/**
 * 序列化URL参数
 */
export function stringifyParams(params: Record<string, any>): string {
    return stringifyQuery(params);
}

/**
 * 构建URL
 */
export function buildUrl(base: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
        return base;
    }
    
    const separator = base.includes('?') ? '&' : '?';
    return base + separator + stringifyParams(params);
}

/**
 * 检查是否为绝对URL
 */
export function isAbsolute(url: string): boolean {
    return /^https?:\/\//.test(url);
}

/**
 * 检查是否为相对URL
 */
export function isRelative(url: string): boolean {
    return !isAbsolute(url);
}

/**
 * 检查是否为有效URL
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 解析URL
 */
export function parse(url: string): {
    protocol: string;
    host: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;
    params: Record<string, string>;
} | null {
    try {
        const urlObj = new URL(url, typeof window !== 'undefined' ? window.location.origin : 'http://localhost');
        return {
            protocol: urlObj.protocol,
            host: urlObj.host,
            hostname: urlObj.hostname,
            port: urlObj.port,
            pathname: urlObj.pathname,
            search: urlObj.search,
            hash: urlObj.hash,
            params: parseParams(url)
        };
    } catch {
        return null;
    }
}

/**
 * 获取URL的域名
 */
export function getDomain(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch {
        return null;
    }
}

/**
 * 获取URL的协议
 */
export function getProtocol(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol;
    } catch {
        return null;
    }
}

/**
 * 获取URL的路径
 */
export function getPathname(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.pathname;
    } catch {
        return null;
    }
}

/**
 * 获取URL的查询字符串
 */
export function getSearch(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.search;
    } catch {
        return null;
    }
}

/**
 * 获取URL的哈希
 */
export function getHash(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.hash;
    } catch {
        return null;
    }
}

/**
 * 连接URL路径
 */
export function join(...paths: string[]): string {
    return paths
        .map((path, index) => {
            if (index === 0) {
                return path.replace(/\/+$/, '');
            }
            return path.replace(/^\/+|\/+$/g, '');
        })
        .filter(Boolean)
        .join('/');
}

/**
 * 规范化URL路径
 */
export function normalize(url: string): string {
    try {
        const urlObj = new URL(url);
        return urlObj.href;
    } catch {
        return url;
    }
}

/**
 * 移除URL参数
 */
export function removeParams(url: string, params: string[]): string {
    try {
        const urlObj = new URL(url);
        params.forEach(param => {
            urlObj.searchParams.delete(param);
        });
        return urlObj.href;
    } catch {
        return url;
    }
}

/**
 * 添加URL参数
 */
export function addParams(url: string, params: Record<string, any>): string {
    try {
        const urlObj = new URL(url);
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                urlObj.searchParams.set(key, String(value));
            }
        });
        return urlObj.href;
    } catch {
        return buildUrl(url, params);
    }
}

/**
 * 更新URL参数
 */
export function updateParams(url: string, params: Record<string, any>): string {
    return addParams(url, params);
}

/**
 * 获取文件扩展名
 */
export function getExtension(url: string): string | null {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const lastDot = pathname.lastIndexOf('.');
        const lastSlash = pathname.lastIndexOf('/');
        
        if (lastDot > lastSlash && lastDot !== -1) {
            return pathname.slice(lastDot + 1);
        }
        return null;
    } catch {
        return null;
    }
}

/**
 * 获取文件名
 */
export function getFilename(url: string): string | null {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const lastSlash = pathname.lastIndexOf('/');
        
        if (lastSlash !== -1) {
            return pathname.slice(lastSlash + 1);
        }
        return pathname;
    } catch {
        return null;
    }
}

/**
 * 检查是否为同源URL
 */
export function isSameOrigin(url1: string, url2: string): boolean {
    try {
        const urlObj1 = new URL(url1);
        const urlObj2 = new URL(url2);
        return urlObj1.origin === urlObj2.origin;
    } catch {
        return false;
    }
}

/**
 * 获取相对URL
 */
export function getRelativeUrl(from: string, to: string): string {
    try {
        const fromObj = new URL(from);
        const toObj = new URL(to);
        
        if (fromObj.origin !== toObj.origin) {
            return to;
        }
        
        return toObj.pathname + toObj.search + toObj.hash;
    } catch {
        return to;
    }
}

/**
 * URL工具集合
 */
export const urlUtils = {
    parseQuery,
    stringifyQuery,
    getUrlParams,
    parseParams,
    stringifyParams,
    buildUrl,
    isAbsolute,
    isRelative,
    isValidUrl,
    parse,
    getDomain,
    getProtocol,
    getPathname,
    getSearch,
    getHash,
    join,
    normalize,
    removeParams,
    addParams,
    updateParams,
    getExtension,
    getFilename,
    isSameOrigin,
    getRelativeUrl
};
