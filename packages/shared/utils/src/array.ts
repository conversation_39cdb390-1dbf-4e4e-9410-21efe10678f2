/**
 * @fileoverview 数组工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 数组去重
 */
export function unique<T>(arr: T[]): T[] {
    return [...new Set(arr)];
}

/**
 * 基于属性的数组去重
 */
export function uniqueBy<T>(arr: T[], keyFn: (item: T) => any): T[] {
    const seen = new Set();
    return arr.filter(item => {
        const key = keyFn(item);
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

/**
 * 数组分块
 */
export function chunk<T>(arr: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < arr.length; i += size) {
        chunks.push(arr.slice(i, i + size));
    }
    return chunks;
}

/**
 * 数组扁平化
 */
export function flatten<T>(arr: (T | T[])[]): T[] {
    return arr.reduce<T[]>((acc, val) => {
        return acc.concat(Array.isArray(val) ? flatten(val) : val);
    }, []);
}

/**
 * 深度扁平化
 */
export function flattenDeep<T>(arr: any[]): T[] {
    return arr.reduce((acc, val) => {
        return acc.concat(Array.isArray(val) ? flattenDeep(val) : val);
    }, []);
}

/**
 * 数组分组
 */
export function groupBy<T>(arr: T[], keyFn: (item: T) => string): Record<string, T[]> {
    return arr.reduce((groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {} as Record<string, T[]>);
}

/**
 * 数组排序
 */
export function sortBy<T>(arr: T[], keyFn: (item: T) => any): T[] {
    return [...arr].sort((a, b) => {
        const aVal = keyFn(a);
        const bVal = keyFn(b);
        if (aVal < bVal) return -1;
        if (aVal > bVal) return 1;
        return 0;
    });
}

/**
 * 多字段排序
 */
export function sortByMultiple<T>(
    arr: T[],
    sortKeys: Array<{ key: (item: T) => any; desc?: boolean }>
): T[] {
    return [...arr].sort((a, b) => {
        for (const { key, desc = false } of sortKeys) {
            const aVal = key(a);
            const bVal = key(b);
            
            if (aVal < bVal) return desc ? 1 : -1;
            if (aVal > bVal) return desc ? -1 : 1;
        }
        return 0;
    });
}

/**
 * 数组差集
 */
export function difference<T>(arr1: T[], arr2: T[]): T[] {
    return arr1.filter(item => !arr2.includes(item));
}

/**
 * 数组交集
 */
export function intersection<T>(arr1: T[], arr2: T[]): T[] {
    return arr1.filter(item => arr2.includes(item));
}

/**
 * 数组并集
 */
export function union<T>(arr1: T[], arr2: T[]): T[] {
    return unique([...arr1, ...arr2]);
}

/**
 * 数组对称差集
 */
export function symmetricDifference<T>(arr1: T[], arr2: T[]): T[] {
    return [
        ...arr1.filter(item => !arr2.includes(item)),
        ...arr2.filter(item => !arr1.includes(item))
    ];
}

/**
 * 数组分区
 */
export function partition<T>(arr: T[], predicate: (item: T) => boolean): [T[], T[]] {
    const truthy: T[] = [];
    const falsy: T[] = [];
    
    arr.forEach(item => {
        if (predicate(item)) {
            truthy.push(item);
        } else {
            falsy.push(item);
        }
    });
    
    return [truthy, falsy];
}

/**
 * 数组随机排序
 */
export function shuffle<T>(arr: T[]): T[] {
    const result = [...arr];
    for (let i = result.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
}

/**
 * 随机选择数组元素
 */
export function sample<T>(arr: T[], count = 1): T[] {
    const shuffled = shuffle(arr);
    return shuffled.slice(0, count);
}

/**
 * 随机选择单个元素
 */
export function sampleOne<T>(arr: T[]): T {
    return arr[Math.floor(Math.random() * arr.length)];
}

/**
 * 数组压缩（zip）
 */
export function zip<T>(...arrays: T[][]): T[][] {
    const maxLength = Math.max(...arrays.map(arr => arr.length));
    const result: T[][] = [];
    
    for (let i = 0; i < maxLength; i++) {
        result.push(arrays.map(arr => arr[i]));
    }
    
    return result;
}

/**
 * 数组解压缩（unzip）
 */
export function unzip<T>(arr: T[][]): T[][] {
    if (arr.length === 0) return [];
    return arr[0].map((_, i) => arr.map(row => row[i]));
}

/**
 * 数组转对象
 */
export function toObject<T>(
    arr: T[],
    keyFn: (item: T) => string,
    valueFn?: (item: T) => any
): Record<string, any> {
    return arr.reduce((obj, item) => {
        const key = keyFn(item);
        const value = valueFn ? valueFn(item) : item;
        obj[key] = value;
        return obj;
    }, {} as Record<string, any>);
}

/**
 * 数组计数
 */
export function countBy<T>(arr: T[], keyFn: (item: T) => string): Record<string, number> {
    return arr.reduce((counts, item) => {
        const key = keyFn(item);
        counts[key] = (counts[key] || 0) + 1;
        return counts;
    }, {} as Record<string, number>);
}

/**
 * 数组求和
 */
export function sum(arr: number[]): number {
    return arr.reduce((total, num) => total + num, 0);
}

/**
 * 基于属性的数组求和
 */
export function sumBy<T>(arr: T[], keyFn: (item: T) => number): number {
    return arr.reduce((total, item) => total + keyFn(item), 0);
}

/**
 * 数组平均值
 */
export function average(arr: number[]): number {
    return arr.length === 0 ? 0 : sum(arr) / arr.length;
}

/**
 * 基于属性的数组平均值
 */
export function averageBy<T>(arr: T[], keyFn: (item: T) => number): number {
    return arr.length === 0 ? 0 : sumBy(arr, keyFn) / arr.length;
}

/**
 * 数组最大值
 */
export function max(arr: number[]): number | undefined {
    return arr.length === 0 ? undefined : Math.max(...arr);
}

/**
 * 基于属性的数组最大值
 */
export function maxBy<T>(arr: T[], keyFn: (item: T) => number): T | undefined {
    if (arr.length === 0) return undefined;
    
    return arr.reduce((max, item) => {
        return keyFn(item) > keyFn(max) ? item : max;
    });
}

/**
 * 数组最小值
 */
export function min(arr: number[]): number | undefined {
    return arr.length === 0 ? undefined : Math.min(...arr);
}

/**
 * 基于属性的数组最小值
 */
export function minBy<T>(arr: T[], keyFn: (item: T) => number): T | undefined {
    if (arr.length === 0) return undefined;
    
    return arr.reduce((min, item) => {
        return keyFn(item) < keyFn(min) ? item : min;
    });
}

/**
 * 数组范围
 */
export function range(start: number, end?: number, step = 1): number[] {
    if (end === undefined) {
        end = start;
        start = 0;
    }
    
    const result: number[] = [];
    for (let i = start; i < end; i += step) {
        result.push(i);
    }
    
    return result;
}

/**
 * 数组填充
 */
export function fill<T>(length: number, value: T | ((index: number) => T)): T[] {
    const result: T[] = [];
    for (let i = 0; i < length; i++) {
        result.push(typeof value === 'function' ? (value as Function)(i) : value);
    }
    return result;
}

/**
 * 数组移动元素
 */
export function move<T>(arr: T[], from: number, to: number): T[] {
    const result = [...arr];
    const item = result.splice(from, 1)[0];
    result.splice(to, 0, item);
    return result;
}

/**
 * 数组插入元素
 */
export function insert<T>(arr: T[], index: number, ...items: T[]): T[] {
    const result = [...arr];
    result.splice(index, 0, ...items);
    return result;
}

/**
 * 数组移除元素
 */
export function remove<T>(arr: T[], predicate: (item: T) => boolean): T[] {
    return arr.filter(item => !predicate(item));
}

/**
 * 数组工具集合
 */
export const arrayUtils = {
    unique,
    uniqueBy,
    chunk,
    flatten,
    flattenDeep,
    groupBy,
    sortBy,
    sortByMultiple,
    difference,
    intersection,
    union,
    symmetricDifference,
    partition,
    shuffle,
    sample,
    sampleOne,
    zip,
    unzip,
    toObject,
    countBy,
    sum,
    sumBy,
    average,
    averageBy,
    max,
    maxBy,
    min,
    minBy,
    range,
    fill,
    move,
    insert,
    remove
};
