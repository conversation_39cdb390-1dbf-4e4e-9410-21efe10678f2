/**
 * @fileoverview 类型检查工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 检查是否为函数
 */
export function isFunction(value: any): value is Function {
    return typeof value === 'function';
}

/**
 * 检查是否为对象
 */
export function isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 检查是否为字符串
 */
export function isString(value: any): value is string {
    return typeof value === 'string';
}

/**
 * 检查是否为数字
 */
export function isNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查是否为布尔值
 */
export function isBoolean(value: any): value is boolean {
    return typeof value === 'boolean';
}

/**
 * 检查是否为数组
 */
export function isArray(value: any): value is any[] {
    return Array.isArray(value);
}

/**
 * 检查是否为Promise
 */
export function isPromise(value: any): value is Promise<any> {
    return value != null && typeof value.then === 'function';
}

/**
 * 检查是否为空值
 */
export function isNullOrUndefined(value: any): value is null | undefined {
    return value === null || value === undefined;
}

/**
 * 检查是否为空
 */
export function isEmpty(value: any): boolean {
    if (isNullOrUndefined(value)) return true;
    if (isString(value) || isArray(value)) return value.length === 0;
    if (isObject(value)) return Object.keys(value).length === 0;
    return false;
}

/**
 * 检查是否为日期对象
 */
export function isDate(value: any): value is Date {
    return value instanceof Date && !isNaN(value.getTime());
}

/**
 * 检查是否为正则表达式
 */
export function isRegExp(value: any): value is RegExp {
    return value instanceof RegExp;
}

/**
 * 检查是否为错误对象
 */
export function isError(value: any): value is Error {
    return value instanceof Error;
}

/**
 * 检查是否为URL对象
 */
export function isURL(value: any): value is URL {
    return value instanceof URL;
}

/**
 * 检查是否为Map对象
 */
export function isMap(value: any): value is Map<any, any> {
    return value instanceof Map;
}

/**
 * 检查是否为Set对象
 */
export function isSet(value: any): value is Set<any> {
    return value instanceof Set;
}

/**
 * 检查是否为WeakMap对象
 */
export function isWeakMap(value: any): value is WeakMap<any, any> {
    return value instanceof WeakMap;
}

/**
 * 检查是否为WeakSet对象
 */
export function isWeakSet(value: any): value is WeakSet<any> {
    return value instanceof WeakSet;
}

/**
 * 检查是否为Symbol
 */
export function isSymbol(value: any): value is symbol {
    return typeof value === 'symbol';
}

/**
 * 检查是否为BigInt
 */
export function isBigInt(value: any): value is bigint {
    return typeof value === 'bigint';
}

/**
 * 检查是否为原始类型
 */
export function isPrimitive(value: any): value is string | number | boolean | null | undefined | symbol | bigint {
    return value == null || (typeof value !== 'object' && typeof value !== 'function');
}

/**
 * 检查是否为纯对象（Plain Object）
 */
export function isPlainObject(value: any): value is Record<string, any> {
    if (!isObject(value)) return false;

    // 检查是否有原型
    if (Object.getPrototypeOf(value) === null) return true;

    // 检查是否是通过 {} 或 new Object() 创建的
    let proto = value;
    while (Object.getPrototypeOf(proto) !== null) {
        proto = Object.getPrototypeOf(proto);
    }

    return Object.getPrototypeOf(value) === Object.prototype;
}

/**
 * 检查是否为类数组对象
 */
export function isArrayLike(value: any): value is ArrayLike<any> {
    return value != null &&
        typeof value !== 'function' &&
        isNumber(value.length) &&
        value.length >= 0 &&
        value.length <= Number.MAX_SAFE_INTEGER;
}

/**
 * 检查是否为可迭代对象
 */
export function isIterable(value: any): value is Iterable<any> {
    return value != null && typeof value[Symbol.iterator] === 'function';
}

/**
 * 检查是否为异步可迭代对象
 */
export function isAsyncIterable(value: any): value is AsyncIterable<any> {
    return value != null && typeof value[Symbol.asyncIterator] === 'function';
}

/**
 * 获取值的类型字符串
 */
export function getType(value: any): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';

    const type = typeof value;
    if (type !== 'object') return type;

    return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();
}

/**
 * 检查两个值是否为相同类型
 */
export function isSameType(a: any, b: any): boolean {
    return getType(a) === getType(b);
}

/**
 * 类型检查工具对象
 */
export const typeUtils = {
    isFunction,
    isObject,
    isString,
    isNumber,
    isBoolean,
    isArray,
    isPromise,
    isNullOrUndefined,
    isEmpty,
    isDate,
    isRegExp,
    isError,
    isURL,
    isMap,
    isSet,
    isWeakMap,
    isWeakSet,
    isSymbol,
    isBigInt,
    isPrimitive,
    isPlainObject,
    isArrayLike,
    isIterable,
    isAsyncIterable,
    getType,
    isSameType
};
