/**
 * @fileoverview 环境检测工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 检查是否为浏览器环境
 */
export function isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * 检查是否为Node.js环境
 */
export function isNode(): boolean {
    return typeof process !== 'undefined' && 
           process.versions != null && 
           process.versions.node != null;
}

/**
 * 检查是否为Web Worker环境
 */
export function isWebWorker(): boolean {
    return typeof importScripts === 'function' && 
           typeof navigator !== 'undefined' && 
           navigator instanceof WorkerNavigator;
}

/**
 * 检查是否为Service Worker环境
 */
export function isServiceWorker(): boolean {
    return typeof importScripts === 'function' && 
           typeof navigator !== 'undefined' && 
           'serviceWorker' in navigator;
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
    if (isBrowser()) {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.startsWith('192.168.') ||
               window.location.hostname.endsWith('.local');
    }
    
    if (isNode()) {
        return process.env.NODE_ENV === 'development';
    }
    
    return false;
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
    if (isNode()) {
        return process.env.NODE_ENV === 'production';
    }
    
    return !isDevelopment();
}

/**
 * 检查是否为测试环境
 */
export function isTest(): boolean {
    if (isNode()) {
        return process.env.NODE_ENV === 'test';
    }
    
    return false;
}

/**
 * 获取用户代理信息
 */
export function getUserAgent(): string {
    if (isBrowser()) {
        return navigator.userAgent;
    }
    
    if (isNode()) {
        return `Node.js/${process.version}`;
    }
    
    return '';
}

/**
 * 检查是否为移动设备
 */
export function isMobile(): boolean {
    if (!isBrowser()) return false;
    
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 检查是否为平板设备
 */
export function isTablet(): boolean {
    if (!isBrowser()) return false;
    
    return /iPad|Android(?!.*Mobile)|Tablet/i.test(navigator.userAgent);
}

/**
 * 检查是否为桌面设备
 */
export function isDesktop(): boolean {
    return isBrowser() && !isMobile() && !isTablet();
}

/**
 * 检查是否为iOS设备
 */
export function isIOS(): boolean {
    if (!isBrowser()) return false;
    
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 检查是否为Android设备
 */
export function isAndroid(): boolean {
    if (!isBrowser()) return false;
    
    return /Android/.test(navigator.userAgent);
}

/**
 * 检查是否为Windows设备
 */
export function isWindows(): boolean {
    if (!isBrowser()) return false;
    
    return /Windows/.test(navigator.userAgent);
}

/**
 * 检查是否为macOS设备
 */
export function isMacOS(): boolean {
    if (!isBrowser()) return false;
    
    return /Macintosh|MacIntel|MacPPC|Mac68K/.test(navigator.userAgent);
}

/**
 * 检查是否为Linux设备
 */
export function isLinux(): boolean {
    if (!isBrowser()) return false;
    
    return /Linux/.test(navigator.userAgent) && !isAndroid();
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo(): {
    name: string;
    version: string;
    engine: string;
} | null {
    if (!isBrowser()) return null;
    
    const ua = navigator.userAgent;
    let name = 'Unknown';
    let version = 'Unknown';
    let engine = 'Unknown';
    
    // 检测浏览器
    if (ua.includes('Chrome')) {
        name = 'Chrome';
        version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
        engine = 'Blink';
    } else if (ua.includes('Firefox')) {
        name = 'Firefox';
        version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
        engine = 'Gecko';
    } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
        name = 'Safari';
        version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
        engine = 'WebKit';
    } else if (ua.includes('Edge')) {
        name = 'Edge';
        version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
        engine = 'EdgeHTML';
    } else if (ua.includes('Edg')) {
        name = 'Edge Chromium';
        version = ua.match(/Edg\/(\d+)/)?.[1] || 'Unknown';
        engine = 'Blink';
    }
    
    return { name, version, engine };
}

/**
 * 获取操作系统信息
 */
export function getOSInfo(): {
    name: string;
    version: string;
} | null {
    if (!isBrowser()) return null;
    
    const ua = navigator.userAgent;
    let name = 'Unknown';
    let version = 'Unknown';
    
    if (isWindows()) {
        name = 'Windows';
        const match = ua.match(/Windows NT (\d+\.\d+)/);
        if (match) {
            const ntVersion = match[1];
            const versionMap: Record<string, string> = {
                '10.0': '10',
                '6.3': '8.1',
                '6.2': '8',
                '6.1': '7',
                '6.0': 'Vista',
                '5.1': 'XP'
            };
            version = versionMap[ntVersion] || ntVersion;
        }
    } else if (isMacOS()) {
        name = 'macOS';
        const match = ua.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);
        if (match) {
            version = match[1].replace(/_/g, '.');
        }
    } else if (isIOS()) {
        name = 'iOS';
        const match = ua.match(/OS (\d+[._]\d+[._]?\d*)/);
        if (match) {
            version = match[1].replace(/_/g, '.');
        }
    } else if (isAndroid()) {
        name = 'Android';
        const match = ua.match(/Android (\d+\.\d+)/);
        if (match) {
            version = match[1];
        }
    } else if (isLinux()) {
        name = 'Linux';
    }
    
    return { name, version };
}

/**
 * 检查是否支持某个特性
 */
export function supports(feature: string): boolean {
    if (!isBrowser()) return false;
    
    switch (feature) {
        case 'localStorage':
            return typeof Storage !== 'undefined' && 'localStorage' in window;
        case 'sessionStorage':
            return typeof Storage !== 'undefined' && 'sessionStorage' in window;
        case 'webWorker':
            return typeof Worker !== 'undefined';
        case 'serviceWorker':
            return 'serviceWorker' in navigator;
        case 'webAssembly':
            return typeof WebAssembly !== 'undefined';
        case 'proxy':
            return typeof Proxy !== 'undefined';
        case 'customElements':
            return typeof customElements !== 'undefined';
        case 'shadowDOM':
            return typeof ShadowRoot !== 'undefined';
        case 'webGL':
            try {
                const canvas = document.createElement('canvas');
                return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            } catch {
                return false;
            }
        case 'webGL2':
            try {
                const canvas = document.createElement('canvas');
                return !!canvas.getContext('webgl2');
            } catch {
                return false;
            }
        case 'webRTC':
            return !!(window as any).RTCPeerConnection || !!(window as any).webkitRTCPeerConnection;
        case 'geolocation':
            return 'geolocation' in navigator;
        case 'notification':
            return 'Notification' in window;
        case 'pushManager':
            return 'PushManager' in window;
        case 'indexedDB':
            return 'indexedDB' in window;
        case 'webSockets':
            return 'WebSocket' in window;
        case 'fetch':
            return 'fetch' in window;
        case 'intersectionObserver':
            return 'IntersectionObserver' in window;
        case 'mutationObserver':
            return 'MutationObserver' in window;
        case 'resizeObserver':
            return 'ResizeObserver' in window;
        case 'performanceObserver':
            return 'PerformanceObserver' in window;
        case 'clipboard':
            return 'clipboard' in navigator;
        case 'share':
            return 'share' in navigator;
        case 'bluetooth':
            return 'bluetooth' in navigator;
        case 'usb':
            return 'usb' in navigator;
        case 'gamepad':
            return 'getGamepads' in navigator;
        case 'vibrate':
            return 'vibrate' in navigator;
        case 'battery':
            return 'getBattery' in navigator;
        case 'connection':
            return 'connection' in navigator;
        case 'deviceMemory':
            return 'deviceMemory' in navigator;
        case 'hardwareConcurrency':
            return 'hardwareConcurrency' in navigator;
        default:
            return false;
    }
}

/**
 * 获取设备信息
 */
export function getDeviceInfo(): {
    type: 'mobile' | 'tablet' | 'desktop';
    os: { name: string; version: string } | null;
    browser: { name: string; version: string; engine: string } | null;
    screen: { width: number; height: number } | null;
    memory: number | null;
    cores: number | null;
    connection: string | null;
} {
    return {
        type: isMobile() ? 'mobile' : isTablet() ? 'tablet' : 'desktop',
        os: getOSInfo(),
        browser: getBrowserInfo(),
        screen: isBrowser() ? { width: screen.width, height: screen.height } : null,
        memory: isBrowser() ? (navigator as any).deviceMemory || null : null,
        cores: isBrowser() ? navigator.hardwareConcurrency || null : null,
        connection: isBrowser() ? (navigator as any).connection?.effectiveType || null : null
    };
}

/**
 * 环境工具集合
 */
export const envUtils = {
    isBrowser,
    isNode,
    isWebWorker,
    isServiceWorker,
    isDevelopment,
    isProduction,
    isTest,
    getUserAgent,
    isMobile,
    isTablet,
    isDesktop,
    isIOS,
    isAndroid,
    isWindows,
    isMacOS,
    isLinux,
    getBrowserInfo,
    getOSInfo,
    supports,
    getDeviceInfo
};
