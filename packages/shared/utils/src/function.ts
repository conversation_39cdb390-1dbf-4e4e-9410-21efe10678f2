/**
 * @fileoverview 函数工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return function executedFunction(...args: Parameters<T>) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };

        const callNow = immediate && !timeout;

        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) func(...args);
    };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;

    return function executedFunction(...args: Parameters<T>) {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 记忆化函数
 */
export function memoize<T extends (...args: any[]) => any>(func: T): T {
    const cache = new Map();

    return ((...args: Parameters<T>) => {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = func(...args);
        cache.set(key, result);
        return result;
    }) as T;
}

/**
 * 函数组合
 */
export function compose<T>(...funcs: Array<(arg: T) => T>): (arg: T) => T {
    return (arg: T) => funcs.reduceRight((acc, func) => func(acc), arg);
}

/**
 * 函数管道
 */
export function pipe<T>(...funcs: Array<(arg: T) => T>): (arg: T) => T {
    return (arg: T) => funcs.reduce((acc, func) => func(acc), arg);
}

/**
 * 柯里化函数
 */
export function curry<T extends (...args: any[]) => any>(func: T): any {
    return function curried(...args: any[]) {
        if (args.length >= func.length) {
            return func(...args);
        }
        return (...nextArgs: any[]) => curried(...args, ...nextArgs);
    };
}

/**
 * 偏函数应用
 */
export function partial<T extends (...args: any[]) => any>(
    func: T,
    ...partialArgs: any[]
): (...args: any[]) => ReturnType<T> {
    return (...args: any[]) => func(...partialArgs, ...args);
}

/**
 * 函数包装器
 */
export function wrap<T extends (...args: any[]) => any>(
    func: T,
    wrapper: (originalFunc: T, ...args: Parameters<T>) => ReturnType<T>
): T {
    return ((...args: Parameters<T>) => wrapper(func, ...args)) as T;
}

/**
 * 函数执行次数限制
 */
export function once<T extends (...args: any[]) => any>(func: T): T {
    let called = false;
    let result: ReturnType<T>;

    return ((...args: Parameters<T>) => {
        if (!called) {
            called = true;
            result = func(...args);
        }
        return result;
    }) as T;
}

/**
 * 函数执行前置条件
 */
export function before<T extends (...args: any[]) => any>(
    n: number,
    func: T
): T {
    let count = 0;
    let result: ReturnType<T>;

    return ((...args: Parameters<T>) => {
        if (count < n) {
            count++;
            result = func(...args);
        }
        return result;
    }) as T;
}

/**
 * 函数执行后置条件
 */
export function after<T extends (...args: any[]) => any>(
    n: number,
    func: T
): (...args: Parameters<T>) => ReturnType<T> | undefined {
    let count = 0;

    return (...args: Parameters<T>) => {
        count++;
        if (count >= n) {
            return func(...args);
        }
    };
}

/**
 * 函数重试
 */
export function retry<T extends (...args: any[]) => any>(
    func: T,
    maxAttempts = 3,
    delay = 1000
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    return async (...args: Parameters<T>) => {
        let lastError: Error;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await func(...args);
            } catch (error) {
                lastError = error as Error;
                if (attempt < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, delay * attempt));
                }
            }
        }

        throw lastError!;
    };
}

/**
 * 函数超时控制
 */
export function timeout<T extends (...args: any[]) => any>(
    func: T,
    ms: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    return async (...args: Parameters<T>) => {
        return Promise.race([
            Promise.resolve(func(...args)),
            new Promise<never>((_, reject) => {
                setTimeout(() => reject(new Error('Function timeout')), ms);
            })
        ]);
    };
}

/**
 * 函数缓存装饰器
 */
export function cached<T extends (...args: any[]) => any>(
    func: T,
    keyGenerator?: (...args: Parameters<T>) => string
): T {
    const cache = new Map();
    const defaultKeyGenerator = (...args: Parameters<T>) => JSON.stringify(args);
    const getKey = keyGenerator || defaultKeyGenerator;

    return ((...args: Parameters<T>) => {
        const key = getKey(...args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = func(...args);
        cache.set(key, result);
        return result;
    }) as T;
}

/**
 * 函数执行时间测量
 */
export function timed<T extends (...args: any[]) => any>(
    func: T,
    onComplete?: (duration: number) => void
): T {
    return ((...args: Parameters<T>) => {
        const start = performance.now();
        const result = func(...args);
        const duration = performance.now() - start;
        
        if (onComplete) {
            onComplete(duration);
        }
        
        return result;
    }) as T;
}

/**
 * 异步函数串行执行
 */
export async function series<T>(
    funcs: Array<() => Promise<T>>
): Promise<T[]> {
    const results: T[] = [];
    for (const func of funcs) {
        results.push(await func());
    }
    return results;
}

/**
 * 异步函数并行执行
 */
export async function parallel<T>(
    funcs: Array<() => Promise<T>>
): Promise<T[]> {
    return Promise.all(funcs.map(func => func()));
}

/**
 * 异步函数并发控制
 */
export async function concurrent<T>(
    funcs: Array<() => Promise<T>>,
    limit = 5
): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (const func of funcs) {
        const promise = func().then(result => {
            results.push(result);
        });

        executing.push(promise);

        if (executing.length >= limit) {
            await Promise.race(executing);
            executing.splice(executing.findIndex(p => p === promise), 1);
        }
    }

    await Promise.all(executing);
    return results;
}

/**
 * 函数工具集合
 */
export const functionUtils = {
    debounce,
    throttle,
    memoize,
    compose,
    pipe,
    curry,
    partial,
    wrap,
    once,
    before,
    after,
    retry,
    timeout,
    cached,
    timed,
    series,
    parallel,
    concurrent
};
