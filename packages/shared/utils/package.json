{"name": "@micro-core/utils", "version": "0.1.0", "description": "Utility functions for Micro-Core", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage"}, "keywords": ["utils", "utilities", "micro-core", "typescript"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/utils"}, "publishConfig": {"access": "public"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "tsup": "^8.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}}