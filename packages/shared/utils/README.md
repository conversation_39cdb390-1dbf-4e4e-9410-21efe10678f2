# @micro-core/utils

Utility functions for Micro-Core projects.

## Installation

```bash
npm install @micro-core/utils
```

## Usage

```typescript
import { 
  // String utilities
  camelCase, 
  kebabCase, 
  snakeCase,
  pascalCase,
  capitalize,
  
  // Object utilities
  deepMerge,
  deepClone,
  get,
  set,
  has,
  
  // Type checking utilities
  isObject,
  isArray,
  isFunction,
  isString,
  isNumber,
  isBoolean,
  isPromise,
  
  // Array utilities
  unique,
  flatten,
  chunk,
  groupBy
} from '@micro-core/utils';

// String utilities
const camelCased = camelCase('hello-world'); // 'helloWorld'
const kebabCased = kebabCase('helloWorld'); // 'hello-world'

// Object utilities
const merged = deepMerge({ a: 1 }, { b: 2 }); // { a: 1, b: 2 }
const cloned = deepClone({ a: { b: 1 } });

// Type checking
if (isObject(value)) {
  // value is an object
}

// Array utilities
const uniqueItems = unique([1, 2, 2, 3]); // [1, 2, 3]
const flattened = flatten([[1, 2], [3, 4]]); // [1, 2, 3, 4]
```

## API Reference

### String Utilities

- `camelCase(str: string): string` - Convert string to camelCase
- `kebabCase(str: string): string` - Convert string to kebab-case
- `snakeCase(str: string): string` - Convert string to snake_case
- `pascalCase(str: string): string` - Convert string to PascalCase
- `capitalize(str: string): string` - Capitalize first letter
- `uncapitalize(str: string): string` - Uncapitalize first letter
- `truncate(str: string, length: number): string` - Truncate string
- `words(str: string): string[]` - Split string into words
- `similarity(str1: string, str2: string): number` - Calculate string similarity
- `random(length?: number, chars?: string): string` - Generate random string
- `uuid(): string` - Generate UUID
- `formatBytes(bytes: number, decimals?: number): string` - Format bytes
- `formatDate(date: Date, format?: string): string` - Format date

### Object Utilities

- `deepMerge<T>(...objects: Partial<T>[]): T` - Deep merge objects
- `deepClone<T>(obj: T): T` - Deep clone object
- `get(obj: any, path: string, defaultValue?: any): any` - Get nested property
- `set(obj: any, path: string, value: any): void` - Set nested property
- `has(obj: any, path: string): boolean` - Check if nested property exists
- `pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>` - Pick properties
- `omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>` - Omit properties
- `keys<T>(obj: T): (keyof T)[]` - Get object keys
- `values<T>(obj: T): T[keyof T][]` - Get object values
- `entries<T>(obj: T): [keyof T, T[keyof T]][]` - Get object entries

### Type Checking Utilities

- `isObject(value: any): value is object` - Check if value is object
- `isArray(value: any): value is any[]` - Check if value is array
- `isFunction(value: any): value is Function` - Check if value is function
- `isString(value: any): value is string` - Check if value is string
- `isNumber(value: any): value is number` - Check if value is number
- `isBoolean(value: any): value is boolean` - Check if value is boolean
- `isPromise(value: any): value is Promise<any>` - Check if value is promise
- `isEmpty(value: any): boolean` - Check if value is empty
- `isArrayLike(value: any): boolean` - Check if value is array-like

### Array Utilities

- `unique<T>(array: T[]): T[]` - Remove duplicates
- `flatten<T>(array: (T | T[])[]): T[]` - Flatten array
- `chunk<T>(array: T[], size: number): T[][]` - Split array into chunks
- `groupBy<T>(array: T[], key: keyof T | ((item: T) => any)): Record<string, T[]>` - Group array by key

## Features

- ✅ Full TypeScript support
- ✅ Tree-shakeable ESM modules
- ✅ CommonJS compatibility
- ✅ Comprehensive test coverage
- ✅ Zero dependencies
- ✅ Optimized for performance

## License

MIT
