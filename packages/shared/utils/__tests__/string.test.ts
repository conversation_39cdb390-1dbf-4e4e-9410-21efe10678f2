/**
 * @fileoverview 字符串工具函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import {
    byteLength,
    camelCase,
    capitalize,
    endsWithIgnoreCase,
    escapeHtml,
    escapeRegExp,
    formatBytes,
    formatDate,
    includesIgnoreCase,
    kebabCase,
    pad,
    padEnd,
    padStart,
    pascalCase,
    random,
    repeat,
    reverse,
    similarity,
    snakeCase,
    split,
    startsWithIgnoreCase,
    stringUtils,
    template,
    trim,
    trimEnd,
    trimStart,
    truncate,
    uncapitalize,
    unescapeHtml,
    uuid,
    words
} from '../src/string';

describe('字符串工具函数', () => {
    describe('camelCase', () => {
        it('应该转换为驼峰命名', () => {
            expect(camelCase('hello-world')).toBe('helloWorld');
            expect(camelCase('hello_world')).toBe('helloWorld');
            expect(camelCase('hello world')).toBe('helloWorld');
            expect(camelCase('HELLO-WORLD')).toBe('helloWorld');
        });

        it('应该处理空字符串', () => {
            expect(camelCase('')).toBe('');
        });
    });

    describe('kebabCase', () => {
        it('应该转换为短横线命名', () => {
            expect(kebabCase('helloWorld')).toBe('hello-world');
            expect(kebabCase('HelloWorld')).toBe('hello-world');
            expect(kebabCase('hello_world')).toBe('hello_world');
        });
    });

    describe('snakeCase', () => {
        it('应该转换为下划线命名', () => {
            expect(snakeCase('helloWorld')).toBe('hello_world');
            expect(snakeCase('HelloWorld')).toBe('hello_world');
            expect(snakeCase('hello-world')).toBe('hello-world');
        });
    });

    describe('pascalCase', () => {
        it('应该转换为帕斯卡命名', () => {
            expect(pascalCase('hello-world')).toBe('HelloWorld');
            expect(pascalCase('hello_world')).toBe('HelloWorld');
            expect(pascalCase('hello world')).toBe('HelloWorld');
        });
    });

    describe('capitalize', () => {
        it('应该首字母大写', () => {
            expect(capitalize('hello')).toBe('Hello');
            expect(capitalize('HELLO')).toBe('HELLO');
            expect(capitalize('')).toBe('');
        });
    });

    describe('uncapitalize', () => {
        it('应该首字母小写', () => {
            expect(uncapitalize('Hello')).toBe('hello');
            expect(uncapitalize('HELLO')).toBe('hELLO');
            expect(uncapitalize('')).toBe('');
        });
    });

    describe('truncate', () => {
        it('应该截断字符串', () => {
            expect(truncate('hello world', 5)).toBe('he...');
            expect(truncate('hello', 10)).toBe('hello');
            expect(truncate('hello world', 8, '---')).toBe('hello---');
        });
    });

    describe('pad', () => {
        it('应该填充字符串', () => {
            expect(pad('hello', 10)).toBe('  hello   ');
            expect(pad('hello', 10, '*')).toBe('**hello***');
            expect(pad('hello', 3)).toBe('hello');
        });
    });

    describe('padStart', () => {
        it('应该左填充字符串', () => {
            expect(padStart('hello', 10)).toBe('     hello');
            expect(padStart('hello', 10, '*')).toBe('*****hello');
        });
    });

    describe('padEnd', () => {
        it('应该右填充字符串', () => {
            expect(padEnd('hello', 10)).toBe('hello     ');
            expect(padEnd('hello', 10, '*')).toBe('hello*****');
        });
    });

    describe('template', () => {
        it('应该替换模板字符串', () => {
            const template1 = 'Hello {{name}}, you are {{age}} years old';
            const data = { name: 'John', age: 30 };
            expect(template(template1, data)).toBe('Hello John, you are 30 years old');
        });

        it('应该保留未匹配的占位符', () => {
            const template1 = 'Hello {{name}}, {{missing}}';
            const data = { name: 'John' };
            expect(template(template1, data)).toBe('Hello John, {{missing}}');
        });
    });

    describe('reverse', () => {
        it('应该反转字符串', () => {
            expect(reverse('hello')).toBe('olleh');
            expect(reverse('12345')).toBe('54321');
            expect(reverse('')).toBe('');
        });
    });

    describe('repeat', () => {
        it('应该重复字符串', () => {
            expect(repeat('hello', 3)).toBe('hellohellohello');
            expect(repeat('a', 0)).toBe('');
            expect(repeat('test', -1)).toBe('');
        });
    });

    describe('trim', () => {
        it('应该移除空白字符', () => {
            expect(trim('  hello  ')).toBe('hello');
            expect(trim('***hello***', '*')).toBe('hello');
        });
    });

    describe('trimStart', () => {
        it('应该移除左端字符', () => {
            expect(trimStart('  hello  ')).toBe('hello  ');
            expect(trimStart('***hello***', '*')).toBe('hello***');
        });
    });

    describe('trimEnd', () => {
        it('应该移除右端字符', () => {
            expect(trimEnd('  hello  ')).toBe('  hello');
            expect(trimEnd('***hello***', '*')).toBe('***hello');
        });
    });

    describe('escapeRegExp', () => {
        it('应该转义正则表达式特殊字符', () => {
            expect(escapeRegExp('hello.world')).toBe('hello\\.world');
            expect(escapeRegExp('test[123]')).toBe('test\\[123\\]');
            expect(escapeRegExp('a+b*c?')).toBe('a\\+b\\*c\\?');
        });
    });

    describe('escapeHtml', () => {
        it('应该转义HTML特殊字符', () => {
            expect(escapeHtml('<div>Hello & "World"</div>')).toBe('&lt;div&gt;Hello &amp; &quot;World&quot;&lt;/div&gt;');
            expect(escapeHtml("It's a 'test'")).toBe('It&#39;s a &#39;test&#39;');
        });
    });

    describe('unescapeHtml', () => {
        it('应该反转义HTML特殊字符', () => {
            expect(unescapeHtml('&lt;div&gt;Hello &amp; &quot;World&quot;&lt;/div&gt;')).toBe('<div>Hello & "World"</div>');
            expect(unescapeHtml('It&#39;s a test')).toBe("It's a test");
        });
    });

    describe('split', () => {
        it('应该按单个分隔符分割', () => {
            expect(split('a,b,c', ',')).toEqual(['a', 'b', 'c']);
        });

        it('应该按多个分隔符分割', () => {
            expect(split('a,b;c:d', [',', ';', ':'])).toEqual(['a', 'b', 'c', 'd']);
        });

        it('应该支持限制数量', () => {
            expect(split('a,b,c,d', ',', 2)).toEqual(['a', 'b']);
        });
    });

    describe('words', () => {
        it('应该提取单词', () => {
            expect(words('hello world test')).toEqual(['hello', 'world', 'test']);
            expect(words('hello, world! test?')).toEqual(['hello', 'world', 'test']);
        });
    });

    describe('includesIgnoreCase', () => {
        it('应该忽略大小写检查包含', () => {
            expect(includesIgnoreCase('Hello World', 'WORLD')).toBe(true);
            expect(includesIgnoreCase('Hello World', 'xyz')).toBe(false);
        });
    });

    describe('startsWithIgnoreCase', () => {
        it('应该忽略大小写检查开始', () => {
            expect(startsWithIgnoreCase('Hello World', 'HELLO')).toBe(true);
            expect(startsWithIgnoreCase('Hello World', 'WORLD')).toBe(false);
        });
    });

    describe('endsWithIgnoreCase', () => {
        it('应该忽略大小写检查结束', () => {
            expect(endsWithIgnoreCase('Hello World', 'WORLD')).toBe(true);
            expect(endsWithIgnoreCase('Hello World', 'HELLO')).toBe(false);
        });
    });

    describe('byteLength', () => {
        it('应该计算字节长度', () => {
            expect(byteLength('hello')).toBe(5);
            expect(byteLength('你好')).toBe(6); // UTF-8编码
        });
    });

    describe('similarity', () => {
        it('应该计算字符串相似度', () => {
            expect(similarity('hello', 'hello')).toBe(1);
            expect(similarity('hello', 'world')).toBeLessThan(0.5);
            expect(similarity('', '')).toBe(1);
        });
    });

    describe('random', () => {
        it('应该生成随机字符串', () => {
            const result = random(10);
            expect(result).toHaveLength(10);
            expect(typeof result).toBe('string');
        });

        it('应该使用自定义字符集', () => {
            const result = random(5, '123');
            expect(result).toHaveLength(5);
            expect(/^[123]+$/.test(result)).toBe(true);
        });
    });

    describe('uuid', () => {
        it('应该生成UUID', () => {
            const result = uuid();
            expect(result).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
        });

        it('应该生成唯一的UUID', () => {
            const uuid1 = uuid();
            const uuid2 = uuid();
            expect(uuid1).not.toBe(uuid2);
        });
    });

    describe('formatBytes', () => {
        it('应该格式化字节数', () => {
            expect(formatBytes(0)).toBe('0 Bytes');
            expect(formatBytes(1024)).toBe('1 KB');
            expect(formatBytes(1048576)).toBe('1 MB');
            expect(formatBytes(1073741824)).toBe('1 GB');
        });

        it('应该支持小数位数', () => {
            expect(formatBytes(1536, 1)).toBe('1.5 KB');
            expect(formatBytes(1536, 0)).toBe('2 KB');
        });
    });

    describe('formatDate', () => {
        it('应该格式化日期', () => {
            // 使用本地时间而不是UTC时间来避免时区问题
            const date = new Date(2023, 0, 1, 12, 30, 45, 123); // 本地时间
            expect(formatDate(date, 'YYYY-MM-DD')).toBe('2023-01-01');
            expect(formatDate(date, 'HH:mm:ss')).toBe('12:30:45');
        });
    });

    describe('stringUtils 对象', () => {
        it('应该包含所有字符串工具函数', () => {
            expect(stringUtils.camelCase).toBe(camelCase);
            expect(stringUtils.kebabCase).toBe(kebabCase);
            expect(stringUtils.snakeCase).toBe(snakeCase);
            expect(stringUtils.capitalize).toBe(capitalize);
            expect(stringUtils.truncate).toBe(truncate);
        });
    });
});
