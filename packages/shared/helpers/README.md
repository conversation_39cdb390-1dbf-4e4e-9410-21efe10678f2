# @micro-core/helpers

Helper functions for Micro-Core projects.

## Installation

```bash
npm install @micro-core/helpers
```

## Usage

```typescript
import {
  // Error handling
  formatErrorMessage,
  safeExecute,
  
  // Configuration
  validateAppConfig,
  normalizeAppConfig,
  
  // Performance
  createPerformanceTimer,
  getMemoryInfo,
  createMemoryMonitor,
  
  // Environment
  checkBrowserCompatibility,
  detectEnvironmentFeatures
} from '@micro-core/helpers';

// Error handling
const error = new Error('Something went wrong');
const formatted = formatErrorMessage(error, { userId: 123 });
console.log(formatted);

// Safe execution
const result = await safeExecute(
  () => riskyOperation(),
  'fallback value',
  { retries: 3, timeout: 5000 }
);

// Configuration validation
const config = { name: 'my-app', entry: 'https://example.com/app.js' };
const errors = validateAppConfig(config);
if (errors.length === 0) {
  const normalized = normalizeAppConfig(config);
}

// Performance monitoring
const timer = createPerformanceTimer();
timer.start('operation');
// ... do something
timer.end('operation');
console.log(timer.getMetrics());

// Browser compatibility
const compatibility = checkBrowserCompatibility();
if (!compatibility.compatible) {
  console.warn('Missing features:', compatibility.missing);
}
```

## API Reference

### Error Handling

#### formatErrorMessage(error, context?)
Formats error messages with optional context:
```typescript
function formatErrorMessage(
  error: Error | string, 
  context?: Record<string, any>
): string
```

#### safeExecute(fn, fallback?, options?)
Safely executes a function with error handling:
```typescript
function safeExecute<T>(
  fn: () => T | Promise<T>,
  fallback?: T,
  options?: {
    retries?: number;
    timeout?: number;
    onError?: (error: Error) => void;
  }
): Promise<T>
```

### Configuration

#### validateAppConfig(config)
Validates micro-app configuration:
```typescript
function validateAppConfig(config: any): string[]
```

#### normalizeAppConfig(config)
Normalizes micro-app configuration with defaults:
```typescript
function normalizeAppConfig(
  config: MicroAppConfig
): Required<Omit<MicroAppConfig, 'entry' | 'activeRule'>> & { 
  entry: string; 
  activeRule: string 
}
```

### Performance

#### createPerformanceTimer()
Creates a performance timer for measuring operations:
```typescript
function createPerformanceTimer(): {
  start(name: string): void;
  end(name: string): number;
  getMetrics(): Record<string, number>;
  clear(): void;
}
```

#### getMemoryInfo()
Gets current memory usage information:
```typescript
function getMemoryInfo(): {
  used: number;
  total: number;
  percentage: number;
} | null
```

#### createMemoryMonitor(options?)
Creates a memory usage monitor:
```typescript
function createMemoryMonitor(options?: {
  interval?: number;
  threshold?: number;
  onThresholdExceeded?: (info: MemoryInfo) => void;
}): {
  start(): void;
  stop(): void;
  getHistory(): MemoryInfo[];
}
```

### Environment Detection

#### checkBrowserCompatibility(requirements?)
Checks browser compatibility:
```typescript
function checkBrowserCompatibility(requirements?: {
  es6?: boolean;
  fetch?: boolean;
  proxy?: boolean;
  customElements?: boolean;
  shadowDOM?: boolean;
}): {
  compatible: boolean;
  missing: string[];
}
```

#### detectEnvironmentFeatures()
Detects available environment features:
```typescript
function detectEnvironmentFeatures(): {
  // ES6 features
  es6: boolean;
  modules: boolean;
  classes: boolean;
  
  // Web APIs
  fetch: boolean;
  localStorage: boolean;
  sessionStorage: boolean;
  
  // Advanced features
  serviceWorker: boolean;
  webWorkers: boolean;
  
  // Security
  crypto: boolean;
  secureContext: boolean;
}
```

### Utility Functions

#### helpers
Object containing all helper functions for convenient access:
```typescript
const helpers = {
  formatErrorMessage,
  validateAppConfig,
  normalizeAppConfig,
  createPerformanceTimer,
  checkBrowserCompatibility,
  safeExecute
};
```

## Features

- ✅ Comprehensive error handling
- ✅ Configuration validation and normalization
- ✅ Performance monitoring utilities
- ✅ Browser compatibility detection
- ✅ Safe execution with retries and timeouts
- ✅ Memory usage monitoring
- ✅ Full TypeScript support
- ✅ Zero external dependencies

## License

MIT
