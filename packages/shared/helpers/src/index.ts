/**
 * @fileoverview 共享辅助函数 - 高级业务逻辑辅助工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '../../utils/src/logger';
import { isFunction, isObject, isString } from '../../utils/src/type-check';

const logger = createLogger('micro-core:helpers');

/**
 * 错误信息格式化器
 */
export function formatErrorMessage(error: Error | string, context?: Record<string, any>): string {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'object' && error.stack ? error.stack : '';

    let formatted = `错误: ${errorMessage}`;

    if (context) {
        const contextStr = Object.entries(context)
            .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
            .join(', ');
        formatted += ` | 上下文: ${contextStr}`;
    }

    if (errorStack && process.env.NODE_ENV === 'development') {
        formatted += `\n堆栈: ${errorStack}`;
    }

    return formatted;
}

/**
 * 应用配置验证器
 */
export function validateAppConfig(config: any): string[] {
    const errors: string[] = [];

    if (!isObject(config)) {
        errors.push('配置必须是一个对象');
        return errors;
    }

    if (!isString(config.name) || config.name.trim() === '') {
        errors.push('应用名称(name)必须是非空字符串');
    }

    if (!isString(config.entry) || config.entry.trim() === '') {
        errors.push('应用入口(entry)必须是非空字符串');
    }

    if (config.container && !isString(config.container)) {
        errors.push('容器选择器(container)必须是字符串');
    }

    if (config.activeRule && !isString(config.activeRule) && !isFunction(config.activeRule)) {
        errors.push('激活规则(activeRule)必须是字符串或函数');
    }

    if (config.props && !isObject(config.props)) {
        errors.push('应用属性(props)必须是对象');
    }

    return errors;
}

/**
 * 应用配置规范化器
 */
export function normalizeAppConfig(config: MicroAppConfig): Required<Omit<MicroAppConfig, 'entry' | 'activeRule'>> & { entry: string; activeRule: string } {
    const normalizedName = config.name.trim();
    return {
        name: normalizedName,
        entry: config.entry.trim(),
        container: config.container || `#${normalizedName}`,
        activeRule: config.activeRule || `/${normalizedName}`,
        props: config.props || {}
    };
}

/**
 * 性能计时器
 */
export interface PerformanceTimer {
    start(): void;
    end(): number;
    reset(): void;
    getElapsed(): number;
}

export function createPerformanceTimer(label?: string): PerformanceTimer {
    let startTime = 0;
    let endTime = 0;
    const timerLabel = label || `timer-${Date.now()}`;

    return {
        start() {
            startTime = performance.now();
            if (label) {
                logger.debug(`性能计时开始: ${timerLabel}`);
            }
        },

        end() {
            endTime = performance.now();
            const elapsed = endTime - startTime;
            if (label) {
                logger.debug(`性能计时结束: ${timerLabel}, 耗时: ${elapsed.toFixed(2)}ms`);
            }
            return elapsed;
        },

        reset() {
            startTime = 0;
            endTime = 0;
        },

        getElapsed() {
            const currentTime = endTime || performance.now();
            return startTime ? currentTime - startTime : 0;
        }
    };
}

/**
 * 浏览器兼容性检查器
 */
export interface CompatibilityResult {
    compatible: boolean;
    missing: string[];
    warnings: string[];
}

export function checkBrowserCompatibility(requirements?: {
    es6?: boolean;
    proxy?: boolean;
    customElements?: boolean;
    shadowDOM?: boolean;
    webComponents?: boolean;
    fetch?: boolean;
    promise?: boolean;
}): CompatibilityResult {
    const result: CompatibilityResult = {
        compatible: true,
        missing: [],
        warnings: []
    };

    const defaultRequirements = {
        es6: true,
        proxy: true,
        customElements: false,
        shadowDOM: false,
        webComponents: false,
        fetch: true,
        promise: true,
        ...requirements
    };

    // 检查 ES6 支持
    if (defaultRequirements.es6) {
        try {
            new Function('(a = 0) => a');
        } catch {
            result.missing.push('ES6 Arrow Functions');
            result.compatible = false;
        }
    }

    // 检查 Proxy 支持
    if (defaultRequirements.proxy && typeof Proxy === 'undefined') {
        result.missing.push('Proxy');
        result.compatible = false;
    }

    // 检查 Custom Elements 支持
    if (defaultRequirements.customElements && typeof customElements === 'undefined') {
        result.missing.push('Custom Elements');
        result.compatible = false;
    }

    // 检查 Shadow DOM 支持
    if (defaultRequirements.shadowDOM && typeof ShadowRoot === 'undefined') {
        result.missing.push('Shadow DOM');
        result.compatible = false;
    }

    // 检查 Fetch 支持
    if (defaultRequirements.fetch && typeof fetch === 'undefined') {
        result.missing.push('Fetch API');
        result.compatible = false;
    }

    // 检查 Promise 支持
    if (defaultRequirements.promise && typeof Promise === 'undefined') {
        result.missing.push('Promise');
        result.compatible = false;
    }

    return result;
}

/**
 * 资源加载器
 */
export interface ResourceLoadOptions {
    timeout?: number;
    retries?: number;
    cache?: boolean;
    integrity?: string;
    crossOrigin?: string;
}

export async function loadResource(
    url: string,
    type: 'script' | 'style' | 'json' | 'text' = 'script',
    options: ResourceLoadOptions = {}
): Promise<any> {
    const {
        timeout = 30000,
        retries = 3,
        cache = true,
        integrity,
        crossOrigin
    } = options;

    const cacheKey = `resource:${url}`;

    // 检查缓存
    if (cache && typeof sessionStorage !== 'undefined') {
        try {
            const cached = sessionStorage.getItem(cacheKey);
            if (cached) {
                logger.debug(`从缓存加载资源: ${url}`);
                return JSON.parse(cached);
            }
        } catch (error) {
            logger.warn(`缓存读取失败: ${url}`, error);
        }
    }

    let lastError: Error;

    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            logger.debug(`加载资源 (尝试 ${attempt}/${retries}): ${url}`);

            let result: any;

            switch (type) {
                case 'script':
                    result = await loadScriptResource(url, { timeout, integrity, crossOrigin });
                    break;
                case 'style':
                    result = await loadStyleResource(url, { timeout, integrity, crossOrigin });
                    break;
                case 'json':
                case 'text':
                    result = await loadDataResource(url, type, { timeout });
                    break;
                default:
                    throw new Error(`不支持的资源类型: ${type}`);
            }

            // 缓存结果
            if (cache && typeof sessionStorage !== 'undefined') {
                try {
                    sessionStorage.setItem(cacheKey, JSON.stringify(result));
                } catch (error) {
                    logger.warn(`缓存写入失败: ${url}`, error);
                }
            }

            logger.debug(`资源加载成功: ${url}`);
            return result;

        } catch (error) {
            lastError = error as Error;
            logger.warn(`资源加载失败 (尝试 ${attempt}/${retries}): ${url}`, error);

            if (attempt < retries) {
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    throw new Error(`资源加载失败，已重试 ${retries} 次: ${url}. 最后错误: ${lastError.message}`);
}

async function loadScriptResource(
    url: string,
    options: { timeout: number; integrity?: string; crossOrigin?: string }
): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = url;
        script.async = true;

        if (options.integrity) {
            script.integrity = options.integrity;
        }

        if (options.crossOrigin) {
            script.crossOrigin = options.crossOrigin;
        }

        const timeoutId = setTimeout(() => {
            script.remove();
            reject(new Error(`脚本加载超时: ${url}`));
        }, options.timeout);

        script.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        script.onerror = () => {
            clearTimeout(timeoutId);
            script.remove();
            reject(new Error(`脚本加载失败: ${url}`));
        };

        document.head.appendChild(script);
    });
}

async function loadStyleResource(
    url: string,
    options: { timeout: number; integrity?: string; crossOrigin?: string }
): Promise<void> {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;

        if (options.integrity) {
            link.integrity = options.integrity;
        }

        if (options.crossOrigin) {
            link.crossOrigin = options.crossOrigin;
        }

        const timeoutId = setTimeout(() => {
            link.remove();
            reject(new Error(`样式加载超时: ${url}`));
        }, options.timeout);

        link.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        link.onerror = () => {
            clearTimeout(timeoutId);
            link.remove();
            reject(new Error(`样式加载失败: ${url}`));
        };

        document.head.appendChild(link);
    });
}

async function loadDataResource(
    url: string,
    type: 'json' | 'text',
    options: { timeout: number }
): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout);

    try {
        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'Accept': type === 'json' ? 'application/json' : 'text/plain'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return type === 'json' ? await response.json() : await response.text();
    } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
            throw new Error(`数据加载超时: ${url}`);
        }
        throw error;
    }
}

// safeExecute 函数已移至 utils/async.ts，避免重复定义

/**
 * 输入验证和清理器
 */
export interface ValidationRule<T = any> {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: T) => boolean | string;
}

export interface ValidationResult {
    valid: boolean;
    errors: string[];
    sanitized?: any;
}

export function validateAndSanitize(
    data: any,
    rules: Record<string, ValidationRule>,
    options: { strict?: boolean; sanitize?: boolean } = {}
): ValidationResult {
    const { strict = false, sanitize = true } = options;
    const result: ValidationResult = {
        valid: true,
        errors: [],
        sanitized: sanitize ? {} : undefined
    };

    // 检查必需字段
    for (const [key, rule] of Object.entries(rules)) {
        if (rule.required && (data[key] === undefined || data[key] === null)) {
            result.errors.push(`字段 '${key}' 是必需的`);
            result.valid = false;
            continue;
        }

        const value = data[key];
        if (value === undefined || value === null) {
            continue;
        }

        // 类型检查
        if (rule.type) {
            const actualType = Array.isArray(value) ? 'array' : typeof value;
            if (actualType !== rule.type) {
                result.errors.push(`字段 '${key}' 应该是 ${rule.type} 类型，实际是 ${actualType}`);
                result.valid = false;
                if (strict) continue;
            }
        }

        // 字符串验证
        if (rule.type === 'string' && typeof value === 'string') {
            if (rule.minLength && value.length < rule.minLength) {
                result.errors.push(`字段 '${key}' 长度不能少于 ${rule.minLength} 个字符`);
                result.valid = false;
            }
            if (rule.maxLength && value.length > rule.maxLength) {
                result.errors.push(`字段 '${key}' 长度不能超过 ${rule.maxLength} 个字符`);
                result.valid = false;
            }
            if (rule.pattern && !rule.pattern.test(value)) {
                result.errors.push(`字段 '${key}' 格式不正确`);
                result.valid = false;
            }
        }

        // 数字验证
        if (rule.type === 'number' && typeof value === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                result.errors.push(`字段 '${key}' 不能小于 ${rule.min}`);
                result.valid = false;
            }
            if (rule.max !== undefined && value > rule.max) {
                result.errors.push(`字段 '${key}' 不能大于 ${rule.max}`);
                result.valid = false;
            }
        }

        // 自定义验证
        if (rule.custom) {
            const customResult = rule.custom(value);
            if (customResult !== true) {
                const errorMessage = typeof customResult === 'string'
                    ? customResult
                    : `字段 '${key}' 验证失败`;
                result.errors.push(errorMessage);
                result.valid = false;
            }
        }

        // 数据清理
        if (sanitize && result.sanitized) {
            if (rule.type === 'string' && typeof value === 'string') {
                // 清理字符串：去除首尾空格，转义HTML
                result.sanitized[key] = value.trim().replace(/[<>]/g, '');
            } else {
                result.sanitized[key] = value;
            }
        }
    }

    return result;
}

/**
 * 内存监控器
 */
export interface MemoryInfo {
    used: number;
    total: number;
    percentage: number;
    warning: boolean;
}

export function getMemoryInfo(): MemoryInfo | null {
    if (typeof performance === 'undefined' || !('memory' in performance)) {
        return null;
    }

    const memory = (performance as any).memory;
    const used = memory.usedJSHeapSize;
    const total = memory.totalJSHeapSize;
    const percentage = (used / total) * 100;

    return {
        used,
        total,
        percentage,
        warning: percentage > 80 // 内存使用超过80%时警告
    };
}

/**
 * 创建内存监控器
 */
export function createMemoryMonitor(options: {
    interval?: number;
    threshold?: number;
    onWarning?: (info: MemoryInfo) => void;
    onCritical?: (info: MemoryInfo) => void;
} = {}) {
    const {
        interval = 5000,
        threshold = 80,
        onWarning,
        onCritical
    } = options;

    let intervalId: NodeJS.Timeout | null = null;
    let lastWarningTime = 0;

    return {
        start() {
            if (intervalId) return;

            intervalId = setInterval(() => {
                const memInfo = getMemoryInfo();
                if (!memInfo) return;

                const now = Date.now();

                if (memInfo.percentage > 90 && onCritical) {
                    // 临界状态，立即通知
                    onCritical(memInfo);
                } else if (memInfo.percentage > threshold && onWarning) {
                    // 警告状态，但避免频繁通知
                    if (now - lastWarningTime > 30000) { // 30秒内最多通知一次
                        onWarning(memInfo);
                        lastWarningTime = now;
                    }
                }
            }, interval);

            logger.debug('内存监控器已启动');
        },

        stop() {
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
                logger.debug('内存监控器已停止');
            }
        },

        getCurrentInfo() {
            return getMemoryInfo();
        }
    };
}

/**
 * 环境特性检测器
 */
export interface EnvironmentFeatures {
    // 基础特性
    es6: boolean;
    es2017: boolean;
    es2020: boolean;

    // Web APIs
    fetch: boolean;
    webWorkers: boolean;
    serviceWorkers: boolean;
    webAssembly: boolean;

    // 存储
    localStorage: boolean;
    sessionStorage: boolean;
    indexedDB: boolean;

    // 网络
    onlineStatus: boolean;
    connectionInfo: boolean;

    // 设备
    deviceMemory: boolean;
    hardwareConcurrency: boolean;

    // 安全
    crypto: boolean;
    secureContext: boolean;
}

export function detectEnvironmentFeatures(): EnvironmentFeatures {
    const features: EnvironmentFeatures = {
        // 基础特性检测
        es6: (() => {
            try {
                new Function('(a = 0) => a');
                return true;
            } catch {
                return false;
            }
        })(),

        es2017: (() => {
            try {
                new Function('async () => {}');
                return true;
            } catch {
                return false;
            }
        })(),

        es2020: (() => {
            try {
                new Function('a?.b');
                return true;
            } catch {
                return false;
            }
        })(),

        // Web APIs
        fetch: typeof fetch !== 'undefined',
        webWorkers: typeof Worker !== 'undefined',
        serviceWorkers: 'serviceWorker' in navigator,
        webAssembly: typeof WebAssembly !== 'undefined',

        // 存储
        localStorage: (() => {
            try {
                return typeof localStorage !== 'undefined' && localStorage !== null;
            } catch {
                return false;
            }
        })(),

        sessionStorage: (() => {
            try {
                return typeof sessionStorage !== 'undefined' && sessionStorage !== null;
            } catch {
                return false;
            }
        })(),

        indexedDB: 'indexedDB' in window,

        // 网络
        onlineStatus: 'onLine' in navigator,
        connectionInfo: 'connection' in navigator,

        // 设备
        deviceMemory: 'deviceMemory' in navigator,
        hardwareConcurrency: 'hardwareConcurrency' in navigator,

        // 安全
        crypto: typeof window !== 'undefined' && 'crypto' in window && 'subtle' in crypto,
        secureContext: typeof window !== 'undefined' && 'isSecureContext' in window ? window.isSecureContext :
            (typeof location !== 'undefined' ? location.protocol === 'https:' : false)
    };

    return features;
}

/**
 * 辅助函数工具集合
 */
export const helpers = {
    formatErrorMessage,
    validateAppConfig,
    normalizeAppConfig,
    createPerformanceTimer,
    checkBrowserCompatibility,
    loadResource,
    validateAndSanitize,
    getMemoryInfo,
    createMemoryMonitor,
    detectEnvironmentFeatures
};