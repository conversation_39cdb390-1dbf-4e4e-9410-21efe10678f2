# @micro-core/sidecar

> 提供 Sidecar 模式入口，实现一行代码接入微前端，简化主应用的集成过程

## 🚀 快速开始

### CDN 引入

```html
<!-- 在主应用 HTML 中引入 -->
<script src="//cdn.jsdelivr.net/npm/@micro-core/sidecar@0.1.0/dist/index.min.js"></script>
<script>
    window.MicroCoreSidecar.init({
        apps: [
            {
                name: 'app1',
                entry: '//localhost:8080/app1.js',
                container: '#app1-container',
                activeWhen: '/app1'
            }
        ]
    });
</script>
```

### NPM 安装

```bash
npm install @micro-core/sidecar
```

```javascript
import { init } from '@micro-core/sidecar';

init({
    apps: [
        {
            name: 'app1',
            entry: '//localhost:8080/app1.js',
            container: '#app1-container',
            activeWhen: '/app1'
        }
    ]
});
```

## 📖 核心特性

- **零配置接入**: 一行代码即可接入微前端架构
- **自动配置检测**: 从 HTML 属性或全局变量自动读取配置
- **兼容模式支持**: 适配旧版浏览器或特殊环境
- **传统应用迁移**: 提供 jQuery、原生 JS 等传统应用的迁移方案
- **渐进式升级**: 支持将传统应用逐步迁移到微前端架构

## 🏗️ 架构设计

```
@micro-core/sidecar
├── SidecarContainer     # 容器核心，初始化微内核和加载配置
├── AutoConfig          # 自动配置检测，从多种来源读取配置
├── CompatMode          # 兼容模式支持，适配特殊环境
├── Bridge System       # 通信桥接系统
├── Isolation System    # 隔离系统
└── Legacy Apps         # 传统应用迁移适配器
```

## 📋 API 文档

### init(options)

初始化 Sidecar 容器

**参数:**
- `options` (SidecarInitOptions): 初始化选项

**返回值:**
- `Promise<SidecarManager>`: Sidecar 管理器实例

**示例:**

```javascript
const sidecar = await init({
    name: 'my-micro-frontend',
    apps: [
        {
            name: 'header-app',
            entry: '//localhost:3001/app.js',
            container: '#header',
            activeWhen: () => true
        },
        {
            name: 'content-app',
            entry: '//localhost:3002/app.js',
            container: '#content',
            activeWhen: '/content'
        }
    ],
    autoConfig: true,
    compatMode: false
});
```

### SidecarInitOptions

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `name` | `string` | - | 应用名称 |
| `apps` | `MicroAppConfig[]` | `[]` | 应用配置列表 |
| `autoConfig` | `boolean` | `true` | 是否启用自动配置检测 |
| `compatMode` | `boolean` | `false` | 是否启用兼容模式 |
| `container` | `string \| HTMLElement` | `'#micro-app-container'` | 默认容器 |
| `autoStart` | `boolean` | `true` | 是否自动启动 |
| `preloadApps` | `string[]` | `[]` | 预加载应用列表 |

### MicroAppConfig

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `name` | `string` | - | 应用名称 |
| `entry` | `string` | - | 应用入口 URL |
| `container` | `string \| HTMLElement` | - | 容器选择器或元素 |
| `activeWhen` | `string \| Function` | - | 激活条件 |
| `customProps` | `Record<string, any>` | `{}` | 自定义属性 |

## 🔧 高级配置

### 自动配置检测

Sidecar 支持从多种来源自动检测配置：

1. **HTML 属性检测**:
```html
<div data-micro-app='{"name":"app1","entry":"//localhost:8080/app.js"}'></div>
```

2. **Meta 标签检测**:
```html
<meta name="micro-apps" content='[{"name":"app1","entry":"//localhost:8080/app.js"}]'>
```

3. **全局变量检测**:
```javascript
window.MICRO_APPS = [
    {
        name: 'app1',
        entry: '//localhost:8080/app.js',
        container: '#app1-container'
    }
];
```

### 兼容模式

针对旧版浏览器或特殊环境提供兼容性支持：

```javascript
init({
    compatMode: true,
    compatOptions: {
        polyfills: ['es6-promise', 'fetch'],
        fallbackContainer: '#fallback-container',
        errorHandler: (error) => {
            console.error('Compatibility error:', error);
        }
    }
});
```

## 🔄 传统应用迁移

### jQuery 应用迁移

```javascript
import { JQueryAppAdapter } from '@micro-core/sidecar/legacy-apps';

const adapter = new JQueryAppAdapter({
    name: 'legacy-jquery-app',
    entry: '/legacy/jquery-app.html',
    jqueryVersion: '3.6.0',
    globalJQuery: true,
    plugins: [
        'https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/dist/jquery-ui.min.js'
    ],
    onReady: () => {
        console.log('jQuery app is ready!');
    }
});
```

### 原生 JS 应用迁移

```javascript
import { VanillaJSAppAdapter } from '@micro-core/sidecar/legacy-apps';

const adapter = new VanillaJSAppAdapter({
    name: 'legacy-vanilla-app',
    entry: '/legacy/vanilla-app.html',
    scripts: ['/js/utils.js', '/js/app.js'],
    styles: ['/css/app.css'],
    initFunction: 'initApp',
    destroyFunction: 'destroyApp'
});
```

## 🛠️ 开发调试

### 启用调试模式

```javascript
init({
    debug: true,
    logLevel: 'debug'
});
```

### 获取运行状态

```javascript
const sidecar = getSidecar();
if (sidecar) {
    const status = sidecar.getStatus();
    console.log('Sidecar status:', status);
}
```

## 🚨 错误处理

### 全局错误处理

```javascript
init({
    errorHandler: (error, context) => {
        console.error('Sidecar error:', error);
        // 发送错误报告
        reportError(error, context);
    }
});
```

### 应用级错误处理

```javascript
init({
    apps: [
        {
            name: 'app1',
            entry: '//localhost:8080/app.js',
            container: '#app1',
            errorBoundary: (error, app) => {
                console.error(`App ${app.name} error:`, error);
                // 显示错误页面
                showErrorPage(error);
            }
        }
    ]
});
```

## 📊 性能优化

### 预加载策略

```javascript
init({
    preloadApps: ['critical-app'],
    preloadStrategy: 'idle', // 'idle' | 'visible' | 'immediate'
    preloadTimeout: 5000
});
```

### 资源缓存

```javascript
init({
    cache: {
        enabled: true,
        maxAge: 3600000, // 1小时
        storage: 'localStorage' // 'localStorage' | 'sessionStorage' | 'memory'
    }
});
```

## 🔗 相关链接

- [完整 API 文档](./API.md)
- [迁移指南](./MIGRATION_GUIDE.md)
- [使用示例](./examples/)
- [常见问题](./FAQ.md)

## 📄 许可证

MIT License
