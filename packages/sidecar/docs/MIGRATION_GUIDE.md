# Sidecar 迁移指南

> 本指南帮助您将传统应用迁移到 Micro-Core Sidecar 架构

## 📋 迁移概述

Sidecar 模式提供了一种渐进式的微前端接入方案，允许您：

- 🚀 **一行代码接入**: 无需大规模重构现有应用
- 🔄 **渐进式迁移**: 逐步将传统应用迁移到微前端架构
- 🛡️ **向下兼容**: 支持各种传统技术栈
- 📦 **零配置启动**: 自动检测和配置应用

## 🎯 迁移策略

### 1. 评估现有应用

在开始迁移之前，需要评估现有应用的技术栈和架构：

```javascript
// 使用内置的应用检测工具
import { FrameworkDetector } from '@micro-core/sidecar';

const detector = new FrameworkDetector();
const appInfo = await detector.detect();

console.log('检测到的应用信息:', appInfo);
// 输出: { framework: 'jquery', version: '3.6.0', dependencies: [...] }
```

### 2. 选择迁移方案

根据应用类型选择合适的迁移方案：

| 应用类型 | 推荐方案 | 迁移难度 | 时间估算 |
|----------|----------|----------|----------|
| jQuery 应用 | JQueryAppAdapter | 低 | 1-2天 |
| 原生 JS 应用 | VanillaJSAppAdapter | 低 | 1-3天 |
| 多页面应用 | 页面级拆分 | 中 | 1-2周 |
| 单体应用 | 模块化拆分 | 高 | 2-4周 |

## 🔧 具体迁移步骤

### jQuery 应用迁移

#### 步骤 1: 准备工作

1. **备份现有代码**
2. **分析依赖关系**
3. **确定拆分边界**

#### 步骤 2: 配置 Sidecar

```javascript
// 1. 引入 Sidecar
import { init, JQueryAppAdapter } from '@micro-core/sidecar';

// 2. 配置 jQuery 应用
const jqueryAppConfig = {
    name: 'legacy-jquery-app',
    entry: '/legacy/jquery-app.html',
    container: '#jquery-app-container',
    activeWhen: '/legacy/jquery',
    jqueryVersion: '3.6.0',
    globalJQuery: true,
    plugins: [
        'https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/dist/jquery-ui.min.js'
    ],
    onReady: () => {
        // 应用初始化逻辑
        initializeJQueryComponents();
    }
};

// 3. 初始化 Sidecar
await init({
    apps: [jqueryAppConfig],
    autoConfig: true
});
```

#### 步骤 3: 适配现有代码

```javascript
// 原有的 jQuery 代码
$(document).ready(function() {
    $('#my-button').click(function() {
        alert('Hello World!');
    });
});

// 迁移后的代码 - 封装在初始化函数中
function initializeJQueryComponents() {
    $('#my-button').click(function() {
        alert('Hello World!');
    });
    
    // 其他初始化逻辑...
}
```

#### 步骤 4: 处理样式隔离

```css
/* 原有样式 */
.my-component {
    color: red;
}

/* 迁移后 - 添加应用前缀 */
.jquery-app .my-component {
    color: red;
}
```

### 原生 JS 应用迁移

#### 步骤 1: 模块化改造

```javascript
// 原有代码 - 全局变量和函数
var appData = {};
function initApp() {
    // 初始化逻辑
}
function destroyApp() {
    // 清理逻辑
}

// 迁移后 - 命名空间封装
window.LegacyApp = {
    data: {},
    init: function(container) {
        // 初始化逻辑
        this.container = container;
        this.bindEvents();
    },
    destroy: function() {
        // 清理逻辑
        this.unbindEvents();
    },
    bindEvents: function() {
        // 事件绑定
    },
    unbindEvents: function() {
        // 事件解绑
    }
};
```

#### 步骤 2: 配置适配器

```javascript
import { VanillaJSAppAdapter } from '@micro-core/sidecar';

const adapter = new VanillaJSAppAdapter({
    name: 'legacy-vanilla-app',
    entry: '/legacy/vanilla-app.html',
    container: '#vanilla-app-container',
    activeWhen: '/legacy/vanilla',
    scripts: [
        '/legacy/js/utils.js',
        '/legacy/js/components.js',
        '/legacy/js/app.js'
    ],
    styles: [
        '/legacy/css/reset.css',
        '/legacy/css/app.css'
    ],
    initFunction: 'LegacyApp.init',
    destroyFunction: 'LegacyApp.destroy',
    namespace: 'LegacyApp'
});
```

## 🚨 常见问题和解决方案

### 1. 全局变量冲突

**问题**: 多个应用使用相同的全局变量名

**解决方案**:
```javascript
// 使用命名空间隔离
window.App1 = { /* 应用1的代码 */ };
window.App2 = { /* 应用2的代码 */ };

// 或使用 IIFE 包装
(function(global) {
    var App = { /* 应用代码 */ };
    global.MyApp = App;
})(window);
```

### 2. CSS 样式冲突

**问题**: 不同应用的样式相互影响

**解决方案**:
```css
/* 方案1: 使用应用前缀 */
.app1-container .button { /* 样式 */ }
.app2-container .button { /* 样式 */ }

/* 方案2: 使用 CSS Modules */
.button_app1_abc123 { /* 样式 */ }

/* 方案3: 使用 Shadow DOM */
/* 由 Sidecar 自动处理 */
```

### 3. 事件监听器泄漏

**问题**: 应用卸载后事件监听器未清理

**解决方案**:
```javascript
// 在应用中维护事件监听器列表
const eventListeners = [];

function addEventListenerWithCleanup(element, event, handler) {
    element.addEventListener(event, handler);
    eventListeners.push({ element, event, handler });
}

function cleanupEventListeners() {
    eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
    });
    eventListeners.length = 0;
}

// 在应用销毁时调用清理函数
function destroyApp() {
    cleanupEventListeners();
}
```

### 4. 异步资源加载

**问题**: 应用依赖的资源加载时序问题

**解决方案**:
```javascript
// 使用 Promise 确保资源加载完成
async function loadDependencies() {
    const promises = [
        loadScript('/js/lib1.js'),
        loadScript('/js/lib2.js'),
        loadCSS('/css/app.css')
    ];
    
    await Promise.all(promises);
    console.log('所有依赖加载完成');
}

function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}
```

## 📊 迁移检查清单

### 迁移前检查

- [ ] 应用技术栈分析完成
- [ ] 依赖关系梳理完成
- [ ] 拆分边界确定
- [ ] 迁移方案选择
- [ ] 代码备份完成

### 迁移中检查

- [ ] Sidecar 配置正确
- [ ] 应用适配器选择合适
- [ ] 全局变量冲突解决
- [ ] 样式隔离处理
- [ ] 事件监听器清理
- [ ] 资源加载顺序正确

### 迁移后检查

- [ ] 应用功能正常
- [ ] 性能指标达标
- [ ] 内存泄漏检查
- [ ] 浏览器兼容性测试
- [ ] 错误处理验证
- [ ] 文档更新完成

## 🎯 最佳实践

### 1. 渐进式迁移

```javascript
// 阶段1: 最小化迁移
init({
    apps: [
        {
            name: 'legacy-app',
            entry: '/legacy/app.html',
            container: '#legacy-container',
            activeWhen: '/legacy'
        }
    ]
});

// 阶段2: 功能拆分
init({
    apps: [
        {
            name: 'header-app',
            entry: '/apps/header.js',
            container: '#header'
        },
        {
            name: 'legacy-content',
            entry: '/legacy/content.html',
            container: '#content'
        }
    ]
});

// 阶段3: 完全微前端化
init({
    apps: [
        { name: 'header-app', entry: '/apps/header.js' },
        { name: 'sidebar-app', entry: '/apps/sidebar.js' },
        { name: 'content-app', entry: '/apps/content.js' },
        { name: 'footer-app', entry: '/apps/footer.js' }
    ]
});
```

### 2. 错误边界处理

```javascript
init({
    apps: [
        {
            name: 'legacy-app',
            entry: '/legacy/app.html',
            container: '#app',
            errorBoundary: (error, app) => {
                // 记录错误
                console.error(`App ${app.name} failed:`, error);
                
                // 显示降级 UI
                document.querySelector('#app').innerHTML = `
                    <div class="error-fallback">
                        <h3>应用暂时不可用</h3>
                        <p>请刷新页面重试</p>
                        <button onclick="location.reload()">刷新</button>
                    </div>
                `;
            }
        }
    ]
});
```

### 3. 性能监控

```javascript
init({
    apps: [...],
    onAppLoad: (app, loadTime) => {
        console.log(`App ${app.name} loaded in ${loadTime}ms`);
        // 发送性能指标
        analytics.track('app_load_time', {
            app: app.name,
            loadTime: loadTime
        });
    }
});
```

## 🔗 相关资源

- [Sidecar API 文档](./README.md)
- [传统应用适配器](../src/legacy-apps/)
- [迁移示例](./examples/)
- [故障排除指南](./TROUBLESHOOTING.md)

## 💬 获取帮助

如果在迁移过程中遇到问题，可以：

1. 查看 [常见问题文档](./FAQ.md)
2. 提交 [GitHub Issue](https://github.com/echo008/micro-core/issues)
3. 加入社区讨论群
