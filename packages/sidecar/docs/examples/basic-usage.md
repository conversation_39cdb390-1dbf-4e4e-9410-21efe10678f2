# Sidecar 基础使用示例

> 本文档提供 @micro-core/sidecar 的基础使用示例和最佳实践

## 🚀 快速开始

### 1. CDN 方式接入

最简单的接入方式，适合快速验证和原型开发：

```html
<!DOCTYPE html>
<html>
<head>
    <title>Micro-Core Sidecar Demo</title>
</head>
<body>
    <div id="header-container"></div>
    <div id="content-container"></div>
    <div id="footer-container"></div>

    <!-- 引入 Sidecar -->
    <script src="//cdn.jsdelivr.net/npm/@micro-core/sidecar@0.1.0/dist/index.min.js"></script>
    <script>
        // 初始化微前端
        window.MicroCoreSidecar.init({
            apps: [
                {
                    name: 'header-app',
                    entry: '//localhost:3001/app.js',
                    container: '#header-container',
                    activeWhen: () => true // 总是激活
                },
                {
                    name: 'content-app',
                    entry: '//localhost:3002/app.js',
                    container: '#content-container',
                    activeWhen: '/content'
                },
                {
                    name: 'footer-app',
                    entry: '//localhost:3003/app.js',
                    container: '#footer-container',
                    activeWhen: () => true
                }
            ]
        });
    </script>
</body>
</html>
```

### 2. NPM 方式接入

适合现代前端项目：

```bash
npm install @micro-core/sidecar
```

```javascript
import { init } from '@micro-core/sidecar';

// 初始化配置
const sidecarConfig = {
    name: 'my-micro-frontend',
    apps: [
        {
            name: 'user-center',
            entry: '//user-center.example.com/app.js',
            container: '#user-center',
            activeWhen: '/user'
        },
        {
            name: 'order-system',
            entry: '//order.example.com/app.js',
            container: '#order-system',
            activeWhen: '/orders'
        }
    ],
    // 启用自动配置检测
    autoConfig: true,
    // 启用兼容模式（支持旧浏览器）
    compatMode: false,
    // 自动启动
    autoStart: true
};

// 初始化 Sidecar
init(sidecarConfig).then(sidecar => {
    console.log('Sidecar initialized successfully:', sidecar);
}).catch(error => {
    console.error('Failed to initialize Sidecar:', error);
});
```

## 📋 配置选项详解

### 应用配置 (MicroAppConfig)

```javascript
const appConfig = {
    // 必需字段
    name: 'my-app',                    // 应用唯一标识
    entry: '//example.com/app.js',     // 应用入口 URL
    
    // 可选字段
    container: '#app-container',       // 容器选择器或 DOM 元素
    activeWhen: '/my-app',            // 激活条件（字符串或函数）
    
    // 自定义属性
    customProps: {
        theme: 'dark',
        apiBase: '//api.example.com'
    },
    
    // 生命周期钩子
    beforeLoad: (app) => {
        console.log(`Loading ${app.name}...`);
    },
    afterLoad: (app) => {
        console.log(`${app.name} loaded successfully`);
    },
    beforeMount: (app) => {
        console.log(`Mounting ${app.name}...`);
    },
    afterMount: (app) => {
        console.log(`${app.name} mounted successfully`);
    },
    beforeUnmount: (app) => {
        console.log(`Unmounting ${app.name}...`);
    },
    afterUnmount: (app) => {
        console.log(`${app.name} unmounted successfully`);
    },
    
    // 错误边界
    errorBoundary: (error, app) => {
        console.error(`App ${app.name} error:`, error);
        // 显示错误页面或降级 UI
        document.querySelector(app.container).innerHTML = `
            <div class="error-fallback">
                <h3>应用暂时不可用</h3>
                <p>请刷新页面重试</p>
            </div>
        `;
    }
};
```

### 激活条件 (activeWhen)

支持多种激活条件：

```javascript
// 1. 字符串匹配（路径前缀）
activeWhen: '/user'  // 匹配 /user, /user/profile, /user/settings 等

// 2. 正则表达式
activeWhen: /^\/user\/\d+$/  // 匹配 /user/123, /user/456 等

// 3. 函数判断
activeWhen: (location) => {
    return location.pathname.startsWith('/user') && 
           location.search.includes('tab=profile');
}

// 4. 数组（多个条件）
activeWhen: ['/user', '/profile', '/settings']

// 5. 总是激活
activeWhen: () => true

// 6. 从不激活（手动控制）
activeWhen: () => false
```

## 🔧 高级配置

### 自动配置检测

Sidecar 支持从多种来源自动检测应用配置：

```html
<!-- 1. HTML 属性配置 -->
<div data-micro-app='{"name":"auto-app","entry":"/auto-app.js","container":"#auto-container"}'></div>

<!-- 2. Meta 标签配置 -->
<meta name="micro-apps" content='[{"name":"meta-app","entry":"/meta-app.js"}]'>

<!-- 3. 全局变量配置 -->
<script>
    window.MICRO_APPS = [
        {
            name: 'global-app',
            entry: '/global-app.js',
            container: '#global-container'
        }
    ];
</script>
```

启用自动配置：

```javascript
init({
    autoConfig: true,
    autoConfigSources: ['html', 'meta', 'global'], // 配置来源
    apps: [] // 手动配置的应用（会与自动检测的合并）
});
```

### 预加载策略

```javascript
init({
    apps: [...],
    
    // 预加载应用列表
    preloadApps: ['critical-app', 'frequently-used-app'],
    
    // 预加载策略
    preloadStrategy: 'idle',  // 'idle' | 'visible' | 'immediate'
    
    // 预加载超时时间
    preloadTimeout: 5000,
    
    // 预加载错误处理
    onPreloadError: (appName, error) => {
        console.warn(`Failed to preload ${appName}:`, error);
    }
});
```

### 性能优化

```javascript
init({
    apps: [...],
    
    // 启用缓存
    cache: {
        enabled: true,
        maxAge: 3600000,  // 1小时
        storage: 'localStorage'  // 'localStorage' | 'sessionStorage' | 'memory'
    },
    
    // 资源优化
    optimization: {
        // 启用资源压缩
        compression: true,
        // 启用并行加载
        parallel: true,
        // 最大并发数
        maxConcurrency: 3,
        // 启用 HTTP/2 推送
        http2Push: true
    },
    
    // 性能监控
    onPerformanceMetric: (metric) => {
        console.log('Performance metric:', metric);
        // 发送到监控系统
        analytics.track('micro_app_performance', metric);
    }
});
```

## 🎯 实际使用场景

### 场景1：电商平台微前端化

```javascript
// 电商平台主应用
init({
    name: 'ecommerce-platform',
    apps: [
        // 公共头部
        {
            name: 'header',
            entry: '//cdn.shop.com/header/app.js',
            container: '#header',
            activeWhen: () => true
        },
        // 商品列表页
        {
            name: 'product-list',
            entry: '//product.shop.com/list/app.js',
            container: '#main-content',
            activeWhen: '/products'
        },
        // 商品详情页
        {
            name: 'product-detail',
            entry: '//product.shop.com/detail/app.js',
            container: '#main-content',
            activeWhen: /^\/product\/\d+$/
        },
        // 购物车
        {
            name: 'shopping-cart',
            entry: '//cart.shop.com/app.js',
            container: '#main-content',
            activeWhen: '/cart'
        },
        // 用户中心
        {
            name: 'user-center',
            entry: '//user.shop.com/app.js',
            container: '#main-content',
            activeWhen: '/user'
        }
    ],
    
    // 预加载关键应用
    preloadApps: ['header', 'shopping-cart'],
    
    // 全局状态管理
    globalState: {
        user: null,
        cart: { items: [], total: 0 },
        theme: 'light'
    },
    
    // 应用间通信
    onMessage: (message) => {
        switch (message.type) {
            case 'ADD_TO_CART':
                // 处理添加购物车事件
                updateCartState(message.data);
                break;
            case 'USER_LOGIN':
                // 处理用户登录事件
                updateUserState(message.data);
                break;
        }
    }
});
```

### 场景2：企业管理系统

```javascript
// 企业管理系统
init({
    name: 'enterprise-management',
    apps: [
        // 导航菜单
        {
            name: 'navigation',
            entry: '//nav.company.com/app.js',
            container: '#navigation',
            activeWhen: () => true
        },
        // 仪表板
        {
            name: 'dashboard',
            entry: '//dashboard.company.com/app.js',
            container: '#content',
            activeWhen: '/dashboard'
        },
        // 人事管理（传统应用）
        {
            name: 'hr-system',
            entry: '//legacy.company.com/hr/index.html',
            container: '#content',
            activeWhen: '/hr',
            adapter: 'jquery',  // 使用 jQuery 适配器
            customProps: {
                apiBase: '//api.company.com/hr'
            }
        },
        // 财务系统
        {
            name: 'finance-system',
            entry: '//finance.company.com/app.js',
            container: '#content',
            activeWhen: '/finance'
        }
    ],
    
    // 启用兼容模式（支持传统应用）
    compatMode: true,
    
    // 权限控制
    beforeAppLoad: async (app) => {
        const hasPermission = await checkUserPermission(app.name);
        if (!hasPermission) {
            throw new Error(`No permission to access ${app.name}`);
        }
    },
    
    // 统一错误处理
    errorHandler: (error, context) => {
        console.error('System error:', error);
        showErrorNotification(error.message);
        reportError(error, context);
    }
});
```

### 场景3：渐进式迁移

```javascript
// 从单体应用逐步迁移到微前端
init({
    name: 'progressive-migration',
    apps: [
        // 新开发的微应用
        {
            name: 'new-feature',
            entry: '//new.example.com/feature/app.js',
            container: '#feature-container',
            activeWhen: '/new-feature'
        },
        
        // 正在迁移的模块
        {
            name: 'migrating-module',
            entry: '//migrating.example.com/module/app.js',
            container: '#module-container',
            activeWhen: '/module'
        },
        
        // 暂时保留的传统页面
        {
            name: 'legacy-pages',
            entry: '//legacy.example.com/pages/index.html',
            container: '#legacy-container',
            activeWhen: '/legacy',
            adapter: 'vanilla'  // 使用原生 JS 适配器
        }
    ],
    
    // 渐进式加载
    loadingStrategy: 'progressive',
    
    // 向后兼容
    fallback: {
        // 当微应用加载失败时，回退到传统页面
        onAppLoadFail: (appName, error) => {
            console.warn(`App ${appName} failed, falling back to legacy`);
            window.location.href = `/legacy${window.location.pathname}`;
        }
    }
});
```

## 🔍 调试和监控

### 启用调试模式

```javascript
init({
    apps: [...],
    
    // 启用调试模式
    debug: true,
    
    // 日志级别
    logLevel: 'debug',  // 'error' | 'warn' | 'info' | 'debug'
    
    // 自定义日志处理
    logger: {
        debug: (message, ...args) => console.debug('[Sidecar Debug]', message, ...args),
        info: (message, ...args) => console.info('[Sidecar Info]', message, ...args),
        warn: (message, ...args) => console.warn('[Sidecar Warn]', message, ...args),
        error: (message, ...args) => console.error('[Sidecar Error]', message, ...args)
    }
});
```

### 运行时监控

```javascript
// 获取 Sidecar 实例
const sidecar = getSidecar();

if (sidecar) {
    // 获取运行状态
    const status = sidecar.getStatus();
    console.log('Sidecar status:', status);
    
    // 获取性能统计
    const stats = sidecar.getStats();
    console.log('Performance stats:', stats);
    
    // 获取已注册的应用
    const apps = sidecar.getRegisteredApps();
    console.log('Registered apps:', apps);
    
    // 获取已加载的应用
    const loadedApps = sidecar.getLoadedApps();
    console.log('Loaded apps:', loadedApps);
}
```

## 🚨 常见问题

### Q: 如何处理应用加载失败？

```javascript
init({
    apps: [...],
    
    // 全局错误处理
    errorHandler: (error, context) => {
        if (error.code === 'APP_LOAD_FAILED') {
            // 显示错误提示
            showErrorMessage(`应用 ${context.appName} 加载失败，请稍后重试`);
            
            // 尝试重新加载
            setTimeout(() => {
                sidecar.reloadApp(context.appName);
            }, 5000);
        }
    },
    
    // 应用级错误边界
    apps: [
        {
            name: 'my-app',
            entry: '/my-app.js',
            container: '#my-app',
            errorBoundary: (error, app) => {
                // 显示降级 UI
                document.querySelector(app.container).innerHTML = `
                    <div class="app-error">
                        <h3>应用暂时不可用</h3>
                        <button onclick="location.reload()">刷新页面</button>
                    </div>
                `;
            }
        }
    ]
});
```

### Q: 如何实现应用间通信？

```javascript
// 初始化时配置通信
init({
    apps: [...],
    
    // 消息处理
    onMessage: (message) => {
        console.log('Received message:', message);
        
        // 转发消息给其他应用
        if (message.target) {
            sidecar.sendMessage(message.from, message.target, message.data);
        }
    }
});

// 在应用中发送消息
const sidecar = getSidecar();
sidecar.sendMessage('sender-app', 'receiver-app', {
    type: 'USER_UPDATED',
    data: { userId: 123, name: 'John Doe' }
});

// 监听消息
sidecar.on('message', (message) => {
    if (message.type === 'USER_UPDATED') {
        updateUserInfo(message.data);
    }
});
```

### Q: 如何处理样式冲突？

```javascript
init({
    apps: [...],
    
    // 启用样式隔离
    isolation: {
        style: true,
        shadowDOM: true,  // 使用 Shadow DOM
        cssPrefix: 'micro-app-'  // CSS 前缀
    }
});
```

## 📚 更多资源

- [API 文档](../README.md)
- [迁移指南](../MIGRATION_GUIDE.md)
- [高级配置](./advanced-config.md)
- [故障排除](./troubleshooting.md)
