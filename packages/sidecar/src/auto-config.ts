import type { AppConfig } from '@micro-core/core';

/**
 * 自动配置选项
 */
export interface AutoConfigOptions {
    /** 是否启用自动配置检测 */
    enabled?: boolean;
    /** 配置来源 */
    sources?: ('html' | 'global' | 'meta')[];
}

/**
 * 检测到的配置
 */
export interface DetectedConfig {
    /** 应用列表 */
    apps?: AppConfig[];
    /** 其他配置 */
    [key: string]: any;
}

/**
 * 自动配置检测器
 */
export class AutoConfig {
    private options: Required<AutoConfigOptions>;

    constructor(options: AutoConfigOptions = {}) {
        this.options = {
            enabled: true,
            sources: ['html', 'global', 'meta'],
            ...options
        };
    }

    /**
     * 检测配置
     */
    async detect(): Promise<DetectedConfig> {
        if (!this.options.enabled) {
            return {};
        }

        const config: DetectedConfig = {
            apps: []
        };

        // 使用 Promise.allSettled 并行检测配置，提高性能
        const detectionPromises = this.options.sources.map(async (source) => {
            try {
                const sourceConfig = await this.detectFromSource(source);
                return { source, config: sourceConfig, success: true };
            } catch (error) {
                console.warn(`从 ${source} 检测配置失败:`, error);
                return { source, error, success: false };
            }
        });

        const results = await Promise.allSettled(detectionPromises);
        
        // 合并成功的配置
        results.forEach((result) => {
            if (result.status === 'fulfilled' && result.value.success && result.value.config) {
                this.mergeConfig(config, result.value.config);
            }
        });

        return config;
    }

    /**
     * 从指定来源检测配置
     */
    private async detectFromSource(source: string): Promise<DetectedConfig> {
        switch (source) {
            case 'html':
                return this.detectFromHTML();
            case 'global':
                return this.detectFromGlobal();
            case 'meta':
                return this.detectFromMeta();
            default:
                return {};
        }
    }

    /**
     * 从 HTML 属性检测配置
     */
    private detectFromHTML(): DetectedConfig {
        const config: DetectedConfig = { apps: [] };

        // 查找带有 data-micro-app 属性的元素
        const appElements = document.querySelectorAll('[data-micro-app]');

        appElements.forEach(element => {
            try {
                const appConfig = this.parseAppElement(element as HTMLElement);
                if (appConfig) {
                    config.apps!.push(appConfig);
                }
            } catch (error) {
                console.warn('解析应用元素失败:', error);
            }
        });

        return config;
    }

    /**
     * 从全局变量检测配置
     */
    private detectFromGlobal(): DetectedConfig {
        const config: DetectedConfig = { apps: [] };

        // 检查全局变量
        const globalConfig = (window as any).__MICRO_CORE_CONFIG__;
        if (globalConfig) {
            if (globalConfig.apps && Array.isArray(globalConfig.apps)) {
                config.apps = globalConfig.apps;
            }

            // 合并其他配置
            Object.keys(globalConfig).forEach(key => {
                if (key !== 'apps') {
                    config[key] = globalConfig[key];
                }
            });
        }

        return config;
    }

    /**
     * 从 meta 标签检测配置
     */
    private detectFromMeta(): DetectedConfig {
        const config: DetectedConfig = { apps: [] };

        // 查找 meta 标签配置
        const metaElements = document.querySelectorAll('meta[name^="micro-core:"]');

        metaElements.forEach(meta => {
            try {
                const name = meta.getAttribute('name');
                const content = meta.getAttribute('content');

                if (name && content) {
                    this.parseMetaConfig(name, content, config);
                }
            } catch (error) {
                console.warn('解析 meta 配置失败:', error);
            }
        });

        return config;
    }

    /**
     * 解析应用元素
     */
    private parseAppElement(element: HTMLElement): AppConfig | null {
        const name = element.getAttribute('data-micro-app');
        const entry = element.getAttribute('data-entry');
        const activeWhen = element.getAttribute('data-active-when');

        if (!name || !entry || !activeWhen) {
            return null;
        }

        const config: AppConfig = {
            name,
            entry,
            activeWhen,
            container: element
        };

        // 解析自定义属性
        const customProps: Record<string, any> = {};
        Array.from(element.attributes).forEach(attr => {
            if (attr.name.startsWith('data-prop-')) {
                const propName = attr.name.replace('data-prop-', '');
                customProps[propName] = this.parseValue(attr.value);
            }
        });

        if (Object.keys(customProps).length > 0) {
            config.customProps = customProps;
        }

        return config;
    }

    /**
     * 解析 meta 配置
     */
    private parseMetaConfig(name: string, content: string, config: DetectedConfig): void {
        const configKey = name.replace('micro-core:', '');

        if (configKey === 'app') {
            try {
                const appConfig = JSON.parse(content) as AppConfig;
                config.apps!.push(appConfig);
            } catch (error) {
                console.warn('解析应用配置失败:', error);
            }
        } else {
            config[configKey] = this.parseValue(content);
        }
    }

    /**
     * 解析值
     */
    private parseValue(value: string): any {
        // 尝试解析为 JSON
        try {
            return JSON.parse(value);
        } catch {
            // 解析为布尔值
            if (value === 'true') return true;
            if (value === 'false') return false;

            // 解析为数字
            const num = Number(value);
            if (!isNaN(num)) return num;

            // 返回字符串
            return value;
        }
    }

    /**
     * 合并配置
     */
    private mergeConfig(target: DetectedConfig, source: DetectedConfig): void {
        if (source.apps && source.apps.length > 0) {
            target.apps = [...(target.apps || []), ...source.apps];
        }

        Object.keys(source).forEach(key => {
            if (key !== 'apps') {
                target[key] = source[key];
            }
        });
    }
}