/**
 * @fileoverview Sidecar 容器核心
 * @description 初始化微内核和加载配置的核心实现
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { MicroCoreOptions, AppConfig } from '@micro-core/core';
import { MicroCoreKernel, logger, createMicroCoreError } from '@micro-core/core';
import { AutoConfig } from './auto-config';
import { CompatMode } from './compat-mode';
import type { SidecarConfig, SidecarStatus, SidecarStats } from './types';

/**
 * Sidecar 容器选项
 */
export interface SidecarOptions extends MicroCoreOptions {
    /** 是否启用自动配置检测 */
    autoConfig?: boolean;
    /** 是否启用兼容模式 */
    compatMode?: boolean;
    /** 容器选择器 */
    container?: string | HTMLElement;
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 预加载应用列表 */
    preloadApps?: string[];
    /** 自定义插件 */
    plugins?: any[];
}

/**
 * Sidecar 容器核心类
 * 负责初始化微内核和加载配置
 */
export class SidecarContainer {
    private kernel: MicroCoreKernel;
    private autoConfig: AutoConfig;
    private compatMode: CompatMode;
    private config: SidecarConfig;
    private status: SidecarStatus = 'idle';
    private stats: SidecarStats;
    private startTime: number = 0;

    constructor(options: SidecarOptions = {}) {
        this.config = this.normalizeConfig(options);
        this.kernel = new MicroCoreKernel(this.config);
        this.autoConfig = new AutoConfig({
            enabled: this.config.autoConfig
        });
        this.compatMode = new CompatMode({
            enabled: this.config.compatMode
        });
        
        this.stats = {
            appsLoaded: 0,
            appsActive: 0,
            appsError: 0,
            totalRequests: 0,
            avgResponseTime: 0,
            memoryUsage: 0,
            lastActivity: Date.now()
        };

        this.initializeEventListeners();
    }

    /**
     * 规范化配置
     */
    private normalizeConfig(options: SidecarOptions): SidecarConfig {
        return {
            autoConfig: options.autoConfig ?? true,
            compatMode: options.compatMode ?? false,
            autoStart: options.autoStart ?? true,
            container: options.container ?? '#micro-app-container',
            preloadApps: options.preloadApps ?? [],
            plugins: options.plugins ?? [],
            ...options
        };
    }

    /**
     * 初始化事件监听器
     */
    private initializeEventListeners(): void {
        // 监听应用生命周期事件
        this.kernel.on('app:loaded', (app: AppConfig) => {
            this.stats.appsLoaded++;
            this.stats.lastActivity = Date.now();
            logger.info(`App loaded: ${app.name}`);
        });

        this.kernel.on('app:mounted', (app: AppConfig) => {
            this.stats.appsActive++;
            this.stats.lastActivity = Date.now();
            logger.info(`App mounted: ${app.name}`);
        });

        this.kernel.on('app:unmounted', (app: AppConfig) => {
            this.stats.appsActive--;
            this.stats.lastActivity = Date.now();
            logger.info(`App unmounted: ${app.name}`);
        });

        this.kernel.on('app:error', (error: Error, app?: AppConfig) => {
            this.stats.appsError++;
            this.stats.lastActivity = Date.now();
            logger.error(`App error: ${app?.name || 'unknown'}`, error);
        });

        // 监听性能指标
        this.kernel.on('performance:metric', (metric: any) => {
            this.updatePerformanceStats(metric);
        });
    }

    /**
     * 更新性能统计
     */
    private updatePerformanceStats(metric: any): void {
        this.stats.totalRequests++;
        
        // 计算平均响应时间
        const currentAvg = this.stats.avgResponseTime;
        const newAvg = (currentAvg * (this.stats.totalRequests - 1) + metric.responseTime) / this.stats.totalRequests;
        this.stats.avgResponseTime = Math.round(newAvg);

        // 更新内存使用情况
        if (performance.memory) {
            this.stats.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        }
    }

    /**
     * 初始化 Sidecar 容器
     */
    async initialize(): Promise<void> {
        try {
            this.status = 'initializing';
            this.startTime = Date.now();
            
            logger.info('Initializing Sidecar container...');

            // 初始化兼容模式
            if (this.config.compatMode) {
                await this.compatMode.initialize();
            }

            // 初始化微内核
            await this.kernel.initialize();

            // 自动配置检测
            if (this.config.autoConfig) {
                const detectedConfig = await this.autoConfig.detect();
                if (detectedConfig.apps && detectedConfig.apps.length > 0) {
                    logger.info(`Auto-detected ${detectedConfig.apps.length} apps`);
                    for (const app of detectedConfig.apps) {
                        await this.kernel.registerApp(app);
                    }
                }
            }

            // 预加载应用
            if (this.config.preloadApps.length > 0) {
                await this.preloadApps();
            }

            this.status = 'ready';
            logger.info('Sidecar container initialized successfully');

        } catch (error) {
            this.status = 'error';
            const sidecarError = createMicroCoreError(
                'SIDECAR_INIT_ERROR',
                'Failed to initialize Sidecar container',
                { originalError: error }
            );
            logger.error('Sidecar initialization failed', sidecarError);
            throw sidecarError;
        }
    }

    /**
     * 启动 Sidecar 容器
     */
    async start(): Promise<void> {
        if (this.status !== 'ready') {
            throw createMicroCoreError(
                'SIDECAR_NOT_READY',
                'Sidecar container is not ready to start'
            );
        }

        try {
            this.status = 'starting';
            logger.info('Starting Sidecar container...');

            await this.kernel.start();

            // 如果启用自动启动，启动所有注册的应用
            if (this.config.autoStart) {
                const apps = this.kernel.getRegisteredApps();
                for (const app of apps) {
                    if (this.shouldAutoStartApp(app)) {
                        await this.kernel.loadApp(app.name);
                    }
                }
            }

            this.status = 'running';
            logger.info('Sidecar container started successfully');

        } catch (error) {
            this.status = 'error';
            const sidecarError = createMicroCoreError(
                'SIDECAR_START_ERROR',
                'Failed to start Sidecar container',
                { originalError: error }
            );
            logger.error('Sidecar start failed', sidecarError);
            throw sidecarError;
        }
    }

    /**
     * 停止 Sidecar 容器
     */
    async stop(): Promise<void> {
        try {
            this.status = 'stopping';
            logger.info('Stopping Sidecar container...');

            await this.kernel.stop();

            this.status = 'stopped';
            logger.info('Sidecar container stopped successfully');

        } catch (error) {
            this.status = 'error';
            const sidecarError = createMicroCoreError(
                'SIDECAR_STOP_ERROR',
                'Failed to stop Sidecar container',
                { originalError: error }
            );
            logger.error('Sidecar stop failed', sidecarError);
            throw sidecarError;
        }
    }

    /**
     * 销毁 Sidecar 容器
     */
    async destroy(): Promise<void> {
        try {
            if (this.status === 'running') {
                await this.stop();
            }

            this.status = 'destroying';
            logger.info('Destroying Sidecar container...');

            await this.kernel.destroy();

            this.status = 'destroyed';
            logger.info('Sidecar container destroyed successfully');

        } catch (error) {
            this.status = 'error';
            const sidecarError = createMicroCoreError(
                'SIDECAR_DESTROY_ERROR',
                'Failed to destroy Sidecar container',
                { originalError: error }
            );
            logger.error('Sidecar destroy failed', sidecarError);
            throw sidecarError;
        }
    }

    /**
     * 预加载应用
     */
    private async preloadApps(): Promise<void> {
        logger.info(`Preloading ${this.config.preloadApps.length} apps...`);
        
        const preloadPromises = this.config.preloadApps.map(async (appName) => {
            try {
                await this.kernel.preloadApp(appName);
                logger.info(`App preloaded: ${appName}`);
            } catch (error) {
                logger.warn(`Failed to preload app: ${appName}`, error);
            }
        });

        await Promise.allSettled(preloadPromises);
    }

    /**
     * 判断是否应该自动启动应用
     */
    private shouldAutoStartApp(app: AppConfig): boolean {
        // 检查当前路由是否匹配应用的激活条件
        if (typeof app.activeWhen === 'string') {
            return location.pathname.startsWith(app.activeWhen);
        } else if (typeof app.activeWhen === 'function') {
            return app.activeWhen(location);
        }
        return false;
    }

    /**
     * 注册应用
     */
    async registerApp(config: AppConfig): Promise<void> {
        await this.kernel.registerApp(config);
    }

    /**
     * 卸载应用
     */
    async unregisterApp(name: string): Promise<void> {
        await this.kernel.unregisterApp(name);
    }

    /**
     * 加载应用
     */
    async loadApp(name: string): Promise<void> {
        await this.kernel.loadApp(name);
    }

    /**
     * 卸载应用
     */
    async unloadApp(name: string): Promise<void> {
        await this.kernel.unloadApp(name);
    }

    /**
     * 获取容器状态
     */
    getStatus(): SidecarStatus {
        return this.status;
    }

    /**
     * 获取统计信息
     */
    getStats(): SidecarStats {
        return {
            ...this.stats,
            uptime: this.startTime > 0 ? Date.now() - this.startTime : 0
        };
    }

    /**
     * 获取配置
     */
    getConfig(): SidecarConfig {
        return { ...this.config };
    }

    /**
     * 获取微内核实例
     */
    getKernel(): MicroCoreKernel {
        return this.kernel;
    }
}

export default SidecarContainer;
