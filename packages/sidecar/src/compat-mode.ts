/**
 * @fileoverview 兼容模式管理器
 * @description 提供对旧版浏览器和传统微前端框架的兼容性支持
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { MicroCoreKernel } from '@micro-core/core';
// Note: Temporary imports until @micro-core/core is fully implemented
// import { logger, createMicroCoreError } from '@micro-core/core';

// Temporary logger implementation
const logger = {
    info: (message: string, ...args: any[]) => console.info(`[Sidecar Info] ${message}`, ...args),
    warn: (message: string, ...args: any[]) => console.warn(`[Sidecar Warn] ${message}`, ...args),
    error: (message: string, ...args: any[]) => console.error(`[Sidecar Error] ${message}`, ...args)
};

// Temporary error creator
const createMicroCoreError = (code: string, message: string, context?: any) => {
    const error = new Error(message);
    (error as any).code = code;
    (error as any).context = context;
    return error;
};

/**
 * 浏览器信息接口
 */
export interface BrowserInfo {
    name: string;
    version: number;
    userAgent: string;
}

/**
 * 兼容模式配置
 */
export interface CompatModeConfig {
    /** 是否启用兼容模式 */
    enabled?: boolean;
    /** 是否启用 qiankun 兼容模式 */
    qiankun?: boolean;
    /** 是否启用 wujie 兼容模式 */
    wujie?: boolean;
    /** 是否启用 single-spa 兼容模式 */
    singleSpa?: boolean;
    /** 需要的 polyfills */
    polyfills?: string[];
    /** 浏览器兼容性配置 */
    browser?: BrowserCompatConfig;
    /** 错误处理器 */
    errorHandler?: (error: Error, context?: string) => void;
}

/**
 * 浏览器兼容性配置
 */
export interface BrowserCompatConfig {
    /** 是否支持 IE11 */
    ie11?: boolean;
    /** 是否支持旧版 Chrome */
    oldChrome?: boolean;
    /** 是否支持旧版 Safari */
    oldSafari?: boolean;
    /** 最低支持的浏览器版本 */
    minVersions?: {
        chrome?: number;
        firefox?: number;
        safari?: number;
        edge?: number;
    };
}

/**
 * Polyfill 配置
 */
export interface PolyfillConfig {
    name: string;
    url: string;
    condition: () => boolean;
    priority: number;
}

/**
 * 兼容模式管理器
 */
export class CompatMode {
    private config: Required<CompatModeConfig>;
    private loadedPolyfills = new Set<string>();
    private compatibilityChecked = false;
    private browserInfo: BrowserInfo | null = null;

    constructor(config: CompatModeConfig = {}) {
        this.config = {
            enabled: config.enabled ?? true,
            qiankun: config.qiankun ?? false,
            wujie: config.wujie ?? false,
            singleSpa: config.singleSpa ?? false,
            polyfills: config.polyfills ?? ['es6-promise', 'fetch', 'object-assign'],
            browser: config.browser ?? {},
            errorHandler: config.errorHandler ?? this.handleError.bind(this)
        };
    }

    /**
     * 初始化兼容模式
     */
    async initialize(): Promise<void> {
        if (!this.config.enabled) {
            logger.info('Compatibility mode is disabled');
            return;
        }

        try {
            logger.info('Initializing compatibility mode...');

            // 检测浏览器兼容性
            await this.checkBrowserCompatibility();

            // 加载必要的 polyfills
            await this.loadPolyfills();

            // 设置浏览器特定的兼容性处理
            await this.setupBrowserSpecificCompat();

            logger.info('Compatibility mode initialized successfully');

        } catch (error) {
            const compatError = createMicroCoreError(
                'COMPAT_INIT_ERROR',
                'Failed to initialize compatibility mode',
                { originalError: error }
            );
            this.config.errorHandler(compatError, 'initialization');
            throw compatError;
        }
    }

    /**
     * 设置兼容模式
     */
    async setup(kernel: MicroCoreKernel): Promise<void> {
        if (!this.config.enabled) {
            return;
        }

        try {
            logger.info('Setting up framework compatibility...');

            // 设置 qiankun 兼容模式
            if (this.config.qiankun) {
                await this.setupQiankunCompat(kernel);
            }

            // 设置 wujie 兼容模式
            if (this.config.wujie) {
                await this.setupWujieCompat(kernel);
            }

            // 设置 single-spa 兼容模式
            if (this.config.singleSpa) {
                await this.setupSingleSpaCompat(kernel);
            }

            logger.info('Framework compatibility setup completed');

        } catch (error) {
            const compatError = createMicroCoreError(
                'COMPAT_SETUP_ERROR',
                'Failed to setup compatibility mode',
                { originalError: error }
            );
            this.config.errorHandler(compatError, 'setup');
            throw compatError;
        }
    }

    /**
     * 检查浏览器兼容性
     */
    private async checkBrowserCompatibility(): Promise<void> {
        if (this.compatibilityChecked) {
            return;
        }

        this.browserInfo = this.detectBrowser();
        logger.info('Detected browser:', this.browserInfo);

        // 检查是否需要启用兼容模式
        const needsCompat = this.shouldEnableCompatMode();
        if (needsCompat) {
            logger.info('Browser requires compatibility mode');
        }

        this.compatibilityChecked = true;
    }

    /**
     * 判断是否需要启用兼容模式
     */
    shouldEnableCompatMode(): boolean {
        if (!this.browserInfo) {
            this.browserInfo = this.detectBrowser();
        }

        const { name, version } = this.browserInfo;
        const minVersions = this.config.browser.minVersions || {};

        // 检查 IE11
        if (name === 'ie' && version <= 11) {
            return true;
        }

        // 检查其他浏览器的最低版本要求
        if (name === 'chrome' && minVersions.chrome && version < minVersions.chrome) {
            return true;
        }

        if (name === 'firefox' && minVersions.firefox && version < minVersions.firefox) {
            return true;
        }

        if (name === 'safari' && minVersions.safari && version < minVersions.safari) {
            return true;
        }

        if (name === 'edge' && minVersions.edge && version < minVersions.edge) {
            return true;
        }

        return false;
    }

    /**
     * 检测浏览器信息
     */
    private detectBrowser(): BrowserInfo {
        const userAgent = navigator.userAgent;
        
        // IE 检测
        if (userAgent.includes('Trident') || userAgent.includes('MSIE')) {
            const version = userAgent.includes('MSIE') 
                ? parseInt(userAgent.split('MSIE ')[1] || '0') 
                : 11;
            return { name: 'ie', version, userAgent };
        }

        // Edge 检测
        if (userAgent.includes('Edge')) {
            const version = parseInt(userAgent.split('Edge/')[1] || '0');
            return { name: 'edge', version, userAgent };
        }

        // Chrome 检测
        if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) {
            const version = parseInt(userAgent.split('Chrome/')[1] || '0');
            return { name: 'chrome', version, userAgent };
        }

        // Firefox 检测
        if (userAgent.includes('Firefox')) {
            const version = parseInt(userAgent.split('Firefox/')[1] || '0');
            return { name: 'firefox', version, userAgent };
        }

        // Safari 检测
        if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            const version = parseInt(userAgent.split('Version/')[1] || '0');
            return { name: 'safari', version, userAgent };
        }

        return { name: 'unknown', version: 0, userAgent };
    }

    /**
     * 加载 Polyfills
     */
    private async loadPolyfills(): Promise<void> {
        const polyfillConfigs = this.getPolyfillConfigs();
        const neededPolyfills = polyfillConfigs
            .filter(config => config.condition())
            .sort((a, b) => a.priority - b.priority);

        if (neededPolyfills.length === 0) {
            logger.info('No polyfills needed');
            return;
        }

        logger.info(`Loading ${neededPolyfills.length} polyfills...`);

        for (const polyfill of neededPolyfills) {
            await this.loadPolyfill(polyfill);
        }

        logger.info('All polyfills loaded successfully');
    }

    /**
     * 获取 Polyfill 配置
     */
    private getPolyfillConfigs(): PolyfillConfig[] {
        return [
            {
                name: 'es6-promise',
                url: 'https://cdn.jsdelivr.net/npm/es6-promise@4.2.8/dist/es6-promise.auto.min.js',
                condition: () => typeof Promise === 'undefined',
                priority: 1
            },
            {
                name: 'fetch',
                url: 'https://cdn.jsdelivr.net/npm/whatwg-fetch@3.6.2/dist/fetch.umd.js',
                condition: () => typeof fetch === 'undefined',
                priority: 2
            },
            {
                name: 'object-assign',
                url: 'https://cdn.jsdelivr.net/npm/object-assign@4.1.1/index.js',
                condition: () => typeof Object.assign === 'undefined',
                priority: 3
            },
            {
                name: 'proxy-polyfill',
                url: 'https://cdn.jsdelivr.net/npm/proxy-polyfill@0.3.2/proxy.min.js',
                condition: () => typeof Proxy === 'undefined',
                priority: 4
            },
            {
                name: 'custom-elements',
                url: 'https://cdn.jsdelivr.net/npm/@webcomponents/custom-elements@1.5.0/custom-elements.min.js',
                condition: () => typeof customElements === 'undefined',
                priority: 5
            }
        ].filter(config => this.config.polyfills.includes(config.name));
    }

    /**
     * 加载单个 Polyfill
     */
    private async loadPolyfill(config: PolyfillConfig): Promise<void> {
        if (this.loadedPolyfills.has(config.name)) {
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = config.url;
            script.onload = () => {
                this.loadedPolyfills.add(config.name);
                logger.info(`Polyfill loaded: ${config.name}`);
                resolve();
            };
            script.onerror = () => {
                const error = new Error(`Failed to load polyfill: ${config.name}`);
                this.config.errorHandler(error, 'polyfill-loading');
                reject(error);
            };
            document.head.appendChild(script);
        });
    }

    /**
     * 设置浏览器特定的兼容性处理
     */
    private async setupBrowserSpecificCompat(): Promise<void> {
        if (!this.browserInfo) {
            return;
        }

        const { name, version } = this.browserInfo;

        // IE11 特殊处理
        if (name === 'ie' && version <= 11) {
            await this.setupIE11Compat();
        }

        // 旧版 Chrome 处理
        if (name === 'chrome' && version < 60) {
            await this.setupOldChromeCompat();
        }

        // 移动端 Safari 处理
        if (name === 'safari' && this.isMobile()) {
            await this.setupMobileSafariCompat();
        }
    }

    /**
     * 设置 IE11 兼容性
     */
    private async setupIE11Compat(): Promise<void> {
        logger.info('Setting up IE11 compatibility...');

        // 禁用不支持的特性
        (window as any).__MICRO_CORE_IE11__ = true;

        // 设置事件监听器兼容性
        this.setupIE11EventCompat();

        // 设置 DOM 操作兼容性
        this.setupIE11DOMCompat();
    }

    /**
     * 设置 IE11 事件兼容性
     */
    private setupIE11EventCompat(): void {
        // CustomEvent polyfill for IE11
        if (typeof CustomEvent !== 'function') {
            (window as any).CustomEvent = function(event: string, params: any) {
                params = params || { bubbles: false, cancelable: false, detail: undefined };
                const evt = document.createEvent('CustomEvent');
                evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
                return evt;
            };
        }
    }

    /**
     * 设置 IE11 DOM 兼容性
     */
    private setupIE11DOMCompat(): void {
        // Element.matches polyfill
        if (!Element.prototype.matches) {
            Element.prototype.matches = 
                (Element.prototype as any).msMatchesSelector || 
                Element.prototype.webkitMatchesSelector;
        }

        // Element.closest polyfill
        if (!Element.prototype.closest) {
            Element.prototype.closest = function(s: string) {
                let el: Element | null = this;
                do {
                    if (el.matches(s)) return el;
                    el = el.parentElement || el.parentNode as Element;
                } while (el !== null && el.nodeType === 1);
                return null;
            };
        }
    }

    /**
     * 设置旧版 Chrome 兼容性
     */
    private async setupOldChromeCompat(): Promise<void> {
        logger.info('Setting up old Chrome compatibility...');
        
        // 设置标志
        (window as any).__MICRO_CORE_OLD_CHROME__ = true;
    }

    /**
     * 设置移动端 Safari 兼容性
     */
    private async setupMobileSafariCompat(): Promise<void> {
        logger.info('Setting up mobile Safari compatibility...');
        
        // 设置标志
        (window as any).__MICRO_CORE_MOBILE_SAFARI__ = true;

        // 处理 iOS Safari 的特殊问题
        this.setupIOSSafariCompat();
    }

    /**
     * 设置 iOS Safari 兼容性
     */
    private setupIOSSafariCompat(): void {
        // 修复 iOS Safari 的 100vh 问题
        const setVH = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };
        
        setVH();
        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
    }

    /**
     * 判断是否为移动设备
     */
    private isMobile(): boolean {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 设置 qiankun 兼容模式
     */
    private async setupQiankunCompat(kernel: MicroCoreKernel): Promise<void> {
        try {
            logger.info('Setting up qiankun compatibility...');
            
            // 模拟 qiankun 兼容插件加载（实际项目中应该动态导入真实插件）
            // const { QiankunCompatPlugin } = await import('@micro-core/plugin-qiankun-compat');
            // kernel.use(QiankunCompatPlugin);
            
            logger.info('Qiankun compatibility setup completed');
        } catch (error) {
            logger.warn('Failed to setup qiankun compatibility:', error);
            // 不抛出错误，允许继续运行
        }
    }

    /**
     * 设置 wujie 兼容模式
     */
    private async setupWujieCompat(kernel: MicroCoreKernel): Promise<void> {
        try {
            logger.info('Setting up wujie compatibility...');
            
            // 模拟 wujie 兼容插件加载（实际项目中应该动态导入真实插件）
            // const { WujieCompatPlugin } = await import('@micro-core/plugin-wujie-compat');
            // kernel.use(WujieCompatPlugin);
            
            logger.info('Wujie compatibility setup completed');
        } catch (error) {
            logger.warn('Failed to setup wujie compatibility:', error);
            // 不抛出错误，允许继续运行
        }
    }

    /**
     * 设置 single-spa 兼容模式
     */
    private async setupSingleSpaCompat(kernel: MicroCoreKernel): Promise<void> {
        try {
            logger.info('Setting up single-spa compatibility...');
            
            // 模拟 single-spa 兼容插件加载（实际项目中应该动态导入真实插件）
            // const { SingleSpaCompatPlugin } = await import('@micro-core/plugin-single-spa-compat');
            // kernel.use(SingleSpaCompatPlugin);
            
            logger.info('Single-spa compatibility setup completed');
        } catch (error) {
            logger.warn('Failed to setup single-spa compatibility:', error);
            // 不抛出错误，允许继续运行
        }
    }

    /**
     * 通用错误处理器
     */
    private handleError(error: Error, context?: any): void {
        logger.error('Compatibility mode error:', error.message, context);
        // 可以在这里添加错误上报逻辑
        if (context?.callback && typeof context.callback === 'function') {
            context.callback(error);
        }
    }
}