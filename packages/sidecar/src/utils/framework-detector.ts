/**
 * 框架检测器
 * 自动检测当前页面使用的前端框架
 */

import { logger } from '@micro-core/core';

/**
 * 支持的框架类型
 */
export type FrameworkType = 'react' | 'vue' | 'angular' | 'svelte' | 'solid' | 'jquery' | 'vanilla' | 'unknown';

/**
 * 框架检测结果
 */
export interface FrameworkDetectionResult {
    framework: FrameworkType;
    version?: string;
    confidence: number; // 0-1之间的置信度
    evidence: string[]; // 检测依据
}

/**
 * 框架检测器
 */
export class FrameworkDetector {
    private detectionCache: FrameworkDetectionResult | null = null;
    private cacheExpiry = 60000; // 缓存1分钟
    private cacheTime = 0;

    /**
     * 检测框架
     */
    detect(forceRefresh = false): FrameworkType {
        const result = this.detectWithDetails(forceRefresh);
        return result.framework;
    }

    /**
     * 检测框架（带详细信息）
     */
    detectWithDetails(forceRefresh = false): FrameworkDetectionResult {
        // 检查缓存
        if (!forceRefresh && this.detectionCache &&
            (Date.now() - this.cacheTime) < this.cacheExpiry) {
            return this.detectionCache;
        }

        logger.debug('Detecting frontend framework...');

        const detectors = [
            this.detectReact.bind(this),
            this.detectVue.bind(this),
            this.detectAngular.bind(this),
            this.detectSvelte.bind(this),
            this.detectSolid.bind(this),
            this.detectJQuery.bind(this),
            this.detectVanilla.bind(this)
        ];

        let bestResult: FrameworkDetectionResult = {
            framework: 'unknown',
            confidence: 0,
            evidence: []
        };

        // 运行所有检测器
        for (const detector of detectors) {
            try {
                const result = detector();
                if (result.confidence > bestResult.confidence) {
                    bestResult = result;
                }
            } catch (error) {
                logger.warn('Framework detector error:', error);
            }
        }

        // 缓存结果
        this.detectionCache = bestResult;
        this.cacheTime = Date.now();

        logger.info(`Framework detected: ${bestResult.framework} (confidence: ${bestResult.confidence})`);

        return bestResult;
    }

    /**
     * 检测React
     */
    private detectReact(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        // 检查全局React对象
        if ((window as any).React) {
            evidence.push('Global React object found');
            confidence += 0.4;
            version = (window as any).React.version;
        }

        // 检查ReactDOM
        if ((window as any).ReactDOM) {
            evidence.push('Global ReactDOM object found');
            confidence += 0.3;
        }

        // 检查React DevTools
        if ((window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            evidence.push('React DevTools detected');
            confidence += 0.2;
        }

        // 检查React Fiber节点
        const reactFiberNodes = document.querySelectorAll('[data-reactroot], [data-reactid]');
        if (reactFiberNodes.length > 0) {
            evidence.push(`React DOM nodes found (${reactFiberNodes.length})`);
            confidence += 0.3;
        }

        // 检查React组件
        const reactComponents = document.querySelectorAll('*[class*="react"], *[id*="react"]');
        if (reactComponents.length > 0) {
            evidence.push(`React-like elements found (${reactComponents.length})`);
            confidence += 0.1;
        }

        return {
            framework: 'react',
            version,
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测Vue
     */
    private detectVue(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        // 检查全局Vue对象
        if ((window as any).Vue) {
            evidence.push('Global Vue object found');
            confidence += 0.4;
            version = (window as any).Vue.version;
        }

        // 检查Vue DevTools
        if ((window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__) {
            evidence.push('Vue DevTools detected');
            confidence += 0.2;
        }

        // 检查Vue实例
        const vueElements = document.querySelectorAll('[data-v-], [v-], *[class*="vue"]');
        if (vueElements.length > 0) {
            evidence.push(`Vue elements found (${vueElements.length})`);
            confidence += 0.3;
        }

        // 检查Vue指令
        const vueDirectives = document.querySelectorAll('*[v-if], *[v-for], *[v-show], *[v-model]');
        if (vueDirectives.length > 0) {
            evidence.push(`Vue directives found (${vueDirectives.length})`);
            confidence += 0.4;
        }

        return {
            framework: 'vue',
            version,
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测Angular
     */
    private detectAngular(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        // 检查Angular全局对象
        if ((window as any).ng) {
            evidence.push('Global ng object found');
            confidence += 0.4;
        }

        // 检查Angular元素
        const angularElements = document.querySelectorAll('*[ng-], *[data-ng-], app-root, *[class*="ng-"]');
        if (angularElements.length > 0) {
            evidence.push(`Angular elements found (${angularElements.length})`);
            confidence += 0.4;
        }

        // 检查Angular应用根元素
        const appRoot = document.querySelector('app-root');
        if (appRoot) {
            evidence.push('Angular app-root found');
            confidence += 0.3;
        }

        // 检查Angular版本
        const scripts = document.querySelectorAll('script[src*="angular"]');
        if (scripts.length > 0) {
            evidence.push(`Angular scripts found (${scripts.length})`);
            confidence += 0.2;
        }

        return {
            framework: 'angular',
            version,
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测Svelte
     */
    private detectSvelte(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;

        // 检查Svelte特征
        const svelteElements = document.querySelectorAll('*[class*="svelte-"]');
        if (svelteElements.length > 0) {
            evidence.push(`Svelte elements found (${svelteElements.length})`);
            confidence += 0.5;
        }

        // 检查Svelte脚本
        const scripts = document.querySelectorAll('script[src*="svelte"]');
        if (scripts.length > 0) {
            evidence.push(`Svelte scripts found (${scripts.length})`);
            confidence += 0.3;
        }

        return {
            framework: 'svelte',
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测Solid
     */
    private detectSolid(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;

        // 检查Solid全局对象
        if ((window as any).Solid) {
            evidence.push('Global Solid object found');
            confidence += 0.4;
        }

        // 检查Solid特征
        const solidElements = document.querySelectorAll('*[data-solid], *[class*="solid"]');
        if (solidElements.length > 0) {
            evidence.push(`Solid elements found (${solidElements.length})`);
            confidence += 0.3;
        }

        return {
            framework: 'solid',
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测jQuery
     */
    private detectJQuery(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        // 检查jQuery全局对象
        if ((window as any).jQuery || (window as any).$) {
            evidence.push('Global jQuery object found');
            confidence += 0.5;
            version = (window as any).jQuery?.fn?.jquery || (window as any).$?.fn?.jquery;
        }

        return {
            framework: 'jquery',
            version,
            confidence: Math.min(confidence, 1),
            evidence
        };
    }

    /**
     * 检测原生JavaScript
     */
    private detectVanilla(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0.1; // 默认最低置信度

        // 如果没有检测到其他框架，则认为是原生JavaScript
        evidence.push('No major framework detected, assuming vanilla JavaScript');

        return {
            framework: 'vanilla',
            confidence,
            evidence
        };
    }

    /**
     * 清除缓存
     */
    clearCache(): void {
        this.detectionCache = null;
        this.cacheTime = 0;
    }

    /**
     * 获取支持的框架列表
     */
    getSupportedFrameworks(): FrameworkType[] {
        return ['react', 'vue', 'angular', 'svelte', 'solid', 'jquery', 'vanilla'];
    }
}

export default FrameworkDetector;