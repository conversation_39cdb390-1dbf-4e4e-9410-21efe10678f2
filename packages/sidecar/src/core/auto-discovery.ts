/**
 * 自动发现模块
 * 自动发现和识别微前端应用
 */

import type { MicroAppConfig } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * 应用发现配置
 */
export interface DiscoveryConfig {
    /** 扫描的根路径 */
    basePath?: string;
    /** 应用清单文件名 */
    manifestFile?: string;
    /** 自动扫描间隔（毫秒） */
    scanInterval?: number;
    /** 是否启用自动发现 */
    enabled?: boolean;
}

/**
 * 应用清单接口
 */
export interface AppManifest {
    name: string;
    version: string;
    entry: string;
    activeWhen: string;
    container?: string;
    framework?: string;
    dependencies?: string[];
    customProps?: Record<string, any>;
}

/**
 * 自动发现管理器
 */
export class AutoDiscovery {
    private config: DiscoveryConfig;
    private discoveredApps = new Map<string, AppManifest>();
    private scanTimer?: NodeJS.Timeout;
    private isScanning = false;

    constructor(config: DiscoveryConfig = {}) {
        this.config = {
            basePath: '/',
            manifestFile: 'micro-app.json',
            scanInterval: 30000, // 30秒
            enabled: true,
            ...config
        };
    }

    /**
     * 启动自动发现
     */
    start(): void {
        if (!this.config.enabled) {
            return;
        }

        logger.info('Starting auto discovery...');

        // 立即执行一次扫描
        this.scan();

        // 设置定时扫描
        if (this.config.scanInterval && this.config.scanInterval > 0) {
            this.scanTimer = setInterval(() => {
                this.scan();
            }, this.config.scanInterval);
        }
    }

    /**
     * 停止自动发现
     */
    stop(): void {
        if (this.scanTimer) {
            clearInterval(this.scanTimer);
            this.scanTimer = undefined;
        }
        logger.info('Auto discovery stopped');
    }

    /**
     * 扫描应用
     */
    async scan(): Promise<AppManifest[]> {
        if (this.isScanning) {
            return Array.from(this.discoveredApps.values());
        }

        this.isScanning = true;
        logger.debug('Scanning for micro apps...');

        try {
            const apps = await this.scanForApps();

            // 更新发现的应用列表
            this.updateDiscoveredApps(apps);

            logger.info(`Discovered ${apps.length} micro apps`);
            return apps;
        } catch (error) {
            logger.error('Error during app scanning:', error);
            return [];
        } finally {
            this.isScanning = false;
        }
    }

    /**
     * 获取已发现的应用
     */
    getDiscoveredApps(): AppManifest[] {
        return Array.from(this.discoveredApps.values());
    }

    /**
     * 将清单转换为应用配置
     */
    manifestToConfig(manifest: AppManifest): MicroAppConfig {
        return {
            name: manifest.name,
            entry: manifest.entry,
            container: manifest.container || `#${manifest.name}-container`,
            activeWhen: manifest.activeWhen,
            customProps: {
                ...manifest.customProps,
                framework: manifest.framework,
                version: manifest.version,
                dependencies: manifest.dependencies
            }
        };
    }

    /**
     * 扫描应用实现
     */
    private async scanForApps(): Promise<AppManifest[]> {
        const apps: AppManifest[] = [];

        try {
            // 方法1: 从已知的清单文件位置加载
            const knownManifests = await this.loadKnownManifests();
            apps.push(...knownManifests);

            // 方法2: 从DOM中发现应用标记
            const domApps = this.scanFromDOM();
            apps.push(...domApps);

            // 方法3: 从全局配置中发现
            const globalApps = this.scanFromGlobalConfig();
            apps.push(...globalApps);

        } catch (error) {
            logger.error('Error scanning for apps:', error);
        }

        return apps;
    }

    /**
     * 加载已知的清单文件
     */
    private async loadKnownManifests(): Promise<AppManifest[]> {
        const manifests: AppManifest[] = [];

        // 尝试从常见位置加载清单文件
        const commonPaths = [
            '/micro-apps.json',
            '/apps/manifest.json',
            '/public/micro-apps.json'
        ];

        for (const path of commonPaths) {
            try {
                const response = await fetch(path);
                if (response.ok) {
                    const data = await response.json();
                    if (Array.isArray(data)) {
                        manifests.push(...data);
                    } else if (data.apps && Array.isArray(data.apps)) {
                        manifests.push(...data.apps);
                    }
                }
            } catch (error) {
                // 忽略加载错误，继续尝试其他路径
            }
        }

        return manifests;
    }

    /**
     * 从DOM中扫描应用
     */
    private scanFromDOM(): AppManifest[] {
        const apps: AppManifest[] = [];

        // 查找带有 data-micro-app 属性的元素
        const elements = document.querySelectorAll('[data-micro-app]');

        elements.forEach(element => {
            try {
                const config = element.getAttribute('data-micro-app');
                if (config) {
                    const manifest = JSON.parse(config) as AppManifest;
                    apps.push(manifest);
                }
            } catch (error) {
                logger.warn('Invalid micro app config in DOM element:', error);
            }
        });

        return apps;
    }

    /**
     * 从全局配置中扫描应用
     */
    private scanFromGlobalConfig(): AppManifest[] {
        const apps: AppManifest[] = [];

        // 检查全局变量
        const globalConfig = (window as any).__MICRO_APPS__;
        if (globalConfig && Array.isArray(globalConfig)) {
            apps.push(...globalConfig);
        }

        return apps;
    }

    /**
     * 更新已发现的应用列表
     */
    private updateDiscoveredApps(apps: AppManifest[]): void {
        const newApps = new Map<string, AppManifest>();

        apps.forEach(app => {
            newApps.set(app.name, app);
        });

        // 检查新增的应用
        newApps.forEach((app, name) => {
            if (!this.discoveredApps.has(name)) {
                logger.info(`New micro app discovered: ${name}`);
            }
        });

        // 检查移除的应用
        this.discoveredApps.forEach((app, name) => {
            if (!newApps.has(name)) {
                logger.info(`Micro app removed: ${name}`);
            }
        });

        this.discoveredApps = newApps;
    }
}

/**
 * 应用发现工具函数
 */
export const discoveryUtils = {
    /**
     * 验证应用清单
     */
    validateManifest(manifest: any): manifest is AppManifest {
        return (
            typeof manifest === 'object' &&
            typeof manifest.name === 'string' &&
            typeof manifest.entry === 'string' &&
            typeof manifest.activeWhen === 'string'
        );
    },

    /**
     * 创建默认清单
     */
    createDefaultManifest(name: string, entry: string, activeWhen: string): AppManifest {
        return {
            name,
            version: '1.0.0',
            entry,
            activeWhen,
            framework: 'unknown'
        };
    }
};

export default AutoDiscovery;