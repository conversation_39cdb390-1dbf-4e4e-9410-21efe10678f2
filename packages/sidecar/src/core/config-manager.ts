/**
 * 配置管理器
 * 管理Sidecar模式的配置加载和管理
 */

import type { MicroAppConfig, MicroCoreOptions } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * Sidecar配置接口
 */
export interface SidecarConfig extends MicroCoreOptions {
    /** 应用列表 */
    apps?: MicroAppConfig[];
    /** 自动发现配置 */
    autoDiscovery?: {
        enabled?: boolean;
        scanInterval?: number;
        manifestFile?: string;
    };
    /** 框架检测配置 */
    frameworkDetection?: {
        enabled?: boolean;
        priority?: string[];
    };
    /** 容器配置 */
    container?: {
        selector?: string;
        createIfNotExists?: boolean;
    };
    /** 路由配置 */
    routing?: {
        mode?: 'hash' | 'history' | 'memory';
        base?: string;
        fallback?: string;
    };
}

/**
 * 配置来源枚举
 */
export enum ConfigSource {
    DEFAULT = 'default',
    SCRIPT_TAG = 'script-tag',
    GLOBAL_VARIABLE = 'global-variable',
    LOCAL_STORAGE = 'local-storage',
    URL_PARAMS = 'url-params',
    REMOTE = 'remote'
}

/**
 * 配置管理器
 */
export class ConfigManager {
    private config: SidecarConfig = {};
    private configSources: Map<ConfigSource, any> = new Map();
    private isLoaded = false;

    /**
     * 加载配置
     */
    async load(): Promise<SidecarConfig> {
        if (this.isLoaded) {
            return this.config;
        }

        logger.info('Loading Sidecar configuration...');

        try {
            // 按优先级加载配置
            await this.loadFromSources();

            // 合并配置
            this.mergeConfigs();

            // 验证配置
            this.validateConfig();

            this.isLoaded = true;
            logger.info('Sidecar configuration loaded successfully');

        } catch (error) {
            logger.error('Failed to load Sidecar configuration:', error);
            throw error;
        }

        return this.config;
    }

    /**
     * 获取配置
     */
    getConfig(): SidecarConfig {
        return { ...this.config };
    }

    /**
     * 更新配置
     */
    updateConfig(updates: Partial<SidecarConfig>): void {
        this.config = { ...this.config, ...updates };
        logger.debug('Configuration updated:', updates);
    }

    /**
     * 重新加载配置
     */
    async reload(): Promise<SidecarConfig> {
        this.isLoaded = false;
        this.configSources.clear();
        return this.load();
    }

    /**
     * 从各种来源加载配置
     */
    private async loadFromSources(): Promise<void> {
        // 1. 默认配置
        this.configSources.set(ConfigSource.DEFAULT, this.getDefaultConfig());

        // 2. 从script标签加载
        const scriptConfig = this.loadFromScriptTag();
        if (scriptConfig) {
            this.configSources.set(ConfigSource.SCRIPT_TAG, scriptConfig);
        }

        // 3. 从全局变量加载
        const globalConfig = this.loadFromGlobalVariable();
        if (globalConfig) {
            this.configSources.set(ConfigSource.GLOBAL_VARIABLE, globalConfig);
        }

        // 4. 从localStorage加载
        const localStorageConfig = this.loadFromLocalStorage();
        if (localStorageConfig) {
            this.configSources.set(ConfigSource.LOCAL_STORAGE, localStorageConfig);
        }

        // 5. 从URL参数加载
        const urlConfig = this.loadFromUrlParams();
        if (urlConfig) {
            this.configSources.set(ConfigSource.URL_PARAMS, urlConfig);
        }

        // 6. 从远程加载
        const remoteConfig = await this.loadFromRemote();
        if (remoteConfig) {
            this.configSources.set(ConfigSource.REMOTE, remoteConfig);
        }
    }

    /**
     * 获取默认配置
     */
    private getDefaultConfig(): SidecarConfig {
        return {
            development: process.env.NODE_ENV === 'development',
            sandbox: {
                enabled: true,
                type: 'proxy'
            },
            autoDiscovery: {
                enabled: true,
                scanInterval: 30000,
                manifestFile: 'micro-app.json'
            },
            frameworkDetection: {
                enabled: true,
                priority: ['react', 'vue', 'angular']
            },
            container: {
                selector: '#micro-app-container',
                createIfNotExists: true
            },
            routing: {
                mode: 'history',
                base: '/',
                fallback: '/'
            },
            apps: []
        };
    }

    /**
     * 从script标签加载配置
     */
    private loadFromScriptTag(): SidecarConfig | null {
        const scriptElement = document.querySelector('script[data-micro-core-config]');
        if (scriptElement) {
            try {
                const configText = scriptElement.getAttribute('data-micro-core-config');
                if (configText) {
                    return JSON.parse(configText);
                }
            } catch (error) {
                logger.warn('Invalid configuration in script tag:', error);
            }
        }
        return null;
    }

    /**
     * 从全局变量加载配置
     */
    private loadFromGlobalVariable(): SidecarConfig | null {
        const globalConfig = (window as any).__MICRO_CORE_CONFIG__;
        if (globalConfig && typeof globalConfig === 'object') {
            return globalConfig;
        }
        return null;
    }

    /**
     * 从localStorage加载配置
     */
    private loadFromLocalStorage(): SidecarConfig | null {
        try {
            const configText = localStorage.getItem('micro-core-config');
            if (configText) {
                return JSON.parse(configText);
            }
        } catch (error) {
            logger.warn('Invalid configuration in localStorage:', error);
        }
        return null;
    }

    /**
     * 从URL参数加载配置
     */
    private loadFromUrlParams(): SidecarConfig | null {
        const urlParams = new URLSearchParams(window.location.search);
        const configParam = urlParams.get('micro-core-config');

        if (configParam) {
            try {
                return JSON.parse(decodeURIComponent(configParam));
            } catch (error) {
                logger.warn('Invalid configuration in URL params:', error);
            }
        }

        // 也支持单独的参数
        const config: Partial<SidecarConfig> = {};

        if (urlParams.has('debug')) {
            config.development = urlParams.get('debug') === 'true';
        }

        if (urlParams.has('sandbox')) {
            config.sandbox = { enabled: urlParams.get('sandbox') === 'true' };
        }

        return Object.keys(config).length > 0 ? config : null;
    }

    /**
     * 从远程加载配置
     */
    private async loadFromRemote(): Promise<SidecarConfig | null> {
        // 检查是否有远程配置URL
        const configUrl = (window as any).__MICRO_CORE_CONFIG_URL__ ||
            new URLSearchParams(window.location.search).get('config-url');

        if (!configUrl) {
            return null;
        }

        try {
            const response = await fetch(configUrl);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            logger.warn('Failed to load remote configuration:', error);
        }

        return null;
    }

    /**
     * 合并配置
     */
    private mergeConfigs(): void {
        // 按优先级合并配置（后面的覆盖前面的）
        const sources = [
            ConfigSource.DEFAULT,
            ConfigSource.REMOTE,
            ConfigSource.LOCAL_STORAGE,
            ConfigSource.GLOBAL_VARIABLE,
            ConfigSource.SCRIPT_TAG,
            ConfigSource.URL_PARAMS
        ];

        this.config = {};

        sources.forEach(source => {
            const sourceConfig = this.configSources.get(source);
            if (sourceConfig) {
                this.config = this.deepMerge(this.config, sourceConfig);
            }
        });
    }

    /**
     * 深度合并对象
     */
    private deepMerge(target: any, source: any): any {
        const result = { ...target };

        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (this.isObject(source[key]) && this.isObject(target[key])) {
                    result[key] = this.deepMerge(target[key], source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }

        return result;
    }

    /**
     * 检查是否为对象
     */
    private isObject(value: any): boolean {
        return value !== null && typeof value === 'object' && !Array.isArray(value);
    }

    /**
     * 验证配置
     */
    private validateConfig(): void {
        // 验证必要的配置项
        if (!this.config.apps) {
            this.config.apps = [];
        }

        // 验证应用配置
        this.config.apps.forEach((app, index) => {
            if (!app.name || !app.entry) {
                throw new Error(`Invalid app configuration at index ${index}: name and entry are required`);
            }
        });

        logger.debug('Configuration validation passed');
    }
}

/**
 * 配置工具函数
 */
export const configUtils = {
    /**
     * 创建默认应用配置
     */
    createDefaultAppConfig(name: string, entry: string): MicroAppConfig {
        return {
            name,
            entry,
            container: `#${name}-container`,
            activeWhen: `/${name}`
        };
    },

    /**
     * 验证应用配置
     */
    validateAppConfig(config: any): config is MicroAppConfig {
        return (
            typeof config === 'object' &&
            typeof config.name === 'string' &&
            typeof config.entry === 'string' &&
            (typeof config.container === 'string' || config.container instanceof HTMLElement) &&
            (typeof config.activeWhen === 'string' || typeof config.activeWhen === 'function')
        );
    }
};

export default ConfigManager;