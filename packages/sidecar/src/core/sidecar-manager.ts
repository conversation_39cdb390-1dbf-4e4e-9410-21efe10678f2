/**
 * Sidecar管理器
 * 实现Sidecar模式的核心管理功能
 */

import type { MicroAppConfig, MicroCoreOptions } from '@micro-core/core';
import { logger, MicroCoreKernel } from '@micro-core/core';
import { FrameworkDetector } from '../utils/framework-detector';
import { AutoDiscovery } from './auto-discovery';
import { ConfigManager } from './config-manager';

/**
 * Sidecar管理器选项
 */
export interface SidecarManagerOptions extends MicroCoreOptions {
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 容器选择器 */
    container?: string;
    /** 是否创建容器 */
    createContainer?: boolean;
}

/**
 * Sidecar管理器
 */
export class SidecarManager {
    private kernel: MicroCoreKernel;
    private configManager: ConfigManager;
    private autoDiscovery: AutoDiscovery;
    private frameworkDetector: FrameworkDetector;
    private isInitialized = false;
    private isStarted = false;

    constructor(options: SidecarManagerOptions = {}) {
        this.kernel = new MicroCoreKernel(options);
        this.configManager = new ConfigManager();
        this.autoDiscovery = new AutoDiscovery();
        this.frameworkDetector = new FrameworkDetector();
    }

    /**
     * 初始化Sidecar
     */
    async init(options: SidecarManagerOptions = {}): Promise<void> {
        if (this.isInitialized) {
            logger.debug('Sidecar already initialized, skipping...');
            return;
        }

        logger.info('Initializing Sidecar...');

        try {
            const mergedOptions = await this.loadAndMergeConfig(options);
            
            await this.initializeFrameworkDetection(mergedOptions);
            await this.initializeContainer(mergedOptions);
            await this.registerConfiguredApps(mergedOptions);
            await this.initializeAutoDiscovery(mergedOptions);

            this.isInitialized = true;
            logger.info('Sidecar initialized successfully');

            // 自动启动
            if (options.autoStart !== false) {
                await this.start();
            }

        } catch (error) {
            logger.error('Failed to initialize Sidecar:', error);
            this.isInitialized = false;
            throw error;
        }
    }

    /**
     * 加载并合并配置
     */
    private async loadAndMergeConfig(options: SidecarManagerOptions): Promise<SidecarManagerOptions> {
        try {
            const config = await this.configManager.load();
            return { ...config, ...options };
        } catch (error) {
            logger.warn('Failed to load config, using provided options only:', error);
            return options;
        }
    }

    /**
     * 初始化框架检测
     */
    private async initializeFrameworkDetection(options: SidecarManagerOptions): Promise<void> {
        if (!options.frameworkDetection?.enabled) {
            return;
        }

        try {
            const detectedFramework = this.frameworkDetector.detect();
            logger.info(`Detected framework: ${detectedFramework || 'unknown'}`);
            
            // 根据检测到的框架调整配置
            if (detectedFramework) {
                await this.applyFrameworkSpecificConfig(detectedFramework, options);
            }
        } catch (error) {
            logger.warn('Framework detection failed:', error);
        }
    }

    /**
     * 应用框架特定配置
     */
    private async applyFrameworkSpecificConfig(framework: string, options: SidecarManagerOptions): Promise<void> {
        // 根据不同框架应用特定配置
        switch (framework.toLowerCase()) {
            case 'react':
                // React 特定配置
                break;
            case 'vue':
                // Vue 特定配置
                break;
            case 'angular':
                // Angular 特定配置
                break;
            default:
                logger.debug(`No specific configuration for framework: ${framework}`);
        }
    }

    /**
     * 初始化容器
     */
    private async initializeContainer(options: SidecarManagerOptions): Promise<void> {
        if (!options.container?.createIfNotExists) {
            return;
        }

        try {
            const selector = options.container.selector || '#micro-app-container';
            this.ensureContainer(selector);
            logger.debug(`Container ensured: ${selector}`);
        } catch (error) {
            logger.error('Failed to initialize container:', error);
            throw error;
        }
    }

    /**
     * 注册配置的应用
     */
    private async registerConfiguredApps(options: SidecarManagerOptions): Promise<void> {
        if (!options.apps || options.apps.length === 0) {
            logger.debug('No configured apps to register');
            return;
        }

        try {
            const registrationPromises = options.apps.map(async (app) => {
                try {
                    await this.kernel.registerApplication(app);
                    logger.debug(`Registered app: ${app.name}`);
                } catch (error) {
                    logger.error(`Failed to register app ${app.name}:`, error);
                    throw error;
                }
            });

            await Promise.allSettled(registrationPromises);
            logger.info(`Registered ${options.apps.length} configured apps`);
        } catch (error) {
            logger.error('Failed to register configured apps:', error);
            throw error;
        }
    }

    /**
     * 初始化自动发现
     */
    private async initializeAutoDiscovery(options: SidecarManagerOptions): Promise<void> {
        if (!options.autoDiscovery?.enabled) {
            logger.debug('Auto discovery disabled');
            return;
        }

        try {
            await this.autoDiscovery.start();
            logger.debug('Auto discovery started');

            // 扫描并注册发现的应用
            const discoveredApps = await this.autoDiscovery.scan();
            
            if (discoveredApps.length > 0) {
                const registrationPromises = discoveredApps.map(async (manifest) => {
                    try {
                        const appConfig = this.autoDiscovery.manifestToConfig(manifest);
                        await this.kernel.registerApplication(appConfig);
                        logger.debug(`Auto-registered app: ${appConfig.name}`);
                    } catch (error) {
                        logger.error(`Failed to auto-register app from manifest:`, error);
                    }
                });

                await Promise.allSettled(registrationPromises);
                logger.info(`Auto-discovered and registered ${discoveredApps.length} apps`);
            } else {
                logger.debug('No apps discovered during auto-discovery');
            }
        } catch (error) {
            logger.error('Failed to initialize auto discovery:', error);
            // 自动发现失败不应该阻止整个初始化过程
            logger.warn('Continuing initialization without auto discovery');
        }
    }

    /**
     * 启动Sidecar
     */
    async start(): Promise<void> {
        if (!this.isInitialized) {
            await this.init();
        }

        if (this.isStarted) {
            logger.debug('Sidecar already started, skipping...');
            return;
        }

        logger.info('Starting Sidecar...');

        try {
            // 启动内核
            await this.kernel.start();

            this.isStarted = true;
            logger.info('Sidecar started successfully');

        } catch (error) {
            logger.error('Failed to start Sidecar:', error);
            this.isStarted = false;
            throw error;
        }
    }

    /**
     * 停止Sidecar
     */
    async stop(): Promise<void> {
        if (!this.isStarted) {
            logger.debug('Sidecar not started, skipping stop...');
            return;
        }

        logger.info('Stopping Sidecar...');

        try {
            // 停止内核
            await this.kernel.stop();

            // 停止自动发现
            await this.autoDiscovery.stop();

            this.isStarted = false;
            logger.info('Sidecar stopped successfully');

        } catch (error) {
            logger.error('Failed to stop Sidecar:', error);
            throw error;
        }
    }

    /**
     * 注册应用
     */
    async registerApp(config: any): Promise<void> {
        try {
            await this.kernel.registerApplication(config);
            logger.debug(`App registered: ${config.name}`);
        } catch (error) {
            logger.error(`Failed to register app ${config.name}:`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unregisterApp(name: string): Promise<void> {
        try {
            await this.kernel.unregisterApplication(name);
            logger.debug(`App unregistered: ${name}`);
        } catch (error) {
            logger.error(`Failed to unregister app ${name}:`, error);
            throw error;
        }
    }

    /**
     * 获取内核实例
     */
    getKernel(): any {
        return this.kernel;
    }

    /**
     * 获取配置管理器
     */
    getConfigManager(): ConfigManager {
        return this.configManager;
    }

    /**
     * 获取自动发现实例
     */
    getAutoDiscovery(): AutoDiscovery {
        return this.autoDiscovery;
    }

    /**
     * 获取框架检测器
     */
    getFrameworkDetector(): FrameworkDetector {
        return this.frameworkDetector;
    }

    /**
     * 重新加载配置
     */
    async reloadConfig(): Promise<void> {
        try {
            logger.info('Reloading configuration...');
            const config = await this.configManager.load();
            
            if (config && this.isInitialized) {
                // 重新应用配置
                logger.info('Configuration reloaded successfully');
            }
        } catch (error) {
            logger.error('Failed to reload configuration:', error);
            throw error;
        }
    }

    /**
     * 获取状态信息
     */
    getStatus(): {
        initialized: boolean;
        started: boolean;
        appCount: number;
        discoveredApps: number;
    } {
        return {
            initialized: this.isInitialized,
            started: this.isStarted,
            appCount: 0, // TODO: 从 kernel 获取实际应用数量
            discoveredApps: 0 // TODO: 从 autoDiscovery 获取发现的应用数量
        };
    }

    /**
     * 确保容器存在
     */
    private ensureContainer(selector: string): void {
        if (typeof document === 'undefined') {
            logger.warn('Document not available, cannot ensure container');
            return;
        }

        let container = document.querySelector(selector);
        if (!container) {
            logger.info(`Creating container: ${selector}`);
            container = document.createElement('div');
            container.id = selector.replace('#', '');
            document.body.appendChild(container);
        }
    }
    }

    /**
     * 获取内核实例
     */
    getKernel(): MicroCoreKernel {
        return this.kernel;
    }

    /**
     * 获取配置管理器
     */
    getConfigManager(): ConfigManager {
        return this.configManager;
    }

    /**
     * 获取自动发现实例
     */
    getAutoDiscovery(): AutoDiscovery {
        return this.autoDiscovery;
    }

    /**
     * 获取框架检测器
     */
    getFrameworkDetector(): FrameworkDetector {
        return this.frameworkDetector;
    }

    /**
     * 重新加载配置
     */
    async reloadConfig(): Promise<void> {
        logger.info('Reloading Sidecar configuration...');

        try {
            // 重新加载配置
            const config = await this.configManager.reload();

            // 重新扫描应用
            if (config.autoDiscovery?.enabled) {
                const discoveredApps = await this.autoDiscovery.scan();
                discoveredApps.forEach(manifest => {
                    const appConfig = this.autoDiscovery.manifestToConfig(manifest);
                    try {
                        this.kernel.registerApplication(appConfig);
                    } catch (error) {
                        // 应用可能已经注册，忽略错误
                    }
                });
            }

            logger.info('Configuration reloaded successfully');

        } catch (error) {
            logger.error('Failed to reload configuration:', error);
            throw error;
        }
    }

    /**
     * 获取状态
     */
    getStatus(): {
        initialized: boolean;
        started: boolean;
        appsCount: number;
        discoveredAppsCount: number;
    } {
        return {
            initialized: this.isInitialized,
            started: this.isStarted,
            appsCount: this.kernel.getApplications().length,
            discoveredAppsCount: this.autoDiscovery.getDiscoveredApps().length
        };
    }

    /**
     * 确保容器存在
     */
    private ensureContainer(selector: string): HTMLElement {
        let container = document.querySelector(selector) as HTMLElement;

        if (!container) {
            // 创建容器
            container = document.createElement('div');
            container.id = selector.replace('#', '');

            // 添加到body
            document.body.appendChild(container);

            logger.info(`Created container: ${selector}`);
        }

        return container;
    }
}

export default SidecarManager;