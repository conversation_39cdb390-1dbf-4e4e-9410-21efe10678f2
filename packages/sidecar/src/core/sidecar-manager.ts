/**
 * Sidecar管理器
 * 实现Sidecar模式的核心管理功能
 */

import type { MicroAppConfig, MicroCoreOptions } from '@micro-core/core';
import { logger, MicroCoreKernel } from '@micro-core/core';
import { FrameworkDetector } from '../utils/framework-detector';
import { AutoDiscovery } from './auto-discovery';
import { ConfigManager } from './config-manager';

/**
 * Sidecar管理器选项
 */
export interface SidecarManagerOptions extends MicroCoreOptions {
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 容器选择器 */
    container?: string;
    /** 是否创建容器 */
    createContainer?: boolean;
}

/**
 * Sidecar管理器
 */
export class SidecarManager {
    private kernel: MicroCoreKernel;
    private configManager: ConfigManager;
    private autoDiscovery: AutoDiscovery;
    private frameworkDetector: FrameworkDetector;
    private isInitialized = false;
    private isStarted = false;

    constructor(options: SidecarManagerOptions = {}) {
        this.kernel = new MicroCoreKernel(options);
        this.configManager = new ConfigManager();
        this.autoDiscovery = new AutoDiscovery();
        this.frameworkDetector = new FrameworkDetector();
    }

    /**
     * 初始化Sidecar
     */
    async init(options: SidecarManagerOptions = {}): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        logger.info('Initializing Sidecar...');

        try {
            // 加载配置
            const config = await this.configManager.load();

            // 合并选项
            const mergedOptions = { ...config, ...options };

            // 检测框架
            if (mergedOptions.frameworkDetection?.enabled) {
                const detectedFramework = this.frameworkDetector.detect();
                logger.info(`Detected framework: ${detectedFramework || 'unknown'}`);
            }

            // 创建容器
            if (mergedOptions.container?.createIfNotExists) {
                this.ensureContainer(mergedOptions.container.selector || '#micro-app-container');
            }

            // 注册应用
            if (mergedOptions.apps && mergedOptions.apps.length > 0) {
                mergedOptions.apps.forEach(app => {
                    this.kernel.registerApplication(app);
                });
            }

            // 启动自动发现
            if (mergedOptions.autoDiscovery?.enabled) {
                this.autoDiscovery.start();

                // 注册发现的应用
                const discoveredApps = await this.autoDiscovery.scan();
                discoveredApps.forEach(manifest => {
                    const appConfig = this.autoDiscovery.manifestToConfig(manifest);
                    this.kernel.registerApplication(appConfig);
                });
            }

            this.isInitialized = true;
            logger.info('Sidecar initialized successfully');

            // 自动启动
            if (options.autoStart !== false) {
                await this.start();
            }

        } catch (error) {
            logger.error('Failed to initialize Sidecar:', error);
            throw error;
        }
    }

    /**
     * 启动Sidecar
     */
    async start(): Promise<void> {
        if (!this.isInitialized) {
            await this.init();
        }

        if (this.isStarted) {
            return;
        }

        logger.info('Starting Sidecar...');

        try {
            // 启动内核
            await this.kernel.start();

            this.isStarted = true;
            logger.info('Sidecar started successfully');

        } catch (error) {
            logger.error('Failed to start Sidecar:', error);
            throw error;
        }
    }

    /**
     * 停止Sidecar
     */
    async stop(): Promise<void> {
        if (!this.isStarted) {
            return;
        }

        logger.info('Stopping Sidecar...');

        try {
            // 停止内核
            await this.kernel.stop();

            // 停止自动发现
            this.autoDiscovery.stop();

            this.isStarted = false;
            logger.info('Sidecar stopped successfully');

        } catch (error) {
            logger.error('Failed to stop Sidecar:', error);
            throw error;
        }
    }

    /**
     * 注册应用
     */
    registerApp(config: MicroAppConfig): void {
        this.kernel.registerApplication(config);
    }

    /**
     * 卸载应用
     */
    unregisterApp(name: string): void {
        this.kernel.unregisterApplication(name);
    }

    /**
     * 获取内核实例
     */
    getKernel(): MicroCoreKernel {
        return this.kernel;
    }

    /**
     * 获取配置管理器
     */
    getConfigManager(): ConfigManager {
        return this.configManager;
    }

    /**
     * 获取自动发现实例
     */
    getAutoDiscovery(): AutoDiscovery {
        return this.autoDiscovery;
    }

    /**
     * 获取框架检测器
     */
    getFrameworkDetector(): FrameworkDetector {
        return this.frameworkDetector;
    }

    /**
     * 重新加载配置
     */
    async reloadConfig(): Promise<void> {
        logger.info('Reloading Sidecar configuration...');

        try {
            // 重新加载配置
            const config = await this.configManager.reload();

            // 重新扫描应用
            if (config.autoDiscovery?.enabled) {
                const discoveredApps = await this.autoDiscovery.scan();
                discoveredApps.forEach(manifest => {
                    const appConfig = this.autoDiscovery.manifestToConfig(manifest);
                    try {
                        this.kernel.registerApplication(appConfig);
                    } catch (error) {
                        // 应用可能已经注册，忽略错误
                    }
                });
            }

            logger.info('Configuration reloaded successfully');

        } catch (error) {
            logger.error('Failed to reload configuration:', error);
            throw error;
        }
    }

    /**
     * 获取状态
     */
    getStatus(): {
        initialized: boolean;
        started: boolean;
        appsCount: number;
        discoveredAppsCount: number;
    } {
        return {
            initialized: this.isInitialized,
            started: this.isStarted,
            appsCount: this.kernel.getApplications().length,
            discoveredAppsCount: this.autoDiscovery.getDiscoveredApps().length
        };
    }

    /**
     * 确保容器存在
     */
    private ensureContainer(selector: string): HTMLElement {
        let container = document.querySelector(selector) as HTMLElement;

        if (!container) {
            // 创建容器
            container = document.createElement('div');
            container.id = selector.replace('#', '');

            // 添加到body
            document.body.appendChild(container);

            logger.info(`Created container: ${selector}`);
        }

        return container;
    }
}

export default SidecarManager;