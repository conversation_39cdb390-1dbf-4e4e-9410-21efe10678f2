/**
 * @fileoverview 原生 JavaScript 应用迁移示例
 * @description 展示如何将传统原生 JS 应用迁移到微前端架构
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { AppConfig } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * 原生 JS 应用配置
 */
export interface VanillaJSAppConfig extends AppConfig {
    /** 应用脚本列表 */
    scripts?: string[];
    /** 应用样式列表 */
    styles?: string[];
    /** HTML 模板 */
    template?: string;
    /** 初始化函数名 */
    initFunction?: string;
    /** 销毁函数名 */
    destroyFunction?: string;
    /** 全局变量命名空间 */
    namespace?: string;
}

/**
 * 原生 JavaScript 应用迁移适配器
 */
export class VanillaJSAppAdapter {
    private config: VanillaJSAppConfig;
    private loadedScripts = new Set<string>();
    private loadedStyles = new Set<string>();
    private appInstance: any = null;

    constructor(config: VanillaJSAppConfig) {
        this.config = {
            scripts: [],
            styles: [],
            namespace: config.name || 'VanillaApp',
            ...config
        };
    }

    /**
     * 引导应用
     */
    async bootstrap(): Promise<void> {
        logger.info(`Bootstrapping Vanilla JS app: ${this.config.name}`);

        try {
            // 加载样式文件
            if (this.config.styles && this.config.styles.length > 0) {
                await this.loadStyles();
            }

            // 加载脚本文件
            if (this.config.scripts && this.config.scripts.length > 0) {
                await this.loadScripts();
            }

            logger.info(`Vanilla JS app bootstrapped: ${this.config.name}`);

        } catch (error) {
            logger.error(`Failed to bootstrap Vanilla JS app: ${this.config.name}`, error);
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    async mount(container: HTMLElement): Promise<void> {
        logger.info(`Mounting Vanilla JS app: ${this.config.name}`);

        try {
            // 设置容器属性
            container.setAttribute('data-app', this.config.name);
            container.setAttribute('data-app-type', 'vanilla-js');

            // 加载 HTML 模板
            if (this.config.template) {
                await this.loadTemplate(container);
            } else if (this.config.entry && this.config.entry.endsWith('.html')) {
                await this.loadHTMLEntry(container);
            }

            // 执行初始化函数
            if (this.config.initFunction) {
                await this.executeInitFunction(container);
            }

            // 如果有命名空间，尝试获取应用实例
            if (this.config.namespace && (window as any)[this.config.namespace]) {
                this.appInstance = (window as any)[this.config.namespace];
                
                // 如果应用实例有 mount 方法，调用它
                if (this.appInstance.mount && typeof this.appInstance.mount === 'function') {
                    await this.appInstance.mount(container);
                }
            }

            logger.info(`Vanilla JS app mounted: ${this.config.name}`);

        } catch (error) {
            logger.error(`Failed to mount Vanilla JS app: ${this.config.name}`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmount(container: HTMLElement): Promise<void> {
        logger.info(`Unmounting Vanilla JS app: ${this.config.name}`);

        try {
            // 执行销毁函数
            if (this.config.destroyFunction) {
                await this.executeDestroyFunction(container);
            }

            // 如果应用实例有 unmount 方法，调用它
            if (this.appInstance && this.appInstance.unmount && typeof this.appInstance.unmount === 'function') {
                await this.appInstance.unmount(container);
            }

            // 清理容器
            container.innerHTML = '';
            container.removeAttribute('data-app');
            container.removeAttribute('data-app-type');

            // 清理全局变量（可选）
            if (this.config.namespace && this.shouldCleanupNamespace()) {
                delete (window as any)[this.config.namespace];
            }

            this.appInstance = null;

            logger.info(`Vanilla JS app unmounted: ${this.config.name}`);

        } catch (error) {
            logger.error(`Failed to unmount Vanilla JS app: ${this.config.name}`, error);
            throw error;
        }
    }

    /**
     * 加载样式文件
     */
    private async loadStyles(): Promise<void> {
        const loadPromises = this.config.styles!.map(styleUrl => this.loadStyle(styleUrl));
        await Promise.all(loadPromises);
    }

    /**
     * 加载单个样式文件
     */
    private async loadStyle(styleUrl: string): Promise<void> {
        if (this.loadedStyles.has(styleUrl)) {
            return;
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = styleUrl;
            link.onload = () => {
                this.loadedStyles.add(styleUrl);
                logger.info(`Style loaded: ${styleUrl}`);
                resolve();
            };
            link.onerror = () => {
                reject(new Error(`Failed to load style: ${styleUrl}`));
            };
            document.head.appendChild(link);
        });
    }

    /**
     * 加载脚本文件
     */
    private async loadScripts(): Promise<void> {
        // 按顺序加载脚本（避免依赖问题）
        for (const scriptUrl of this.config.scripts!) {
            await this.loadScript(scriptUrl);
        }
    }

    /**
     * 加载单个脚本文件
     */
    private async loadScript(scriptUrl: string): Promise<void> {
        if (this.loadedScripts.has(scriptUrl)) {
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptUrl;
            script.onload = () => {
                this.loadedScripts.add(scriptUrl);
                logger.info(`Script loaded: ${scriptUrl}`);
                resolve();
            };
            script.onerror = () => {
                reject(new Error(`Failed to load script: ${scriptUrl}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * 加载 HTML 模板
     */
    private async loadTemplate(container: HTMLElement): Promise<void> {
        if (!this.config.template) {
            return;
        }

        try {
            // 如果是 URL，获取内容
            if (this.config.template.startsWith('http') || this.config.template.startsWith('/')) {
                const response = await fetch(this.config.template);
                if (!response.ok) {
                    throw new Error(`Failed to fetch template: ${response.statusText}`);
                }
                const html = await response.text();
                container.innerHTML = html;
            } else {
                // 直接作为 HTML 内容
                container.innerHTML = this.config.template;
            }

        } catch (error) {
            logger.error(`Failed to load template: ${this.config.template}`, error);
            throw error;
        }
    }

    /**
     * 加载 HTML 入口文件
     */
    private async loadHTMLEntry(container: HTMLElement): Promise<void> {
        if (!this.config.entry) {
            return;
        }

        try {
            const response = await fetch(this.config.entry);
            if (!response.ok) {
                throw new Error(`Failed to fetch HTML entry: ${response.statusText}`);
            }
            const html = await response.text();
            container.innerHTML = html;

        } catch (error) {
            logger.error(`Failed to load HTML entry: ${this.config.entry}`, error);
            throw error;
        }
    }

    /**
     * 执行初始化函数
     */
    private async executeInitFunction(container: HTMLElement): Promise<void> {
        if (!this.config.initFunction) {
            return;
        }

        try {
            const initFn = (window as any)[this.config.initFunction];
            if (typeof initFn === 'function') {
                await initFn(container, this.config);
            } else {
                logger.warn(`Init function not found: ${this.config.initFunction}`);
            }

        } catch (error) {
            logger.error(`Failed to execute init function: ${this.config.initFunction}`, error);
            throw error;
        }
    }

    /**
     * 执行销毁函数
     */
    private async executeDestroyFunction(container: HTMLElement): Promise<void> {
        if (!this.config.destroyFunction) {
            return;
        }

        try {
            const destroyFn = (window as any)[this.config.destroyFunction];
            if (typeof destroyFn === 'function') {
                await destroyFn(container, this.config);
            } else {
                logger.warn(`Destroy function not found: ${this.config.destroyFunction}`);
            }

        } catch (error) {
            logger.error(`Failed to execute destroy function: ${this.config.destroyFunction}`, error);
            // 不抛出错误，避免阻塞卸载过程
        }
    }

    /**
     * 判断是否应该清理命名空间
     */
    private shouldCleanupNamespace(): boolean {
        // 检查是否有其他同类型应用还在使用相同的命名空间
        const sameNamespaceApps = document.querySelectorAll(`[data-app-namespace="${this.config.namespace}"]`);
        return sameNamespaceApps.length <= 1;
    }
}

/**
 * 创建原生 JS 应用配置的辅助函数
 */
export function createVanillaJSAppConfig(config: Partial<VanillaJSAppConfig>): VanillaJSAppConfig {
    return {
        name: config.name || 'vanilla-js-app',
        entry: config.entry || '',
        container: config.container,
        activeWhen: config.activeWhen,
        scripts: config.scripts || [],
        styles: config.styles || [],
        template: config.template,
        initFunction: config.initFunction,
        destroyFunction: config.destroyFunction,
        namespace: config.namespace || config.name || 'VanillaApp',
        ...config
    };
}

/**
 * 原生 JS 应用迁移示例
 */
export const vanillaJSMigrationExample = {
    name: 'legacy-vanilla-app',
    entry: '/legacy-apps/vanilla-app.html',
    container: '#vanilla-app-container',
    activeWhen: '/legacy/vanilla',
    scripts: [
        '/legacy-apps/js/utils.js',
        '/legacy-apps/js/components.js',
        '/legacy-apps/js/app.js'
    ],
    styles: [
        '/legacy-apps/css/reset.css',
        '/legacy-apps/css/components.css',
        '/legacy-apps/css/app.css'
    ],
    initFunction: 'initLegacyApp',
    destroyFunction: 'destroyLegacyApp',
    namespace: 'LegacyApp'
};

export default VanillaJSAppAdapter;
