/**
 * @fileoverview 传统应用迁移适配器导出
 * @description 提供各种传统应用的迁移适配器和示例
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// jQuery 应用适配器
export {
    JQueryAppAdapter,
    createJQueryAppConfig,
    jqueryMigrationExample,
    type JQueryAppConfig
} from './jquery-app';

// 原生 JS 应用适配器
export {
    VanillaJSAppAdapter,
    createVanillaJSAppConfig,
    vanillaJSMigrationExample,
    type VanillaJSAppConfig
} from './vanilla-js-app';

/**
 * 通用迁移工具
 */
export class LegacyAppMigrationHelper {
    /**
     * 检测应用类型
     */
    static detectAppType(entry: string): 'jquery' | 'vanilla' | 'unknown' {
        if (entry.includes('jquery') || entry.includes('$')) {
            return 'jquery';
        }
        
        if (entry.endsWith('.js') || entry.endsWith('.html')) {
            return 'vanilla';
        }
        
        return 'unknown';
    }

    /**
     * 创建适配器
     */
    static createAdapter(config: any) {
        const appType = this.detectAppType(config.entry || '');
        
        switch (appType) {
            case 'jquery':
                return new (require('./jquery-app').JQueryAppAdapter)(config);
            case 'vanilla':
                return new (require('./vanilla-js-app').VanillaJSAppAdapter)(config);
            default:
                throw new Error(`Unsupported app type: ${appType}`);
        }
    }
}

/**
 * 迁移示例集合
 */
export const migrationExamples = {
    jquery: jqueryMigrationExample,
    vanillaJS: vanillaJSMigrationExample
};

export default {
    JQueryAppAdapter,
    VanillaJSAppAdapter,
    LegacyAppMigrationHelper,
    migrationExamples
};
