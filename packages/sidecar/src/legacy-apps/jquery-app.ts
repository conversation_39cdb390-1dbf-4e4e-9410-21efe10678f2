/**
 * @fileoverview jQuery 应用迁移示例
 * @description 展示如何将传统 jQuery 应用迁移到微前端架构
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { AppConfig } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * jQuery 应用配置
 */
export interface JQueryAppConfig extends AppConfig {
    /** jQuery 版本 */
    jqueryVersion?: string;
    /** 是否需要全局 jQuery */
    globalJQuery?: boolean;
    /** 依赖的 jQuery 插件 */
    plugins?: string[];
    /** DOM 准备回调 */
    onReady?: () => void;
}

/**
 * jQuery 应用迁移适配器
 */
export class JQueryAppAdapter {
    private config: JQueryAppConfig;
    private jqueryLoaded = false;
    private pluginsLoaded = new Set<string>();

    constructor(config: JQueryAppConfig) {
        this.config = {
            jqueryVersion: '3.6.0',
            globalJQuery: true,
            plugins: [],
            ...config
        };
    }

    /**
     * 引导应用
     */
    async bootstrap(): Promise<void> {
        logger.info(`Bootstrapping jQuery app: ${this.config.name}`);

        // 加载 jQuery
        await this.loadJQuery();

        // 加载插件
        if (this.config.plugins && this.config.plugins.length > 0) {
            await this.loadPlugins();
        }

        // 设置全局变量
        if (this.config.globalJQuery && window.jQuery) {
            (window as any).$ = window.jQuery;
        }

        logger.info(`jQuery app bootstrapped: ${this.config.name}`);
    }

    /**
     * 挂载应用
     */
    async mount(container: HTMLElement): Promise<void> {
        logger.info(`Mounting jQuery app: ${this.config.name}`);

        try {
            // 确保 jQuery 已加载
            if (!this.jqueryLoaded) {
                await this.loadJQuery();
            }

            // 加载应用内容
            if (this.config.entry) {
                await this.loadAppContent(container);
            }

            // 执行 DOM 准备回调
            if (this.config.onReady) {
                if (window.jQuery) {
                    window.jQuery(document).ready(() => {
                        this.config.onReady!();
                    });
                } else {
                    // 降级到原生 DOM 事件
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', this.config.onReady);
                    } else {
                        this.config.onReady();
                    }
                }
            }

            logger.info(`jQuery app mounted: ${this.config.name}`);

        } catch (error) {
            logger.error(`Failed to mount jQuery app: ${this.config.name}`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmount(container: HTMLElement): Promise<void> {
        logger.info(`Unmounting jQuery app: ${this.config.name}`);

        try {
            // 清理事件监听器
            if (window.jQuery) {
                window.jQuery(container).off();
                window.jQuery(container).empty();
            } else {
                // 原生清理
                container.innerHTML = '';
            }

            // 清理全局变量（如果需要）
            if (this.config.globalJQuery && !this.shouldKeepGlobalJQuery()) {
                delete (window as any).$;
            }

            logger.info(`jQuery app unmounted: ${this.config.name}`);

        } catch (error) {
            logger.error(`Failed to unmount jQuery app: ${this.config.name}`, error);
            throw error;
        }
    }

    /**
     * 加载 jQuery
     */
    private async loadJQuery(): Promise<void> {
        if (this.jqueryLoaded || window.jQuery) {
            this.jqueryLoaded = true;
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = `https://cdn.jsdelivr.net/npm/jquery@${this.config.jqueryVersion}/dist/jquery.min.js`;
            script.onload = () => {
                this.jqueryLoaded = true;
                logger.info(`jQuery ${this.config.jqueryVersion} loaded`);
                resolve();
            };
            script.onerror = () => {
                reject(new Error(`Failed to load jQuery ${this.config.jqueryVersion}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * 加载插件
     */
    private async loadPlugins(): Promise<void> {
        const loadPromises = this.config.plugins!.map(plugin => this.loadPlugin(plugin));
        await Promise.all(loadPromises);
    }

    /**
     * 加载单个插件
     */
    private async loadPlugin(pluginUrl: string): Promise<void> {
        if (this.pluginsLoaded.has(pluginUrl)) {
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = pluginUrl;
            script.onload = () => {
                this.pluginsLoaded.add(pluginUrl);
                logger.info(`jQuery plugin loaded: ${pluginUrl}`);
                resolve();
            };
            script.onerror = () => {
                reject(new Error(`Failed to load jQuery plugin: ${pluginUrl}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * 加载应用内容
     */
    private async loadAppContent(container: HTMLElement): Promise<void> {
        if (!this.config.entry) {
            return;
        }

        try {
            const response = await fetch(this.config.entry);
            if (!response.ok) {
                throw new Error(`Failed to fetch app content: ${response.statusText}`);
            }

            const content = await response.text();
            
            // 如果是 HTML 内容，直接插入
            if (this.config.entry.endsWith('.html')) {
                container.innerHTML = content;
            } else if (this.config.entry.endsWith('.js')) {
                // 如果是 JS 文件，创建 script 标签执行
                const script = document.createElement('script');
                script.textContent = content;
                container.appendChild(script);
            } else {
                // 默认作为 HTML 处理
                container.innerHTML = content;
            }

        } catch (error) {
            logger.error(`Failed to load app content from: ${this.config.entry}`, error);
            throw error;
        }
    }

    /**
     * 判断是否应该保留全局 jQuery
     */
    private shouldKeepGlobalJQuery(): boolean {
        // 检查是否有其他应用还在使用 jQuery
        const otherJQueryApps = document.querySelectorAll('[data-jquery-app]');
        return otherJQueryApps.length > 1;
    }
}

/**
 * 创建 jQuery 应用配置的辅助函数
 */
export function createJQueryAppConfig(config: Partial<JQueryAppConfig>): JQueryAppConfig {
    return {
        name: config.name || 'jquery-app',
        entry: config.entry || '',
        container: config.container,
        activeWhen: config.activeWhen,
        jqueryVersion: config.jqueryVersion || '3.6.0',
        globalJQuery: config.globalJQuery ?? true,
        plugins: config.plugins || [],
        onReady: config.onReady,
        ...config
    };
}

/**
 * jQuery 应用迁移示例
 */
export const jqueryMigrationExample = {
    name: 'legacy-jquery-app',
    entry: '/legacy-apps/jquery-app.html',
    container: '#jquery-app-container',
    activeWhen: '/legacy/jquery',
    jqueryVersion: '3.6.0',
    globalJQuery: true,
    plugins: [
        'https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/dist/jquery-ui.min.js'
    ],
    onReady: () => {
        console.log('jQuery app is ready!');
        // 初始化 jQuery UI 组件
        if ((window as any).$ && (window as any).$.ui) {
            (window as any).$('.datepicker').datepicker();
            (window as any).$('.draggable').draggable();
        }
    }
};

export default JQueryAppAdapter;
