/**
 * @fileoverview 消息桥接器基类
 * @description 提供应用间通信的基础桥接功能
 * <AUTHOR> <<EMAIL>>
 */

import { createMicroCoreError, logger } from '@micro-core/shared';
import { EventEmitter } from 'events';
import type {
    BridgeConfig,
    BridgeMessage,
    BridgeStats,
    IBridge,
    MessageFilter,
    MessageSerializer
} from '../types';

/**
 * 消息桥接器基类
 */
export abstract class MessageBridge extends EventEmitter implements IBridge {
    protected config: Required<BridgeConfig>;
    protected stats: BridgeStats;
    protected isInitialized = false;
    protected isDestroyed = false;
    protected messageQueue: BridgeMessage[] = [];
    protected listeners = new Set<(message: BridgeMessage) => void>();

    constructor(config: BridgeConfig = {}) {
        super();

        this.config = {
            protocol: 'postMessage',
            format: 'json',
            timeout: 5000,
            serializer: this.createDefaultSerializer(),
            filter: this.createDefaultFilter(),
            retry: {
                maxAttempts: 3,
                delay: 1000,
                backoff: 'exponential',
                condition: (error: Error) => !error.message.includes('timeout')
            },
            ...config
        };

        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            failedMessages: 0,
            averageLatency: 0
        };
    }

    /**
     * 初始化桥接器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        if (this.isDestroyed) {
            throw createMicroCoreError(
                'BRIDGE_DESTROYED',
                '桥接器已被销毁，无法重新初始化'
            );
        }

        logger.info(`正在初始化 ${this.config.protocol} 桥接器...`);

        try {
            await this.doInitialize();
            this.isInitialized = true;

            // 处理队列中的消息
            await this.processMessageQueue();

            logger.info(`${this.config.protocol} 桥接器初始化成功`);
            this.emit('initialized');

        } catch (error) {
            logger.error('桥接器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁桥接器
     */
    async destroy(): Promise<void> {
        if (this.isDestroyed) {
            return;
        }

        logger.info(`正在销毁 ${this.config.protocol} 桥接器...`);

        try {
            await this.doDestroy();

            this.isDestroyed = true;
            this.isInitialized = false;
            this.messageQueue = [];
            this.listeners.clear();
            this.removeAllListeners();

            logger.info(`${this.config.protocol} 桥接器已销毁`);

        } catch (error) {
            logger.error('桥接器销毁失败:', error);
            throw error;
        }
    }

    /**
     * 发送消息
     */
    async send(message: BridgeMessage): Promise<void> {
        if (this.isDestroyed) {
            throw createMicroCoreError(
                'BRIDGE_DESTROYED',
                '桥接器已被销毁，无法发送消息'
            );
        }

        // 如果未初始化，将消息加入队列
        if (!this.isInitialized) {
            this.messageQueue.push(message);
            logger.debug('桥接器未初始化，消息已加入队列', { messageId: message.id });
            return;
        }

        // 过滤消息
        if (!this.config.filter.filter(message)) {
            logger.debug('消息被过滤器拒绝', { messageId: message.id });
            return;
        }

        const startTime = Date.now();

        try {
            // 序列化消息
            const serializedMessage = this.config.serializer.serialize(message);

            // 发送消息
            await this.doSend(serializedMessage, message);

            // 更新统计
            this.stats.messagesSent++;
            this.updateLatency(Date.now() - startTime);

            logger.debug('消息发送成功', {
                messageId: message.id,
                type: message.type,
                latency: Date.now() - startTime
            });

            this.emit('message-sent', { message });

        } catch (error) {
            this.stats.failedMessages++;
            logger.error('消息发送失败:', error, { messageId: message.id });

            this.emit('send-error', { message, error });
            throw error;
        }
    }

    /**
     * 监听消息
     */
    listen(callback: (message: BridgeMessage) => void): () => void {
        this.listeners.add(callback);

        return () => {
            this.listeners.delete(callback);
        };
    }

    /**
     * 获取统计信息
     */
    getStats(): BridgeStats {
        return { ...this.stats };
    }

    /**
     * 处理接收到的消息
     */
    protected handleReceivedMessage(data: string | ArrayBuffer): void {
        try {
            // 反序列化消息
            const message = this.config.serializer.deserialize(data);

            // 验证消息格式
            if (!this.isValidMessage(message)) {
                logger.warn('接收到无效消息格式', { data });
                return;
            }

            // 过滤消息
            if (!this.config.filter.filter(message)) {
                logger.debug('接收消息被过滤器拒绝', { messageId: message.id });
                return;
            }

            // 更新统计
            this.stats.messagesReceived++;

            logger.debug('接收到消息', {
                messageId: message.id,
                type: message.type,
                from: message.from
            });

            // 通知监听器
            this.listeners.forEach(callback => {
                try {
                    callback(message);
                } catch (error) {
                    logger.error('消息监听器执行失败:', error);
                }
            });

            this.emit('message-received', { message });

        } catch (error) {
            logger.error('处理接收消息失败:', error);
            this.emit('receive-error', { error, data });
        }
    }

    /**
     * 验证消息格式
     */
    private isValidMessage(message: any): message is BridgeMessage {
        return (
            message &&
            typeof message === 'object' &&
            typeof message.id === 'string' &&
            typeof message.type === 'string' &&
            typeof message.from === 'string' &&
            typeof message.to === 'string' &&
            typeof message.timestamp === 'number'
        );
    }

    /**
     * 处理消息队列
     */
    private async processMessageQueue(): Promise<void> {
        if (this.messageQueue.length === 0) {
            return;
        }

        logger.info(`处理队列中的 ${this.messageQueue.length} 条消息`);

        const messages = [...this.messageQueue];
        this.messageQueue = [];

        for (const message of messages) {
            try {
                await this.send(message);
            } catch (error) {
                logger.error('处理队列消息失败:', error, { messageId: message.id });
            }
        }
    }

    /**
     * 更新延迟统计
     */
    private updateLatency(latency: number): void {
        const totalMessages = this.stats.messagesSent;
        const currentAverage = this.stats.averageLatency;

        this.stats.averageLatency = (currentAverage * (totalMessages - 1) + latency) / totalMessages;
    }

    /**
     * 创建默认序列化器
     */
    private createDefaultSerializer(): MessageSerializer {
        return {
            serialize: (data: any) => JSON.stringify(data),
            deserialize: (data: string | ArrayBuffer) => {
                const str = typeof data === 'string' ? data : new TextDecoder().decode(data);
                return JSON.parse(str);
            }
        };
    }

    /**
     * 创建默认过滤器
     */
    private createDefaultFilter(): MessageFilter {
        return {
            filter: () => true,
            rules: []
        };
    }

    // 抽象方法，由子类实现
    protected abstract doInitialize(): Promise<void>;
    protected abstract doDestroy(): Promise<void>;
    protected abstract doSend(data: string | ArrayBuffer, originalMessage: BridgeMessage): Promise<void>;
}

export default MessageBridge;