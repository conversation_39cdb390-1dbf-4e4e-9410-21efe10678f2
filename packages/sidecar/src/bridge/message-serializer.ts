/**
 * @fileoverview 消息序列化器
 * @description 提供多种消息序列化和反序列化方式
 * <AUTHOR> <<EMAIL>>
 */

import { createMicroCoreError, logger } from '@micro-core/shared';
import type { MessageSerializer } from '../types';

/**
 * JSON 序列化器
 */
export class JsonSerializer implements MessageSerializer {
    serialize(data: any): string {
        try {
            return JSON.stringify(data);
        } catch (error) {
            throw createMicroCoreError(
                'SERIALIZATION_ERROR',
                `JSON 序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    deserialize(data: string | ArrayBuffer): any {
        try {
            const str = typeof data === 'string' ? data : new TextDecoder().decode(data);
            return JSON.parse(str);
        } catch (error) {
            throw createMicroCoreError(
                'DESERIALIZATION_ERROR',
                `JSON 反序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }
}

/**
 * 二进制序列化器
 */
export class BinarySerializer implements MessageSerializer {
    serialize(data: any): ArrayBuffer {
        try {
            const jsonString = JSON.stringify(data);
            const encoder = new TextEncoder();
            return encoder.encode(jsonString).buffer;
        } catch (error) {
            throw createMicroCoreError(
                'SERIALIZATION_ERROR',
                `二进制序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    deserialize(data: string | ArrayBuffer): any {
        try {
            let str: string;

            if (typeof data === 'string') {
                str = data;
            } else {
                const decoder = new TextDecoder();
                str = decoder.decode(data);
            }

            return JSON.parse(str);
        } catch (error) {
            throw createMicroCoreError(
                'DESERIALIZATION_ERROR',
                `二进制反序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }
}

/**
 * 压缩序列化器
 */
export class CompressedSerializer implements MessageSerializer {
    private baseSerializer: MessageSerializer;

    constructor(baseSerializer: MessageSerializer = new JsonSerializer()) {
        this.baseSerializer = baseSerializer;
    }

    serialize(data: any): string | ArrayBuffer {
        try {
            const serialized = this.baseSerializer.serialize(data);

            // 如果支持压缩，使用 CompressionStream
            if (typeof CompressionStream !== 'undefined') {
                return this.compress(serialized);
            }

            // 否则返回原始数据
            logger.warn('压缩不可用，返回未压缩数据');
            return serialized;

        } catch (error) {
            throw createMicroCoreError(
                'SERIALIZATION_ERROR',
                `压缩序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    deserialize(data: string | ArrayBuffer): any {
        try {
            // 如果支持解压缩，使用 DecompressionStream
            if (typeof DecompressionStream !== 'undefined' && data instanceof ArrayBuffer) {
                const decompressed = this.decompress(data);
                return this.baseSerializer.deserialize(decompressed);
            }

            // 否则直接反序列化
            return this.baseSerializer.deserialize(data);

        } catch (error) {
            throw createMicroCoreError(
                'DESERIALIZATION_ERROR',
                `压缩反序列化失败: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    private async compress(data: string | ArrayBuffer): Promise<ArrayBuffer> {
        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();

        // 写入数据
        const input = typeof data === 'string' ? new TextEncoder().encode(data) : new Uint8Array(data);
        await writer.write(input);
        await writer.close();

        // 读取压缩结果
        const chunks: Uint8Array[] = [];
        let done = false;

        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            if (value) {
                chunks.push(value);
            }
        }

        // 合并结果
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return result.buffer;
    }

    private async decompress(data: ArrayBuffer): Promise<string> {
        const stream = new DecompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();

        // 写入压缩数据
        await writer.write(new Uint8Array(data));
        await writer.close();

        // 读取解压结果
        const chunks: Uint8Array[] = [];
        let done = false;

        while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;
            if (value) {
                chunks.push(value);
            }
        }

        // 合并并解码结果
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return new TextDecoder().decode(result);
    }
}

/**
 * 序列化器工厂
 */
export class SerializerFactory {
    private static serializers = new Map<string, () => MessageSerializer>([
        ['json', () => new JsonSerializer()],
        ['binary', () => new BinarySerializer()],
        ['compressed', () => new CompressedSerializer()],
        ['compressed-binary', () => new CompressedSerializer(new BinarySerializer())]
    ]);

    /**
     * 创建序列化器
     */
    static create(type: string): MessageSerializer {
        const factory = this.serializers.get(type);

        if (!factory) {
            throw createMicroCoreError(
                'UNKNOWN_SERIALIZER',
                `未知的序列化器类型: ${type}`
            );
        }

        return factory();
    }

    /**
     * 注册自定义序列化器
     */
    static register(type: string, factory: () => MessageSerializer): void {
        this.serializers.set(type, factory);
        logger.debug(`已注册序列化器: ${type}`);
    }

    /**
     * 获取可用的序列化器类型
     */
    static getAvailableTypes(): string[] {
        return Array.from(this.serializers.keys());
    }
}

export default {
    JsonSerializer,
    BinarySerializer,
    CompressedSerializer,
    SerializerFactory
};