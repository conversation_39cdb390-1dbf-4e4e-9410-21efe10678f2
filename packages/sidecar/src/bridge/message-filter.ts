/**
 * @fileoverview 消息过滤器
 * @description 提供消息过滤和路由功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { BridgeMessage, FilterRule, MessageFilter } from '../types';

/**
 * 基础消息过滤器
 */
export class BaseMessageFilter implements MessageFilter {
    protected rules: FilterRule[] = [];

    constructor(rules: FilterRule[] = []) {
        this.rules = rules;
    }

    /**
     * 过滤消息
     */
    filter(message: BridgeMessage): boolean {
        // 如果没有规则，默认允许所有消息
        if (this.rules.length === 0) {
            return true;
        }

        // 应用所有规则
        for (const rule of this.rules) {
            const matches = this.matchesRule(message, rule);

            if (matches) {
                const allowed = rule.type === 'allow';
                logger.debug(`消息 ${message.id} ${allowed ? '通过' : '被拒绝'} 规则: ${rule.type}`, {
                    messageId: message.id,
                    ruleType: rule.type
                });
                return allowed;
            }
        }

        // 如果没有匹配的规则，默认拒绝
        return false;
    }

    /**
     * 添加规则
     */
    addRule(rule: FilterRule): void {
        this.rules.push(rule);
        logger.debug('已添加过滤规则', { ruleType: rule.type });
    }

    /**
     * 移除规则
     */
    removeRule(index: number): void {
        if (index >= 0 && index < this.rules.length) {
            const removed = this.rules.splice(index, 1)[0];
            logger.debug('已移除过滤规则', { ruleType: removed.type });
        }
    }

    /**
     * 清空规则
     */
    clearRules(): void {
        this.rules = [];
        logger.debug('已清空所有过滤规则');
    }

    /**
     * 获取规则列表
     */
    getRules(): FilterRule[] {
        return [...this.rules];
    }

    /**
     * 检查消息是否匹配规则
     */
    private matchesRule(message: BridgeMessage, rule: FilterRule): boolean {
        const { condition } = rule;

        if (typeof condition === 'string') {
            // 字符串匹配：检查消息类型
            return message.type === condition;
        }

        if (condition instanceof RegExp) {
            // 正则表达式匹配：检查消息类型
            return condition.test(message.type);
        }

        if (typeof condition === 'function') {
            // 函数匹配：执行自定义逻辑
            try {
                return condition(message);
            } catch (error) {
                logger.error('过滤规则函数执行失败:', error);
                return false;
            }
        }

        return false;
    }
}

/**
 * 类型过滤器
 */
export class TypeFilter extends BaseMessageFilter {
    constructor(allowedTypes: string[] = [], deniedTypes: string[] = []) {
        const rules: FilterRule[] = [];

        // 添加拒绝规则
        deniedTypes.forEach(type => {
            rules.push({
                type: 'deny',
                condition: type
            });
        });

        // 添加允许规则
        allowedTypes.forEach(type => {
            rules.push({
                type: 'allow',
                condition: type
            });
        });

        super(rules);
    }

    /**
     * 允许消息类型
     */
    allowType(type: string): void {
        this.addRule({
            type: 'allow',
            condition: type
        });
    }

    /**
     * 拒绝消息类型
     */
    denyType(type: string): void {
        this.addRule({
            type: 'deny',
            condition: type
        });
    }
}

/**
 * 源过滤器
 */
export class SourceFilter extends BaseMessageFilter {
    constructor(allowedSources: string[] = [], deniedSources: string[] = []) {
        const rules: FilterRule[] = [];

        // 添加拒绝规则
        deniedSources.forEach(source => {
            rules.push({
                type: 'deny',
                condition: (message: BridgeMessage) => message.from === source
            });
        });

        // 添加允许规则
        allowedSources.forEach(source => {
            rules.push({
                type: 'allow',
                condition: (message: BridgeMessage) => message.from === source
            });
        });

        super(rules);
    }

    /**
     * 允许消息源
     */
    allowSource(source: string): void {
        this.addRule({
            type: 'allow',
            condition: (message: BridgeMessage) => message.from === source
        });
    }

    /**
     * 拒绝消息源
     */
    denySource(source: string): void {
        this.addRule({
            type: 'deny',
            condition: (message: BridgeMessage) => message.from === source
        });
    }
}

/**
 * 复合过滤器
 */
export class CompositeFilter implements MessageFilter {
    private filters: MessageFilter[] = [];
    private operator: 'and' | 'or' = 'and';

    constructor(filters: MessageFilter[] = [], operator: 'and' | 'or' = 'and') {
        this.filters = filters;
        this.operator = operator;
    }

    /**
     * 过滤消息
     */
    filter(message: BridgeMessage): boolean {
        if (this.filters.length === 0) {
            return true;
        }

        if (this.operator === 'and') {
            // 所有过滤器都必须通过
            return this.filters.every(filter => filter.filter(message));
        } else {
            // 至少一个过滤器通过
            return this.filters.some(filter => filter.filter(message));
        }
    }

    /**
     * 添加过滤器
     */
    addFilter(filter: MessageFilter): void {
        this.filters.push(filter);
    }

    /**
     * 移除过滤器
     */
    removeFilter(index: number): void {
        if (index >= 0 && index < this.filters.length) {
            this.filters.splice(index, 1);
        }
    }

    /**
     * 设置操作符
     */
    setOperator(operator: 'and' | 'or'): void {
        this.operator = operator;
    }

    /**
     * 获取规则（复合过滤器没有直接规则）
     */
    get rules(): FilterRule[] {
        return [];
    }
}

/**
 * 过滤器工厂
 */
export class FilterFactory {
    /**
     * 创建类型过滤器
     */
    static createTypeFilter(allowedTypes: string[], deniedTypes: string[] = []): TypeFilter {
        return new TypeFilter(allowedTypes, deniedTypes);
    }

    /**
     * 创建源过滤器
     */
    static createSourceFilter(allowedSources: string[], deniedSources: string[] = []): SourceFilter {
        return new SourceFilter(allowedSources, deniedSources);
    }

    /**
     * 创建复合过滤器
     */
    static createCompositeFilter(filters: MessageFilter[], operator: 'and' | 'or' = 'and'): CompositeFilter {
        return new CompositeFilter(filters, operator);
    }

    /**
     * 创建自定义过滤器
     */
    static createCustomFilter(rules: FilterRule[]): BaseMessageFilter {
        return new BaseMessageFilter(rules);
    }
}

export default {
    BaseMessageFilter,
    TypeFilter,
    SourceFilter,
    CompositeFilter,
    FilterFactory
};