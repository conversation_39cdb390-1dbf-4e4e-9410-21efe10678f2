/**
 * @fileoverview PostMessage 桥接器
 * @description 基于 window.postMessage 的跨窗口通信桥接器
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { BridgeConfig, BridgeMessage } from '../types';
import { MessageBridge } from './message-bridge';

/**
 * PostMessage 桥接器配置
 */
export interface PostMessageBridgeConfig extends BridgeConfig {
    /** 目标窗口 */
    targetWindow?: Window;
    /** 目标源 */
    targetOrigin?: string;
    /** 是否监听所有源 */
    allowAllOrigins?: boolean;
    /** 允许的源列表 */
    allowedOrigins?: string[];
}

/**
 * PostMessage 桥接器
 */
export class PostMessageBridge extends MessageBridge {
    private config: Required<PostMessageBridgeConfig>;
    private messageHandler?: (event: MessageEvent) => void;

    constructor(config: PostMessageBridgeConfig = {}) {
        super(config);

        this.config = {
            ...this.config,
            targetWindow: window.parent,
            targetOrigin: '*',
            allowAllOrigins: false,
            allowedOrigins: [],
            ...config
        } as Required<PostMessageBridgeConfig>;
    }

    /**
     * 初始化桥接器
     */
    protected async doInitialize(): Promise<void> {
        // 创建消息处理器
        this.messageHandler = (event: MessageEvent) => {
            // 验证源
            if (!this.isOriginAllowed(event.origin)) {
                logger.debug('消息源不被允许', { origin: event.origin });
                return;
            }

            // 验证数据格式
            if (!event.data || typeof event.data !== 'object') {
                return;
            }

            // 检查是否是我们的消息格式
            if (!this.isMicroCoreMessage(event.data)) {
                return;
            }

            this.handleReceivedMessage(JSON.stringify(event.data));
        };

        // 添加事件监听器
        window.addEventListener('message', this.messageHandler);

        logger.debug('PostMessage 桥接器监听器已添加');
    }

    /**
     * 销毁桥接器
     */
    protected async doDestroy(): Promise<void> {
        if (this.messageHandler) {
            window.removeEventListener('message', this.messageHandler);
            this.messageHandler = undefined;
        }

        logger.debug('PostMessage 桥接器监听器已移除');
    }

    /**
     * 发送消息
     */
    protected async doSend(data: string | ArrayBuffer, originalMessage: BridgeMessage): Promise<void> {
        const messageData = typeof data === 'string' ? JSON.parse(data) : data;

        // 添加标识符
        const wrappedMessage = {
            __microCore: true,
            ...messageData
        };

        // 发送消息
        this.config.targetWindow.postMessage(wrappedMessage, this.config.targetOrigin);

        logger.debug('PostMessage 消息已发送', {
            messageId: originalMessage.id,
            targetOrigin: this.config.targetOrigin
        });
    }

    /**
     * 设置目标窗口
     */
    setTargetWindow(window: Window, origin = '*'): void {
        this.config.targetWindow = window;
        this.config.targetOrigin = origin;

        logger.debug('目标窗口已更新', { origin });
    }

    /**
     * 添加允许的源
     */
    addAllowedOrigin(origin: string): void {
        if (!this.config.allowedOrigins.includes(origin)) {
            this.config.allowedOrigins.push(origin);
            logger.debug('已添加允许的源', { origin });
        }
    }

    /**
     * 移除允许的源
     */
    removeAllowedOrigin(origin: string): void {
        const index = this.config.allowedOrigins.indexOf(origin);
        if (index > -1) {
            this.config.allowedOrigins.splice(index, 1);
            logger.debug('已移除允许的源', { origin });
        }
    }

    /**
     * 检查源是否被允许
     */
    private isOriginAllowed(origin: string): boolean {
        if (this.config.allowAllOrigins) {
            return true;
        }

        if (this.config.allowedOrigins.length === 0) {
            return true; // 如果没有配置限制，则允许所有源
        }

        return this.config.allowedOrigins.includes(origin) ||
            this.config.allowedOrigins.includes('*');
    }

    /**
     * 检查是否是 MicroCore 消息
     */
    private isMicroCoreMessage(data: any): boolean {
        return data && typeof data === 'object' && data.__microCore === true;
    }
}

export default PostMessageBridge;