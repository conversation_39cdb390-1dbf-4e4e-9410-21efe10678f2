/**
 * @fileoverview 自定义事件桥接器
 * @description 基于 CustomEvent 的同窗口通信桥接器
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { BridgeConfig, BridgeMessage } from '../types';
import { MessageBridge } from './message-bridge';

/**
 * 自定义事件桥接器配置
 */
export interface CustomEventBridgeConfig extends BridgeConfig {
    /** 事件名称前缀 */
    eventPrefix?: string;
    /** 目标元素 */
    targetElement?: EventTarget;
    /** 是否冒泡 */
    bubbles?: boolean;
    /** 是否可取消 */
    cancelable?: boolean;
}

/**
 * 自定义事件桥接器
 */
export class CustomEventBridge extends MessageBridge {
    private config: Required<CustomEventBridgeConfig>;
    private eventHandler?: (event: CustomEvent) => void;
    private eventName: string;

    constructor(config: CustomEventBridgeConfig = {}) {
        super(config);

        this.config = {
            ...this.config,
            eventPrefix: 'micro-core',
            targetElement: document,
            bubbles: true,
            cancelable: true,
            ...config
        } as Required<CustomEventBridgeConfig>;

        this.eventName = `${this.config.eventPrefix}-message`;
    }

    /**
     * 初始化桥接器
     */
    protected async doInitialize(): Promise<void> {
        // 创建事件处理器
        this.eventHandler = (event: CustomEvent) => {
            // 验证事件数据
            if (!event.detail || typeof event.detail !== 'object') {
                return;
            }

            // 检查是否是我们的消息格式
            if (!this.isMicroCoreMessage(event.detail)) {
                return;
            }

            this.handleReceivedMessage(JSON.stringify(event.detail));
        };

        // 添加事件监听器
        this.config.targetElement.addEventListener(this.eventName, this.eventHandler as EventListener);

        logger.debug('CustomEvent 桥接器监听器已添加', { eventName: this.eventName });
    }

    /**
     * 销毁桥接器
     */
    protected async doDestroy(): Promise<void> {
        if (this.eventHandler) {
            this.config.targetElement.removeEventListener(this.eventName, this.eventHandler as EventListener);
            this.eventHandler = undefined;
        }

        logger.debug('CustomEvent 桥接器监听器已移除', { eventName: this.eventName });
    }

    /**
     * 发送消息
     */
    protected async doSend(data: string | ArrayBuffer, originalMessage: BridgeMessage): Promise<void> {
        const messageData = typeof data === 'string' ? JSON.parse(data) : data;

        // 添加标识符
        const wrappedMessage = {
            __microCore: true,
            ...messageData
        };

        // 创建自定义事件
        const customEvent = new CustomEvent(this.eventName, {
            detail: wrappedMessage,
            bubbles: this.config.bubbles,
            cancelable: this.config.cancelable
        });

        // 分发事件
        this.config.targetElement.dispatchEvent(customEvent);

        logger.debug('CustomEvent 消息已发送', {
            messageId: originalMessage.id,
            eventName: this.eventName
        });
    }

    /**
     * 设置目标元素
     */
    setTargetElement(element: EventTarget): void {
        // 移除旧的监听器
        if (this.eventHandler) {
            this.config.targetElement.removeEventListener(this.eventName, this.eventHandler as EventListener);
        }

        // 更新目标元素
        this.config.targetElement = element;

        // 添加新的监听器
        if (this.eventHandler) {
            this.config.targetElement.addEventListener(this.eventName, this.eventHandler as EventListener);
        }

        logger.debug('目标元素已更新');
    }

    /**
     * 设置事件名称前缀
     */
    setEventPrefix(prefix: string): void {
        const oldEventName = this.eventName;
        this.eventName = `${prefix}-message`;

        // 如果已初始化，需要重新绑定事件
        if (this.eventHandler) {
            this.config.targetElement.removeEventListener(oldEventName, this.eventHandler as EventListener);
            this.config.targetElement.addEventListener(this.eventName, this.eventHandler as EventListener);
        }

        this.config.eventPrefix = prefix;
        logger.debug('事件前缀已更新', { oldEventName, newEventName: this.eventName });
    }

    /**
     * 检查是否是 MicroCore 消息
     */
    private isMicroCoreMessage(data: any): boolean {
        return data && typeof data === 'object' && data.__microCore === true;
    }
}

export default CustomEventBridge;