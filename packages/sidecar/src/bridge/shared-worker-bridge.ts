/**
 * @fileoverview SharedWorker 桥接器
 * @description 基于 SharedWorker 的跨标签页通信桥接器
 * <AUTHOR> <<EMAIL>>
 */

import { createMicroCoreError, logger } from '@micro-core/shared';
import type { BridgeConfig, BridgeMessage } from '../types';
import { MessageBridge } from './message-bridge';

/**
 * SharedWorker 桥接器配置
 */
export interface SharedWorkerBridgeConfig extends BridgeConfig {
    /** Worker 脚本路径 */
    workerScript?: string;
    /** Worker 名称 */
    workerName?: string;
    /** 连接超时时间 */
    connectionTimeout?: number;
}

/**
 * SharedWorker 桥接器
 */
export class SharedWorkerBridge extends MessageBridge {
    private config: Required<SharedWorkerBridgeConfig>;
    private worker?: SharedWorker;
    private port?: MessagePort;
    private isConnected = false;

    constructor(config: SharedWorkerBridgeConfig = {}) {
        super(config);

        this.config = {
            ...this.config,
            workerScript: '/micro-core-worker.js',
            workerName: 'micro-core-bridge',
            connectionTimeout: 10000,
            ...config
        } as Required<SharedWorkerBridgeConfig>;
    }

    /**
     * 初始化桥接器
     */
    protected async doInitialize(): Promise<void> {
        // 检查 SharedWorker 支持
        if (typeof SharedWorker === 'undefined') {
            throw createMicroCoreError(
                'SHARED_WORKER_NOT_SUPPORTED',
                '当前浏览器不支持 SharedWorker'
            );
        }

        try {
            // 创建 SharedWorker
            this.worker = new SharedWorker(this.config.workerScript, this.config.workerName);
            this.port = this.worker.port;

            // 设置消息处理器
            this.port.onmessage = (event: MessageEvent) => {
                this.handleWorkerMessage(event.data);
            };

            // 设置错误处理器
            this.worker.onerror = (error: ErrorEvent) => {
                logger.error('SharedWorker 错误:', error);
                this.emit('worker-error', { error });
            };

            // 启动端口
            this.port.start();

            // 等待连接建立
            await this.waitForConnection();

            logger.debug('SharedWorker 桥接器已连接');

        } catch (error) {
            logger.error('SharedWorker 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁桥接器
     */
    protected async doDestroy(): Promise<void> {
        if (this.port) {
            this.port.close();
            this.port = undefined;
        }

        this.worker = undefined;
        this.isConnected = false;

        logger.debug('SharedWorker 桥接器已断开');
    }

    /**
     * 发送消息
     */
    protected async doSend(data: string | ArrayBuffer, originalMessage: BridgeMessage): Promise<void> {
        if (!this.port || !this.isConnected) {
            throw createMicroCoreError(
                'WORKER_NOT_CONNECTED',
                'SharedWorker 未连接'
            );
        }

        const messageData = typeof data === 'string' ? JSON.parse(data) : data;

        // 发送消息到 Worker
        this.port.postMessage({
            type: 'bridge-message',
            data: messageData
        });

        logger.debug('SharedWorker 消息已发送', { messageId: originalMessage.id });
    }

    /**
     * 等待连接建立
     */
    private async waitForConnection(): Promise<void> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(createMicroCoreError(
                    'CONNECTION_TIMEOUT',
                    'SharedWorker 连接超时'
                ));
            }, this.config.connectionTimeout);

            // 发送连接请求
            this.port!.postMessage({ type: 'connect' });

            // 监听连接响应
            const handleMessage = (event: MessageEvent) => {
                if (event.data.type === 'connected') {
                    clearTimeout(timeout);
                    this.port!.removeEventListener('message', handleMessage);
                    this.isConnected = true;
                    resolve();
                }
            };

            this.port!.addEventListener('message', handleMessage);
        });
    }

    /**
     * 处理 Worker 消息
     */
    private handleWorkerMessage(data: any): void {
        if (data.type === 'bridge-message') {
            this.handleReceivedMessage(JSON.stringify(data.data));
        } else if (data.type === 'error') {
            logger.error('Worker 报告错误:', data.error);
            this.emit('worker-error', { error: data.error });
        }
    }

    /**
     * 获取连接状态
     */
    isWorkerConnected(): boolean {
        return this.isConnected;
    }
}

export default SharedWorkerBridge;