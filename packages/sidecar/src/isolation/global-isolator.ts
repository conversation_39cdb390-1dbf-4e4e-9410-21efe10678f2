/**
 * @fileoverview 全局变量隔离器
 * @description 提供全局变量隔离和管理功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { GlobalIsolationConfig } from '../types';

/**
 * 全局变量隔离器
 */
export class GlobalIsolator {
    private config: Required<GlobalIsolationConfig>;
    private isolatedGlobals = new Map<string, any>();
    private originalGlobals = new Map<string, any>();
    private isInitialized = false;

    constructor(config: GlobalIsolationConfig) {
        this.config = {
            shared: ['console', 'document', 'window', 'navigator'],
            private: [],
            mapping: {},
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        logger.debug('全局变量隔离器初始化');

        // 保存原始全局变量
        this.saveOriginalGlobals();

        // 设置变量映射
        this.setupVariableMapping();

        this.isInitialized = true;
    }

    /**
     * 销毁隔离器
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        logger.debug('销毁全局变量隔离器');

        // 恢复原始全局变量
        this.restoreOriginalGlobals();

        // 清理隔离变量
        this.isolatedGlobals.clear();
        this.originalGlobals.clear();

        this.isInitialized = false;
    }

    /**
     * 设置全局变量
     */
    setGlobal(name: string, value: any): void {
        // 检查是否为共享变量
        if (this.config.shared.includes(name)) {
            (window as any)[name] = value;
            logger.debug(`设置共享全局变量: ${name}`);
            return;
        }

        // 检查是否为私有变量
        if (this.config.private.includes(name)) {
            this.isolatedGlobals.set(name, value);
            logger.debug(`设置私有全局变量: ${name}`);
            return;
        }

        // 检查变量映射
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            this.isolatedGlobals.set(mappedName, value);
            logger.debug(`设置映射全局变量: ${name} -> ${mappedName}`);
            return;
        }

        // 默认设置为隔离变量
        this.isolatedGlobals.set(name, value);
        logger.debug(`设置隔离全局变量: ${name}`);
    }

    /**
     * 获取全局变量
     */
    getGlobal(name: string): any {
        // 检查是否为共享变量
        if (this.config.shared.includes(name)) {
            return (window as any)[name];
        }

        // 检查是否为私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.get(name);
        }

        // 检查变量映射
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.get(mappedName);
        }

        // 检查隔离变量
        if (this.isolatedGlobals.has(name)) {
            return this.isolatedGlobals.get(name);
        }

        // 返回原始全局变量
        return (window as any)[name];
    }

    /**
     * 删除全局变量
     */
    deleteGlobal(name: string): boolean {
        // 不能删除共享变量
        if (this.config.shared.includes(name)) {
            logger.warn(`无法删除共享全局变量: ${name}`);
            return false;
        }

        // 删除私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.delete(name);
        }

        // 删除映射变量
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.delete(mappedName);
        }

        // 删除隔离变量
        if (this.isolatedGlobals.has(name)) {
            return this.isolatedGlobals.delete(name);
        }

        return false;
    }

    /**
     * 清理全局变量
     */
    clearGlobals(): void {
        logger.debug('清理隔离的全局变量');
        this.isolatedGlobals.clear();
    }

    /**
     * 获取所有隔离变量
     */
    getIsolatedGlobals(): Map<string, any> {
        return new Map(this.isolatedGlobals);
    }

    /**
     * 检查变量是否存在
     */
    hasGlobal(name: string): boolean {
        // 检查共享变量
        if (this.config.shared.includes(name)) {
            return name in window;
        }

        // 检查私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.has(name);
        }

        // 检查映射变量
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.has(mappedName);
        }

        // 检查隔离变量
        return this.isolatedGlobals.has(name) || (name in window);
    }

    /**
     * 保存原始全局变量
     */
    private saveOriginalGlobals(): void {
        // 保存需要隔离的全局变量
        Object.keys(window).forEach(key => {
            if (!this.config.shared.includes(key)) {
                try {
                    this.originalGlobals.set(key, (window as any)[key]);
                } catch (error) {
                    // 忽略无法访问的属性
                }
            }
        });

        logger.debug(`已保存 ${this.originalGlobals.size} 个原始全局变量`);
    }

    /**
     * 恢复原始全局变量
     */
    private restoreOriginalGlobals(): void {
        this.originalGlobals.forEach((value, key) => {
            try {
                (window as any)[key] = value;
            } catch (error) {
                // 忽略无法恢复的属性
            }
        });

        logger.debug(`已恢复 ${this.originalGlobals.size} 个原始全局变量`);
    }

    /**
     * 设置变量映射
     */
    private setupVariableMapping(): void {
        Object.entries(this.config.mapping).forEach(([from, to]) => {
            if ((window as any)[from] !== undefined) {
                this.isolatedGlobals.set(to, (window as any)[from]);
                logger.debug(`设置变量映射: ${from} -> ${to}`);
            }
        });
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        isolatedCount: number;
        sharedCount: number;
        privateCount: number;
        mappingCount: number;
    } {
        return {
            isolatedCount: this.isolatedGlobals.size,
            sharedCount: this.config.shared.length,
            privateCount: this.config.private.length,
            mappingCount: Object.keys(this.config.mapping).length
        };
    }
}

export default GlobalIsolator;