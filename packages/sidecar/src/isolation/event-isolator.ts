/**
 * @fileoverview 事件隔离器
 * @description 提供DOM事件和自定义事件隔离功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { EventIsolationConfig } from '../types';

/**
 * 事件隔离器
 */
export class EventIsolator {
    private config: Required<EventIsolationConfig>;
    private isolatedEvents = new Map<string, Set<EventListener>>();
    private originalAddEventListener?: typeof EventTarget.prototype.addEventListener;
    private originalRemoveEventListener?: typeof EventTarget.prototype.removeEventListener;
    private originalDispatchEvent?: typeof EventTarget.prototype.dispatchEvent;
    private isInitialized = false;

    constructor(config: EventIsolationConfig) {
        this.config = {
            dom: true,
            custom: true,
            namespace: 'micro-app',
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        logger.debug('事件隔离器初始化');

        // 保存原始方法
        this.originalAddEventListener = EventTarget.prototype.addEventListener;
        this.originalRemoveEventListener = EventTarget.prototype.removeEventListener;
        this.originalDispatchEvent = EventTarget.prototype.dispatchEvent;

        // 如果启用DOM事件隔离
        if (this.config.dom) {
            this.setupDOMEventIsolation();
        }

        // 如果启用自定义事件隔离
        if (this.config.custom) {
            this.setupCustomEventIsolation();
        }

        this.isInitialized = true;
    }

    /**
     * 销毁隔离器
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        logger.debug('销毁事件隔离器');

        // 恢复原始方法
        if (this.originalAddEventListener) {
            EventTarget.prototype.addEventListener = this.originalAddEventListener;
        }

        if (this.originalRemoveEventListener) {
            EventTarget.prototype.removeEventListener = this.originalRemoveEventListener;
        }

        if (this.originalDispatchEvent) {
            EventTarget.prototype.dispatchEvent = this.originalDispatchEvent;
        }

        // 清理隔离事件
        this.isolatedEvents.clear();

        this.isInitialized = false;
    }

    /**
     * 添加隔离事件监听器
     */
    addEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | AddEventListenerOptions
    ): void {
        const namespacedType = this.getNamespacedEventType(type);
        const eventKey = this.getEventKey(target, namespacedType);

        // 记录隔离事件
        if (!this.isolatedEvents.has(eventKey)) {
            this.isolatedEvents.set(eventKey, new Set());
        }
        this.isolatedEvents.get(eventKey)!.add(listener);

        // 添加事件监听器
        if (this.originalAddEventListener) {
            this.originalAddEventListener.call(target, namespacedType, listener, options);
        }

        logger.debug(`添加隔离事件监听器: ${type} -> ${namespacedType}`);
    }

    /**
     * 移除隔离事件监听器
     */
    removeEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | EventListenerOptions
    ): void {
        const namespacedType = this.getNamespacedEventType(type);
        const eventKey = this.getEventKey(target, namespacedType);

        // 移除隔离事件记录
        const listeners = this.isolatedEvents.get(eventKey);
        if (listeners) {
            listeners.delete(listener);
            if (listeners.size === 0) {
                this.isolatedEvents.delete(eventKey);
            }
        }

        // 移除事件监听器
        if (this.originalRemoveEventListener) {
            this.originalRemoveEventListener.call(target, namespacedType, listener, options);
        }

        logger.debug(`移除隔离事件监听器: ${type} -> ${namespacedType}`);
    }

    /**
     * 分发隔离事件
     */
    dispatchEvent(target: EventTarget, event: Event): boolean {
        // 如果是自定义事件，添加命名空间
        if (this.config.custom && event instanceof CustomEvent) {
            const namespacedEvent = this.createNamespacedEvent(event);
            if (this.originalDispatchEvent) {
                return this.originalDispatchEvent.call(target, namespacedEvent);
            }
        }

        // 普通事件直接分发
        if (this.originalDispatchEvent) {
            return this.originalDispatchEvent.call(target, event);
        }

        return false;
    }

    /**
     * 清理所有隔离事件
     */
    clearAllEvents(): void {
        logger.debug('清理所有隔离事件');
        this.isolatedEvents.clear();
    }

    /**
     * 设置DOM事件隔离
     */
    private setupDOMEventIsolation(): void {
        const self = this;

        // 重写 addEventListener
        EventTarget.prototype.addEventListener = function (
            type: string,
            listener: EventListener | EventListenerObject | null,
            options?: boolean | AddEventListenerOptions
        ) {
            if (listener && typeof listener === 'function') {
                self.addEventListener(this, type, listener, options);
            } else if (listener && typeof listener === 'object' && listener.handleEvent) {
                self.addEventListener(this, type, listener.handleEvent.bind(listener), options);
            }
        };

        // 重写 removeEventListener
        EventTarget.prototype.removeEventListener = function (
            type: string,
            listener: EventListener | EventListenerObject | null,
            options?: boolean | EventListenerOptions
        ) {
            if (listener && typeof listener === 'function') {
                self.removeEventListener(this, type, listener, options);
            } else if (listener && typeof listener === 'object' && listener.handleEvent) {
                self.removeEventListener(this, type, listener.handleEvent.bind(listener), options);
            }
        };

        logger.debug('DOM事件隔离已设置');
    }

    /**
     * 设置自定义事件隔离
     */
    private setupCustomEventIsolation(): void {
        const self = this;

        // 重写 dispatchEvent
        EventTarget.prototype.dispatchEvent = function (event: Event) {
            return self.dispatchEvent(this, event);
        };

        logger.debug('自定义事件隔离已设置');
    }

    /**
     * 获取命名空间事件类型
     */
    private getNamespacedEventType(type: string): string {
        if (this.config.namespace && !type.startsWith(`${this.config.namespace}:`)) {
            return `${this.config.namespace}:${type}`;
        }
        return type;
    }

    /**
     * 创建命名空间事件
     */
    private createNamespacedEvent(originalEvent: CustomEvent): CustomEvent {
        const namespacedType = this.getNamespacedEventType(originalEvent.type);

        return new CustomEvent(namespacedType, {
            detail: originalEvent.detail,
            bubbles: originalEvent.bubbles,
            cancelable: originalEvent.cancelable,
            composed: originalEvent.composed
        });
    }

    /**
     * 获取事件键
     */
    private getEventKey(target: EventTarget, type: string): string {
        // 使用目标对象的构造函数名和事件类型作为键
        const targetName = target.constructor.name || 'Unknown';
        return `${targetName}:${type}`;
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        totalEvents: number;
        eventTypes: string[];
        domIsolation: boolean;
        customIsolation: boolean;
    } {
        const eventTypes = Array.from(this.isolatedEvents.keys());

        return {
            totalEvents: this.isolatedEvents.size,
            eventTypes,
            domIsolation: this.config.dom,
            customIsolation: this.config.custom
        };
    }
}

export default EventIsolator;