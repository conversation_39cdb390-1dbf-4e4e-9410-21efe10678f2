/**
 * @fileoverview 脚本隔离器
 * @description 提供JavaScript代码执行隔离功能
 * <AUTHOR> <<EMAIL>>
 */

import { createMicroCoreError, logger } from '@micro-core/shared';
import type { ScriptIsolationConfig } from '../types';

/**
 * 脚本隔离器
 */
export class ScriptIsolator {
    private config: Required<ScriptIsolationConfig>;
    private sandboxWindow?: any;
    private originalWindow?: any;
    private isInitialized = false;

    constructor(config: ScriptIsolationConfig) {
        this.config = {
            mode: 'proxy',
            whitelist: ['console', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'],
            blacklist: ['eval', 'Function', 'XMLHttpRequest', 'fetch'],
            strict: true,
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        logger.debug(`脚本隔离器初始化 (模式: ${this.config.mode})`);

        switch (this.config.mode) {
            case 'proxy':
                await this.initializeProxyMode();
                break;
            case 'snapshot':
                await this.initializeSnapshotMode();
                break;
            case 'iframe':
                await this.initializeIframeMode();
                break;
        }

        this.isInitialized = true;
    }

    /**
     * 销毁隔离器
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        logger.debug('销毁脚本隔离器');

        // 恢复原始环境
        if (this.originalWindow) {
            Object.keys(this.originalWindow).forEach(key => {
                try {
                    (window as any)[key] = this.originalWindow[key];
                } catch (error) {
                    // 忽略无法恢复的属性
                }
            });
        }

        this.sandboxWindow = undefined;
        this.originalWindow = undefined;
        this.isInitialized = false;
    }

    /**
     * 执行代码
     */
    async execute(code: string): Promise<any> {
        if (!this.isInitialized) {
            throw createMicroCoreError(
                'ISOLATOR_NOT_INITIALIZED',
                '脚本隔离器未初始化'
            );
        }

        try {
            logger.debug('在隔离环境中执行脚本', { codeLength: code.length });

            switch (this.config.mode) {
                case 'proxy':
                    return this.executeInProxy(code);
                case 'snapshot':
                    return this.executeInSnapshot(code);
                case 'iframe':
                    return this.executeInIframe(code);
                default:
                    throw createMicroCoreError(
                        'UNKNOWN_ISOLATION_MODE',
                        `未知的隔离模式: ${this.config.mode}`
                    );
            }
        } catch (error) {
            logger.error('脚本执行失败:', error);
            throw error;
        }
    }

    /**
     * 初始化代理模式
     */
    private async initializeProxyMode(): Promise<void> {
        // 创建代理窗口对象
        this.sandboxWindow = new Proxy(window, {
            get: (target, prop) => {
                // 检查黑名单
                if (this.config.blacklist.includes(prop as string)) {
                    throw createMicroCoreError(
                        'ACCESS_DENIED',
                        `访问被禁止的属性: ${String(prop)}`
                    );
                }

                // 检查白名单
                if (this.config.whitelist.length > 0 &&
                    !this.config.whitelist.includes(prop as string)) {
                    if (this.config.strict) {
                        throw createMicroCoreError(
                            'ACCESS_DENIED',
                            `访问未授权的属性: ${String(prop)}`
                        );
                    }
                    return undefined;
                }

                return (target as any)[prop];
            },

            set: (target, prop, value) => {
                // 检查黑名单
                if (this.config.blacklist.includes(prop as string)) {
                    throw createMicroCoreError(
                        'ACCESS_DENIED',
                        `设置被禁止的属性: ${String(prop)}`
                    );
                }

                // 在沙箱中设置属性，而不是全局窗口
                (this.sandboxWindow as any)[prop] = value;
                return true;
            },

            has: (target, prop) => {
                return prop in target;
            }
        });
    }

    /**
     * 初始化快照模式
     */
    private async initializeSnapshotMode(): Promise<void> {
        // 保存当前窗口状态
        this.originalWindow = {};

        Object.keys(window).forEach(key => {
            try {
                this.originalWindow[key] = (window as any)[key];
            } catch (error) {
                // 忽略无法访问的属性
            }
        });

        logger.debug('窗口快照已创建');
    }

    /**
     * 初始化iframe模式
     */
    private async initializeIframeMode(): Promise<void> {
        // 创建隐藏的iframe作为沙箱环境
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = 'about:blank';

        document.body.appendChild(iframe);

        // 等待iframe加载完成
        await new Promise((resolve) => {
            iframe.onload = resolve;
        });

        this.sandboxWindow = iframe.contentWindow;
        logger.debug('Iframe 沙箱已创建');
    }

    /**
     * 在代理环境中执行代码
     */
    private executeInProxy(code: string): any {
        // 创建执行函数
        const executeFunction = new Function('window', 'global', code);

        // 在代理环境中执行
        return executeFunction.call(this.sandboxWindow, this.sandboxWindow, this.sandboxWindow);
    }

    /**
     * 在快照环境中执行代码
     */
    private executeInSnapshot(code: string): any {
        try {
            // 执行代码
            const result = eval(code);

            // 恢复窗口状态
            this.restoreWindowSnapshot();

            return result;
        } catch (error) {
            // 确保恢复窗口状态
            this.restoreWindowSnapshot();
            throw error;
        }
    }

    /**
     * 在iframe环境中执行代码
     */
    private executeInIframe(code: string): any {
        if (!this.sandboxWindow) {
            throw createMicroCoreError(
                'IFRAME_NOT_READY',
                'Iframe 沙箱未准备就绪'
            );
        }

        // 在iframe中执行代码
        return this.sandboxWindow.eval(code);
    }

    /**
     * 恢复窗口快照
     */
    private restoreWindowSnapshot(): void {
        if (!this.originalWindow) {
            return;
        }

        // 移除新增的属性
        Object.keys(window).forEach(key => {
            if (!(key in this.originalWindow)) {
                try {
                    delete (window as any)[key];
                } catch (error) {
                    // 忽略无法删除的属性
                }
            }
        });

        // 恢复修改的属性
        Object.keys(this.originalWindow).forEach(key => {
            try {
                if ((window as any)[key] !== this.originalWindow[key]) {
                    (window as any)[key] = this.originalWindow[key];
                }
            } catch (error) {
                // 忽略无法恢复的属性
            }
        });
    }

    /**
     * 获取沙箱窗口
     */
    getSandboxWindow(): any {
        return this.sandboxWindow;
    }
}

export default ScriptIsolator;