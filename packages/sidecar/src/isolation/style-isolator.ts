/**
 * @fileoverview 样式隔离器
 * @description 提供CSS样式隔离功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { StyleIsolationConfig } from '../types';

/**
 * 样式隔离器
 */
export class StyleIsolator {
    private config: Required<StyleIsolationConfig>;
    private isolatedElements = new WeakSet<HTMLElement>();
    private originalStyles = new WeakMap<HTMLElement, string>();
    private scopeId: string;

    constructor(config: StyleIsolationConfig) {
        this.config = {
            mode: 'scoped',
            prefix: 'micro-app',
            excludeSelectors: [],
            isolateGlobal: true,
            ...config
        };

        this.scopeId = `${this.config.prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 初始化隔离器
     */
    async initialize(): Promise<void> {
        logger.debug(`样式隔离器初始化 (模式: ${this.config.mode})`);

        switch (this.config.mode) {
            case 'scoped':
                await this.initializeScopedMode();
                break;
            case 'shadow':
                await this.initializeShadowMode();
                break;
            case 'namespace':
                await this.initializeNamespaceMode();
                break;
        }
    }

    /**
     * 销毁隔离器
     */
    async destroy(): Promise<void> {
        logger.debug('销毁样式隔离器');
        // 清理资源
        this.isolatedElements = new WeakSet();
        this.originalStyles = new WeakMap();
    }

    /**
     * 隔离元素
     */
    isolateElement(element: HTMLElement): void {
        if (this.isolatedElements.has(element)) {
            return;
        }

        // 保存原始样式
        this.originalStyles.set(element, element.getAttribute('style') || '');

        switch (this.config.mode) {
            case 'scoped':
                this.applyScopedIsolation(element);
                break;
            case 'shadow':
                this.applyShadowIsolation(element);
                break;
            case 'namespace':
                this.applyNamespaceIsolation(element);
                break;
        }

        this.isolatedElements.add(element);
        logger.debug('元素样式隔离已应用', { mode: this.config.mode, tagName: element.tagName });
    }

    /**
     * 恢复元素
     */
    restoreElement(element: HTMLElement): void {
        if (!this.isolatedElements.has(element)) {
            return;
        }

        const originalStyle = this.originalStyles.get(element);
        if (originalStyle !== undefined) {
            if (originalStyle) {
                element.setAttribute('style', originalStyle);
            } else {
                element.removeAttribute('style');
            }
        }

        // 移除作用域属性
        element.removeAttribute(`data-${this.scopeId}`);
        element.classList.remove(`${this.config.prefix}-isolated`);

        this.isolatedElements.delete(element);
        this.originalStyles.delete(element);

        logger.debug('元素样式隔离已恢复', { tagName: element.tagName });
    }

    /**
     * 初始化作用域模式
     */
    private async initializeScopedMode(): Promise<void> {
        // 注入作用域样式
        const scopeStyle = this.createScopeStyle();
        this.injectStyle(scopeStyle);
    }

    /**
     * 初始化 Shadow DOM 模式
     */
    private async initializeShadowMode(): Promise<void> {
        // Shadow DOM 模式需要在具体使用时创建 shadow root
        logger.debug('Shadow DOM 模式已准备就绪');
    }

    /**
     * 初始化命名空间模式
     */
    private async initializeNamespaceMode(): Promise<void> {
        // 注入命名空间样式
        const namespaceStyle = this.createNamespaceStyle();
        this.injectStyle(namespaceStyle);
    }

    /**
     * 应用作用域隔离
     */
    private applyScopedIsolation(element: HTMLElement): void {
        // 添加作用域属性
        element.setAttribute(`data-${this.scopeId}`, '');

        // 处理子元素
        const children = element.querySelectorAll('*');
        children.forEach(child => {
            if (child instanceof HTMLElement) {
                child.setAttribute(`data-${this.scopeId}`, '');
            }
        });
    }

    /**
     * 应用 Shadow DOM 隔离
     */
    private applyShadowIsolation(element: HTMLElement): void {
        // 如果元素还没有 shadow root，创建一个
        if (!element.shadowRoot) {
            const shadowRoot = element.attachShadow({ mode: 'open' });

            // 移动子元素到 shadow root
            while (element.firstChild) {
                shadowRoot.appendChild(element.firstChild);
            }
        }
    }

    /**
     * 应用命名空间隔离
     */
    private applyNamespaceIsolation(element: HTMLElement): void {
        // 添加命名空间类
        element.classList.add(`${this.config.prefix}-isolated`);

        // 处理子元素
        const children = element.querySelectorAll('*');
        children.forEach(child => {
            if (child instanceof HTMLElement) {
                child.classList.add(`${this.config.prefix}-isolated`);
            }
        });
    }

    /**
     * 创建作用域样式
     */
    private createScopeStyle(): string {
        return `
            /* Micro-Core 作用域样式隔离 */
            [data-${this.scopeId}] {
                /* 重置可能影响的全局样式 */
                box-sizing: border-box;
            }
            
            /* 防止全局样式泄漏 */
            [data-${this.scopeId}] * {
                box-sizing: inherit;
            }
        `;
    }

    /**
     * 创建命名空间样式
     */
    private createNamespaceStyle(): string {
        return `
            /* Micro-Core 命名空间样式隔离 */
            .${this.config.prefix}-isolated {
                /* 重置可能影响的全局样式 */
                box-sizing: border-box;
            }
            
            .${this.config.prefix}-isolated * {
                box-sizing: inherit;
            }
        `;
    }

    /**
     * 注入样式
     */
    private injectStyle(css: string): void {
        const style = document.createElement('style');
        style.textContent = css;
        style.setAttribute('data-micro-core-style', this.scopeId);
        document.head.appendChild(style);
    }

    /**
     * 获取作用域 ID
     */
    getScopeId(): string {
        return this.scopeId;
    }
}

export default StyleIsolator;