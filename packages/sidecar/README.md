# @micro-core/sidecar

微前端边车模式支持 - 提供应用隔离、资源代理、通信桥接等核心功能，实现零配置微前端解决方案。

## 📦 核心功能

### 🚀 边车代理

- **资源代理** - 智能代理微前端应用的静态资源和API请求
- **缓存管理** - 内置缓存机制，提升资源加载性能
- **安全防护** - CORS、CSP、请求限制等安全特性
- **中间件支持** - 可扩展的代理中间件系统

### 🔒 应用隔离

- **样式隔离** - 支持Scoped、Shadow DOM、命名空间等隔离模式
- **脚本隔离** - 基于Proxy的JavaScript沙箱隔离
- **全局变量隔离** - 智能的全局变量管理和隔离
- **事件隔离** - DOM事件和自定义事件的隔离机制

### 🌉 通信桥接

- **消息传递** - 支持PostMessage、CustomEvent、SharedWorker等通信协议
- **序列化支持** - 灵活的消息序列化和反序列化
- **消息过滤** - 可配置的消息过滤和路由机制
- **错误重试** - 智能的消息重试和错误恢复

### 🔍 自动发现

- **应用发现** - 自动发现和注册微前端应用
- **框架检测** - 智能检测应用使用的前端框架
- **配置管理** - 多源配置加载和合并
- **热重载** - 开发模式下的热重载支持

## 🚀 快速开始

### 安装

```bash
# 使用 pnpm
pnpm add @micro-core/sidecar

# 使用 npm
npm install @micro-core/sidecar

# 使用 yarn
yarn add @micro-core/sidecar
```

### 基本使用

```typescript
import { Sidecar, createSidecar } from '@micro-core/sidecar';

// 创建边车实例
const sidecar = createSidecar({
  // 自动发现配置
  autoDiscovery: {
    enabled: true,
    scanInterval: 5000,
    patterns: [
      'http://localhost:*',
      'http://127.0.0.1:*'
    ]
  },
  
  // 代理配置
  proxy: {
    port: 8080,
    host: 'localhost',
    rules: [
      {
        path: '/api/*',
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    ]
  },
  
  // 隔离配置
  isolation: {
    style: {
      mode: 'scoped',
      prefix: 'micro-app'
    },
    script: {
      mode: 'proxy',
      strict: true
    }
  },
  
  // 桥接配置
  bridge: {
    protocol: 'postMessage',
    timeout: 5000
  }
});

// 初始化边车
await sidecar.initialize();

// 手动注册应用
await sidecar.registerApp({
  name: 'app1',
  entry: 'http://localhost:3001',
  container: '#app1-container',
  activeWhen: '/app1'
});

// 启动应用
await sidecar.startApp('app1');
```

### 零配置模式

```typescript
import '@micro-core/sidecar';

// 边车会自动初始化并发现应用
// 无需任何配置代码
```

## 📖 详细配置

### 边车配置

```typescript
interface SidecarConfig {
  // 自动发现配置
  autoDiscovery?: {
    enabled?: boolean;           // 是否启用自动发现
    scanInterval?: number;       // 扫描间隔(毫秒)
    patterns?: string[];         // 扫描模式
    exclude?: string[];          // 排除模式
  };

  // 框架检测配置
  frameworkDetection?: {
    enabled?: boolean;           // 是否启用框架检测
    timeout?: number;            // 检测超时时间
    fallback?: string;           // 默认框架
  };

  // 路由配置
  routing?: {
    mode?: 'hash' | 'history' | 'memory';  // 路由模式
    base?: string;               // 基础路径
    fallback?: string;           // 404页面
  };

  // 沙箱配置
  sandbox?: {
    enabled?: boolean;           // 是否启用沙箱
    type?: 'proxy' | 'iframe' | 'snapshot';  // 沙箱类型
    isolation?: boolean;         // 是否启用隔离
  };

  // 预加载配置
  prefetch?: {
    enabled?: boolean;           // 是否启用预加载
    strategy?: 'idle' | 'visible' | 'hover' | 'immediate';  // 预加载策略
    delay?: number;              // 延迟时间
  };

  // 开发模式配置
  development?: {
    enabled?: boolean;           // 是否启用开发模式
    hotReload?: boolean;         // 是否启用热重载
    devtools?: boolean;          // 是否启用开发工具
    logging?: boolean;           // 是否启用日志
  };

  // 自定义插件
  plugins?: string[];

  // 全局配置
  global?: {
    timeout?: number;            // 全局超时时间
    retryCount?: number;         // 重试次数
    errorHandler?: (error: Error) => void;  // 错误处理器
  };
}
```

### 代理配置

```typescript
interface SidecarProxyConfig {
  port?: number;                 // 代理端口
  host?: string;                 // 代理主机
  https?: boolean;               // 是否启用HTTPS
  rules?: ProxyRule[];           // 代理规则
  middleware?: ProxyMiddleware[]; // 中间件
  cache?: CacheConfig;           // 缓存配置
  security?: SecurityConfig;     // 安全配置
}

// 代理规则
interface ProxyRule {
  path: string | RegExp;         // 匹配路径
  target: string;                // 目标地址
  changeOrigin?: boolean;        // 是否改变源
  pathRewrite?: Record<string, string>;  // 路径重写
  headers?: Record<string, string>;      // 请求头设置
  timeout?: number;              // 超时时间
  retry?: number;                // 重试次数
}
```

### 隔离配置

```typescript
interface IsolationConfig {
  // 样式隔离
  style?: {
    mode?: 'scoped' | 'shadow' | 'namespace';  // 隔离模式
    prefix?: string;             // 命名空间前缀
    excludeSelectors?: string[]; // 排除选择器
    isolateGlobal?: boolean;     // 是否隔离全局样式
  };

  // 脚本隔离
  script?: {
    mode?: 'proxy' | 'snapshot' | 'iframe';    // 隔离模式
    whitelist?: string[];        // 白名单变量
    blacklist?: string[];        // 黑名单变量
    strict?: boolean;            // 是否严格模式
  };

  // 全局变量隔离
  global?: {
    shared?: string[];           // 共享变量
    private?: string[];          // 私有变量
    mapping?: Record<string, string>;  // 变量映射
  };

  // 事件隔离
  event?: {
    dom?: boolean;               // 是否隔离DOM事件
    custom?: boolean;            // 是否隔离自定义事件
    namespace?: string;          // 事件命名空间
  };
}
```

### 桥接配置

```typescript
interface BridgeConfig {
  protocol?: 'postMessage' | 'customEvent' | 'sharedWorker';  // 通信协议
  format?: 'json' | 'binary' | 'custom';     // 消息格式
  serializer?: MessageSerializer;            // 序列化器
  filter?: MessageFilter;                    // 消息过滤器
  timeout?: number;                          // 超时时间
  retry?: RetryConfig;                       // 重试配置
}
```

## 🔧 高级用法

### 自定义代理中间件

```typescript
import { createSidecar } from '@micro-core/sidecar';

const sidecar = createSidecar({
  proxy: {
    middleware: [
      {
        name: 'auth-middleware',
        handler: (req, res, next) => {
          // 添加认证头
          req.headers.authorization = `Bearer ${getToken()}`;
          next();
        },
        priority: 1
      },
      {
        name: 'logging-middleware',
        handler: (req, res, next) => {
          console.log(`${req.method} ${req.url}`);
          next();
        },
        priority: 2
      }
    ]
  }
});
```

### 自定义隔离策略

```typescript
import { IsolationContainer } from '@micro-core/sidecar/isolation';

const container = new IsolationContainer({
  style: {
    mode: 'scoped',
    prefix: 'my-app',
    excludeSelectors: ['.global-style']
  },
  script: {
    mode: 'proxy',
    whitelist: ['React', 'ReactDOM'],
    blacklist: ['eval', 'Function']
  }
});

await container.initialize();

// 执行隔离代码
const result = await container.execute(`
  console.log('这段代码在隔离环境中执行');
  return window.myGlobalVar;
`);
```

### 自定义通信桥接

```typescript
import { MessageBridge } from '@micro-core/sidecar/bridge';

const bridge = new MessageBridge({
  protocol: 'postMessage',
  serializer: {
    serialize: (data) => JSON.stringify(data),
    deserialize: (data) => JSON.parse(data)
  },
  filter: {
    filter: (message) => message.type !== 'internal'
  }
});

await bridge.initialize();

// 发送消息
await bridge.send({
  type: 'user-action',
  from: 'app1',
  to: 'app2',
  data: { action: 'click', target: 'button' }
});

// 监听消息
const unsubscribe = bridge.listen((message) => {
  console.log('收到消息:', message);
});
```

### 应用生命周期管理

```typescript
const sidecar = createSidecar();

// 监听应用事件
sidecar.getKernel().on('app:mounted', (event) => {
  console.log('应用已挂载:', event.data.name);
});

sidecar.getKernel().on('app:unmounted', (event) => {
  console.log('应用已卸载:', event.data.name);
});

sidecar.getKernel().on('app:error', (event) => {
  console.error('应用错误:', event.data.error);
});

// 手动控制应用
await sidecar.startApp('app1');
await sidecar.stopApp('app1');

// 获取应用状态
const status = sidecar.getAppStatus('app1');
console.log('应用状态:', status);
```

### 配置热更新

```typescript
const sidecar = createSidecar();

// 监听配置更新
sidecar.getKernel().on('sidecar:config-updated', (event) => {
  console.log('配置已更新:', event.data.config);
});

// 动态更新配置
sidecar.updateConfig({
  autoDiscovery: {
    scanInterval: 3000
  },
  development: {
    logging: false
  }
});
```

## 🛠️ 开发工具

### 调试模式

```typescript
const sidecar = createSidecar({
  development: {
    enabled: true,
    devtools: true,
    logging: true
  }
});

// 在浏览器控制台中访问
console.log(window.__MICRO_CORE_SIDECAR__);
```

### 性能监控

```typescript
// 获取边车统计信息
const stats = sidecar.getManager().getStats();
console.log('边车统计:', stats);

// 获取代理统计信息
const proxyStats = sidecar.getManager().getProxyStats();
console.log('代理统计:', proxyStats);
```

### 错误处理

```typescript
const sidecar = createSidecar({
  global: {
    errorHandler: (error) => {
      // 发送错误到监控系统
      errorReporting.captureException(error);
      
      // 显示用户友好的错误信息
      showErrorNotification('应用加载失败，请刷新页面重试');
    }
  }
});
```

## 📊 最佳实践

### 性能优化

```typescript
const sidecar = createSidecar({
  // 启用预加载
  prefetch: {
    enabled: true,
    strategy: 'idle',
    delay: 2000
  },
  
  // 配置缓存
  proxy: {
    cache: {
      enabled: true,
      type: 'memory',
      ttl: 300,
      maxSize: 100
    }
  },
  
  // 优化扫描间隔
  autoDiscovery: {
    scanInterval: 10000  // 减少扫描频率
  }
});
```

### 安全配置

```typescript
const sidecar = createSidecar({
  proxy: {
    security: {
      cors: {
        origin: ['https://trusted-domain.com'],
        credentials: true
      },
      csp: {
        directives: {
          'script-src': ["'self'", "'unsafe-inline'"],
          'style-src': ["'self'", "'unsafe-inline'"]
        }
      },
      rateLimit: {
        windowMs: 15 * 60 * 1000,  // 15分钟
        max: 100  // 最多100个请求
      }
    }
  },
  
  isolation: {
    script: {
      mode: 'proxy',
      strict: true,
      blacklist: ['eval', 'Function', 'setTimeout', 'setInterval']
    }
  }
});
```

### 生产环境配置

```typescript
const sidecar = createSidecar({
  development: {
    enabled: false,
    hotReload: false,
    devtools: false,
    logging: false
  },
  
  global: {
    timeout: 10000,
    retryCount: 3,
    errorHandler: (error) => {
      // 生产环境错误处理
      console.error('[Sidecar] 生产环境错误:', error);
      errorReporting.captureException(error);
    }
  },
  
  proxy: {
    cache: {
      enabled: true,
      type: 'redis',  // 使用Redis缓存
      ttl: 3600
    }
  }
});
```

## 🤝 贡献指南

欢迎贡献新功能或改进现有功能！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/new-feature`)
3. 提交更改 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件。

## 🔗 相关链接

- [微前端核心文档](../core/README.md)
- [适配器系统文档](../adapters/README.md)
- [插件系统文档](../plugins/README.md)
- [构建工具文档](../builders/README.md)
- [示例应用](../../apps/examples/README.md)