/**
 * @fileoverview Vitest 配置
 * @description Sidecar 包的测试配置
 * <AUTHOR> <<EMAIL>>
 */

import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./tests/setup.ts'],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html', 'lcov'],
            exclude: [
                'node_modules/',
                'dist/',
                'coverage/',
                '**/*.d.ts',
                '**/*.test.ts',
                '**/*.test.tsx',
                'tests/',
                'vitest.config.ts',
                'tsup.config.ts',
                'vite.config.ts'
            ],
            thresholds: {
                global: {
                    branches: 90,
                    functions: 90,
                    lines: 90,
                    statements: 90
                }
            }
        },
        testTimeout: 10000,
        hookTimeout: 10000,
        include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        exclude: [
            '**/node_modules/**',
            '**/dist/**',
            '**/cypress/**',
            '**/.{idea,git,cache,output,temp}/**',
            '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*'
        ]
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@micro-core/shared': resolve(__dirname, '../shared/src'),
            '@micro-core/core': resolve(__dirname, '../core/src')
        }
    },
    define: {
        __DEV__: true,
        __TEST__: true
    }
});