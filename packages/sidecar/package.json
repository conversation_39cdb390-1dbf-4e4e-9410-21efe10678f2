{"name": "@micro-core/sidecar", "version": "0.1.0", "description": "微前端边车模式支持 - 提供应用隔离、资源代理、通信桥接等核心功能", "keywords": ["microfrontend", "sidecar", "proxy", "isolation", "bridge"], "author": "Echo <<EMAIL>>", "license": "MIT", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./proxy": {"types": "./dist/proxy.d.ts", "import": "./dist/proxy.js", "require": "./dist/proxy.cjs"}, "./bridge": {"types": "./dist/bridge.d.ts", "import": "./dist/bridge.js", "require": "./dist/bridge.cjs"}, "./isolation": {"types": "./dist/isolation.d.ts", "import": "./dist/isolation.js", "require": "./dist/isolation.cjs"}}, "files": ["dist", "README.md", "CHANGELOG.md"], "scripts": {"build": "tsup", "build:watch": "tsup --watch", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/shared": "workspace:*", "@micro-core/core": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsup": "^8.0.0", "vitest": "^1.0.0", "rimraf": "^5.0.0", "eslint": "^8.0.0"}, "peerDependencies": {"typescript": ">=4.5.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/micro-core/micro-core.git", "directory": "packages/sidecar"}, "bugs": {"url": "https://github.com/micro-core/micro-core/issues"}, "homepage": "https://github.com/micro-core/micro-core/tree/main/packages/sidecar#readme"}