/**
 * @fileoverview Sidecar 集成测试
 * @description 测试 Sidecar 各组件之间的集成和协作
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { init, getSidecar, destroy, registerApp, unregisterApp, start, stop } from '../../src/index';
import { SidecarContainer } from '../../src/sidecar';
import { AutoConfig } from '../../src/auto-config';
import { CompatMode } from '../../src/compat-mode';
import { JQueryAppAdapter } from '../../src/legacy-apps/jquery-app';
import { VanillaJSAppAdapter } from '../../src/legacy-apps/vanilla-js-app';

// 模拟 DOM 环境
const mockDOM = () => {
    const container = document.createElement('div');
    container.id = 'test-container';
    document.body.appendChild(container);
    return container;
};

// 模拟应用配置
const mockAppConfigs = {
    basicApp: {
        name: 'basic-app',
        entry: '/mock-apps/basic-app.js',
        container: '#basic-container',
        activeWhen: '/basic'
    },
    jqueryApp: {
        name: 'jquery-app',
        entry: '/mock-apps/jquery-app.html',
        container: '#jquery-container',
        activeWhen: '/jquery',
        jqueryVersion: '3.6.0',
        globalJQuery: true
    },
    vanillaApp: {
        name: 'vanilla-app',
        entry: '/mock-apps/vanilla-app.html',
        container: '#vanilla-container',
        activeWhen: '/vanilla',
        scripts: ['/js/app.js'],
        styles: ['/css/app.css']
    }
};

describe('Sidecar Integration Tests', () => {
    let testContainer: HTMLElement;

    beforeEach(() => {
        testContainer = mockDOM();
        
        // 模拟 fetch
        global.fetch = vi.fn().mockImplementation((url: string) => {
            return Promise.resolve({
                ok: true,
                text: () => Promise.resolve(`<div>Mock content for ${url}</div>`),
                json: () => Promise.resolve({ mock: true })
            });
        });

        // 清理之前的实例
        destroy();
    });

    afterEach(async () => {
        if (testContainer && testContainer.parentNode) {
            testContainer.parentNode.removeChild(testContainer);
        }
        await destroy();
        vi.clearAllMocks();
    });

    describe('Complete Sidecar Lifecycle', () => {
        it('should complete full initialization and startup flow', async () => {
            const sidecar = await init({
                name: 'integration-test',
                apps: [mockAppConfigs.basicApp],
                autoConfig: true,
                compatMode: false,
                autoStart: true
            });

            expect(sidecar).toBeDefined();
            expect(getSidecar()).toBeTruthy();

            // 验证应用已注册
            const registeredApps = sidecar.getRegisteredApps();
            expect(registeredApps).toHaveLength(1);
            expect(registeredApps[0].name).toBe('basic-app');

            // 启动 Sidecar
            await start();

            // 验证状态
            const status = sidecar.getStatus();
            expect(status.status).toBe('running');
        });

        it('should handle multiple apps registration and lifecycle', async () => {
            const sidecar = await init({
                apps: [
                    mockAppConfigs.basicApp,
                    mockAppConfigs.jqueryApp,
                    mockAppConfigs.vanillaApp
                ]
            });

            expect(sidecar.getRegisteredApps()).toHaveLength(3);

            // 测试单个应用加载
            await sidecar.loadApp('basic-app');
            expect(sidecar.getLoadedApps()).toContain('basic-app');

            // 测试应用卸载
            await sidecar.unloadApp('basic-app');
            expect(sidecar.getLoadedApps()).not.toContain('basic-app');

            // 测试应用注销
            await unregisterApp('jquery-app');
            expect(sidecar.getRegisteredApps()).toHaveLength(2);
        });

        it('should handle graceful shutdown', async () => {
            const sidecar = await init({
                apps: [mockAppConfigs.basicApp]
            });

            await start();
            expect(sidecar.getStatus().status).toBe('running');

            await stop();
            expect(sidecar.getStatus().status).toBe('stopped');

            await destroy();
            expect(getSidecar()).toBeNull();
        });
    });

    describe('Auto Configuration Integration', () => {
        it('should detect and apply HTML-based configuration', async () => {
            // 设置 HTML 配置
            const configDiv = document.createElement('div');
            configDiv.setAttribute('data-micro-app', JSON.stringify({
                name: 'html-detected-app',
                entry: '/html-app.js',
                container: '#html-container'
            }));
            document.body.appendChild(configDiv);

            const sidecar = await init({
                autoConfig: true
            });

            const registeredApps = sidecar.getRegisteredApps();
            const detectedApp = registeredApps.find(app => app.name === 'html-detected-app');
            
            expect(detectedApp).toBeDefined();
            expect(detectedApp?.entry).toBe('/html-app.js');

            document.body.removeChild(configDiv);
        });

        it('should detect and apply meta tag configuration', async () => {
            // 设置 meta 配置
            const metaTag = document.createElement('meta');
            metaTag.name = 'micro-apps';
            metaTag.content = JSON.stringify([{
                name: 'meta-detected-app',
                entry: '/meta-app.js',
                container: '#meta-container'
            }]);
            document.head.appendChild(metaTag);

            const sidecar = await init({
                autoConfig: true
            });

            const registeredApps = sidecar.getRegisteredApps();
            const detectedApp = registeredApps.find(app => app.name === 'meta-detected-app');
            
            expect(detectedApp).toBeDefined();
            expect(detectedApp?.entry).toBe('/meta-app.js');

            document.head.removeChild(metaTag);
        });

        it('should detect and apply global variable configuration', async () => {
            // 设置全局变量配置
            (window as any).MICRO_APPS = [{
                name: 'global-detected-app',
                entry: '/global-app.js',
                container: '#global-container'
            }];

            const sidecar = await init({
                autoConfig: true
            });

            const registeredApps = sidecar.getRegisteredApps();
            const detectedApp = registeredApps.find(app => app.name === 'global-detected-app');
            
            expect(detectedApp).toBeDefined();
            expect(detectedApp?.entry).toBe('/global-app.js');

            delete (window as any).MICRO_APPS;
        });
    });

    describe('Compatibility Mode Integration', () => {
        it('should enable compatibility mode for legacy browsers', async () => {
            // 模拟 IE11 环境
            const originalUserAgent = navigator.userAgent;
            Object.defineProperty(navigator, 'userAgent', {
                value: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
                configurable: true
            });

            const sidecar = await init({
                apps: [mockAppConfigs.basicApp],
                compatMode: true
            });

            expect(sidecar).toBeDefined();

            // 恢复原始 User Agent
            Object.defineProperty(navigator, 'userAgent', {
                value: originalUserAgent,
                configurable: true
            });
        });

        it('should provide polyfills for missing features', async () => {
            // 模拟缺失的 Promise
            const originalPromise = window.Promise;
            delete (window as any).Promise;

            const compatMode = new CompatMode({ enabled: true });
            await compatMode.initialize();

            // 验证 Promise polyfill 已加载
            expect((window as any).Promise).toBeDefined();

            // 恢复原始 Promise
            (window as any).Promise = originalPromise;
        });
    });

    describe('Legacy Apps Integration', () => {
        it('should integrate jQuery apps successfully', async () => {
            const jqueryAdapter = new JQueryAppAdapter(mockAppConfigs.jqueryApp);
            
            await jqueryAdapter.bootstrap();
            
            const container = document.createElement('div');
            container.id = 'jquery-container';
            document.body.appendChild(container);

            await jqueryAdapter.mount(container);
            
            expect(container.innerHTML).toContain('Mock content');
            
            await jqueryAdapter.unmount(container);
            expect(container.innerHTML).toBe('');

            document.body.removeChild(container);
        });

        it('should integrate vanilla JS apps successfully', async () => {
            const vanillaAdapter = new VanillaJSAppAdapter(mockAppConfigs.vanillaApp);
            
            await vanillaAdapter.bootstrap();
            
            const container = document.createElement('div');
            container.id = 'vanilla-container';
            document.body.appendChild(container);

            await vanillaAdapter.mount(container);
            
            expect(container.innerHTML).toContain('Mock content');
            
            await vanillaAdapter.unmount(container);
            expect(container.innerHTML).toBe('');

            document.body.removeChild(container);
        });
    });

    describe('Bridge System Integration', () => {
        it('should handle cross-app communication', async () => {
            const sidecar = await init({
                apps: [
                    { ...mockAppConfigs.basicApp, name: 'sender-app' },
                    { ...mockAppConfigs.basicApp, name: 'receiver-app' }
                ]
            });

            await start();

            // 模拟应用间通信
            const messageHandler = vi.fn();
            sidecar.on('app:message', messageHandler);

            // 发送消息
            sidecar.sendMessage('sender-app', 'receiver-app', {
                type: 'test-message',
                data: { hello: 'world' }
            });

            // 验证消息处理
            expect(messageHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    from: 'sender-app',
                    to: 'receiver-app',
                    payload: {
                        type: 'test-message',
                        data: { hello: 'world' }
                    }
                })
            );
        });

        it('should handle global state management', async () => {
            const sidecar = await init({
                apps: [mockAppConfigs.basicApp]
            });

            await start();

            // 设置全局状态
            sidecar.setGlobalState('user', { id: 1, name: 'Test User' });
            
            // 获取全局状态
            const user = sidecar.getGlobalState('user');
            expect(user).toEqual({ id: 1, name: 'Test User' });

            // 监听状态变化
            const stateChangeHandler = vi.fn();
            sidecar.on('state:changed', stateChangeHandler);

            sidecar.setGlobalState('user', { id: 1, name: 'Updated User' });
            
            expect(stateChangeHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    key: 'user',
                    newValue: { id: 1, name: 'Updated User' },
                    oldValue: { id: 1, name: 'Test User' }
                })
            );
        });
    });

    describe('Isolation System Integration', () => {
        it('should provide proper script isolation', async () => {
            const sidecar = await init({
                apps: [
                    { ...mockAppConfigs.basicApp, name: 'isolated-app-1' },
                    { ...mockAppConfigs.basicApp, name: 'isolated-app-2' }
                ],
                isolation: {
                    script: true,
                    style: true,
                    global: true
                }
            });

            await start();

            // 模拟应用1设置全局变量
            await sidecar.loadApp('isolated-app-1');
            (window as any).testVar = 'app1-value';

            // 模拟应用2设置同名全局变量
            await sidecar.loadApp('isolated-app-2');
            (window as any).testVar = 'app2-value';

            // 验证隔离效果
            const app1Context = sidecar.getAppContext('isolated-app-1');
            const app2Context = sidecar.getAppContext('isolated-app-2');

            expect(app1Context.globals.testVar).toBe('app1-value');
            expect(app2Context.globals.testVar).toBe('app2-value');
        });

        it('should provide proper style isolation', async () => {
            const sidecar = await init({
                apps: [mockAppConfigs.basicApp],
                isolation: {
                    style: true,
                    shadowDOM: true
                }
            });

            await start();
            await sidecar.loadApp('basic-app');

            const appContainer = document.querySelector('#basic-container');
            expect(appContainer).toBeTruthy();

            // 验证 Shadow DOM 创建
            const shadowRoot = (appContainer as any)?.shadowRoot;
            expect(shadowRoot).toBeTruthy();
        });
    });

    describe('Error Handling Integration', () => {
        it('should handle app loading errors gracefully', async () => {
            const errorHandler = vi.fn();
            
            // 模拟网络错误
            global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

            const sidecar = await init({
                apps: [{
                    name: 'failing-app',
                    entry: '/non-existent-app.js',
                    container: '#failing-container'
                }],
                errorHandler
            });

            await start();

            // 尝试加载失败的应用
            try {
                await sidecar.loadApp('failing-app');
            } catch (error) {
                // 预期会抛出错误
            }

            expect(errorHandler).toHaveBeenCalled();
        });

        it('should provide error boundaries for apps', async () => {
            const errorBoundaryHandler = vi.fn();
            
            const sidecar = await init({
                apps: [{
                    name: 'error-app',
                    entry: '/error-app.js',
                    container: '#error-container',
                    errorBoundary: errorBoundaryHandler
                }]
            });

            await start();

            // 模拟应用运行时错误
            const appError = new Error('Runtime error in app');
            sidecar.handleAppError('error-app', appError);

            expect(errorBoundaryHandler).toHaveBeenCalledWith(
                appError,
                expect.objectContaining({ name: 'error-app' })
            );
        });
    });

    describe('Performance Integration', () => {
        it('should track performance metrics', async () => {
            const performanceHandler = vi.fn();
            
            const sidecar = await init({
                apps: [mockAppConfigs.basicApp],
                onPerformanceMetric: performanceHandler
            });

            await start();
            await sidecar.loadApp('basic-app');

            expect(performanceHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    appName: 'basic-app',
                    metric: 'load_time',
                    value: expect.any(Number)
                })
            );
        });

        it('should handle concurrent app loading efficiently', async () => {
            const apps = Array.from({ length: 5 }, (_, i) => ({
                name: `concurrent-app-${i}`,
                entry: `/concurrent-app-${i}.js`,
                container: `#concurrent-container-${i}`,
                activeWhen: `/concurrent-${i}`
            }));

            const startTime = performance.now();
            
            const sidecar = await init({ apps });
            await start();

            // 并发加载所有应用
            const loadPromises = apps.map(app => sidecar.loadApp(app.name));
            await Promise.all(loadPromises);

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            // 并发加载应该在合理时间内完成
            expect(totalTime).toBeLessThan(2000);
            expect(sidecar.getLoadedApps()).toHaveLength(5);
        });
    });

    describe('Real-world Scenarios', () => {
        it('should handle complex multi-app scenario', async () => {
            // 模拟真实的多应用场景
            const sidecar = await init({
                name: 'complex-scenario',
                apps: [
                    {
                        name: 'header-app',
                        entry: '/apps/header.js',
                        container: '#header',
                        activeWhen: () => true // 总是激活
                    },
                    {
                        name: 'sidebar-app',
                        entry: '/apps/sidebar.js',
                        container: '#sidebar',
                        activeWhen: '/dashboard'
                    },
                    {
                        name: 'content-app',
                        entry: '/apps/content.js',
                        container: '#content',
                        activeWhen: '/dashboard/content'
                    },
                    {
                        name: 'legacy-reports',
                        entry: '/legacy/reports.html',
                        container: '#reports',
                        activeWhen: '/reports',
                        adapter: 'jquery'
                    }
                ],
                autoConfig: true,
                compatMode: true,
                preloadApps: ['header-app'],
                isolation: {
                    script: true,
                    style: true
                }
            });

            await start();

            // 验证预加载应用
            expect(sidecar.getLoadedApps()).toContain('header-app');

            // 模拟路由变化
            Object.defineProperty(window, 'location', {
                value: { pathname: '/dashboard' },
                configurable: true
            });

            // 触发路由匹配
            await sidecar.handleRouteChange('/dashboard');

            // 验证相关应用已加载
            expect(sidecar.getLoadedApps()).toContain('sidebar-app');

            // 模拟深层路由
            await sidecar.handleRouteChange('/dashboard/content');
            expect(sidecar.getLoadedApps()).toContain('content-app');

            // 验证应用间通信
            const messageHandler = vi.fn();
            sidecar.on('app:message', messageHandler);

            sidecar.sendMessage('sidebar-app', 'content-app', {
                type: 'navigation',
                data: { section: 'users' }
            });

            expect(messageHandler).toHaveBeenCalled();
        });

        it('should handle app hot-reload scenario', async () => {
            const sidecar = await init({
                apps: [mockAppConfigs.basicApp],
                hotReload: true
            });

            await start();
            await sidecar.loadApp('basic-app');

            // 模拟应用更新
            const reloadHandler = vi.fn();
            sidecar.on('app:reloaded', reloadHandler);

            await sidecar.reloadApp('basic-app');

            expect(reloadHandler).toHaveBeenCalledWith(
                expect.objectContaining({ name: 'basic-app' })
            );
        });
    });
});

/**
 * 集成测试工具类
 */
export class SidecarIntegrationTestHelper {
    /**
     * 创建测试应用配置
     */
    static createTestAppConfig(overrides: any = {}) {
        return {
            name: 'test-app',
            entry: '/test-app.js',
            container: '#test-container',
            activeWhen: '/test',
            ...overrides
        };
    }

    /**
     * 模拟应用加载延迟
     */
    static async simulateLoadDelay(ms: number = 100) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 验证应用状态
     */
    static verifyAppState(sidecar: any, appName: string, expectedState: string) {
        const appStatus = sidecar.getAppStatus(appName);
        expect(appStatus.state).toBe(expectedState);
    }
}

export default SidecarIntegrationTestHelper;
