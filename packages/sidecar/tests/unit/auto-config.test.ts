/**
 * @fileoverview AutoConfig 测试
 * @description 测试自动配置检测功能
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { AutoConfig } from '../auto-config';

// Mock DOM
const mockDocument = {
    querySelectorAll: vi.fn(),
    querySelector: vi.fn()
};

// Mock window
const mockWindow = {
    __MICRO_CORE_CONFIG__: undefined
};

// 设置全局 mock
Object.defineProperty(global, 'document', {
    value: mockDocument,
    writable: true
});

Object.defineProperty(global, 'window', {
    value: mockWindow,
    writable: true
});

describe('AutoConfig', () => {
    let autoConfig: AutoConfig;

    beforeEach(() => {
        autoConfig = new AutoConfig();
        vi.clearAllMocks();
    });

    afterEach(() => {
        // 清理全局状态
        mockWindow.__MICRO_CORE_CONFIG__ = undefined;
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建实例', () => {
            const config = new AutoConfig();
            expect(config).toBeInstanceOf(AutoConfig);
        });

        it('应该接受自定义配置', () => {
            const customConfig = {
                enabled: false,
                sources: ['html']
            };
            const config = new AutoConfig(customConfig);
            expect(config).toBeInstanceOf(AutoConfig);
        });
    });

    describe('配置检测', () => {
        it('应该能够检测配置', async () => {
            // Mock HTML 元素
            const mockElement = {
                getAttribute: vi.fn().mockImplementation((attr) => {
                    switch (attr) {
                        case 'data-micro-app': return 'test-app';
                        case 'data-entry': return 'http://localhost:3000';
                        case 'data-active-when': return '/test';
                        default: return null;
                    }
                }),
                attributes: []
            };

            mockDocument.querySelectorAll.mockReturnValue([mockElement]);

            const result = await autoConfig.detect();

            expect(result).toBeDefined();
            expect(result.apps).toBeDefined();
            expect(Array.isArray(result.apps)).toBe(true);
        });

        it('应该处理空的检测结果', async () => {
            mockDocument.querySelectorAll.mockReturnValue([]);

            const result = await autoConfig.detect();

            expect(result).toBeDefined();
            expect(result.apps).toBeDefined();
            expect(result.apps).toHaveLength(0);
        });

        it('应该从全局变量检测配置', async () => {
            const globalConfig = {
                apps: [
                    {
                        name: 'global-app',
                        entry: 'http://localhost:4000',
                        activeWhen: '/global'
                    }
                ]
            };

            mockWindow.__MICRO_CORE_CONFIG__ = globalConfig;
            mockDocument.querySelectorAll.mockReturnValue([]);

            const result = await autoConfig.detect();

            expect(result.apps).toHaveLength(1);
            expect(result.apps![0].name).toBe('global-app');
        });
    });

    describe('错误处理', () => {
        it('应该处理检测过程中的错误', async () => {
            mockDocument.querySelectorAll.mockImplementation(() => {
                throw new Error('DOM 访问错误');
            });

            // 应该不抛出错误，而是返回空结果
            const result = await autoConfig.detect();
            expect(result).toBeDefined();
            expect(result.apps).toHaveLength(0);
        });

        it('应该处理无效的应用配置', async () => {
            const mockElement = {
                getAttribute: vi.fn().mockReturnValue(null),
                attributes: []
            };

            mockDocument.querySelectorAll.mockReturnValue([mockElement]);

            const result = await autoConfig.detect();
            expect(result.apps).toHaveLength(0);
        });
    });

    describe('配置源', () => {
        it('应该支持禁用特定配置源', async () => {
            const config = new AutoConfig({
                sources: ['html'] // 只启用 HTML 源
            });

            mockWindow.__MICRO_CORE_CONFIG__ = {
                apps: [{ name: 'global-app', entry: 'test', activeWhen: 'test' }]
            };
            mockDocument.querySelectorAll.mockReturnValue([]);

            const result = await config.detect();

            // 由于只启用了 HTML 源，全局配置应该被忽略
            expect(result.apps).toHaveLength(0);
        });

        it('应该支持完全禁用自动配置', async () => {
            const config = new AutoConfig({
                enabled: false
            });

            const result = await config.detect();
            expect(result).toEqual({});
        });
    });

    describe('值解析', () => {
        it('应该正确解析不同类型的值', async () => {
            const mockElement = {
                getAttribute: vi.fn().mockImplementation((attr) => {
                    switch (attr) {
                        case 'data-micro-app': return 'test-app';
                        case 'data-entry': return 'http://localhost:3000';
                        case 'data-active-when': return '/test';
                        case 'data-prop-enabled': return 'true';
                        case 'data-prop-count': return '42';
                        case 'data-prop-config': return '{"key":"value"}';
                        default: return null;
                    }
                }),
                attributes: [
                    { name: 'data-prop-enabled', value: 'true' },
                    { name: 'data-prop-count', value: '42' },
                    { name: 'data-prop-config', value: '{"key":"value"}' }
                ]
            };

            mockDocument.querySelectorAll.mockReturnValue([mockElement]);

            const result = await autoConfig.detect();

            if (result.apps && result.apps.length > 0) {
                const app = result.apps[0];
                expect(app.customProps?.enabled).toBe(true);
                expect(app.customProps?.count).toBe(42);
                expect(app.customProps?.config).toEqual({ key: 'value' });
            }
        });
    });
});