/**
 * @fileoverview 浏览器兼容性测试
 * @description 测试 Sidecar 在不同浏览器环境下的兼容性
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { init, getSidecar, destroy } from '../src/index';
import { CompatMode } from '../src/compat-mode';

// 模拟不同浏览器环境
const mockBrowserEnvironments = {
    // 现代浏览器
    modern: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        features: {
            Promise: true,
            fetch: true,
            Proxy: true,
            WeakMap: true,
            Symbol: true,
            customElements: true
        }
    },
    // IE11
    ie11: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
        features: {
            Promise: false,
            fetch: false,
            Proxy: false,
            WeakMap: true,
            Symbol: false,
            customElements: false
        }
    },
    // 旧版 Chrome
    oldChrome: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36',
        features: {
            Promise: true,
            fetch: true,
            Proxy: true,
            WeakMap: true,
            Symbol: true,
            customElements: false
        }
    },
    // 移动端 Safari
    mobileSafari: {
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1',
        features: {
            Promise: true,
            fetch: true,
            Proxy: true,
            WeakMap: true,
            Symbol: true,
            customElements: true
        }
    }
};

describe('Browser Compatibility Tests', () => {
    let originalUserAgent: string;
    let originalWindow: any;

    beforeEach(() => {
        originalUserAgent = navigator.userAgent;
        originalWindow = { ...window };
        
        // 清理之前的实例
        destroy();
    });

    afterEach(async () => {
        // 恢复原始环境
        Object.defineProperty(navigator, 'userAgent', {
            value: originalUserAgent,
            configurable: true
        });
        
        // 恢复 window 对象
        Object.keys(originalWindow).forEach(key => {
            if (!(key in window)) {
                (window as any)[key] = originalWindow[key];
            }
        });

        await destroy();
    });

    /**
     * 模拟浏览器环境
     */
    function mockBrowserEnvironment(envName: keyof typeof mockBrowserEnvironments) {
        const env = mockBrowserEnvironments[envName];
        
        // 模拟 User Agent
        Object.defineProperty(navigator, 'userAgent', {
            value: env.userAgent,
            configurable: true
        });

        // 模拟浏览器特性
        Object.keys(env.features).forEach(feature => {
            if (!env.features[feature as keyof typeof env.features]) {
                delete (window as any)[feature];
            }
        });
    }

    describe('Modern Browser Support', () => {
        beforeEach(() => {
            mockBrowserEnvironment('modern');
        });

        it('should initialize successfully in modern browsers', async () => {
            const sidecar = await init({
                apps: [{
                    name: 'test-app',
                    entry: '/test-app.js',
                    container: '#test-container'
                }]
            });

            expect(sidecar).toBeDefined();
            expect(getSidecar()).toBeTruthy();
        });

        it('should support all modern features', async () => {
            await init({ compatMode: false });
            
            const sidecar = getSidecar();
            expect(sidecar).toBeTruthy();
            
            // 验证现代特性可用
            expect(window.Promise).toBeDefined();
            expect(window.fetch).toBeDefined();
            expect(window.Proxy).toBeDefined();
        });

        it('should handle ES6+ features correctly', async () => {
            await init({
                apps: [{
                    name: 'es6-app',
                    entry: '/es6-app.js',
                    container: '#es6-container'
                }]
            });

            // 测试 ES6 特性
            expect(() => {
                const testMap = new Map();
                const testSet = new Set();
                const testSymbol = Symbol('test');
                
                testMap.set('key', 'value');
                testSet.add('item');
                
                expect(testMap.get('key')).toBe('value');
                expect(testSet.has('item')).toBe(true);
                expect(typeof testSymbol).toBe('symbol');
            }).not.toThrow();
        });
    });

    describe('IE11 Compatibility', () => {
        beforeEach(() => {
            mockBrowserEnvironment('ie11');
        });

        it('should enable compatibility mode automatically for IE11', async () => {
            const compatMode = new CompatMode();
            const shouldEnable = compatMode.shouldEnableCompatMode();
            
            expect(shouldEnable).toBe(true);
        });

        it('should provide polyfills for missing features', async () => {
            // 模拟缺失的 Promise
            delete (window as any).Promise;
            delete (window as any).fetch;

            const sidecar = await init({
                compatMode: true,
                apps: [{
                    name: 'ie11-app',
                    entry: '/ie11-app.js',
                    container: '#ie11-container'
                }]
            });

            expect(sidecar).toBeDefined();
            
            // 验证 polyfills 已加载
            expect((window as any).Promise).toBeDefined();
            expect((window as any).fetch).toBeDefined();
        });

        it('should fallback to compatible implementations', async () => {
            // 模拟不支持 Proxy
            delete (window as any).Proxy;

            await init({
                compatMode: true,
                apps: [{
                    name: 'fallback-app',
                    entry: '/fallback-app.js',
                    container: '#fallback-container'
                }]
            });

            const sidecar = getSidecar();
            expect(sidecar).toBeTruthy();
            
            // 应该使用 defineProperty 作为 Proxy 的替代
            expect(Object.defineProperty).toBeDefined();
        });
    });

    describe('Mobile Browser Support', () => {
        beforeEach(() => {
            mockBrowserEnvironment('mobileSafari');
        });

        it('should work correctly on mobile Safari', async () => {
            const sidecar = await init({
                apps: [{
                    name: 'mobile-app',
                    entry: '/mobile-app.js',
                    container: '#mobile-container'
                }]
            });

            expect(sidecar).toBeDefined();
        });

        it('should handle touch events properly', async () => {
            await init({
                apps: [{
                    name: 'touch-app',
                    entry: '/touch-app.js',
                    container: '#touch-container'
                }]
            });

            // 模拟触摸事件
            const touchEvent = new TouchEvent('touchstart', {
                touches: [{
                    clientX: 100,
                    clientY: 100
                } as Touch]
            });

            expect(() => {
                document.dispatchEvent(touchEvent);
            }).not.toThrow();
        });

        it('should optimize for mobile performance', async () => {
            const startTime = performance.now();
            
            await init({
                apps: [{
                    name: 'perf-app',
                    entry: '/perf-app.js',
                    container: '#perf-container'
                }],
                mobileOptimization: true
            });

            const endTime = performance.now();
            const initTime = endTime - startTime;

            // 移动端初始化应该在合理时间内完成
            expect(initTime).toBeLessThan(1000);
        });
    });

    describe('Cross-Browser Event Handling', () => {
        it('should handle events consistently across browsers', async () => {
            const eventHandler = vi.fn();
            
            await init({
                apps: [{
                    name: 'event-app',
                    entry: '/event-app.js',
                    container: '#event-container'
                }]
            });

            // 测试不同类型的事件
            const events = ['click', 'keydown', 'resize', 'scroll'];
            
            events.forEach(eventType => {
                document.addEventListener(eventType, eventHandler);
                
                const event = new Event(eventType);
                document.dispatchEvent(event);
                
                expect(eventHandler).toHaveBeenCalled();
                
                document.removeEventListener(eventType, eventHandler);
                eventHandler.mockClear();
            });
        });

        it('should handle custom events properly', async () => {
            const customEventHandler = vi.fn();
            
            await init({
                apps: [{
                    name: 'custom-event-app',
                    entry: '/custom-event-app.js',
                    container: '#custom-event-container'
                }]
            });

            document.addEventListener('micro-app:loaded', customEventHandler);
            
            const customEvent = new CustomEvent('micro-app:loaded', {
                detail: { appName: 'test-app' }
            });
            
            document.dispatchEvent(customEvent);
            
            expect(customEventHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    detail: { appName: 'test-app' }
                })
            );
        });
    });

    describe('Memory Management', () => {
        it('should not leak memory in long-running applications', async () => {
            const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
            
            // 创建和销毁多个应用实例
            for (let i = 0; i < 10; i++) {
                const sidecar = await init({
                    apps: [{
                        name: `test-app-${i}`,
                        entry: `/test-app-${i}.js`,
                        container: `#test-container-${i}`
                    }]
                });
                
                expect(sidecar).toBeDefined();
                await destroy();
            }

            // 强制垃圾回收（如果支持）
            if ((global as any).gc) {
                (global as any).gc();
            }

            const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
            const memoryIncrease = finalMemory - initialMemory;

            // 内存增长应该在合理范围内（小于 10MB）
            expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
        });

        it('should clean up event listeners properly', async () => {
            const eventListenerCount = () => {
                return (document as any)._eventListeners?.length || 0;
            };

            const initialCount = eventListenerCount();

            await init({
                apps: [{
                    name: 'cleanup-app',
                    entry: '/cleanup-app.js',
                    container: '#cleanup-container'
                }]
            });

            await destroy();

            const finalCount = eventListenerCount();
            
            // 事件监听器应该被正确清理
            expect(finalCount).toBeLessThanOrEqual(initialCount);
        });
    });

    describe('Error Handling Across Browsers', () => {
        it('should handle errors consistently', async () => {
            const errorHandler = vi.fn();
            
            await init({
                apps: [{
                    name: 'error-app',
                    entry: '/non-existent-app.js',
                    container: '#error-container'
                }],
                errorHandler
            });

            // 等待错误处理
            await new Promise(resolve => setTimeout(resolve, 100));

            expect(errorHandler).toHaveBeenCalled();
        });

        it('should provide meaningful error messages', async () => {
            const errors: Error[] = [];
            
            await init({
                apps: [{
                    name: 'invalid-app',
                    entry: 'invalid-url',
                    container: '#invalid-container'
                }],
                errorHandler: (error: Error) => {
                    errors.push(error);
                }
            });

            await new Promise(resolve => setTimeout(resolve, 100));

            expect(errors.length).toBeGreaterThan(0);
            expect(errors[0].message).toContain('invalid-url');
        });
    });

    describe('Performance Across Browsers', () => {
        it('should meet performance benchmarks', async () => {
            const performanceMetrics: number[] = [];
            
            for (let i = 0; i < 5; i++) {
                const startTime = performance.now();
                
                await init({
                    apps: [{
                        name: `perf-test-${i}`,
                        entry: `/perf-test-${i}.js`,
                        container: `#perf-container-${i}`
                    }]
                });

                const endTime = performance.now();
                performanceMetrics.push(endTime - startTime);
                
                await destroy();
            }

            const averageTime = performanceMetrics.reduce((a, b) => a + b, 0) / performanceMetrics.length;
            
            // 平均初始化时间应该小于 500ms
            expect(averageTime).toBeLessThan(500);
        });

        it('should handle concurrent app loading efficiently', async () => {
            const startTime = performance.now();
            
            const apps = Array.from({ length: 5 }, (_, i) => ({
                name: `concurrent-app-${i}`,
                entry: `/concurrent-app-${i}.js`,
                container: `#concurrent-container-${i}`
            }));

            await init({ apps });

            const endTime = performance.now();
            const totalTime = endTime - startTime;

            // 并发加载应该比串行加载更快
            expect(totalTime).toBeLessThan(1000);
        });
    });
});

/**
 * 浏览器特性检测工具
 */
export class BrowserFeatureDetector {
    /**
     * 检测浏览器类型
     */
    static detectBrowser(): string {
        const userAgent = navigator.userAgent;
        
        if (userAgent.includes('Chrome')) return 'chrome';
        if (userAgent.includes('Firefox')) return 'firefox';
        if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'safari';
        if (userAgent.includes('Edge')) return 'edge';
        if (userAgent.includes('Trident')) return 'ie';
        
        return 'unknown';
    }

    /**
     * 检测是否为移动设备
     */
    static isMobile(): boolean {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 检测支持的特性
     */
    static getSupportedFeatures(): Record<string, boolean> {
        return {
            promise: typeof Promise !== 'undefined',
            fetch: typeof fetch !== 'undefined',
            proxy: typeof Proxy !== 'undefined',
            weakMap: typeof WeakMap !== 'undefined',
            symbol: typeof Symbol !== 'undefined',
            customElements: typeof customElements !== 'undefined',
            shadowDOM: typeof ShadowRoot !== 'undefined',
            webComponents: typeof HTMLElement.prototype.attachShadow !== 'undefined',
            intersectionObserver: typeof IntersectionObserver !== 'undefined',
            mutationObserver: typeof MutationObserver !== 'undefined'
        };
    }
}

export default BrowserFeatureDetector;
