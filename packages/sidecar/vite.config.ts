import { resolve } from 'path'
import { defineConfig } from 'vite'

export default defineConfig({
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'MicroCoreSidecar',
            fileName: 'index',
            formats: ['es', 'cjs', 'umd']
        },
        rollupOptions: {
            external: ['@micro-core/core'],
            output: {
                globals: {
                    '@micro-core/core': 'MicroCore'
                }
            }
        },
        sourcemap: true,
        minify: 'terser'
    },
    test: {
        globals: true,
        environment: 'jsdom'
    }
})