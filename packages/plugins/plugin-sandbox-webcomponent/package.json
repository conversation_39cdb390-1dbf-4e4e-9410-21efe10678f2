{"name": "@micro-core/plugin-sandbox-webcomponent", "version": "0.1.0", "description": "WebComponent 沙箱插件，基于 Web Components 和 Shadow DOM 实现的沙箱策略", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "sandbox", "webcomponent", "shadow-dom", "plugin", "微前端"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0", "tsup": "^8.0.0", "vitest": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-sandbox-webcomponent"}, "publishConfig": {"access": "public"}}