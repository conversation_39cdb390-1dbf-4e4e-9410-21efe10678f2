/**
 * WebComponent 沙箱插件
 * 提供基于 WebComponent 和 Shadow DOM 的样式隔离功能
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';

/**
 * WebComponent 沙箱配置选项
 */
export interface WebComponentSandboxOptions {
    /** Shadow DOM 模式 */
    mode?: 'open' | 'closed';
    /** 是否启用样式隔离 */
    enableStyleIsolation?: boolean;
    /** 自定义元素标签前缀 */
    tagPrefix?: string;
    /** 是否启用日志 */
    enableLogging?: boolean;
}

/**
 * WebComponent 沙箱实例
 */
export class WebComponentSandbox extends HTMLElement {
    private shadowRoot: ShadowRoot;
    private appName: string;
    private options: WebComponentSandboxOptions;
    private isActive = false;
    private styleSheets: CSSStyleSheet[] = [];

    constructor(appName: string, options: WebComponentSandboxOptions = {}) {
        super();

        this.appName = appName;
        this.options = {
            mode: 'open',
            enableStyleIsolation: true,
            tagPrefix: 'micro-app',
            enableLogging: false,
            ...options
        };

        // 创建 Shadow DOM
        this.shadowRoot = this.attachShadow({ mode: this.options.mode! });

        // 设置基本样式
        this.setupBasicStyles();
    }

    /**
     * 设置基本样式
     */
    private setupBasicStyles(): void {
        if (!this.options.enableStyleIsolation) {
            return;
        }

        const style = document.createElement('style');
        style.textContent = `
            :host {
                display: block;
                width: 100%;
                height: 100%;
                box-sizing: border-box;
            }
            
            :host([hidden]) {
                display: none !important;
            }
            
            .micro-app-container {
                width: 100%;
                height: 100%;
                overflow: auto;
            }
        `;

        this.shadowRoot.appendChild(style);
    }

    /**
     * 激活沙箱
     */
    activate(): void {
        if (this.isActive) {
            return;
        }

        this.isActive = true;
        this.style.display = 'block';

        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 沙箱已激活`);
        }
    }

    /**
     * 停用沙箱
     */
    deactivate(): void {
        if (!this.isActive) {
            return;
        }

        this.isActive = false;
        this.style.display = 'none';

        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 沙箱已停用`);
        }
    }

    /**
     * 加载 HTML 内容
     */
    loadContent(html: string): void {
        if (!this.isActive) {
            console.warn(`[WebComponentSandbox:${this.appName}] 沙箱未激活，无法加载内容`);
            return;
        }

        // 创建容器
        const container = document.createElement('div');
        container.className = 'micro-app-container';
        container.innerHTML = html;

        // 清空现有内容
        this.clearContent();

        // 添加新内容
        this.shadowRoot.appendChild(container);

        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 内容已加载`);
        }
    }

    /**
     * 添加样式
     */
    addStyle(css: string): void {
        const style = document.createElement('style');
        style.textContent = css;
        this.shadowRoot.appendChild(style);

        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 样式已添加`);
        }
    }

    /**
     * 添加样式表
     */
    addStyleSheet(href: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;

            link.onload = () => {
                if (this.options.enableLogging) {
                    console.log(`[WebComponentSandbox:${this.appName}] 样式表已加载: ${href}`);
                }
                resolve();
            };

            link.onerror = () => {
                console.error(`[WebComponentSandbox:${this.appName}] 样式表加载失败: ${href}`);
                reject(new Error(`样式表加载失败: ${href}`));
            };

            this.shadowRoot.appendChild(link);
        });
    }

    /**
     * 执行脚本
     */
    execScript(code: string): any {
        if (!this.isActive) {
            throw new Error('沙箱未激活，无法执行脚本');
        }

        try {
            // 在当前上下文中执行脚本
            // 注意：Shadow DOM 不能完全隔离 JavaScript，这里主要是样式隔离
            const func = new Function(code);
            return func.call(this);
        } catch (error) {
            console.error(`[WebComponentSandbox:${this.appName}] 脚本执行错误:`, error);
            throw error;
        }
    }

    /**
     * 获取 Shadow Root
     */
    getShadowRoot(): ShadowRoot {
        return this.shadowRoot;
    }

    /**
     * 查找元素
     */
    querySelector(selector: string): Element | null {
        return this.shadowRoot.querySelector(selector);
    }

    /**
     * 查找所有元素
     */
    querySelectorAll(selector: string): NodeListOf<Element> {
        return this.shadowRoot.querySelectorAll(selector);
    }

    /**
     * 清空内容
     */
    clearContent(): void {
        // 保留样式元素，清空其他内容
        const styles = this.shadowRoot.querySelectorAll('style, link[rel="stylesheet"]');
        this.shadowRoot.innerHTML = '';

        // 重新添加样式
        styles.forEach(style => {
            this.shadowRoot.appendChild(style);
        });
    }

    /**
     * 销毁沙箱
     */
    destroy(): void {
        this.deactivate();
        this.clearContent();

        // 从 DOM 中移除
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    }

    /**
     * 连接到 DOM 时的回调
     */
    connectedCallback(): void {
        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 组件已连接到 DOM`);
        }
    }

    /**
     * 从 DOM 断开时的回调
     */
    disconnectedCallback(): void {
        if (this.options.enableLogging) {
            console.log(`[WebComponentSandbox:${this.appName}] 组件已从 DOM 断开`);
        }
    }
}

/**
 * WebComponent 沙箱插件
 */
export class WebComponentSandboxPlugin implements Plugin {
    name = 'sandbox-webcomponent';
    version = '1.0.0';

    private sandboxes = new Map<string, WebComponentSandbox>();
    private options: WebComponentSandboxOptions;
    private registeredElements = new Set<string>();

    constructor(options: WebComponentSandboxOptions = {}) {
        this.options = options;
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        // 注册沙箱创建器
        kernel.registerSandboxCreator?.('webcomponent', (appName: string, config: any) => {
            return this.createSandbox(appName, config);
        });

        console.log('[WebComponentSandboxPlugin] WebComponent 沙箱插件已安装');
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            sandbox.destroy();
        }
        this.sandboxes.clear();

        // 注销沙箱创建器
        kernel.unregisterSandboxCreator?.('webcomponent');

        console.log('[WebComponentSandboxPlugin] WebComponent 沙箱插件已卸载');
    }

    /**
     * 创建沙箱
     */
    createSandbox(appName: string, config: any): WebComponentSandbox {
        if (this.sandboxes.has(appName)) {
            return this.sandboxes.get(appName)!;
        }

        const options = {
            ...this.options,
            ...config.sandboxOptions
        };

        // 注册自定义元素
        const tagName = this.getTagName(appName, options.tagPrefix);
        this.registerCustomElement(tagName, appName, options);

        // 创建沙箱实例
        const sandbox = new WebComponentSandbox(appName, options);
        this.sandboxes.set(appName, sandbox);

        return sandbox;
    }

    /**
     * 获取标签名
     */
    private getTagName(appName: string, prefix = 'micro-app'): string {
        return `${prefix}-${appName.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
    }

    /**
     * 注册自定义元素
     */
    private registerCustomElement(tagName: string, appName: string, options: WebComponentSandboxOptions): void {
        if (this.registeredElements.has(tagName)) {
            return;
        }

        if (customElements.get(tagName)) {
            console.warn(`[WebComponentSandboxPlugin] 自定义元素 ${tagName} 已存在`);
            return;
        }

        // 创建自定义元素类
        class MicroAppElement extends WebComponentSandbox {
            constructor() {
                super(appName, options);
            }
        }

        // 注册自定义元素
        customElements.define(tagName, MicroAppElement);
        this.registeredElements.add(tagName);

        if (options.enableLogging) {
            console.log(`[WebComponentSandboxPlugin] 自定义元素已注册: ${tagName}`);
        }
    }

    /**
     * 获取沙箱
     */
    getSandbox(appName: string): WebComponentSandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 销毁沙箱
     */
    destroySandbox(appName: string): void {
        const sandbox = this.sandboxes.get(appName);
        if (sandbox) {
            sandbox.destroy();
            this.sandboxes.delete(appName);
        }
    }
}

// 导出工厂函数
export function createWebComponentSandboxPlugin(options?: WebComponentSandboxOptions): WebComponentSandboxPlugin {
    return new WebComponentSandboxPlugin(options);
}

// 默认导出
export default WebComponentSandboxPlugin;

