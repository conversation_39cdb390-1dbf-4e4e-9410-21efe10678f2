/**
 * 沙箱容器元素
 * 专门用于微前端沙箱环境的容器组件
 */
export class SandboxContainer extends HTMLElement {
    private shadowRoot: ShadowRoot;
    private sandboxName: string = '';
    private isolationLevel: 'strict' | 'moderate' | 'loose' = 'moderate';
    private eventBus: Map<string, Function[]> = new Map();
    private cssVariables: Map<string, string> = new Map();

    constructor() {
        super();

        // 创建 Shadow DOM
        this.shadowRoot = this.attachShadow({ mode: 'closed' });

        // 初始化容器
        this.initializeContainer();

        // 设置事件隔离
        this.setupEventIsolation();
    }

    /**
     * 监听的属性列表
     */
    static get observedAttributes() {
        return ['sandbox-name', 'isolation-level', 'css-variables'];
    }

    /**
     * 属性变化回调
     */
    attributeChangedCallback(name: string, oldValue: string, newValue: string) {
        if (oldValue === newValue) return;

        switch (name) {
            case 'sandbox-name':
                this.sandboxName = newValue || '';
                this.updateSandboxName();
                break;
            case 'isolation-level':
                this.isolationLevel = (newValue as any) || 'moderate';
                this.updateIsolationLevel();
                break;
            case 'css-variables':
                this.updateCSSVariables(newValue);
                break;
        }
    }

    /**
     * 元素连接到 DOM 时调用
     */
    connectedCallback() {
        this.dispatchEvent(new CustomEvent('sandbox-connected', {
            detail: { sandboxName: this.sandboxName },
            bubbles: false // 不冒泡，保持隔离
        }));
    }

    /**
     * 元素从 DOM 断开时调用
     */
    disconnectedCallback() {
        this.cleanup();
        this.dispatchEvent(new CustomEvent('sandbox-disconnected', {
            detail: { sandboxName: this.sandboxName },
            bubbles: false
        }));
    }

    /**
     * 初始化容器
     */
    private initializeContainer() {
        const style = document.createElement('style');
        style.textContent = `
            :host {
                display: block;
                position: relative;
                width: 100%;
                height: 100%;
                contain: layout style paint;
                isolation: isolate;
                overflow: hidden;
            }
            
            .sandbox-wrapper {
                width: 100%;
                height: 100%;
                position: relative;
                background: var(--sandbox-bg, transparent);
                border: var(--sandbox-border, none);
                border-radius: var(--sandbox-radius, 0);
            }
            
            .sandbox-content {
                width: 100%;
                height: 100%;
                overflow: auto;
                padding: var(--sandbox-padding, 0);
            }
            
            /* 严格隔离模式样式 */
            :host([isolation-level="strict"]) {
                contain: strict;
                transform: translateZ(0); /* 创建新的层叠上下文 */
            }
            
            :host([isolation-level="strict"]) .sandbox-wrapper {
                border: 1px solid var(--sandbox-border-color, #e0e0e0);
                background: var(--sandbox-bg-strict, #fafafa);
            }
            
            /* 中等隔离模式样式 */
            :host([isolation-level="moderate"]) {
                contain: layout style;
            }
            
            /* 宽松隔离模式样式 */
            :host([isolation-level="loose"]) {
                contain: style;
            }
            
            /* 调试模式样式 */
            :host([debug="true"]) .sandbox-wrapper {
                border: 2px dashed #ff6b6b;
                background: rgba(255, 107, 107, 0.05);
            }
            
            :host([debug="true"]) .sandbox-wrapper::before {
                content: attr(sandbox-name);
                position: absolute;
                top: 0;
                left: 0;
                background: #ff6b6b;
                color: white;
                padding: 2px 6px;
                font-size: 12px;
                font-family: monospace;
                z-index: 1000;
            }
        `;
        this.shadowRoot.appendChild(style);

        // 创建容器结构
        const wrapper = document.createElement('div');
        wrapper.className = 'sandbox-wrapper';

        const content = document.createElement('div');
        content.className = 'sandbox-content';

        // 创建插槽用于内容投影
        const slot = document.createElement('slot');
        content.appendChild(slot);

        wrapper.appendChild(content);
        this.shadowRoot.appendChild(wrapper);
    }

    /**
     * 设置事件隔离
     */
    private setupEventIsolation() {
        // 阻止特定事件向外冒泡
        const isolatedEvents = [
            'click', 'dblclick', 'mousedown', 'mouseup', 'mousemove',
            'keydown', 'keyup', 'keypress',
            'focus', 'blur', 'focusin', 'focusout',
            'input', 'change', 'submit',
            'touchstart', 'touchmove', 'touchend'
        ];

        isolatedEvents.forEach(eventType => {
            this.addEventListener(eventType, (event) => {
                if (this.isolationLevel === 'strict') {
                    event.stopPropagation();
                } else if (this.isolationLevel === 'moderate') {
                    // 只阻止某些敏感事件
                    if (['focus', 'blur', 'input', 'change'].includes(eventType)) {
                        event.stopPropagation();
                    }
                }
                // loose 模式不阻止事件冒泡
            }, true);
        });
    }

    /**
     * 更新沙箱名称
     */
    private updateSandboxName() {
        this.setAttribute('data-sandbox', this.sandboxName);

        const wrapper = this.shadowRoot.querySelector('.sandbox-wrapper') as HTMLElement;
        if (wrapper) {
            wrapper.setAttribute('sandbox-name', this.sandboxName);
        }
    }

    /**
     * 更新隔离级别
     */
    private updateIsolationLevel() {
        this.setAttribute('isolation-level', this.isolationLevel);

        // 根据隔离级别调整容器行为
        const wrapper = this.shadowRoot.querySelector('.sandbox-wrapper') as HTMLElement;
        if (wrapper) {
            wrapper.setAttribute('data-isolation', this.isolationLevel);
        }
    }

    /**
     * 更新 CSS 变量
     */
    private updateCSSVariables(variablesString: string) {
        try {
            const variables = variablesString ? JSON.parse(variablesString) : {};

            // 清除旧的 CSS 变量
            this.cssVariables.clear();

            // 设置新的 CSS 变量
            Object.entries(variables).forEach(([key, value]) => {
                this.cssVariables.set(key, value as string);
                this.style.setProperty(`--${key}`, value as string);
            });
        } catch (error) {
            console.error('Failed to parse CSS variables:', error);
        }
    }

    /**
     * 公共方法：设置 CSS 变量
     */
    setCSSVariable(name: string, value: string) {
        this.cssVariables.set(name, value);
        this.style.setProperty(`--${name}`, value);
    }

    /**
     * 公共方法：获取 CSS 变量
     */
    getCSSVariable(name: string): string | undefined {
        return this.cssVariables.get(name);
    }

    /**
     * 公共方法：添加事件监听器（沙箱内部）
     */
    addSandboxEventListener(eventType: string, handler: Function) {
        if (!this.eventBus.has(eventType)) {
            this.eventBus.set(eventType, []);
        }
        this.eventBus.get(eventType)!.push(handler);
    }

    /**
     * 公共方法：移除事件监听器
     */
    removeSandboxEventListener(eventType: string, handler: Function) {
        const handlers = this.eventBus.get(eventType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * 公共方法：触发沙箱内部事件
     */
    emitSandboxEvent(eventType: string, data?: any) {
        const handlers = this.eventBus.get(eventType);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in sandbox event handler for ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * 公共方法：注入内容
     */
    injectContent(html: string) {
        const content = this.shadowRoot.querySelector('.sandbox-content') as HTMLElement;
        if (content) {
            // 清除现有内容（保留插槽）
            const slot = content.querySelector('slot');
            content.innerHTML = '';
            if (slot) {
                content.appendChild(slot);
            }

            // 注入新内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            while (tempDiv.firstChild) {
                content.appendChild(tempDiv.firstChild);
            }
        }
    }

    /**
     * 公共方法：清空内容
     */
    clearContent() {
        const content = this.shadowRoot.querySelector('.sandbox-content') as HTMLElement;
        if (content) {
            const slot = content.querySelector('slot');
            content.innerHTML = '';
            if (slot) {
                content.appendChild(slot);
            }
        }
    }

    /**
     * 公共方法：获取沙箱信息
     */
    getSandboxInfo() {
        return {
            name: this.sandboxName,
            isolationLevel: this.isolationLevel,
            cssVariables: Object.fromEntries(this.cssVariables),
            eventTypes: Array.from(this.eventBus.keys())
        };
    }

    /**
     * 清理资源
     */
    private cleanup() {
        // 清理事件监听器
        this.eventBus.clear();

        // 清理 CSS 变量
        this.cssVariables.forEach((_, name) => {
            this.style.removeProperty(`--${name}`);
        });
        this.cssVariables.clear();

        // 清空内容
        this.clearContent();
    }
}