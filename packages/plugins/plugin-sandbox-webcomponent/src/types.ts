/**
 * WebComponent 沙箱插件类型定义
 */

/**
 * WebComponent 沙箱插件选项
 */
export interface WebComponentSandboxPluginOptions {
    /** 是否启用日志记录 */
    enableLogging?: boolean;
    /** 是否启用样式隔离 */
    enableStyleIsolation?: boolean;
    /** 是否启用 DOM 隔离 */
    enableDOMIsolation?: boolean;
    /** 是否启用事件隔离 */
    enableEventIsolation?: boolean;
    /** 自定义元素前缀 */
    customElementPrefix?: string;
    /** Shadow DOM 模式 */
    shadowDOMMode?: 'open' | 'closed';
    /** 是否启用插槽支持 */
    enableSlotSupport?: boolean;
    /** 是否启用 CSS 变量支持 */
    enableCSSVariables?: boolean;
}

/**
 * WebComponent 沙箱选项
 */
export interface WebComponentSandboxOptions extends WebComponentSandboxPluginOptions {
    /** 隔离级别 */
    isolationLevel?: 'strict' | 'moderate' | 'loose';
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 自定义样式 */
    customStyles?: string;
    /** 预设 CSS 变量 */
    presetCSSVariables?: Record<string, string>;
}

/**
 * Shadow DOM 上下文
 */
export interface ShadowDOMContext {
    /** Shadow Root 实例 */
    shadowRoot: ShadowRoot;
    /** 文档对象（Shadow Root 作为文档） */
    document: Document;
    /** 窗口对象 */
    window: Window;
    /** 元素映射 */
    elements: Map<string, HTMLElement>;
    /** 样式映射 */
    styles: Map<string, HTMLStyleElement>;
    /** 脚本映射 */
    scripts: Map<string, HTMLScriptElement>;
}

/**
 * 样式隔离上下文
 */
export interface StyleIsolationContext {
    /** Shadow Root 实例 */
    shadowRoot: ShadowRoot;
    /** 样式表映射 */
    styleSheets: Map<string, CSSStyleSheet>;
    /** CSS 规则映射 */
    cssRules: Map<string, CSSRule>;
    /** 计算样式映射 */
    computedStyles: Map<HTMLElement, CSSStyleDeclaration>;
    /** CSS 变量映射 */
    cssVariables: Map<string, string>;
}

/**
 * 事件隔离配置
 */
export interface EventIsolationConfig {
    /** 需要隔离的事件类型 */
    isolatedEvents?: string[];
    /** 需要透传的事件类型 */
    passthroughEvents?: string[];
    /** 事件处理策略 */
    strategy?: 'block' | 'transform' | 'proxy';
}

/**
 * 微应用元素配置
 */
export interface MicroAppElementConfig {
    /** 应用名称 */
    appName: string;
    /** 应用入口 */
    appEntry: string;
    /** 应用属性 */
    appProps?: Record<string, any>;
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 自定义加载器 */
    customLoader?: (entry: string) => Promise<string>;
    /** 错误处理器 */
    errorHandler?: (error: Error) => void;
}

/**
 * 沙箱容器配置
 */
export interface SandboxContainerConfig {
    /** 沙箱名称 */
    sandboxName: string;
    /** 隔离级别 */
    isolationLevel?: 'strict' | 'moderate' | 'loose';
    /** CSS 变量 */
    cssVariables?: Record<string, string>;
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 事件隔离配置 */
    eventIsolation?: EventIsolationConfig;
}

/**
 * 内容注入选项
 */
export interface ContentInjectionOptions {
    /** 是否处理样式 */
    processStyles?: boolean;
    /** 是否处理脚本 */
    processScripts?: boolean;
    /** 是否处理链接 */
    processLinks?: boolean;
    /** 样式处理器 */
    styleProcessor?: (style: HTMLStyleElement | HTMLLinkElement) => void;
    /** 脚本处理器 */
    scriptProcessor?: (script: HTMLScriptElement) => Promise<void>;
    /** 内容转换器 */
    contentTransformer?: (html: string) => string;
}

/**
 * 资源加载选项
 */
export interface ResourceLoadOptions {
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 缓存策略 */
    cache?: 'default' | 'no-cache' | 'reload' | 'force-cache';
    /** 请求头 */
    headers?: Record<string, string>;
    /** 跨域模式 */
    mode?: 'cors' | 'no-cors' | 'same-origin';
}

/**
 * 沙箱状态
 */
export interface SandboxState {
    /** 沙箱名称 */
    name: string;
    /** 是否激活 */
    isActive: boolean;
    /** 是否已加载 */
    isLoaded: boolean;
    /** 创建时间 */
    createdAt: Date;
    /** 激活时间 */
    activatedAt?: Date;
    /** 最后更新时间 */
    lastUpdatedAt: Date;
    /** 错误信息 */
    error?: Error;
}

/**
 * 沙箱事件类型
 */
export type SandboxEventType =
    | 'sandbox-created'
    | 'sandbox-activated'
    | 'sandbox-deactivated'
    | 'sandbox-destroyed'
    | 'sandbox-error'
    | 'app-loaded'
    | 'app-mounted'
    | 'app-unmounted'
    | 'content-injected'
    | 'style-applied'
    | 'script-executed';

/**
 * 沙箱事件数据
 */
export interface SandboxEventData {
    /** 沙箱名称 */
    sandboxName: string;
    /** 事件类型 */
    eventType: SandboxEventType;
    /** 时间戳 */
    timestamp: Date;
    /** 事件数据 */
    data?: any;
    /** 错误信息 */
    error?: Error;
}

/**
 * 沙箱生命周期钩子
 */
export interface SandboxLifecycleHooks {
    /** 沙箱创建前 */
    beforeCreate?: (config: WebComponentSandboxOptions) => void | Promise<void>;
    /** 沙箱创建后 */
    afterCreate?: (sandbox: any) => void | Promise<void>;
    /** 沙箱激活前 */
    beforeActivate?: (sandbox: any) => void | Promise<void>;
    /** 沙箱激活后 */
    afterActivate?: (sandbox: any) => void | Promise<void>;
    /** 沙箱停用前 */
    beforeDeactivate?: (sandbox: any) => void | Promise<void>;
    /** 沙箱停用后 */
    afterDeactivate?: (sandbox: any) => void | Promise<void>;
    /** 沙箱销毁前 */
    beforeDestroy?: (sandbox: any) => void | Promise<void>;
    /** 沙箱销毁后 */
    afterDestroy?: (sandbox: any) => void | Promise<void>;
    /** 应用加载前 */
    beforeAppLoad?: (entry: string) => void | Promise<void>;
    /** 应用加载后 */
    afterAppLoad?: (entry: string) => void | Promise<void>;
    /** 内容注入前 */
    beforeContentInject?: (html: string) => string | Promise<string>;
    /** 内容注入后 */
    afterContentInject?: (element: HTMLElement) => void | Promise<void>;
}

/**
 * 沙箱性能指标
 */
export interface SandboxPerformanceMetrics {
    /** 创建时间（毫秒） */
    creationTime: number;
    /** 激活时间（毫秒） */
    activationTime: number;
    /** 内容加载时间（毫秒） */
    loadTime: number;
    /** 内存使用量（字节） */
    memoryUsage: number;
    /** DOM 节点数量 */
    domNodeCount: number;
    /** 样式表数量 */
    styleSheetCount: number;
    /** 脚本数量 */
    scriptCount: number;
    /** 事件监听器数量 */
    eventListenerCount: number;
}

/**
 * 沙箱调试信息
 */
export interface SandboxDebugInfo {
    /** 沙箱名称 */
    name: string;
    /** 沙箱状态 */
    state: SandboxState;
    /** 性能指标 */
    metrics: SandboxPerformanceMetrics;
    /** 配置信息 */
    config: WebComponentSandboxOptions;
    /** Shadow DOM 信息 */
    shadowDOMInfo: {
        mode: 'open' | 'closed';
        childElementCount: number;
        innerHTML: string;
    };
    /** 样式信息 */
    styleInfo: {
        cssVariables: Record<string, string>;
        styleSheets: string[];
        computedStyles: Record<string, string>;
    };
    /** 事件信息 */
    eventInfo: {
        registeredEvents: string[];
        eventListenerCount: number;
    };
}

/**
 * 沙箱错误类型
 */
export enum SandboxErrorType {
    /** 创建错误 */
    CREATION_ERROR = 'creation_error',
    /** 激活错误 */
    ACTIVATION_ERROR = 'activation_error',
    /** 加载错误 */
    LOAD_ERROR = 'load_error',
    /** 注入错误 */
    INJECTION_ERROR = 'injection_error',
    /** 脚本执行错误 */
    SCRIPT_ERROR = 'script_error',
    /** 样式错误 */
    STYLE_ERROR = 'style_error',
    /** 事件错误 */
    EVENT_ERROR = 'event_error',
    /** 销毁错误 */
    DESTROY_ERROR = 'destroy_error'
}

/**
 * 沙箱错误信息
 */
export interface SandboxError extends Error {
    /** 错误类型 */
    type: SandboxErrorType;
    /** 沙箱名称 */
    sandboxName: string;
    /** 错误代码 */
    code?: string;
    /** 错误详情 */
    details?: any;
    /** 错误时间 */
    timestamp: Date;
    /** 错误堆栈 */
    stack?: string;
}

/**
 * 自定义元素注册选项
 */
export interface CustomElementRegistrationOptions {
    /** 元素名称 */
    elementName: string;
    /** 元素类 */
    elementClass: CustomElementConstructor;
    /** 是否强制重新注册 */
    forceReregister?: boolean;
    /** 注册前回调 */
    beforeRegister?: (elementName: string, elementClass: CustomElementConstructor) => void;
    /** 注册后回调 */
    afterRegister?: (elementName: string, elementClass: CustomElementConstructor) => void;
}

/**
 * 插件配置验证器
 */
export interface PluginConfigValidator {
    /** 验证函数 */
    validate: (config: WebComponentSandboxPluginOptions) => boolean | string;
    /** 错误消息 */
    errorMessage?: string;
    /** 警告消息 */
    warningMessage?: string;
}

/**
 * 沙箱工厂选项
 */
export interface SandboxFactoryOptions {
    /** 默认配置 */
    defaultConfig?: WebComponentSandboxOptions;
    /** 配置验证器 */
    validators?: PluginConfigValidator[];
    /** 生命周期钩子 */
    hooks?: SandboxLifecycleHooks;
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 最大沙箱数量 */
    maxSandboxCount?: number;
}
