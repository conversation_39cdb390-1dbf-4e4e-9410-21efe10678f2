/**
 * CSS 处理器
 * 用于处理和转换 CSS 样式，确保在 Shadow DOM 中正确隔离
 */

/**
 * CSS 处理选项
 */
export interface CSSProcessorOptions {
    /** 是否启用样式隔离 */
    enableIsolation?: boolean;
    /** 是否处理 CSS 变量 */
    processCSSVariables?: boolean;
    /** 是否处理媒体查询 */
    processMediaQueries?: boolean;
    /** 是否处理关键帧动画 */
    processKeyframes?: boolean;
    /** 自定义前缀 */
    customPrefix?: string;
    /** 是否启用调试模式 */
    debug?: boolean;
}

/**
 * CSS 规则类型
 */
export enum CSSRuleType {
    STYLE = 1,
    IMPORT = 3,
    MEDIA = 4,
    FONT_FACE = 5,
    PAGE = 6,
    KEYFRAMES = 7,
    KEYFRAME = 8,
    SUPPORTS = 12
}

/**
 * CSS 处理器类
 */
export class CSSProcessor {
    private options: Required<CSSProcessorOptions>;

    constructor(options: CSSProcessorOptions = {}) {
        this.options = {
            enableIsolation: true,
            processCSSVariables: true,
            processMediaQueries: true,
            processKeyframes: true,
            customPrefix: '',
            debug: false,
            ...options
        };
    }

    /**
     * 处理 CSS 字符串
     */
    processCSS(css: string): string {
        let processedCSS = css;

        if (this.options.enableIsolation) {
            processedCSS = this.applyScopeIsolation(processedCSS);
        }

        if (this.options.processCSSVariables) {
            processedCSS = this.processCSSVariables(processedCSS);
        }

        if (this.options.processMediaQueries) {
            processedCSS = this.processMediaQueries(processedCSS);
        }

        if (this.options.processKeyframes) {
            processedCSS = this.processKeyframes(processedCSS);
        }

        if (this.options.debug) {
            console.log('[CSSProcessor] 原始 CSS:', css);
            console.log('[CSSProcessor] 处理后 CSS:', processedCSS);
        }

        return processedCSS;
    }

    /**
     * 应用作用域隔离
     */
    private applyScopeIsolation(css: string): string {
        // 处理普通选择器
        let isolatedCSS = css.replace(/([^{}@]+)\s*{/g, (match, selector) => {
            // 跳过 @规则
            if (selector.trim().startsWith('@')) {
                return match;
            }

            // 跳过已经包含 :host 的选择器
            if (selector.includes(':host')) {
                return match;
            }

            // 处理多个选择器（逗号分隔）
            const isolatedSelectors = selector
                .split(',')
                .map((s: string) => this.isolateSelector(s.trim()))
                .join(', ');

            return `${isolatedSelectors} {`;
        });

        return isolatedCSS;
    }

    /**
     * 隔离单个选择器
     */
    private isolateSelector(selector: string): string {
        // 处理伪类选择器
        if (selector.startsWith(':')) {
            return `:host(${selector})`;
        }

        // 处理通用选择器
        if (selector === '*') {
            return ':host *';
        }

        // 处理标签选择器
        if (/^[a-zA-Z][a-zA-Z0-9]*$/.test(selector)) {
            return `:host ${selector}`;
        }

        // 处理类选择器和 ID 选择器
        if (selector.startsWith('.') || selector.startsWith('#')) {
            return `:host ${selector}`;
        }

        // 处理复合选择器
        return `:host ${selector}`;
    }

    /**
     * 处理 CSS 变量
     */
    private processCSSVariables(css: string): string {
        // 为 CSS 变量添加前缀（如果指定了自定义前缀）
        if (this.options.customPrefix) {
            const prefix = this.options.customPrefix;

            // 处理变量定义
            css = css.replace(/--([a-zA-Z][a-zA-Z0-9-]*)/g, `--${prefix}-$1`);

            // 处理变量使用
            css = css.replace(/var\(--([a-zA-Z][a-zA-Z0-9-]*)/g, `var(--${prefix}-$1`);
        }

        return css;
    }

    /**
     * 处理媒体查询
     */
    private processMediaQueries(css: string): string {
        // 媒体查询通常不需要特殊处理，但可以在这里添加自定义逻辑
        return css.replace(/@media\s+([^{]+)\s*{/g, (match, query) => {
            // 可以在这里添加媒体查询的处理逻辑
            return match;
        });
    }

    /**
     * 处理关键帧动画
     */
    private processKeyframes(css: string): string {
        // 为关键帧动画名称添加前缀，避免冲突
        if (this.options.customPrefix) {
            const prefix = this.options.customPrefix;

            // 处理 @keyframes 定义
            css = css.replace(/@keyframes\s+([a-zA-Z][a-zA-Z0-9-]*)/g, `@keyframes ${prefix}-$1`);

            // 处理 animation 属性中的动画名称
            css = css.replace(/animation(-name)?\s*:\s*([a-zA-Z][a-zA-Z0-9-]*)/g, (match, prop, name) => {
                return match.replace(name, `${prefix}-${name}`);
            });
        }

        return css;
    }

    /**
     * 解析 CSS 规则
     */
    parseCSSRules(css: string): CSSRule[] {
        const rules: CSSRule[] = [];

        try {
            // 创建临时样式表来解析 CSS
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);

            const sheet = style.sheet as CSSStyleSheet;
            if (sheet && sheet.cssRules) {
                for (let i = 0; i < sheet.cssRules.length; i++) {
                    rules.push(sheet.cssRules[i]);
                }
            }

            document.head.removeChild(style);
        } catch (error) {
            console.error('[CSSProcessor] 解析 CSS 规则失败:', error);
        }

        return rules;
    }

    /**
     * 提取 CSS 变量
     */
    extractCSSVariables(css: string): Record<string, string> {
        const variables: Record<string, string> = {};

        // 匹配 CSS 变量定义
        const variableRegex = /--([a-zA-Z][a-zA-Z0-9-]*)\s*:\s*([^;]+);/g;
        let match;

        while ((match = variableRegex.exec(css)) !== null) {
            const [, name, value] = match;
            variables[name] = value.trim();
        }

        return variables;
    }

    /**
     * 提取媒体查询
     */
    extractMediaQueries(css: string): string[] {
        const mediaQueries: string[] = [];

        // 匹配媒体查询
        const mediaRegex = /@media\s+([^{]+)\s*{/g;
        let match;

        while ((match = mediaRegex.exec(css)) !== null) {
            mediaQueries.push(match[1].trim());
        }

        return mediaQueries;
    }

    /**
     * 提取关键帧动画
     */
    extractKeyframes(css: string): Record<string, string> {
        const keyframes: Record<string, string> = {};

        // 匹配 @keyframes 规则
        const keyframesRegex = /@keyframes\s+([a-zA-Z][a-zA-Z0-9-]*)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/g;
        let match;

        while ((match = keyframesRegex.exec(css)) !== null) {
            const [, name, content] = match;
            keyframes[name] = content.trim();
        }

        return keyframes;
    }

    /**
     * 压缩 CSS
     */
    minifyCSS(css: string): string {
        return css
            // 移除注释
            .replace(/\/\*[\s\S]*?\*\//g, '')
            // 移除多余的空白字符
            .replace(/\s+/g, ' ')
            // 移除分号前的空格
            .replace(/\s*;\s*/g, ';')
            // 移除冒号前后的空格
            .replace(/\s*:\s*/g, ':')
            // 移除大括号前后的空格
            .replace(/\s*{\s*/g, '{')
            .replace(/\s*}\s*/g, '}')
            // 移除逗号后的空格
            .replace(/,\s*/g, ',')
            // 移除开头和结尾的空格
            .trim();
    }

    /**
     * 美化 CSS
     */
    beautifyCSS(css: string, indent = '  '): string {
        let beautified = css;
        let indentLevel = 0;

        // 添加换行和缩进
        beautified = beautified.replace(/({|})/g, (match) => {
            if (match === '{') {
                indentLevel++;
                return ' {\n' + indent.repeat(indentLevel);
            } else {
                indentLevel--;
                return '\n' + indent.repeat(indentLevel) + '}\n' + indent.repeat(indentLevel);
            }
        });

        // 处理分号后的换行
        beautified = beautified.replace(/;/g, ';\n' + indent.repeat(indentLevel));

        // 清理多余的空行
        beautified = beautified.replace(/\n\s*\n/g, '\n');

        return beautified.trim();
    }

    /**
     * 验证 CSS 语法
     */
    validateCSS(css: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        try {
            // 创建临时样式表来验证 CSS
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);

            const sheet = style.sheet as CSSStyleSheet;
            if (!sheet) {
                errors.push('无法创建样式表');
            }

            document.head.removeChild(style);
        } catch (error) {
            errors.push(`CSS 语法错误: ${error instanceof Error ? error.message : String(error)}`);
        }

        // 检查常见的语法错误
        if (css.includes('{{') || css.includes('}}')) {
            errors.push('检测到重复的大括号');
        }

        // 检查未闭合的大括号
        const openBraces = (css.match(/{/g) || []).length;
        const closeBraces = (css.match(/}/g) || []).length;
        if (openBraces !== closeBraces) {
            errors.push('大括号不匹配');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 获取 CSS 统计信息
     */
    getCSSStats(css: string) {
        const rules = this.parseCSSRules(css);
        const variables = this.extractCSSVariables(css);
        const mediaQueries = this.extractMediaQueries(css);
        const keyframes = this.extractKeyframes(css);

        return {
            totalSize: css.length,
            ruleCount: rules.length,
            variableCount: Object.keys(variables).length,
            mediaQueryCount: mediaQueries.length,
            keyframeCount: Object.keys(keyframes).length,
            variables,
            mediaQueries,
            keyframes
        };
    }

    /**
     * 转换 CSS 为 Shadow DOM 兼容格式
     */
    convertToShadowDOMCSS(css: string): string {
        let convertedCSS = css;

        // 处理 :root 选择器，转换为 :host
        convertedCSS = convertedCSS.replace(/:root\b/g, ':host');

        // 处理 body 选择器，转换为 :host
        convertedCSS = convertedCSS.replace(/\bbody\b/g, ':host');

        // 处理 html 选择器，转换为 :host
        convertedCSS = convertedCSS.replace(/\bhtml\b/g, ':host');

        // 处理全局选择器
        convertedCSS = convertedCSS.replace(/^\s*\*/gm, ':host *');

        return this.processCSS(convertedCSS);
    }

    /**
     * 更新配置
     */
    updateOptions(newOptions: Partial<CSSProcessorOptions>) {
        this.options = { ...this.options, ...newOptions };
    }

    /**
     * 获取当前配置
     */
    getOptions(): CSSProcessorOptions {
        return { ...this.options };
    }
}

/**
 * 创建 CSS 处理器实例
 */
export function createCSSProcessor(options?: CSSProcessorOptions): CSSProcessor {
    return new CSSProcessor(options);
}

/**
 * 快速处理 CSS 字符串
 */
export function processCSS(css: string, options?: CSSProcessorOptions): string {
    const processor = new CSSProcessor(options);
    return processor.processCSS(css);
}

/**
 * 快速验证 CSS 语法
 */
export function validateCSS(css: string): { isValid: boolean; errors: string[] } {
    const processor = new CSSProcessor();
    return processor.validateCSS(css);
}

/**
 * 快速压缩 CSS
 */
export function minifyCSS(css: string): string {
    const processor = new CSSProcessor();
    return processor.minifyCSS(css);
}

/**
 * 快速美化 CSS
 */
export function beautifyCSS(css: string, indent?: string): string {
    const processor = new CSSProcessor();
    return processor.beautifyCSS(css, indent);
}
