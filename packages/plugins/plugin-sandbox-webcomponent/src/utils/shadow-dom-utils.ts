/**
 * Shadow DOM 工具函数
 */

/**
 * 检查浏览器是否支持 Shadow DOM
 */
export function isShadowDOMSupported(): boolean {
    return 'attachShadow' in Element.prototype;
}

/**
 * 检查浏览器是否支持自定义元素
 */
export function isCustomElementsSupported(): boolean {
    return 'customElements' in window;
}

/**
 * 检查元素是否在 Shadow DOM 中
 */
export function isInShadowDOM(element: Element): boolean {
    return element.getRootNode() !== document;
}

/**
 * 获取元素的 Shadow Root
 */
export function getShadowRoot(element: Element): ShadowRoot | null {
    const root = element.getRootNode();
    return root instanceof ShadowRoot ? root : null;
}

/**
 * 获取 Shadow DOM 的宿主元素
 */
export function getShadowHost(shadowRoot: ShadowRoot): Element {
    return shadowRoot.host;
}

/**
 * 在 Shadow DOM 中查找元素
 */
export function queryShadowDOM(shadowRoot: ShadowRoot, selector: string): Element | null {
    return shadowRoot.querySelector(selector);
}

/**
 * 在 Shadow DOM 中查找所有匹配的元素
 */
export function queryShadowDOMAll(shadowRoot: ShadowRoot, selector: string): NodeListOf<Element> {
    return shadowRoot.querySelectorAll(selector);
}

/**
 * 创建 Shadow DOM
 */
export function createShadowDOM(
    element: Element,
    options: ShadowRootInit = { mode: 'closed' }
): ShadowRoot {
    if (!isShadowDOMSupported()) {
        throw new Error('Shadow DOM is not supported in this browser');
    }

    return element.attachShadow(options);
}

/**
 * 克隆节点到 Shadow DOM
 */
export function cloneToShadowDOM(
    sourceNode: Node,
    shadowRoot: ShadowRoot,
    deep = true
): Node {
    const clonedNode = sourceNode.cloneNode(deep);
    shadowRoot.appendChild(clonedNode);
    return clonedNode;
}

/**
 * 将 HTML 字符串注入到 Shadow DOM
 */
export function injectHTMLToShadowDOM(shadowRoot: ShadowRoot, html: string): void {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    while (tempDiv.firstChild) {
        shadowRoot.appendChild(tempDiv.firstChild);
    }
}

/**
 * 清空 Shadow DOM 内容
 */
export function clearShadowDOM(shadowRoot: ShadowRoot): void {
    while (shadowRoot.firstChild) {
        shadowRoot.removeChild(shadowRoot.firstChild);
    }
}

/**
 * 获取 Shadow DOM 的统计信息
 */
export function getShadowDOMStats(shadowRoot: ShadowRoot) {
    const walker = document.createTreeWalker(
        shadowRoot,
        NodeFilter.SHOW_ELEMENT,
        null,
        false
    );

    let elementCount = 0;
    let textNodeCount = 0;
    let styleElementCount = 0;
    let scriptElementCount = 0;

    while (walker.nextNode()) {
        const node = walker.currentNode as Element;
        elementCount++;

        if (node.tagName === 'STYLE') {
            styleElementCount++;
        } else if (node.tagName === 'SCRIPT') {
            scriptElementCount++;
        }

        // 计算文本节点
        for (let i = 0; i < node.childNodes.length; i++) {
            if (node.childNodes[i].nodeType === Node.TEXT_NODE) {
                textNodeCount++;
            }
        }
    }

    return {
        elementCount,
        textNodeCount,
        styleElementCount,
        scriptElementCount,
        totalSize: shadowRoot.innerHTML.length
    };
}

/**
 * 监听 Shadow DOM 变化
 */
export function observeShadowDOMChanges(
    shadowRoot: ShadowRoot,
    callback: MutationCallback,
    options: MutationObserverInit = {
        childList: true,
        subtree: true,
        attributes: true,
        attributeOldValue: true,
        characterData: true,
        characterDataOldValue: true
    }
): MutationObserver {
    const observer = new MutationObserver(callback);
    observer.observe(shadowRoot, options);
    return observer;
}

/**
 * 在 Shadow DOM 中执行脚本
 */
export function executeScriptInShadowDOM(
    shadowRoot: ShadowRoot,
    scriptContent: string,
    scriptId?: string
): HTMLScriptElement {
    const script = document.createElement('script');

    if (scriptId) {
        script.setAttribute('data-script-id', scriptId);
    }

    script.textContent = scriptContent;
    shadowRoot.appendChild(script);

    return script;
}

/**
 * 在 Shadow DOM 中加载外部脚本
 */
export function loadExternalScriptInShadowDOM(
    shadowRoot: ShadowRoot,
    src: string,
    scriptId?: string
): Promise<HTMLScriptElement> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');

        if (scriptId) {
            script.setAttribute('data-script-id', scriptId);
        }

        script.src = src;
        script.onload = () => resolve(script);
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));

        shadowRoot.appendChild(script);
    });
}

/**
 * 获取 Shadow DOM 中的所有样式
 */
export function getShadowDOMStyles(shadowRoot: ShadowRoot): {
    inlineStyles: HTMLStyleElement[];
    externalStyles: HTMLLinkElement[];
    cssText: string;
} {
    const inlineStyles = Array.from(shadowRoot.querySelectorAll('style'));
    const externalStyles = Array.from(shadowRoot.querySelectorAll('link[rel="stylesheet"]'));

    const cssText = inlineStyles
        .map(style => style.textContent || '')
        .join('\n');

    return {
        inlineStyles,
        externalStyles,
        cssText
    };
}

/**
 * 设置 Shadow DOM 的 CSS 变量
 */
export function setShadowDOMCSSVariables(
    shadowRoot: ShadowRoot,
    variables: Record<string, string>
): void {
    const host = shadowRoot.host as HTMLElement;

    Object.entries(variables).forEach(([key, value]) => {
        host.style.setProperty(`--${key}`, value);
    });
}

/**
 * 获取 Shadow DOM 的 CSS 变量
 */
export function getShadowDOMCSSVariables(shadowRoot: ShadowRoot): Record<string, string> {
    const host = shadowRoot.host as HTMLElement;
    const computedStyle = getComputedStyle(host);
    const variables: Record<string, string> = {};

    // 遍历所有 CSS 属性，找出自定义属性（CSS 变量）
    for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle[i];
        if (property.startsWith('--')) {
            variables[property] = computedStyle.getPropertyValue(property);
        }
    }

    return variables;
}

/**
 * 检查 Shadow DOM 是否包含指定元素
 */
export function shadowDOMContains(shadowRoot: ShadowRoot, element: Node): boolean {
    return shadowRoot.contains(element);
}

/**
 * 获取 Shadow DOM 的深度
 */
export function getShadowDOMDepth(element: Element): number {
    let depth = 0;
    let current = element.getRootNode();

    while (current instanceof ShadowRoot) {
        depth++;
        current = current.host.getRootNode();
    }

    return depth;
}