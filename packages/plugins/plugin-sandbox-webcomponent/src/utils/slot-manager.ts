/**
 * 插槽管理器
 * 用于管理 Shadow DOM 中的插槽系统
 */

/**
 * 插槽配置
 */
export interface SlotConfig {
    /** 插槽名称 */
    name: string;
    /** 默认内容 */
    defaultContent?: string;
    /** 是否允许多个元素 */
    allowMultiple?: boolean;
    /** 插槽类型 */
    type?: 'default' | 'named' | 'scoped';
    /** 自定义属性 */
    attributes?: Record<string, string>;
}

/**
 * 插槽事件数据
 */
export interface SlotEventData {
    slotName: string;
    assignedNodes: Node[];
    previousNodes: Node[];
}

/**
 * 插槽管理器类
 */
export class SlotManager {
    private shadowRoot: ShadowRoot;
    private slots = new Map<string, HTMLSlotElement>();
    private slotObservers = new Map<string, MutationObserver>();
    private eventListeners = new Map<string, Function[]>();
    private enableLogging: boolean;

    constructor(shadowRoot: ShadowRoot, enableLogging = false) {
        this.shadowRoot = shadowRoot;
        this.enableLogging = enableLogging;
        this.initializeSlotObservation();
    }

    /**
     * 创建插槽
     */
    createSlot(config: SlotConfig): HTMLSlotElement {
        const slot = document.createElement('slot');

        // 设置插槽名称
        if (config.name && config.name !== 'default') {
            slot.name = config.name;
        }

        // 设置默认内容
        if (config.defaultContent) {
            slot.innerHTML = config.defaultContent;
        }

        // 设置自定义属性
        if (config.attributes) {
            Object.entries(config.attributes).forEach(([key, value]) => {
                slot.setAttribute(key, value);
            });
        }

        // 添加到 Shadow DOM
        this.shadowRoot.appendChild(slot);

        // 注册插槽
        this.slots.set(config.name, slot);

        // 监听插槽变化
        this.observeSlot(config.name, slot);

        this.log(`插槽 ${config.name} 创建完成`);
        return slot;
    }

    /**
     * 获取插槽
     */
    getSlot(name: string): HTMLSlotElement | undefined {
        return this.slots.get(name);
    }

    /**
     * 获取所有插槽
     */
    getAllSlots(): Map<string, HTMLSlotElement> {
        return new Map(this.slots);
    }

    /**
     * 移除插槽
     */
    removeSlot(name: string): boolean {
        const slot = this.slots.get(name);
        if (slot) {
            // 停止观察
            const observer = this.slotObservers.get(name);
            if (observer) {
                observer.disconnect();
                this.slotObservers.delete(name);
            }

            // 从 DOM 中移除
            if (slot.parentNode) {
                slot.parentNode.removeChild(slot);
            }

            // 从映射中移除
            this.slots.delete(name);

            this.log(`插槽 ${name} 移除完成`);
            return true;
        }
        return false;
    }

    /**
     * 获取插槽分配的节点
     */
    getAssignedNodes(slotName: string, options?: AssignedNodesOptions): Node[] {
        const slot = this.slots.get(slotName);
        if (slot) {
            return slot.assignedNodes(options);
        }
        return [];
    }

    /**
     * 获取插槽分配的元素
     */
    getAssignedElements(slotName: string, options?: AssignedNodesOptions): Element[] {
        const slot = this.slots.get(slotName);
        if (slot) {
            return slot.assignedElements(options);
        }
        return [];
    }

    /**
     * 设置插槽内容
     */
    setSlotContent(slotName: string, content: string | Node | Node[]): void {
        const slot = this.slots.get(slotName);
        if (!slot) {
            this.log(`插槽 ${slotName} 不存在`);
            return;
        }

        // 清空现有内容
        while (slot.firstChild) {
            slot.removeChild(slot.firstChild);
        }

        // 设置新内容
        if (typeof content === 'string') {
            slot.innerHTML = content;
        } else if (content instanceof Node) {
            slot.appendChild(content);
        } else if (Array.isArray(content)) {
            content.forEach(node => slot.appendChild(node));
        }

        this.log(`插槽 ${slotName} 内容更新完成`);
    }

    /**
     * 添加插槽内容
     */
    appendSlotContent(slotName: string, content: string | Node | Node[]): void {
        const slot = this.slots.get(slotName);
        if (!slot) {
            this.log(`插槽 ${slotName} 不存在`);
            return;
        }

        if (typeof content === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            while (tempDiv.firstChild) {
                slot.appendChild(tempDiv.firstChild);
            }
        } else if (content instanceof Node) {
            slot.appendChild(content);
        } else if (Array.isArray(content)) {
            content.forEach(node => slot.appendChild(node));
        }

        this.log(`插槽 ${slotName} 内容追加完成`);
    }

    /**
     * 清空插槽内容
     */
    clearSlotContent(slotName: string): void {
        const slot = this.slots.get(slotName);
        if (slot) {
            while (slot.firstChild) {
                slot.removeChild(slot.firstChild);
            }
            this.log(`插槽 ${slotName} 内容清空完成`);
        }
    }

    /**
     * 监听插槽事件
     */
    addEventListener(eventType: string, handler: (data: SlotEventData) => void): void {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        this.eventListeners.get(eventType)!.push(handler);
    }

    /**
     * 移除插槽事件监听器
     */
    removeEventListener(eventType: string, handler: (data: SlotEventData) => void): void {
        const handlers = this.eventListeners.get(eventType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * 触发插槽事件
     */
    private emitEvent(eventType: string, data: SlotEventData): void {
        const handlers = this.eventListeners.get(eventType);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`插槽事件处理器错误 (${eventType}):`, error);
                }
            });
        }
    }

    /**
     * 初始化插槽观察
     */
    private initializeSlotObservation(): void {
        // 监听 Shadow DOM 中插槽的变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node instanceof HTMLSlotElement) {
                            const slotName = node.name || 'default';
                            this.slots.set(slotName, node);
                            this.observeSlot(slotName, node);
                            this.log(`检测到新插槽: ${slotName}`);
                        }
                    });

                    mutation.removedNodes.forEach((node) => {
                        if (node instanceof HTMLSlotElement) {
                            const slotName = node.name || 'default';
                            this.slots.delete(slotName);
                            const slotObserver = this.slotObservers.get(slotName);
                            if (slotObserver) {
                                slotObserver.disconnect();
                                this.slotObservers.delete(slotName);
                            }
                            this.log(`插槽已移除: ${slotName}`);
                        }
                    });
                }
            });
        });

        observer.observe(this.shadowRoot, {
            childList: true,
            subtree: true
        });
    }

    /**
     * 观察单个插槽
     */
    private observeSlot(slotName: string, slot: HTMLSlotElement): void {
        // 如果已经在观察，先停止
        const existingObserver = this.slotObservers.get(slotName);
        if (existingObserver) {
            existingObserver.disconnect();
        }

        // 监听插槽内容变化
        let previousNodes = slot.assignedNodes();

        const observer = new MutationObserver(() => {
            const currentNodes = slot.assignedNodes();

            // 检查节点是否发生变化
            if (this.nodesChanged(previousNodes, currentNodes)) {
                const eventData: SlotEventData = {
                    slotName,
                    assignedNodes: currentNodes,
                    previousNodes
                };

                this.emitEvent('slotchange', eventData);
                previousNodes = currentNodes;

                this.log(`插槽 ${slotName} 内容发生变化`);
            }
        });

        // 观察宿主元素的变化
        const host = this.shadowRoot.host;
        observer.observe(host, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['slot']
        });

        this.slotObservers.set(slotName, observer);
    }

    /**
     * 检查节点是否发生变化
     */
    private nodesChanged(previous: Node[], current: Node[]): boolean {
        if (previous.length !== current.length) {
            return true;
        }

        for (let i = 0; i < previous.length; i++) {
            if (previous[i] !== current[i]) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取插槽统计信息
     */
    getSlotStats() {
        const stats = {
            totalSlots: this.slots.size,
            slots: {} as Record<string, {
                assignedNodeCount: number;
                assignedElementCount: number;
                hasDefaultContent: boolean;
            }>
        };

        for (const [name, slot] of this.slots) {
            stats.slots[name] = {
                assignedNodeCount: slot.assignedNodes().length,
                assignedElementCount: slot.assignedElements().length,
                hasDefaultContent: slot.children.length > 0
            };
        }

        return stats;
    }

    /**
     * 销毁插槽管理器
     */
    destroy(): void {
        // 停止所有观察器
        for (const observer of this.slotObservers.values()) {
            observer.disconnect();
        }
        this.slotObservers.clear();

        // 清空事件监听器
        this.eventListeners.clear();

        // 清空插槽映射
        this.slots.clear();

        this.log('插槽管理器销毁完成');
    }

    /**
     * 记录日志
     */
    private log(message: string): void {
        if (this.enableLogging) {
            console.log(`[SlotManager] ${message}`);
        }
    }
}

/**
 * 创建插槽管理器
 */
export function createSlotManager(shadowRoot: ShadowRoot, enableLogging?: boolean): SlotManager {
    return new SlotManager(shadowRoot, enableLogging);
}

/**
 * 检查浏览器是否支持插槽
 */
export function isSlotSupported(): boolean {
    return 'HTMLSlotElement' in window;
}

/**
 * 获取元素的插槽分配信息
 */
export function getElementSlotInfo(element: Element): {
    slotName: string | null;
    assignedSlot: HTMLSlotElement | null;
} {
    return {
        slotName: element.getAttribute('slot'),
        assignedSlot: element.assignedSlot
    };
}