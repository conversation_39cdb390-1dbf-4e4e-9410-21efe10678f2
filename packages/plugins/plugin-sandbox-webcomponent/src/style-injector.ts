/**
 * 样式注入器
 * 负责在 Shadow DOM 中注入和管理样式
 */
export class StyleInjector {
    private shadowRoot: ShadowRoot;
    private styleMap = new Map<string, HTMLStyleElement>();
    private cssVariables = new Map<string, string>();
    private enableLogging: boolean;

    constructor(shadowRoot: ShadowRoot, enableLogging = false) {
        this.shadowRoot = shadowRoot;
        this.enableLogging = enableLogging;
        this.initializeBaseStyles();
    }

    /**
     * 初始化基础样式
     */
    private initializeBaseStyles() {
        const baseStyle = this.createStyleElement('base-styles', `
            :host {
                display: block;
                contain: layout style paint;
                isolation: isolate;
            }
            
            * {
                box-sizing: border-box;
            }
            
            /* 重置样式，避免外部样式影响 */
            div, span, p, h1, h2, h3, h4, h5, h6 {
                margin: 0;
                padding: 0;
                border: 0;
                font-size: 100%;
                font: inherit;
                vertical-align: baseline;
            }
        `);

        this.shadowRoot.appendChild(baseStyle);
        this.log('基础样式初始化完成');
    }

    /**
     * 注入样式字符串
     */
    injectStyleString(id: string, css: string): HTMLStyleElement {
        // 如果已存在相同 ID 的样式，先移除
        if (this.styleMap.has(id)) {
            this.removeStyle(id);
        }

        const styleElement = this.createStyleElement(id, css);
        this.shadowRoot.appendChild(styleElement);
        this.styleMap.set(id, styleElement);

        this.log(`样式 ${id} 注入完成`);
        return styleElement;
    }

    /**
     * 注入外部样式表
     */
    async injectExternalStylesheet(id: string, href: string): Promise<HTMLLinkElement> {
        return new Promise((resolve, reject) => {
            // 检查是否已存在
            const existingLink = this.shadowRoot.querySelector(`link[data-style-id="${id}"]`) as HTMLLinkElement;
            if (existingLink) {
                resolve(existingLink);
                return;
            }

            const linkElement = document.createElement('link');
            linkElement.rel = 'stylesheet';
            linkElement.href = href;
            linkElement.setAttribute('data-style-id', id);

            linkElement.onload = () => {
                this.log(`外部样式表 ${href} 加载完成`);
                resolve(linkElement);
            };

            linkElement.onerror = () => {
                const error = new Error(`Failed to load stylesheet: ${href}`);
                this.log(`外部样式表 ${href} 加载失败: ${error.message}`);
                reject(error);
            };

            this.shadowRoot.appendChild(linkElement);
        });
    }

    /**
     * 注入 CSS 变量
     */
    injectCSSVariables(variables: Record<string, string>) {
        // 更新内部映射
        Object.entries(variables).forEach(([key, value]) => {
            this.cssVariables.set(key, value);
        });

        // 生成 CSS 变量样式
        const cssText = Array.from(this.cssVariables.entries())
            .map(([key, value]) => `--${key}: ${value};`)
            .join('\n');

        const variableStyle = `:host {\n${cssText}\n}`;
        this.injectStyleString('css-variables', variableStyle);

        this.log(`CSS 变量注入完成，共 ${Object.keys(variables).length} 个变量`);
    }

    /**
     * 设置单个 CSS 变量
     */
    setCSSVariable(name: string, value: string) {
        this.cssVariables.set(name, value);
        this.injectCSSVariables(Object.fromEntries(this.cssVariables));
    }

    /**
     * 获取 CSS 变量
     */
    getCSSVariable(name: string): string | undefined {
        return this.cssVariables.get(name);
    }

    /**
     * 移除样式
     */
    removeStyle(id: string): boolean {
        const styleElement = this.styleMap.get(id);
        if (styleElement && styleElement.parentNode) {
            styleElement.parentNode.removeChild(styleElement);
            this.styleMap.delete(id);
            this.log(`样式 ${id} 移除完成`);
            return true;
        }
        return false;
    }

    /**
     * 清空所有样式
     */
    clearAllStyles() {
        // 移除所有样式元素
        for (const [id, styleElement] of this.styleMap) {
            if (styleElement.parentNode) {
                styleElement.parentNode.removeChild(styleElement);
            }
        }
        this.styleMap.clear();

        // 移除所有链接元素
        const linkElements = this.shadowRoot.querySelectorAll('link[rel="stylesheet"]');
        linkElements.forEach(link => {
            if (link.parentNode) {
                link.parentNode.removeChild(link);
            }
        });

        // 清空 CSS 变量
        this.cssVariables.clear();

        this.log('所有样式清空完成');
    }

    /**
     * 处理样式隔离
     */
    processStyleIsolation(css: string): string {
        // 为所有选择器添加 :host 前缀，确保样式只在当前 Shadow DOM 中生效
        return css.replace(/([^{}]+){/g, (match, selector) => {
            // 跳过已经包含 :host 的选择器
            if (selector.includes(':host')) {
                return match;
            }

            // 跳过 @规则
            if (selector.trim().startsWith('@')) {
                return match;
            }

            // 为选择器添加 :host 前缀
            const isolatedSelector = selector
                .split(',')
                .map((s: string) => `:host ${s.trim()}`)
                .join(', ');

            return `${isolatedSelector} {`;
        });
    }

    /**
     * 创建样式元素
     */
    private createStyleElement(id: string, css: string): HTMLStyleElement {
        const styleElement = document.createElement('style');
        styleElement.setAttribute('data-style-id', id);

        // 处理样式隔离
        const isolatedCSS = this.processStyleIsolation(css);
        styleElement.textContent = isolatedCSS;

        return styleElement;
    }

    /**
     * 获取所有样式信息
     */
    getStyleInfo() {
        return {
            styleCount: this.styleMap.size,
            styles: Array.from(this.styleMap.keys()),
            cssVariables: Object.fromEntries(this.cssVariables),
            externalStylesheets: Array.from(this.shadowRoot.querySelectorAll('link[rel="stylesheet"]'))
                .map(link => ({
                    id: link.getAttribute('data-style-id'),
                    href: (link as HTMLLinkElement).href
                }))
        };
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.enableLogging) {
            console.log(`[StyleInjector] ${message}`);
        }
    }
}