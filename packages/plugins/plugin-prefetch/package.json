{"name": "@micro-core/plugin-prefetch", "version": "0.1.0", "description": "Micro-Core 智能预加载插件", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build && tsc --emitDeclarationOnly", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["micro-frontend", "prefetch", "preload", "performance", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-prefetch"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared-types": "workspace:*"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "typescript": "^5.3.3", "vite": "^5.0.0", "vitest": "^1.1.0"}, "peerDependencies": {"@micro-core/core": "^0.1.0"}}