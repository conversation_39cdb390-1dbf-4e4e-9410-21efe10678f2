/**
 * 智能预加载插件类型定义
 */

/**
 * 预加载策略类型
 */
export type PrefetchStrategy =
    | 'idle'           // 浏览器空闲时预加载
    | 'visible'        // 元素可见时预加载
    | 'hover'          // 鼠标悬停时预加载
    | 'route-predict'  // 路由预测预加载
    | 'manual';        // 手动预加载

/**
 * 预加载配置
 */
export interface PrefetchConfig {
    /** 预加载策略 */
    strategy: PrefetchStrategy[];

    /** 预加载优先级 */
    priority?: 'high' | 'low' | 'auto';

    /** 最大并发预加载数量 */
    maxConcurrent?: number;

    /** 预加载超时时间（毫秒） */
    timeout?: number;

    /** 是否启用缓存 */
    enableCache?: boolean;

    /** 缓存策略 */
    cacheStrategy?: 'memory' | 'disk' | 'both';

    /** 网络条件限制 */
    networkConditions?: {
        /** 最小网络速度（Mbps） */
        minSpeed?: number;
        /** 是否在移动网络下预加载 */
        allowMobile?: boolean;
        /** 是否在省电模式下预加载 */
        allowSaveData?: boolean;
    };

    /** 预测配置 */
    prediction?: {
        /** 预测算法 */
        algorithm?: 'ml' | 'frequency' | 'pattern';
        /** 预测阈值 */
        threshold?: number;
        /** 历史数据保留天数 */
        historyDays?: number;
    };
}

/**
 * 预加载项
 */
export interface PrefetchItem {
    /** 资源URL */
    url: string;

    /** 资源类型 */
    type: 'script' | 'style' | 'image' | 'font' | 'document';

    /** 优先级 */
    priority: number;

    /** 预加载策略 */
    strategy: PrefetchStrategy;

    /** 是否已预加载 */
    prefetched: boolean;

    /** 预加载时间 */
    prefetchedAt?: number;

    /** 预加载耗时 */
    duration?: number;

    /** 错误信息 */
    error?: string;
}

/**
 * 路由预测数据
 */
export interface RoutePrediction {
    /** 当前路由 */
    currentRoute: string;

    /** 预测的下一个路由 */
    nextRoute: string;

    /** 预测概率 */
    probability: number;

    /** 预测依据 */
    reason: 'frequency' | 'pattern' | 'ml' | 'manual';

    /** 预测时间 */
    predictedAt: number;
}

/**
 * 用户行为数据
 */
export interface UserBehavior {
    /** 路由路径 */
    route: string;

    /** 访问时间 */
    timestamp: number;

    /** 停留时间 */
    duration: number;

    /** 来源路由 */
    fromRoute?: string;

    /** 用户操作 */
    actions: UserAction[];
}

/**
 * 用户操作
 */
export interface UserAction {
    /** 操作类型 */
    type: 'click' | 'hover' | 'scroll' | 'focus';

    /** 目标元素 */
    target: string;

    /** 操作时间 */
    timestamp: number;

    /** 相关数据 */
    data?: Record<string, any>;
}

/**
 * 网络信息
 */
export interface NetworkInfo {
    /** 连接类型 */
    effectiveType: '2g' | '3g' | '4g' | 'slow-2g';

    /** 下行速度（Mbps） */
    downlink: number;

    /** 往返时间（毫秒） */
    rtt: number;

    /** 是否省流量模式 */
    saveData: boolean;
}

/**
 * 预加载事件
 */
export interface PrefetchEvent {
    /** 事件类型 */
    type: 'start' | 'success' | 'error' | 'cancel';

    /** 预加载项 */
    item: PrefetchItem;

    /** 事件时间 */
    timestamp: number;

    /** 事件数据 */
    data?: any;
}

/**
 * 预加载统计
 */
export interface PrefetchStats {
    /** 总预加载数量 */
    total: number;

    /** 成功数量 */
    success: number;

    /** 失败数量 */
    failed: number;

    /** 缓存命中数量 */
    cacheHit: number;

    /** 平均预加载时间 */
    avgDuration: number;

    /** 节省的加载时间 */
    timeSaved: number;

    /** 预测准确率 */
    predictionAccuracy: number;
}