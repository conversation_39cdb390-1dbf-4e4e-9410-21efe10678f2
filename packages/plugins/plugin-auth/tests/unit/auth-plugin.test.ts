/**
 * @fileoverview 认证插件单元测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel } from '@micro-core/core';
import { AuthPlugin } from '../../src/auth-plugin';
import type { AuthConfig, AuthUser } from '../../src/types';

// Use global mock kernel
const mockKernel = global.mockKernel as MicroCoreKernel;

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

describe('AuthPlugin', () => {
  let authPlugin: AuthPlugin;
  let mockConfig: AuthConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    sessionStorageMock.getItem.mockReturnValue(null);
    
    mockConfig = {
      tokenKey: 'test-token',
      refreshTokenKey: 'test-refresh-token',
      tokenExpireTime: 3600000, // 1 hour
      autoRefresh: true,
      loginUrl: '/login',
      storage: 'localStorage'
    };
    
    authPlugin = new AuthPlugin(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Plugin Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultPlugin = new AuthPlugin();
      expect(defaultPlugin.name).toBe('auth');
      expect(defaultPlugin.version).toBe('1.0.0');
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: Partial<AuthConfig> = {
        tokenKey: 'custom-token',
        autoRefresh: false,
      };
      const plugin = new AuthPlugin(customConfig);
      expect(plugin).toBeDefined();
    });

    it('should install plugin successfully', async () => {
      await expect(authPlugin.install(mockKernel as MicroCoreKernel)).resolves.not.toThrow();
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeMount', expect.any(Function));
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeUnmount', expect.any(Function));
    });
  });

  describe('Token Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should set and get token correctly', async () => {
      const token = 'test-jwt-token';
      const refreshToken = 'test-refresh-token';
      
      await authPlugin.setToken(token, refreshToken);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('test-token', token);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('test-refresh-token', refreshToken);
      
      const retrievedToken = authPlugin.getToken();
      expect(retrievedToken).toBe(token);
    });

    it('should handle token expiry correctly', async () => {
      const expiredToken = 'expired-token';
      const mockTokenData = {
        token: expiredToken,
        expiry: Date.now() - 1000, // Expired 1 second ago
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockTokenData));
      
      const token = authPlugin.getToken();
      expect(token).toBeNull();
    });

    it('should refresh token automatically when enabled', async () => {
      // Mock fetch for refresh token API
      const mockRefreshResponse = {
        success: true,
        data: {
          token: 'new-token',
          refreshToken: 'new-refresh-token',
          user: { id: '1', username: 'testuser', roles: [], permissions: [] }
        }
      };
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockRefreshResponse)
      });
      
      // Mock an expired token
      localStorageMock.getItem.mockReturnValue('expired-token');
      
      const result = await authPlugin.refreshToken();
      
      expect(global.fetch).toHaveBeenCalled();
      expect(result).toBe('new-token');
    });

    it('should clear tokens on logout', async () => {
      await authPlugin.logout();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-refresh-token');
    });
  });

  describe('User Authentication', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should authenticate user with valid credentials', async () => {
      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };
      
      const mockLoginFn = vi.fn().mockResolvedValue({
        user: mockUser,
        token: 'valid-token',
        refreshToken: 'valid-refresh-token',
      });
      
      authPlugin.setLoginHandler(mockLoginFn);
      
      const result = await authPlugin.login('testuser', 'password');
      
      expect(mockLoginFn).toHaveBeenCalledWith('testuser', 'password');
      expect(result.user).toEqual(mockUser);
      expect(authPlugin.getCurrentUser()).toEqual(mockUser);
    });

    it('should handle login failure gracefully', async () => {
      const mockLoginFn = vi.fn().mockRejectedValue(new Error('Invalid credentials'));
      authPlugin.setLoginHandler(mockLoginFn);
      
      await expect(authPlugin.login('invalid', 'credentials')).rejects.toThrow('Invalid credentials');
    });

    it('should check if user is authenticated', async () => {
      expect(authPlugin.isAuthenticated()).toBe(false);
      
      // Mock valid token
      const validTokenData = {
        token: 'valid-token',
        expiry: Date.now() + 3600000, // Valid for 1 hour
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(validTokenData));
      
      expect(authPlugin.isAuthenticated()).toBe(true);
    });
  });

  describe('Permission Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
      
      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['admin', 'user'],
        permissions: ['read', 'write', 'delete'],
      };
      
      await authPlugin.setCurrentUser(mockUser);
    });

    it('should check user permissions correctly', () => {
      expect(authPlugin.hasPermission('read')).toBe(true);
      expect(authPlugin.hasPermission('write')).toBe(true);
      expect(authPlugin.hasPermission('admin')).toBe(false);
    });

    it('should check user roles correctly', () => {
      expect(authPlugin.hasRole('admin')).toBe(true);
      expect(authPlugin.hasRole('user')).toBe(true);
      expect(authPlugin.hasRole('superadmin')).toBe(false);
    });

    it('should check multiple permissions with AND logic', () => {
      expect(authPlugin.hasPermissions(['read', 'write'])).toBe(true);
      expect(authPlugin.hasPermissions(['read', 'admin'])).toBe(false);
    });

    it('should check multiple roles with AND logic', () => {
      expect(authPlugin.hasRoles(['admin', 'user'])).toBe(true);
      expect(authPlugin.hasRoles(['admin', 'superadmin'])).toBe(false);
    });
  });

  describe('Route Guard Integration', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should allow access to public routes', async () => {
      const mockRoute = { path: '/public', meta: { requiresAuth: false } };
      const canAccess = await authPlugin.canAccessRoute(mockRoute);
      expect(canAccess).toBe(true);
    });

    it('should block access to protected routes when not authenticated', async () => {
      const mockRoute = { path: '/protected', meta: { requiresAuth: true } };
      const canAccess = await authPlugin.canAccessRoute(mockRoute);
      expect(canAccess).toBe(false);
    });

    it('should allow access to protected routes when authenticated', async () => {
      // Mock authenticated state
      const validTokenData = {
        token: 'valid-token',
        expiry: Date.now() + 3600000,
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(validTokenData));
      
      const mockRoute = { path: '/protected', meta: { requiresAuth: true } };
      const canAccess = await authPlugin.canAccessRoute(mockRoute);
      expect(canAccess).toBe(true);
    });

    it('should check role-based access control', async () => {
      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };
      
      await authPlugin.setCurrentUser(mockUser);
      
      const adminRoute = { path: '/admin', meta: { requiresAuth: true, requiredRoles: ['admin'] } };
      const userRoute = { path: '/user', meta: { requiresAuth: true, requiredRoles: ['user'] } };
      
      expect(await authPlugin.canAccessRoute(adminRoute)).toBe(false);
      expect(await authPlugin.canAccessRoute(userRoute)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle storage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      
      expect(() => authPlugin.getToken()).not.toThrow();
      expect(authPlugin.getToken()).toBeNull();
    });

    it('should handle invalid token format gracefully', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json');
      
      expect(() => authPlugin.getToken()).not.toThrow();
      expect(authPlugin.getToken()).toBeNull();
    });

    it('should handle network errors during token refresh', async () => {
      const mockRefreshFn = vi.fn().mockRejectedValue(new Error('Network error'));
      authPlugin.setRefreshTokenHandler(mockRefreshFn);
      
      await expect(authPlugin.refreshToken()).rejects.toThrow('Network error');
    });
  });

  describe('Plugin Lifecycle', () => {
    it('should uninstall plugin cleanly', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
      await authPlugin.uninstall();
      
      expect(mockKernel.off).toHaveBeenCalledWith('app:beforeMount', expect.any(Function));
      expect(mockKernel.off).toHaveBeenCalledWith('app:beforeUnmount', expect.any(Function));
    });

    it('should handle multiple install/uninstall cycles', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);
      await authPlugin.uninstall();
      await authPlugin.install(mockKernel as MicroCoreKernel);
      
      expect(mockKernel.on).toHaveBeenCalledTimes(4); // 2 events × 2 installs
    });
  });

  describe('Configuration Edge Cases', () => {
    it('should handle sessionStorage configuration', () => {
      const sessionConfig: AuthConfig = {
        ...mockConfig,
        storage: 'sessionStorage',
      };
      
      const plugin = new AuthPlugin(sessionConfig);
      expect(plugin).toBeDefined();
    });

    it('should handle disabled auto-refresh', () => {
      const noRefreshConfig: AuthConfig = {
        ...mockConfig,
        autoRefresh: false,
      };
      
      const plugin = new AuthPlugin(noRefreshConfig);
      expect(plugin).toBeDefined();
    });

    it('should handle disabled permission checks', () => {
      const noPermissionConfig: AuthConfig = {
        ...mockConfig,
        enablePermissionCheck: false,
        enableRoleCheck: false,
      };
      
      const plugin = new AuthPlugin(noPermissionConfig);
      expect(plugin).toBeDefined();
    });
  });
});
