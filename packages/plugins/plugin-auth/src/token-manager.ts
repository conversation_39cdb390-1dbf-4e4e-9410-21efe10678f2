/**
 * 令牌管理器
 */

import type { TokenConfig } from './types';

/**
 * 令牌管理器类
 */
export class TokenManager {
    private config: TokenConfig;
    private memoryStorage: Map<string, string> = new Map();

    constructor(config: TokenConfig) {
        this.config = config;
    }

    /**
     * 设置令牌
     */
    setToken(token: string, expiresIn?: number): void {
        const key = this.getStorageKey();
        const data = {
            token,
            expiresAt: expiresIn ? Date.now() + expiresIn * 1000 : undefined
        };

        this.setItem(key, JSON.stringify(data));
    }

    /**
     * 获取令牌
     */
    getToken(): string | null {
        const key = this.getStorageKey();
        const data = this.getItem(key);

        if (!data) return null;

        try {
            const parsed = JSON.parse(data);

            // 检查是否过期
            if (parsed.expiresAt && Date.now() > parsed.expiresAt) {
                this.clearToken();
                return null;
            }

            return parsed.token;
        } catch {
            return null;
        }
    }

    /**
     * 设置刷新令牌
     */
    setRefreshToken(refreshToken: string): void {
        const key = this.getRefreshTokenKey();
        this.setItem(key, refreshToken);
    }

    /**
     * 获取刷新令牌
     */
    getRefreshToken(): string | null {
        const key = this.getRefreshTokenKey();
        return this.getItem(key);
    }

    /**
     * 清除令牌
     */
    clearToken(): void {
        const key = this.getStorageKey();
        this.removeItem(key);
    }

    /**
     * 清除刷新令牌
     */
    clearRefreshToken(): void {
        const key = this.getRefreshTokenKey();
        this.removeItem(key);
    }

    /**
     * 清除所有令牌
     */
    clearTokens(): void {
        this.clearToken();
        this.clearRefreshToken();
    }

    /**
     * 检查令牌是否过期
     */
    isExpired(): boolean {
        const key = this.getStorageKey();
        const data = this.getItem(key);

        if (!data) return true;

        try {
            const parsed = JSON.parse(data);
            return parsed.expiresAt ? Date.now() > parsed.expiresAt : false;
        } catch {
            return true;
        }
    }

    /**
     * 获取令牌剩余时间（秒）
     */
    getTimeToExpire(): number {
        const key = this.getStorageKey();
        const data = this.getItem(key);

        if (!data) return 0;

        try {
            const parsed = JSON.parse(data);
            if (!parsed.expiresAt) return Infinity;

            const remaining = parsed.expiresAt - Date.now();
            return Math.max(0, Math.floor(remaining / 1000));
        } catch {
            return 0;
        }
    }

    /**
     * 刷新令牌
     */
    async refreshToken(): Promise<string | null> {
        if (!this.config.autoRefresh) {
            throw new Error('自动刷新未启用');
        }

        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            throw new Error('刷新令牌不存在');
        }

        try {
            // 这里应该调用后端API刷新令牌
            // 暂时返回模拟数据
            const newToken = 'new-mock-token';
            const expiresIn = this.config.expireTime || 7200;

            this.setToken(newToken, expiresIn);
            return newToken;
        } catch (error) {
            this.clearToken();
            this.clearRefreshToken();
            throw error;
        }
    }

    /**
     * 获取存储键
     */
    private getStorageKey(): string {
        const prefix = this.config.prefix || '';
        return `${prefix}${this.config.key}`;
    }

    /**
     * 获取刷新令牌存储键
     */
    private getRefreshTokenKey(): string {
        const prefix = this.config.prefix || '';
        return `${prefix}${this.config.key}_refresh`;
    }

    /**
     * 设置存储项
     */
    private setItem(key: string, value: string): void {
        switch (this.config.storage) {
            case 'localStorage':
                localStorage.setItem(key, value);
                break;
            case 'sessionStorage':
                sessionStorage.setItem(key, value);
                break;
            case 'memory':
                this.memoryStorage.set(key, value);
                break;
        }
    }

    /**
     * 获取存储项
     */
    private getItem(key: string): string | null {
        switch (this.config.storage) {
            case 'localStorage':
                return localStorage.getItem(key);
            case 'sessionStorage':
                return sessionStorage.getItem(key);
            case 'memory':
                return this.memoryStorage.get(key) || null;
            default:
                return null;
        }
    }

    /**
     * 移除存储项
     */
    private removeItem(key: string): void {
        switch (this.config.storage) {
            case 'localStorage':
                localStorage.removeItem(key);
                break;
            case 'sessionStorage':
                sessionStorage.removeItem(key);
                break;
            case 'memory':
                this.memoryStorage.delete(key);
                break;
        }
    }
}