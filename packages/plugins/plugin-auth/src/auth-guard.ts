/**
 * 认证路由守卫
 */

import type { AuthGuardConfig, AuthGuardContext, AuthToken, AuthUser } from './types';

/**
 * 认证路由守卫类
 */
export class AuthGuard {
    private guards: AuthGuardConfig[];

    constructor(guards: AuthGuardConfig[] = []) {
        this.guards = guards;
    }

    /**
     * 添加守卫规则
     */
    addGuard(guard: AuthGuardConfig): void {
        this.guards.push(guard);
    }

    /**
     * 移除守卫规则
     */
    removeGuard(path: string | RegExp): void {
        this.guards = this.guards.filter(guard => guard.path !== path);
    }

    /**
     * 检查路由访问权限
     */
    async check(path: string, user?: AuthUser, token?: AuthToken): Promise<boolean> {
        const matchedGuards = this.findMatchingGuards(path);

        if (matchedGuards.length === 0) {
            // 没有匹配的守卫规则，允许访问
            return true;
        }

        for (const guard of matchedGuards) {
            const context: AuthGuardContext = {
                path,
                user,
                token,
                config: guard
            };

            const allowed = await this.checkGuard(context);
            if (!allowed) {
                // 处理未授权访问
                await this.handleUnauthorized(context);
                return false;
            }
        }

        return true;
    }

    /**
     * 查找匹配的守卫规则
     */
    private findMatchingGuards(path: string): AuthGuardConfig[] {
        return this.guards.filter(guard => {
            if (typeof guard.path === 'string') {
                return this.matchPath(guard.path, path);
            } else {
                return guard.path.test(path);
            }
        });
    }

    /**
     * 检查单个守卫规则
     */
    private async checkGuard(context: AuthGuardContext): Promise<boolean> {
        const { config, user, token } = context;

        // 检查是否需要认证
        if (config.requireAuth !== false) {
            if (!user || !token) {
                return false;
            }
        }

        // 检查角色要求
        if (config.requiredRoles && config.requiredRoles.length > 0) {
            if (!user || !this.hasAnyRole(user, config.requiredRoles)) {
                return false;
            }
        }

        // 检查权限要求
        if (config.requiredPermissions && config.requiredPermissions.length > 0) {
            if (!user || !this.hasAnyPermission(user, config.requiredPermissions)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 处理未授权访问
     */
    private async handleUnauthorized(context: AuthGuardContext): Promise<void> {
        const { config } = context;

        // 执行自定义未授权处理函数
        if (config.onUnauthorized) {
            try {
                await config.onUnauthorized(context);
            } catch (error) {
                console.error('[AuthGuard] 自定义未授权处理函数执行失败:', error);
            }
        }

        // 重定向到指定页面
        if (config.redirectTo) {
            this.redirect(config.redirectTo);
        } else {
            // 默认重定向到登录页面
            this.redirect('/login');
        }
    }

    /**
     * 检查用户是否有任一指定角色
     */
    private hasAnyRole(user: AuthUser, roles: string[]): boolean {
        return user.roles.some(userRole =>
            roles.includes(userRole.code) || roles.includes(userRole.name)
        );
    }

    /**
     * 检查用户是否有任一指定权限
     */
    private hasAnyPermission(user: AuthUser, permissions: string[]): boolean {
        return user.permissions.some(userPermission =>
            permissions.includes(userPermission.code) || permissions.includes(userPermission.name)
        );
    }

    /**
     * 路径匹配
     */
    private matchPath(pattern: string, path: string): boolean {
        // 简单的路径匹配，支持通配符
        if (pattern === '*') return true;
        if (pattern === path) return true;

        // 支持通配符匹配
        if (pattern.includes('*')) {
            const regex = new RegExp(
                '^' + pattern.replace(/\*/g, '.*') + '$'
            );
            return regex.test(path);
        }

        // 支持路径前缀匹配
        if (pattern.endsWith('/*')) {
            const prefix = pattern.slice(0, -2);
            return path.startsWith(prefix);
        }

        return false;
    }

    /**
     * 重定向到指定路径
     */
    private redirect(path: string): void {
        if (typeof window !== 'undefined') {
            window.location.href = path;
        }
    }
}