/**
 * @fileoverview 认证插件 - 提供统一的身份认证和权限管理
 * <AUTHOR> <<EMAIL>>
 */

import type { AppInfo, MicroCoreKernel, Plugin } from '@micro-core/core';
import { AuthGuard } from './auth-guard';
import { PermissionChecker } from './permission-checker';
import { TokenManager } from './token-manager';
import type { AuthConfig, AuthUser, Permission, Role } from './types';

/**
 * 认证插件
 * 提供统一的身份认证和权限管理功能
 */
export class AuthPlugin implements Plugin {
    public readonly name = 'auth';
    public readonly version = '1.0.0';

    private kernel?: MicroCoreKernel;
    private tokenManager?: TokenManager;
    private authGuard?: AuthGuard;
    private permissionChecker?: PermissionChecker;
    private options: AuthConfig;
    private currentUser?: AuthUser;

    constructor(options: AuthConfig = {}) {
        this.options = {
            tokenKey: 'micro-core-token',
            refreshTokenKey: 'micro-core-refresh-token',
            tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
            refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000, // 7天
            autoRefresh: true,
            enablePermissionCheck: true,
            enableRoleCheck: true,
            loginUrl: '/login',
            unauthorizedUrl: '/unauthorized',
            storage: 'localStorage',
            ...options
        };
    }

    /**
     * 安装插件
     */
    async install(kernel: MicroCoreKernel): Promise<void> {
        this.kernel = kernel;

        // 创建令牌管理器
        this.tokenManager = new TokenManager(this.options);

        // 创建认证守卫
        this.authGuard = new AuthGuard(this.options, this.tokenManager);

        // 创建权限检查器
        this.permissionChecker = new PermissionChecker(this.options);

        // 注册生命周期钩子
        this.registerHooks();

        // 设置全局认证接口
        this.setupGlobalAuthInterface();

        // 初始化认证状态
        await this.initializeAuth();

        console.log('[AuthPlugin] 认证插件已安装');
    }

    /**
     * 卸载插件
     */
    async uninstall(): Promise<void> {
        // 清理认证状态
        this.logout();

        // 清理全局接口
        this.cleanupGlobalAuthInterface();

        this.tokenManager = undefined;
        this.authGuard = undefined;
        this.permissionChecker = undefined;
        this.currentUser = undefined;
        this.kernel = undefined;

        console.log('[AuthPlugin] 认证插件已卸载');
    }

    /**
     * 用户登录
     */
    async login(credentials: {
        username?: string;
        email?: string;
        password: string;
        [key: string]: any;
    }): Promise<AuthUser> {
        try {
            // 调用登录API
            const response = await this.callAuthAPI('login', credentials);

            if (response.success) {
                const { user, token, refreshToken } = response.data;

                // 保存令牌
                if (token) {
                    this.tokenManager?.setToken(token);
                }
                if (refreshToken) {
                    this.tokenManager?.setRefreshToken(refreshToken);
                }

                // 设置当前用户
                this.currentUser = user;

                // 发射登录成功事件
                this.kernel?.getEventBus().emit('auth:login:success', { user });

                console.log('[AuthPlugin] 用户登录成功:', user.username || user.email);
                return user;
            } else {
                throw new Error(response.message || '登录失败');
            }
        } catch (error) {
            console.error('[AuthPlugin] 登录失败:', error);

            // 发射登录失败事件
            this.kernel?.getEventBus().emit('auth:login:error', { error });

            throw error;
        }
    }

    /**
     * 用户登出
     */
    async logout(): Promise<void> {
        try {
            // 调用登出API
            if (this.currentUser) {
                await this.callAuthAPI('logout', {});
            }

            // 清理令牌
            this.tokenManager?.clearTokens();

            // 清理用户信息
            const previousUser = this.currentUser;
            this.currentUser = undefined;

            // 发射登出事件
            this.kernel?.getEventBus().emit('auth:logout', { user: previousUser });

            console.log('[AuthPlugin] 用户已登出');
        } catch (error) {
            console.error('[AuthPlugin] 登出失败:', error);

            // 即使登出API失败，也要清理本地状态
            this.tokenManager?.clearTokens();
            this.currentUser = undefined;
        }
    }

    /**
     * 刷新令牌
     */
    async refreshToken(): Promise<string | null> {
        if (!this.tokenManager) {
            return null;
        }

        try {
            const refreshToken = this.tokenManager.getRefreshToken();
            if (!refreshToken) {
                throw new Error('刷新令牌不存在');
            }

            const response = await this.callAuthAPI('refresh', { refreshToken });

            if (response.success) {
                const { token, refreshToken: newRefreshToken } = response.data;

                this.tokenManager.setToken(token);
                if (newRefreshToken) {
                    this.tokenManager.setRefreshToken(newRefreshToken);
                }

                console.log('[AuthPlugin] 令牌刷新成功');
                return token;
            } else {
                throw new Error(response.message || '令牌刷新失败');
            }
        } catch (error) {
            console.error('[AuthPlugin] 令牌刷新失败:', error);

            // 刷新失败，清理令牌并跳转到登录页
            this.tokenManager.clearTokens();
            this.currentUser = undefined;

            // 发射令牌过期事件
            this.kernel?.getEventBus().emit('auth:token:expired', { error });

            return null;
        }
    }

    /**
     * 获取当前用户
     */
    getCurrentUser(): AuthUser | undefined {
        return this.currentUser;
    }

    /**
     * 检查是否已认证
     */
    isAuthenticated(): boolean {
        return !!this.currentUser && !!this.tokenManager?.getToken();
    }

    /**
     * 检查用户权限
     */
    hasPermission(permission: string | Permission): boolean {
        if (!this.currentUser || !this.permissionChecker) {
            return false;
        }

        return this.permissionChecker.hasPermission(this.currentUser, permission);
    }

    /**
     * 检查用户角色
     */
    hasRole(role: string | Role): boolean {
        if (!this.currentUser || !this.permissionChecker) {
            return false;
        }

        return this.permissionChecker.hasRole(this.currentUser, role);
    }

    /**
     * 检查多个权限（AND关系）
     */
    hasAllPermissions(permissions: (string | Permission)[]): boolean {
        return permissions.every(permission => this.hasPermission(permission));
    }

    /**
     * 检查多个权限（OR关系）
     */
    hasAnyPermission(permissions: (string | Permission)[]): boolean {
        return permissions.some(permission => this.hasPermission(permission));
    }

    /**
     * 检查多个角色（AND关系）
     */
    hasAllRoles(roles: (string | Role)[]): boolean {
        return roles.every(role => this.hasRole(role));
    }

    /**
     * 检查多个角色（OR关系）
     */
    hasAnyRole(roles: (string | Role)[]): boolean {
        return roles.some(role => this.hasRole(role));
    }

    /**
     * 获取用户权限列表
     */
    getUserPermissions(): Permission[] {
        if (!this.currentUser || !this.permissionChecker) {
            return [];
        }

        return this.permissionChecker.getUserPermissions(this.currentUser);
    }

    /**
     * 获取用户角色列表
     */
    getUserRoles(): Role[] {
        if (!this.currentUser || !this.permissionChecker) {
            return [];
        }

        return this.permissionChecker.getUserRoles(this.currentUser);
    }

    /**
     * 获取令牌
     */
    getToken(): string | null {
        return this.tokenManager?.getToken() || null;
    }

    /**
     * 获取刷新令牌
     */
    getRefreshToken(): string | null {
        return this.tokenManager?.getRefreshToken() || null;
    }

    /**
     * 注册生命周期钩子
     */
    private registerHooks(): void {
        if (!this.kernel || !this.authGuard) {
            return;
        }

        // 应用挂载前检查认证
        this.kernel.registerHook('beforeAppMount', async (appInfo: AppInfo) => {
            if (this.authGuard && !await this.authGuard.canActivateApp(appInfo, this.currentUser)) {
                throw new Error(`应用 ${appInfo.name} 需要认证或权限不足`);
            }
        });

        // 路由变化时检查认证
        this.kernel.getEventBus().on('router:change', async (event) => {
            if (this.authGuard && !await this.authGuard.canActivateRoute(event.to, this.currentUser)) {
                console.warn(`[AuthPlugin] 路由 ${event.to} 需要认证或权限不足`);

                // 重定向到登录页或未授权页
                if (!this.isAuthenticated()) {
                    window.location.href = this.options.loginUrl || '/login';
                } else {
                    window.location.href = this.options.unauthorizedUrl || '/unauthorized';
                }
            }
        });
    }

    /**
     * 设置全局认证接口
     */
    private setupGlobalAuthInterface(): void {
        const globalObj = (window as any);

        if (!globalObj.__MICRO_CORE_AUTH__) {
            globalObj.__MICRO_CORE_AUTH__ = {
                login: this.login.bind(this),
                logout: this.logout.bind(this),
                refreshToken: this.refreshToken.bind(this),
                getCurrentUser: this.getCurrentUser.bind(this),
                isAuthenticated: this.isAuthenticated.bind(this),
                hasPermission: this.hasPermission.bind(this),
                hasRole: this.hasRole.bind(this),
                hasAllPermissions: this.hasAllPermissions.bind(this),
                hasAnyPermission: this.hasAnyPermission.bind(this),
                hasAllRoles: this.hasAllRoles.bind(this),
                hasAnyRole: this.hasAnyRole.bind(this),
                getUserPermissions: this.getUserPermissions.bind(this),
                getUserRoles: this.getUserRoles.bind(this),
                getToken: this.getToken.bind(this),
                getRefreshToken: this.getRefreshToken.bind(this)
            };
        }
    }

    /**
     * 清理全局认证接口
     */
    private cleanupGlobalAuthInterface(): void {
        const globalObj = (window as any);
        if (globalObj.__MICRO_CORE_AUTH__) {
            delete globalObj.__MICRO_CORE_AUTH__;
        }
    }

    /**
     * 初始化认证状态
     */
    private async initializeAuth(): Promise<void> {
        if (!this.tokenManager) {
            return;
        }

        const token = this.tokenManager.getToken();
        if (!token) {
            console.log('[AuthPlugin] 未找到有效令牌');
            return;
        }

        try {
            // 验证令牌并获取用户信息
            const response = await this.callAuthAPI('verify', { token });

            if (response.success) {
                this.currentUser = response.data.user;
                console.log('[AuthPlugin] 认证状态初始化成功:', this.currentUser.username || this.currentUser.email);

                // 发射认证初始化成功事件
                this.kernel?.getEventBus().emit('auth:initialized', { user: this.currentUser });
            } else {
                // 令牌无效，尝试刷新
                if (this.options.autoRefresh) {
                    await this.refreshToken();
                } else {
                    this.tokenManager.clearTokens();
                }
            }
        } catch (error) {
            console.error('[AuthPlugin] 认证状态初始化失败:', error);

            // 尝试刷新令牌
            if (this.options.autoRefresh) {
                await this.refreshToken();
            } else {
                this.tokenManager.clearTokens();
            }
        }
    }

    /**
     * 调用认证API
     */
    private async callAuthAPI(endpoint: string, data: any): Promise<any> {
        const apiUrl = this.options.apiUrl || '/api/auth';
        const url = `${apiUrl}/${endpoint}`;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(this.tokenManager?.getToken() && {
                    'Authorization': `Bearer ${this.tokenManager.getToken()}`
                })
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }
}

/**
 * 创建认证插件实例
 */
export function createAuthPlugin(options?: AuthConfig): AuthPlugin {
    return new AuthPlugin(options);
}