/**
 * 权限检查器
 */

import type { AuthUser, Permission, Role } from './types';

/**
 * 权限检查器类
 */
export class PermissionChecker {
    /**
     * 检查用户是否有指定权限
     */
    hasPermission(user: AuthUser, permission: string): boolean {
        if (!user || !user.permissions) return false;

        return user.permissions.some(p =>
            p.code === permission || p.name === permission
        );
    }

    /**
     * 检查用户是否有任一指定权限
     */
    hasAnyPermission(user: AuthUser, permissions: string[]): boolean {
        if (!user || !user.permissions || permissions.length === 0) return false;

        return permissions.some(permission => this.hasPermission(user, permission));
    }

    /**
     * 检查用户是否有所有指定权限
     */
    hasAllPermissions(user: AuthUser, permissions: string[]): boolean {
        if (!user || !user.permissions || permissions.length === 0) return false;

        return permissions.every(permission => this.hasPermission(user, permission));
    }

    /**
     * 检查用户是否有指定角色
     */
    hasRole(user: AuthUser, role: string): boolean {
        if (!user || !user.roles) return false;

        return user.roles.some(r =>
            r.code === role || r.name === role
        );
    }

    /**
     * 检查用户是否有任一指定角色
     */
    hasAnyRole(user: AuthUser, roles: string[]): boolean {
        if (!user || !user.roles || roles.length === 0) return false;

        return roles.some(role => this.hasRole(user, role));
    }

    /**
     * 检查用户是否有所有指定角色
     */
    hasAllRoles(user: AuthUser, roles: string[]): boolean {
        if (!user || !user.roles || roles.length === 0) return false;

        return roles.every(role => this.hasRole(user, role));
    }

    /**
     * 检查用户是否是超级管理员
     */
    isSuperAdmin(user: AuthUser): boolean {
        if (!user) return false;

        // 检查是否有超级管理员角色
        const superAdminRoles = ['super_admin', 'superadmin', 'admin', '超级管理员'];
        if (this.hasAnyRole(user, superAdminRoles)) return true;

        // 检查是否有超级管理员权限
        const superAdminPermissions = ['*', 'all', 'super_admin', '全部权限'];
        if (this.hasAnyPermission(user, superAdminPermissions)) return true;

        return false;
    }

    /**
     * 获取用户的所有权限代码
     */
    getUserPermissions(user: AuthUser): string[] {
        if (!user || !user.permissions) return [];

        return user.permissions.map(p => p.code);
    }

    /**
     * 获取用户的所有角色代码
     */
    getUserRoles(user: AuthUser): string[] {
        if (!user || !user.roles) return [];

        return user.roles.map(r => r.code);
    }

    /**
     * 检查权限是否过期
     */
    isPermissionExpired(permission: Permission): boolean {
        if (!permission.expiresAt) return false;

        return new Date(permission.expiresAt) < new Date();
    }

    /**
     * 检查角色是否过期
     */
    isRoleExpired(role: Role): boolean {
        if (!role.expiresAt) return false;

        return new Date(role.expiresAt) < new Date();
    }

    /**
     * 获取用户的有效权限（排除过期权限）
     */
    getValidPermissions(user: AuthUser): Permission[] {
        if (!user || !user.permissions) return [];

        return user.permissions.filter(permission =>
            !this.isPermissionExpired(permission)
        );
    }

    /**
     * 获取用户的有效角色（排除过期角色）
     */
    getValidRoles(user: AuthUser): Role[] {
        if (!user || !user.roles) return [];

        return user.roles.filter(role =>
            !this.isRoleExpired(role)
        );
    }

    /**
     * 检查用户权限是否在指定时间范围内有效
     */
    isPermissionValidAt(permission: Permission, date: Date = new Date()): boolean {
        // 检查生效时间
        if (permission.effectiveAt && new Date(permission.effectiveAt) > date) {
            return false;
        }

        // 检查过期时间
        if (permission.expiresAt && new Date(permission.expiresAt) < date) {
            return false;
        }

        return true;
    }

    /**
     * 检查用户角色是否在指定时间范围内有效
     */
    isRoleValidAt(role: Role, date: Date = new Date()): boolean {
        // 检查生效时间
        if (role.effectiveAt && new Date(role.effectiveAt) > date) {
            return false;
        }

        // 检查过期时间
        if (role.expiresAt && new Date(role.expiresAt) < date) {
            return false;
        }

        return true;
    }

    /**
     * 获取权限层级关系
     */
    getPermissionHierarchy(permissions: Permission[]): Map<string, Permission[]> {
        const hierarchy = new Map<string, Permission[]>();

        permissions.forEach(permission => {
            if (permission.parentCode) {
                if (!hierarchy.has(permission.parentCode)) {
                    hierarchy.set(permission.parentCode, []);
                }
                hierarchy.get(permission.parentCode)!.push(permission);
            }
        });

        return hierarchy;
    }

    /**
     * 检查用户是否有权限访问资源
     */
    canAccessResource(user: AuthUser, resource: string, action: string = 'read'): boolean {
        if (!user) return false;

        // 超级管理员拥有所有权限
        if (this.isSuperAdmin(user)) return true;

        // 检查具体的资源权限
        const resourcePermission = `${resource}:${action}`;
        if (this.hasPermission(user, resourcePermission)) return true;

        // 检查通配符权限
        const wildcardPermission = `${resource}:*`;
        if (this.hasPermission(user, wildcardPermission)) return true;

        // 检查全局权限
        if (this.hasPermission(user, '*')) return true;

        return false;
    }
}