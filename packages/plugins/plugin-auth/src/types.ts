/**
 * 权限管理插件类型定义
 */

/**
 * 权限配置接口
 */
export interface AuthConfig {
    tokenKey?: string;
    refreshTokenKey?: string;
    tokenExpireTime?: number;
    autoRefresh?: boolean;
    loginUrl?: string;
    logoutUrl?: string;
    refreshUrl?: string;
    storage?: 'localStorage' | 'sessionStorage' | 'memory';
    guards?: AuthGuardConfig[];
}

/**
 * 用户信息接口
 */
export interface AuthUser {
    id: string | number;
    username: string;
    email?: string;
    avatar?: string;
    roles: Role[];
    permissions: Permission[];
    profile?: Record<string, any>;
}

/**
 * 认证令牌接口
 */
export interface AuthToken {
    accessToken: string;
    refreshToken?: string;
    tokenType?: string;
    expiresIn?: number;
    expiresAt?: number;
    scope?: string;
}

/**
 * 权限接口
 */
export interface Permission {
    id: string | number;
    name: string;
    code: string;
    description?: string;
    resource?: string;
    action?: string;
    effect?: 'allow' | 'deny';
}

/**
 * 角色接口
 */
export interface Role {
    id: string | number;
    name: string;
    code: string;
    description?: string;
    permissions: Permission[];
    level?: number;
}

/**
 * 路由守卫配置接口
 */
export interface AuthGuardConfig {
    path: string | RegExp;
    requireAuth?: boolean;
    requiredRoles?: string[];
    requiredPermissions?: string[];
    redirectTo?: string;
    onUnauthorized?: (context: AuthGuardContext) => void;
}

/**
 * 路由守卫上下文接口
 */
export interface AuthGuardContext {
    path: string;
    user?: AuthUser;
    token?: AuthToken;
    config: AuthGuardConfig;
}

/**
 * 令牌配置接口
 */
export interface TokenConfig {
    key: string;
    storage: 'localStorage' | 'sessionStorage' | 'memory';
    prefix?: string;
    expireTime?: number;
    autoRefresh?: boolean;
}

/**
 * 认证状态接口
 */
export interface AuthState {
    isAuthenticated: boolean;
    user?: AuthUser;
    token?: AuthToken;
    loading: boolean;
    error?: Error;
}

/**
 * 认证事件类型
 */
export type AuthEventType =
    | 'login'
    | 'logout'
    | 'token-refresh'
    | 'token-expire'
    | 'permission-check'
    | 'role-check'
    | 'auth-error';

/**
 * 认证事件接口
 */
export interface AuthEvent {
    type: AuthEventType;
    data?: any;
    timestamp: number;
    user?: AuthUser;
    error?: Error;
}

/**
 * 认证监听器类型
 */
export type AuthListener = (event: AuthEvent) => void;