# @micro-core/plugin-auth

统一的身份认证和权限管理插件，为微前端应用提供完整的认证解决方案。

## 功能特性

- 🔐 **统一认证**: 提供跨应用的统一身份认证机制
- 🛡️ **权限控制**: 基于角色和权限的访问控制系统
- 🔄 **自动刷新**: 支持 Token 自动刷新和续期
- 🚪 **路由守卫**: 集成路由级别的权限验证
- 💾 **持久化存储**: 支持 localStorage 和 sessionStorage
- 🔒 **安全性**: 内置安全最佳实践和防护机制

## 安装

```bash
pnpm add @micro-core/plugin-auth
```

## 基础用法

### 插件注册

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { AuthPlugin } from '@micro-core/plugin-auth';

const kernel = new MicroCoreKernel();

// 注册认证插件
await kernel.installPlugin(new AuthPlugin({
  tokenKey: 'micro-core-token',
  refreshTokenKey: 'micro-core-refresh-token',
  tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
  autoRefresh: true,
  enablePermissionCheck: true,
  loginUrl: '/login',
  storage: 'localStorage'
}));
```

### 用户登录

```typescript
// 设置登录处理器
authPlugin.setLoginHandler(async (username, password) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });
  
  if (!response.ok) {
    throw new Error('登录失败');
  }
  
  return response.json();
});

// 执行登录
try {
  const result = await authPlugin.login('username', 'password');
  console.log('登录成功:', result.user);
} catch (error) {
  console.error('登录失败:', error.message);
}
```

### 权限检查

```typescript
// 检查用户权限
if (authPlugin.hasPermission('read')) {
  console.log('用户有读取权限');
}

// 检查用户角色
if (authPlugin.hasRole('admin')) {
  console.log('用户是管理员');
}

// 检查多个权限
if (authPlugin.hasPermissions(['read', 'write'])) {
  console.log('用户有读写权限');
}
```

### 路由守卫

```typescript
// 在路由配置中使用
const routes = [
  {
    path: '/admin',
    component: AdminComponent,
    meta: {
      requiresAuth: true,
      requiredRoles: ['admin'],
      requiredPermissions: ['admin:read']
    }
  }
];

// 路由守卫会自动检查权限
router.beforeEach(async (to, from, next) => {
  if (to.meta.requiresAuth && !authPlugin.isAuthenticated()) {
    next('/login');
    return;
  }
  
  if (to.meta.requiredRoles && !authPlugin.hasRoles(to.meta.requiredRoles)) {
    next('/unauthorized');
    return;
  }
  
  next();
});
```

## 配置选项

```typescript
interface AuthConfig {
  // Token 配置
  tokenKey?: string;                    // Token 存储键名
  refreshTokenKey?: string;             // 刷新 Token 存储键名
  tokenExpiry?: number;                 // Token 过期时间（毫秒）
  refreshTokenExpiry?: number;          // 刷新 Token 过期时间
  autoRefresh?: boolean;                // 是否自动刷新 Token
  
  // 权限配置
  enablePermissionCheck?: boolean;      // 是否启用权限检查
  enableRoleCheck?: boolean;            // 是否启用角色检查
  
  // 路由配置
  loginUrl?: string;                    // 登录页面 URL
  unauthorizedUrl?: string;             // 未授权页面 URL
  
  // 存储配置
  storage?: 'localStorage' | 'sessionStorage';  // 存储方式
  
  // API 端点配置
  apiEndpoints?: {
    login?: string;                     // 登录 API
    refresh?: string;                   // 刷新 Token API
    logout?: string;                    // 登出 API
    profile?: string;                   // 用户信息 API
  };
}
```

## API 参考

### 核心方法

#### `login(username: string, password: string): Promise<LoginResult>`
用户登录

#### `logout(): Promise<void>`
用户登出

#### `isAuthenticated(): boolean`
检查用户是否已认证

#### `getCurrentUser(): AuthUser | null`
获取当前用户信息

#### `getToken(): string | null`
获取当前 Token

#### `refreshToken(): Promise<void>`
刷新 Token

### 权限方法

#### `hasPermission(permission: Permission): boolean`
检查单个权限

#### `hasPermissions(permissions: Permission[]): boolean`
检查多个权限（AND 逻辑）

#### `hasRole(role: Role): boolean`
检查单个角色

#### `hasRoles(roles: Role[]): boolean`
检查多个角色（AND 逻辑）

### 事件监听

```typescript
// 监听登录事件
kernel.on('auth:login', (user: AuthUser) => {
  console.log('用户已登录:', user);
});

// 监听登出事件
kernel.on('auth:logout', () => {
  console.log('用户已登出');
});

// 监听用户信息变更
kernel.on('auth:userChanged', (user: AuthUser | null) => {
  console.log('用户信息已变更:', user);
});

// 监听权限变更
kernel.on('auth:permissionsChanged', (user: AuthUser) => {
  console.log('用户权限已变更:', user);
});
```

## 类型定义

```typescript
interface AuthUser {
  id: string;
  username: string;
  email?: string;
  roles: Role[];
  permissions: Permission[];
  profile?: Record<string, any>;
}

interface LoginResult {
  user: AuthUser;
  token: string;
  refreshToken?: string;
  expiresIn?: number;
}

type Role = string;
type Permission = string;
```

## 最佳实践

### 1. 安全存储
```typescript
// 推荐使用 localStorage 进行持久化存储
const authPlugin = new AuthPlugin({
  storage: 'localStorage',
  tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
  autoRefresh: true
});
```

### 2. 错误处理
```typescript
try {
  await authPlugin.login(username, password);
} catch (error) {
  if (error.code === 'INVALID_CREDENTIALS') {
    showError('用户名或密码错误');
  } else if (error.code === 'ACCOUNT_LOCKED') {
    showError('账户已被锁定');
  } else {
    showError('登录失败，请稍后重试');
  }
}
```

### 3. 权限粒度控制
```typescript
// 使用细粒度权限控制
const permissions = [
  'user:read',
  'user:write',
  'user:delete',
  'admin:system',
  'admin:users'
];

// 在组件中检查权限
if (authPlugin.hasPermission('user:delete')) {
  showDeleteButton();
}
```

### 4. 路由级权限
```typescript
// 在路由元信息中定义权限要求
const routes = [
  {
    path: '/users',
    meta: {
      requiresAuth: true,
      requiredPermissions: ['user:read']
    }
  },
  {
    path: '/admin',
    meta: {
      requiresAuth: true,
      requiredRoles: ['admin']
    }
  }
];
```

## 故障排除

### 常见问题

**Q: Token 自动刷新失败**
A: 检查 `refreshTokenHandler` 是否正确设置，确保刷新 API 返回正确格式的数据。

**Q: 权限检查不生效**
A: 确保在插件配置中启用了 `enablePermissionCheck` 和 `enableRoleCheck`。

**Q: 存储的 Token 丢失**
A: 检查浏览器存储限制，考虑使用 `sessionStorage` 或实现自定义存储策略。

### 调试模式

```typescript
const authPlugin = new AuthPlugin({
  enableDebug: true  // 启用调试模式
});
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础认证功能
- 支持权限和角色管理
- 支持 Token 自动刷新
- 支持路由守卫集成
