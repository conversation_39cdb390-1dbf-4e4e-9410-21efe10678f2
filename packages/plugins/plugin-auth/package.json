{"name": "@micro-core/plugin-auth", "version": "0.1.0", "description": "Micro-Core 权限管理插件", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build && tsc --emitDeclarationOnly", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage"}, "keywords": ["micro-frontend", "micro-core", "plugin", "auth", "authentication", "authorization"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-auth"}, "dependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "typescript": "^5.3.0", "vite": "^5.4.0", "vitest": "^2.1.0"}, "peerDependencies": {"@micro-core/core": "^0.1.0"}}