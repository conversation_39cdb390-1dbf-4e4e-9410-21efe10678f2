/**
 * Iframe 沙箱插件类型定义
 */

export interface IframeSandboxOptions {
    /** 是否启用 PostMessage 通信 */
    enablePostMessage?: boolean;
    /** 是否启用沙箱属性 */
    enableSandboxAttributes?: boolean;
    /** 是否允许脚本执行 */
    allowScripts?: boolean;
    /** 是否允许同源访问 */
    allowSameOrigin?: boolean;
    /** 是否允许表单提交 */
    allowForms?: boolean;
    /** 是否允许弹窗 */
    allowPopups?: boolean;
    /** 自定义沙箱属性 */
    customSandboxAttrs?: string[];
    /** iframe 样式配置 */
    iframeStyle?: Partial<CSSStyleDeclaration>;
    /** 加载超时时间（毫秒） */
    loadTimeout?: number;
}

export interface PostMessageData {
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data: any;
    /** 消息时间戳 */
    timestamp: number;
    /** 发送方应用名称 */
    from?: string;
    /** 接收方应用名称 */
    to?: string;
}

export interface IframeCommunicationEvent {
    /** 事件类型 */
    type: 'app-ready' | 'app-error' | 'route-change' | 'custom';
    /** 事件数据 */
    data: any;
    /** 应用名称 */
    appName: string;
}