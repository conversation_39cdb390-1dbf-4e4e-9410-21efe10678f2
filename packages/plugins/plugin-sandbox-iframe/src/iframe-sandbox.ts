/**
 * Iframe 沙箱实现
 */

import { MicroApp, Sandbox } from '@micro-core/core';
import { PostMessageBridge } from './post-message-bridge';
import { IframeSandboxOptions } from './types';

export class IframeSandbox implements Sandbox {
    private app: MicroApp;
    private options: IframeSandboxOptions;
    private messageBridge: PostMessageBridge;
    private iframe?: HTMLIFrameElement;
    private container?: HTMLElement;
    private isActive = false;

    constructor(app: MicroApp, options: IframeSandboxOptions, messageBridge: PostMessageBridge) {
        this.app = app;
        this.options = options;
        this.messageBridge = messageBridge;
    }

    /**
     * 初始化沙箱
     */
    async initialize(): Promise<void> {
        // 获取容器元素
        this.container = this.getContainer();
        if (!this.container) {
            throw new Error(`Container not found for app ${this.app.name}`);
        }

        // 创建 iframe
        this.createIframe();

        // 设置消息通信
        if (this.options.enablePostMessage) {
            this.setupMessageCommunication();
        }
    }

    /**
     * 激活沙箱
     */
    async activate(): Promise<void> {
        if (this.isActive) {
            return;
        }

        if (!this.iframe) {
            throw new Error('Iframe not initialized');
        }

        // 加载应用资源
        await this.loadApp();

        this.isActive = true;
    }

    /**
     * 停用沙箱
     */
    async deactivate(): Promise<void> {
        if (!this.isActive) {
            return;
        }

        // 清理应用资源
        if (this.iframe && this.iframe.contentWindow) {
            // 通知应用即将卸载
            this.messageBridge.sendMessage(this.app.name, {
                type: 'app-unmount',
                data: {},
                timestamp: Date.now()
            });
        }

        this.isActive = false;
    }

    /**
     * 销毁沙箱
     */
    async destroy(): Promise<void> {
        await this.deactivate();

        // 移除 iframe
        if (this.iframe) {
            this.iframe.remove();
            this.iframe = undefined;
        }

        // 清理消息监听器
        this.messageBridge.removeAppListener(this.app.name);
    }

    /**
     * 获取沙箱窗口对象
     */
    getWindow(): Window | undefined {
        return this.iframe?.contentWindow || undefined;
    }

    /**
     * 获取沙箱文档对象
     */
    getDocument(): Document | undefined {
        return this.iframe?.contentDocument || undefined;
    }

    /**
     * 创建 iframe 元素
     */
    private createIframe(): void {
        this.iframe = document.createElement('iframe');

        // 设置基本样式
        this.iframe.style.width = '100%';
        this.iframe.style.height = '100%';
        this.iframe.style.border = 'none';
        this.iframe.style.display = 'block';

        // 设置沙箱属性
        if (this.options.enableSandboxAttributes) {
            const sandboxAttrs = this.buildSandboxAttributes();
            this.iframe.setAttribute('sandbox', sandboxAttrs);
        }

        // 设置其他属性
        this.iframe.setAttribute('name', `micro-app-${this.app.name}`);
        this.iframe.setAttribute('id', `micro-app-iframe-${this.app.name}`);

        // 添加到容器
        this.container!.appendChild(this.iframe);
    }

    /**
     * 构建沙箱属性
     */
    private buildSandboxAttributes(): string {
        const attrs: string[] = [];

        if (this.options.allowScripts) {
            attrs.push('allow-scripts');
        }

        if (this.options.allowSameOrigin) {
            attrs.push('allow-same-origin');
        }

        if (this.options.allowForms) {
            attrs.push('allow-forms');
        }

        if (this.options.allowPopups) {
            attrs.push('allow-popups');
        }

        // 添加其他必要的权限
        attrs.push('allow-modals');
        attrs.push('allow-orientation-lock');
        attrs.push('allow-pointer-lock');
        attrs.push('allow-presentation');

        return attrs.join(' ');
    }

    /**
     * 设置消息通信
     */
    private setupMessageCommunication(): void {
        // 监听来自 iframe 的消息
        this.messageBridge.onAppMessage(this.app.name, (message) => {
            this.handleAppMessage(message);
        });
    }

    /**
     * 处理来自应用的消息
     */
    private handleAppMessage(message: any): void {
        switch (message.type) {
            case 'app-ready':
                this.handleAppReady();
                break;
            case 'app-error':
                this.handleAppError(message.data);
                break;
            case 'route-change':
                this.handleRouteChange(message.data);
                break;
            default:
                // 转发给主应用
                window.dispatchEvent(new CustomEvent('micro-app-message', {
                    detail: {
                        app: this.app.name,
                        message
                    }
                }));
        }
    }

    /**
     * 处理应用就绪事件
     */
    private handleAppReady(): void {
        console.log(`App ${this.app.name} is ready in iframe sandbox`);

        // 触发应用就绪事件
        window.dispatchEvent(new CustomEvent('micro-app-ready', {
            detail: { app: this.app.name }
        }));
    }

    /**
     * 处理应用错误事件
     */
    private handleAppError(error: any): void {
        console.error(`App ${this.app.name} error in iframe sandbox:`, error);

        // 触发应用错误事件
        window.dispatchEvent(new CustomEvent('micro-app-error', {
            detail: { app: this.app.name, error }
        }));
    }

    /**
     * 处理路由变更事件
     */
    private handleRouteChange(routeData: any): void {
        console.log(`App ${this.app.name} route changed:`, routeData);

        // 触发路由变更事件
        window.dispatchEvent(new CustomEvent('micro-app-route-change', {
            detail: { app: this.app.name, route: routeData }
        }));
    }

    /**
     * 加载应用
     */
    private async loadApp(): Promise<void> {
        if (!this.iframe || !this.iframe.contentWindow) {
            throw new Error('Iframe not ready');
        }

        try {
            // 设置 iframe 的 src 或直接写入 HTML
            if (this.app.entry.startsWith('http')) {
                this.iframe.src = this.app.entry;
            } else {
                // 如果是 HTML 内容，直接写入
                const doc = this.iframe.contentDocument;
                if (doc) {
                    doc.open();
                    doc.write(this.app.entry);
                    doc.close();
                }
            }

            // 等待应用加载完成
            await this.waitForAppLoad();

        } catch (error) {
            console.error(`Failed to load app ${this.app.name}:`, error);
            throw error;
        }
    }

    /**
     * 等待应用加载完成
     */
    private waitForAppLoad(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.iframe) {
                reject(new Error('Iframe not available'));
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error(`App ${this.app.name} load timeout`));
            }, 30000); // 30秒超时

            this.iframe.onload = () => {
                clearTimeout(timeout);
                resolve();
            };

            this.iframe.onerror = () => {
                clearTimeout(timeout);
                reject(new Error(`Failed to load app ${this.app.name}`));
            };
        });
    }

    /**
     * 获取容器元素
     */
    private getContainer(): HTMLElement | null {
        if (typeof this.app.container === 'string') {
            return document.querySelector(this.app.container);
        }
        return this.app.container as HTMLElement;
    }
}