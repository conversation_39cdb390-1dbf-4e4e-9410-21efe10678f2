{"name": "@micro-core/plugin-sandbox-namespace", "version": "0.1.0", "description": "命名空间沙箱插件，通过命名空间前缀实现的轻量级沙箱策略", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "sandbox", "namespace", "plugin", "微前端"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0", "tsup": "^8.0.0", "vitest": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-sandbox-namespace"}, "publishConfig": {"access": "public"}}