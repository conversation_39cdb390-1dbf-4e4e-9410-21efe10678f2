import type { MicroCorePlugin, PluginContext } from '@micro-core/core';
import { NamespaceSandbox } from './namespace-sandbox';
import type { NamespaceSandboxPluginOptions } from './types';

/**
 * 命名空间沙箱插件
 * 提供基于命名空间的轻量级沙箱隔离能力
 */
export class NamespaceSandboxPlugin implements MicroCorePlugin {
    public readonly name = 'namespace-sandbox';
    public readonly version = '0.1.0';

    private options: NamespaceSandboxPluginOptions;
    private sandboxes = new Map<string, NamespaceSandbox>();

    constructor(options: NamespaceSandboxPluginOptions = {}) {
        this.options = {
            enableLogging: false,
            namespacePrefix: '__MICRO_APP_',
            enableAutoCleanup: true,
            enableConflictDetection: true,
            ...options
        };
    }

    /**
     * 插件安装
     */
    install(context: PluginContext) {
        this.log('命名空间沙箱插件正在安装...');

        // 注册沙箱创建钩子
        context.hooks.beforeAppMount.tap(this.name, (appConfig) => {
            this.createSandbox(appConfig.name, {
                enableLogging: this.options.enableLogging,
                namespacePrefix: this.options.namespacePrefix,
                enableAutoCleanup: this.options.enableAutoCleanup,
                enableConflictDetection: this.options.enableConflictDetection
            });
        });

        // 注册沙箱激活钩子
        context.hooks.appMounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.activate();
                this.log(`应用 ${appConfig.name} 的命名空间沙箱已激活`);
            }
        });

        // 注册沙箱停用钩子
        context.hooks.beforeAppUnmount.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.deactivate();
                this.log(`应用 ${appConfig.name} 的命名空间沙箱已停用`);
            }
        });

        // 注册沙箱销毁钩子
        context.hooks.appUnmounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.destroy();
                this.sandboxes.delete(appConfig.name);
                this.log(`应用 ${appConfig.name} 的命名空间沙箱已销毁`);
            }
        });

        this.log('命名空间沙箱插件安装完成');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        this.log('命名空间沙箱插件正在卸载...');

        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            try {
                sandbox.destroy();
                this.log(`应用 ${appName} 的命名空间沙箱已销毁`);
            } catch (error) {
                console.error(`[NamespaceSandboxPlugin] 销毁应用 ${appName} 的沙箱失败:`, error);
            }
        }

        this.sandboxes.clear();
        this.log('命名空间沙箱插件卸载完成');
    }

    /**
     * 创建沙箱
     */
    private createSandbox(appName: string, options: any) {
        try {
            const sandbox = new NamespaceSandbox(appName, options);
            this.sandboxes.set(appName, sandbox);
            this.log(`为应用 ${appName} 创建命名空间沙箱成功`);
        } catch (error) {
            console.error(`[NamespaceSandboxPlugin] 为应用 ${appName} 创建沙箱失败:`, error);
        }
    }

    /**
     * 获取指定应用的沙箱
     */
    getSandbox(appName: string): NamespaceSandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): Map<string, NamespaceSandbox> {
        return new Map(this.sandboxes);
    }

    /**
     * 检查命名空间冲突
     */
    checkNamespaceConflicts(): string[] {
        const conflicts: string[] = [];
        const namespaces = new Set<string>();

        for (const [appName, sandbox] of this.sandboxes) {
            const namespace = sandbox.getNamespace();
            if (namespaces.has(namespace)) {
                conflicts.push(`应用 ${appName} 的命名空间 ${namespace} 存在冲突`);
            } else {
                namespaces.add(namespace);
            }
        }

        return conflicts;
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[NamespaceSandboxPlugin] ${message}`);
        }
    }
}