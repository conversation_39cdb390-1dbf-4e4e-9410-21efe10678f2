/**
 * qiankun 兼容 API
 * 提供与 qiankun 完全兼容的全局函数
 */

import type { QiankunAppConfig, QiankunGlobalState } from './qiankun-adapter';

// 全局适配器实例
let globalAdapter: any = null;

/**
 * 设置全局适配器实例
 */
export function setGlobalAdapter(adapter: any): void {
    globalAdapter = adapter;
}

/**
 * 注册微应用
 */
export function registerMicroApps(apps: QiankunAppConfig[]): void {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    globalAdapter.registerMicroApps(apps);
}

/**
 * 启动 qiankun
 */
export async function start(options?: {
    prefetch?: boolean | 'all' | string[];
    jsSandbox?: boolean;
    singular?: boolean;
    fetch?: typeof window.fetch;
    getPublicPath?: (entry: string) => string;
    getTemplate?: (tpl: string) => string;
    excludeAssetFilter?: (assetUrl: string) => boolean;
}): Promise<void> {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    return globalAdapter.start(options);
}

/**
 * 手动加载微应用
 */
export async function loadMicroApp(app: QiankunAppConfig, configuration?: {
    sandbox?: boolean | { strictStyleIsolation?: boolean };
    singular?: boolean;
    fetch?: typeof window.fetch;
    getPublicPath?: (entry: string) => string;
    getTemplate?: (tpl: string) => string;
}): Promise<{
    mount: () => Promise<void>;
    unmount: () => Promise<void>;
    update: (customProps: Record<string, any>) => Promise<void>;
    getStatus: () => string;
    loadPromise: Promise<void>;
    bootstrapPromise: Promise<void>;
    mountPromise: Promise<void>;
    unmountPromise: Promise<void>;
}> {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    return globalAdapter.loadMicroApp(app, configuration);
}

/**
 * 初始化全局状态
 */
export function initGlobalState(state: Record<string, any> = {}): QiankunGlobalState {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    return globalAdapter.initGlobalState(state);
}

/**
 * 获取应用状态
 */
export function getAppStatus(name: string): string {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    return globalAdapter.getAppStatus(name);
}

/**
 * 获取所有应用
 */
export function getApps(): string[] {
    if (!globalAdapter) {
        throw new Error('qiankun 兼容插件未初始化，请先安装插件');
    }
    return globalAdapter.getApps();
}