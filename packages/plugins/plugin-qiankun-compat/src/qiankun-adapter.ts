import type { MicroCoreKernel } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * qiankun 应用配置
 */
export interface QiankunAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string;
    /** 应用容器 */
    container: string;
    /** 激活规则 */
    activeRule: string | string[] | ((location: Location) => boolean);
    /** 自定义属性 */
    props?: Record<string, any>;
    /** 加载器配置 */
    loader?: {
        fetch?: RequestInit;
        getExternalStyleSheets?: boolean;
        getExternalScripts?: boolean;
        excludeAssetFilter?: (url: string) => boolean;
    };
    /** 沙箱配置 */
    sandbox?: boolean | {
        strictStyleIsolation?: boolean;
        experimentalStyleIsolation?: boolean;
        [key: string]: any;
    };
}

/**
 * qiankun 全局状态
 */
export interface QiankunGlobalState {
    /** 设置全局状态 */
    setGlobalState: (state: Record<string, any>) => boolean;
    /** 监听全局状态变化 */
    onGlobalStateChange: (callback: (state: Record<string, any>, prev: Record<string, any>) => void, fireImmediately?: boolean) => void;
    /** 取消监听全局状态变化 */
    offGlobalStateChange: () => void;
}

/**
 * qiankun 适配器
 * 提供与 qiankun 完全兼容的 API 接口
 */
export class QiankunAdapter {
    private kernel: MicroCoreKernel;
    private registeredApps = new Map<string, QiankunAppConfig>();
    private globalStateCallbacks = new Set<(state: Record<string, any>, prev: Record<string, any>) => void>();
    private isStarted = false;

    constructor(kernel: MicroCoreKernel) {
        this.kernel = kernel;
        this.setupGlobalStateSync();
    }

    /**
     * 注册微应用
     */
    registerMicroApps(apps: QiankunAppConfig[]): void {
        apps.forEach(app => {
            this.registerMicroApp(app);
        });

        logger.info(`注册 ${apps.length} 个 qiankun 兼容应用`);
    }

    /**
     * 注册单个微应用
     */
    registerMicroApp(app: QiankunAppConfig): void {
        // 转换为 Micro-Core 格式
        const microCoreConfig = this.convertToMicroCoreConfig(app);

        // 注册到 Micro-Core
        this.kernel.registerApplication(microCoreConfig);

        // 保存原始配置
        this.registeredApps.set(app.name, app);

        logger.debug(`注册 qiankun 兼容应用: ${app.name}`);
    }

    /**
     * 启动 qiankun
     */
    async start(options?: {
        prefetch?: boolean | 'all' | string[];
        jsSandbox?: boolean;
        singular?: boolean;
        fetch?: typeof window.fetch;
        getPublicPath?: (entry: string) => string;
        getTemplate?: (tpl: string) => string;
        excludeAssetFilter?: (assetUrl: string) => boolean;
    }): Promise<void> {
        if (this.isStarted) {
            logger.warn('qiankun 已经启动');
            return;
        }

        // 启动 Micro-Core
        await this.kernel.start();
        this.isStarted = true;

        logger.info('qiankun 兼容模式启动完成');
    }

    /**
     * 手动加载微应用
     */
    async loadMicroApp(app: QiankunAppConfig, configuration?: {
        sandbox?: boolean | { strictStyleIsolation?: boolean };
        singular?: boolean;
        fetch?: typeof window.fetch;
        getPublicPath?: (entry: string) => string;
        getTemplate?: (tpl: string) => string;
    }): Promise<{
        mount: () => Promise<void>;
        unmount: () => Promise<void>;
        update: (customProps: Record<string, any>) => Promise<void>;
        getStatus: () => string;
        loadPromise: Promise<void>;
        bootstrapPromise: Promise<void>;
        mountPromise: Promise<void>;
        unmountPromise: Promise<void>;
    }> {
        // 注册应用（如果尚未注册）
        if (!this.registeredApps.has(app.name)) {
            this.registerMicroApp(app);
        }

        // 加载应用
        await this.kernel.loadApp(app.name);

        // 返回 qiankun 兼容的控制接口
        return {
            mount: async () => {
                await this.kernel.loadApp(app.name);
            },
            unmount: async () => {
                await this.kernel.unloadApp(app.name);
            },
            update: async (customProps: Record<string, any>) => {
                const appInstance = this.kernel.getApplication(app.name);
                if (appInstance) {
                    Object.assign(appInstance.props, customProps);
                }
            },
            getStatus: () => {
                const appInstance = this.kernel.getApplication(app.name);
                return appInstance?.status || 'NOT_LOADED';
            },
            loadPromise: Promise.resolve(),
            bootstrapPromise: Promise.resolve(),
            mountPromise: Promise.resolve(),
            unmountPromise: Promise.resolve()
        };
    }

    /**
     * 初始化全局状态
     */
    initGlobalState(state: Record<string, any> = {}): QiankunGlobalState {
        // 设置初始状态
        Object.entries(state).forEach(([key, value]) => {
            (this.kernel as any).setState?.(key, value);
        });

        return {
            setGlobalState: (newState: Record<string, any>) => {
                try {
                    Object.entries(newState).forEach(([key, value]) => {
                        (this.kernel as any).setState?.(key, value);
                    });
                    return true;
                } catch (error) {
                    logger.error('设置全局状态失败:', error);
                    return false;
                }
            },
            onGlobalStateChange: (callback, fireImmediately = false) => {
                this.globalStateCallbacks.add(callback);

                if (fireImmediately) {
                    const currentState = (this.kernel as any).getStateSnapshot?.() || {};
                    callback(currentState, {});
                }
            },
            offGlobalStateChange: () => {
                this.globalStateCallbacks.clear();
            }
        };
    }

    /**
     * 获取应用状态
     */
    getAppStatus(name: string): string {
        const app = this.kernel.getApplication(name);
        return app?.status || 'NOT_LOADED';
    }

    /**
     * 获取所有应用
     */
    getApps(): string[] {
        return Array.from(this.registeredApps.keys());
    }

    /**
     * 转换为 Micro-Core 配置格式
     */
    private convertToMicroCoreConfig(app: QiankunAppConfig) {
        return {
            name: app.name,
            entry: app.entry,
            container: app.container,
            activeWhen: app.activeRule,
            customProps: app.props || {},
            loader: app.loader,
            sandbox: this.convertSandboxConfig(app.sandbox)
        };
    }

    /**
     * 转换沙箱配置
     */
    private convertSandboxConfig(sandbox?: boolean | Record<string, any>) {
        if (sandbox === false) {
            return undefined;
        }

        if (sandbox === true || !sandbox) {
            return {
                type: 'proxy',
                styleIsolation: true,
                jsIsolation: true
            };
        }

        return {
            type: 'proxy',
            styleIsolation: sandbox.strictStyleIsolation || sandbox.experimentalStyleIsolation || true,
            jsIsolation: true,
            options: sandbox
        };
    }

    /**
     * 设置全局状态同步
     */
    private setupGlobalStateSync(): void {
        // 监听 Micro-Core 的状态变化，同步到 qiankun 回调
        if ((this.kernel as any).communication?.on) {
            (this.kernel as any).communication.on('state:changed', ({ key, newValue, oldValue }: any) => {
                const currentState = (this.kernel as any).getStateSnapshot?.() || {};
                const prevState = { ...currentState, [key]: oldValue };

                this.globalStateCallbacks.forEach(callback => {
                    try {
                        callback(currentState, prevState);
                    } catch (error) {
                        logger.error('qiankun 全局状态回调执行失败:', error);
                    }
                });
            });
        }
    }
}