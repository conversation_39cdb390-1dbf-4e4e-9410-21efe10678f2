/**
 * qiankun 兼容 API 实现
 */

import type { MicroCoreKernel } from '@micro-core/core';
import type {
    QiankunAppConfig,
    QiankunGlobalState,
    QiankunLifeCycles,
    QiankunMicroApp,
    QiankunStartOptions
} from './types';

/**
 * qiankun 兼容 API 类
 */
export class QiankunCompatAPI {
    private kernel: MicroCoreKernel;
    private registeredApps: QiankunAppConfig[] = [];
    private globalState: Record<string, any> = {};
    private globalStateCallbacks: Array<(state: Record<string, any>, prev: Record<string, any>) => void> = [];
    private isStarted = false;

    constructor(kernel: MicroCoreKernel) {
        this.kernel = kernel;
    }

    /**
     * 注册微应用
     */
    public registerMicroApps(
        apps: QiankunAppConfig[],
        lifeCycles?: QiankunLifeCycles
    ): void {
        this.registeredApps.push(...apps);

        // 将 qiankun 应用配置转换为 Micro-Core 配置
        apps.forEach(app => {
            const microCoreConfig = this.convertAppConfig(app);
            this.kernel.registerApplication(microCoreConfig);
        });

        // 注册生命周期钩子
        if (lifeCycles) {
            this.registerLifeCycles(lifeCycles);
        }
    }

    /**
     * 启动 qiankun
     */
    public start(opts: QiankunStartOptions = {}): void {
        if (this.isStarted) {
            console.warn('qiankun has already been started');
            return;
        }

        // 配置 Micro-Core 选项
        this.configureKernel(opts);

        // 启动 Micro-Core
        this.kernel.start();
        this.isStarted = true;
    }

    /**
     * 手动加载微应用
     */
    public async loadMicroApp(
        app: QiankunAppConfig,
        configuration?: QiankunStartOptions
    ): Promise<QiankunMicroApp> {
        const microCoreConfig = this.convertAppConfig(app);

        // 注册应用到内核
        this.kernel.registerApplication(microCoreConfig);

        // 加载应用
        await this.kernel.loadApp(app.name);

        // 返回 qiankun 风格的应用实例
        return this.createMicroAppInstance(app);
    }

    /**
     * 初始化全局状态
     */
    public initGlobalState(state: Record<string, any> = {}): QiankunGlobalState {
        this.globalState = { ...state };

        return {
            setGlobalState: (newState: Record<string, any>) => {
                const prevState = { ...this.globalState };
                this.globalState = { ...this.globalState, ...newState };

                // 触发回调
                this.globalStateCallbacks.forEach(callback => {
                    try {
                        callback(this.globalState, prevState);
                    } catch (error) {
                        console.error('Global state callback error:', error);
                    }
                });

                return true;
            },

            onGlobalStateChange: (callback, fireImmediately = false) => {
                this.globalStateCallbacks.push(callback);

                if (fireImmediately) {
                    callback(this.globalState, {});
                }
            },

            offGlobalStateChange: () => {
                this.globalStateCallbacks.length = 0;
                return true;
            }
        };
    }

    /**
     * 转换应用配置
     */
    private convertAppConfig(app: QiankunAppConfig): any {
        return {
            name: app.name,
            entry: typeof app.entry === 'string' ? app.entry : app.entry.html || '',
            container: typeof app.container === 'string' ? app.container : app.container,
            activeWhen: this.convertActiveRule(app.activeRule),
            customProps: app.props || {},
            loader: app.loader
        };
    }

    /**
     * 转换激活规则
     */
    private convertActiveRule(activeRule: QiankunAppConfig['activeRule']): any {
        if (typeof activeRule === 'string') {
            return (location: Location) => location.pathname.startsWith(activeRule);
        }

        if (typeof activeRule === 'function') {
            return activeRule;
        }

        if (Array.isArray(activeRule)) {
            return (location: Location) => {
                return activeRule.some(rule => {
                    if (typeof rule === 'string') {
                        return location.pathname.startsWith(rule);
                    }
                    if (typeof rule === 'function') {
                        return rule(location);
                    }
                    return false;
                });
            };
        }

        return () => false;
    }

    /**
     * 注册生命周期钩子
     */
    private registerLifeCycles(lifeCycles: QiankunLifeCycles): void {
        // 这里可以将 qiankun 的生命周期钩子转换为 Micro-Core 的钩子
        // 由于 Micro-Core 的钩子系统可能不同，这里做简化处理
        if (lifeCycles.beforeLoad) {
            // 注册到 Micro-Core 的相应钩子
        }

        if (lifeCycles.beforeMount) {
            // 注册到 Micro-Core 的相应钩子
        }

        // ... 其他生命周期钩子
    }

    /**
     * 配置内核选项
     */
    private configureKernel(opts: QiankunStartOptions): void {
        // 根据 qiankun 选项配置 Micro-Core
        if (opts.prefetch) {
            // 配置预加载
        }

        if (opts.sandbox) {
            // 配置沙箱
        }

        if (opts.singular) {
            // 配置单例模式
        }
    }

    /**
     * 创建微应用实例
     */
    private createMicroAppInstance(app: QiankunAppConfig): QiankunMicroApp {
        return {
            name: app.name,
            mount: async (props?: Record<string, any>) => {
                await this.kernel.mountApp(app.name, props);
            },
            unmount: async () => {
                await this.kernel.unmountApp(app.name);
            },
            update: async (props: Record<string, any>) => {
                // 更新应用属性
                const appInstance = this.kernel.getApplication(app.name);
                if (appInstance) {
                    // 更新属性逻辑
                }
            },
            getStatus: () => {
                const appInstance = this.kernel.getApplication(app.name);
                return appInstance ? appInstance.status : 'NOT_LOADED';
            }
        };
    }

    /**
     * 获取已注册的应用
     */
    public getApps(): QiankunAppConfig[] {
        return [...this.registeredApps];
    }

    /**
     * 卸载微应用
     */
    public async unloadApplication(appName: string): Promise<void> {
        await this.kernel.unloadApp(appName);
        this.registeredApps = this.registeredApps.filter(app => app.name !== appName);
    }
}