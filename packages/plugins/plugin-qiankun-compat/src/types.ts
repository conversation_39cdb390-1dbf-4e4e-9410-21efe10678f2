/**
 * qiankun 兼容插件类型定义
 */

export interface QiankunAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | { scripts?: string[]; styles?: string[]; html?: string };
    /** 应用容器 */
    container: string | HTMLElement;
    /** 激活规则 */
    activeRule: string | ((location: Location) => boolean) | Array<string | ((location: Location) => boolean)>;
    /** 自定义属性 */
    props?: Record<string, any>;
    /** 加载器配置 */
    loader?: (loading: boolean) => void;
}

export interface QiankunLifeCycles {
    /** 应用加载前 */
    beforeLoad?: (app: QiankunAppConfig) => Promise<any> | any;
    /** 应用挂载前 */
    beforeMount?: (app: QiankunAppConfig) => Promise<any> | any;
    /** 应用挂载后 */
    afterMount?: (app: QiankunAppConfig) => Promise<any> | any;
    /** 应用卸载前 */
    beforeUnmount?: (app: QiankunAppConfig) => Promise<any> | any;
    /** 应用卸载后 */
    afterUnmount?: (app: QiankunAppConfig) => Promise<any> | any;
}

export interface QiankunStartOptions {
    /** 预加载策略 */
    prefetch?: boolean | 'all' | string[] | ((apps: QiankunAppConfig[]) => { criticalAppNames: string[]; minorAppsName: string[] });
    /** 沙箱配置 */
    sandbox?: boolean | { strictStyleIsolation?: boolean; experimentalStyleIsolation?: boolean };
    /** 单例模式 */
    singular?: boolean | ((app: QiankunAppConfig) => Promise<boolean>);
    /** 获取公共资源 */
    getPublicPath?: (entry: string) => string;
    /** 获取模板 */
    getTemplate?: (tpl: string) => string;
    /** 排除资源 */
    excludeAssetFilter?: (assetUrl: string) => boolean;
}

export interface QiankunGlobalState {
    /** 设置全局状态 */
    setGlobalState: (state: Record<string, any>) => boolean;
    /** 监听全局状态变化 */
    onGlobalStateChange: (callback: (state: Record<string, any>, prev: Record<string, any>) => void, fireImmediately?: boolean) => void;
    /** 取消监听 */
    offGlobalStateChange: () => boolean;
}

export interface QiankunMicroApp {
    /** 应用名称 */
    name: string;
    /** 挂载应用 */
    mount: (props?: Record<string, any>) => Promise<void>;
    /** 卸载应用 */
    unmount: () => Promise<void>;
    /** 更新应用 */
    update: (props: Record<string, any>) => Promise<void>;
    /** 获取应用状态 */
    getStatus: () => string;
}

export interface QiankunLoadMicroAppOptions {
    /** 应用配置 */
    app: QiankunAppConfig;
    /** 配置选项 */
    configuration?: QiankunStartOptions;
}

export interface HTMLEntry {
    /** HTML 模板 */
    template: string;
    /** 资源入口 */
    assetPublicPath: string;
    /** 获取外部脚本 */
    getExternalScripts: () => Promise<string[]>;
    /** 获取外部样式 */
    getExternalStyleSheets: () => Promise<string[]>;
    /** 执行脚本 */
    execScripts: (proxy?: any, strictGlobal?: boolean) => Promise<any>;
}

export interface ImportHTMLResult {
    /** HTML 模板 */
    template: string;
    /** 资源公共路径 */
    assetPublicPath: string;
    /** 获取外部脚本 */
    getExternalScripts: () => Promise<string[]>;
    /** 获取外部样式 */
    getExternalStyleSheets: () => Promise<string[]>;
    /** 执行脚本 */
    execScripts: (proxy?: any, strictGlobal?: boolean, execScriptsHooks?: { fetch?: typeof fetch }) => Promise<any>;
}