/**
 * HTML Entry 处理器 - 解析和处理 HTML 入口
 */

import type { ImportHTMLResult } from './types';

/**
 * 解析 HTML 内容，提取脚本和样式
 */
export class HTMLEntryProcessor {
    private cache: Map<string, ImportHTMLResult> = new Map();

    /**
     * 导入 HTML 入口
     */
    public async importHTML(url: string, opts: { fetch?: typeof fetch } = {}): Promise<ImportHTMLResult> {
        // 检查缓存
        if (this.cache.has(url)) {
            return this.cache.get(url)!;
        }

        const fetchFn = opts.fetch || fetch;

        try {
            const response = await fetchFn(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${url}: ${response.status} ${response.statusText}`);
            }

            const html = await response.text();
            const result = this.parseHTML(html, url);

            // 缓存结果
            this.cache.set(url, result);

            return result;
        } catch (error) {
            throw new Error(`导入 HTML 失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 解析 HTML 内容
     */
    private parseHTML(html: string, baseUrl: string): ImportHTMLResult {
        const template = this.processTemplate(html);
        const assetPublicPath = this.getAssetPublicPath(baseUrl);

        // 提取脚本和样式链接
        const scripts = this.extractScripts(html, assetPublicPath);
        const styles = this.extractStyles(html, assetPublicPath);

        return {
            template,
            assetPublicPath,
            getExternalScripts: () => Promise.resolve(scripts),
            getExternalStyleSheets: () => Promise.resolve(styles),
            execScripts: (proxy?: any, strictGlobal?: boolean, execScriptsHooks?: { fetch?: typeof fetch }) =>
                this.execScripts(scripts, proxy, strictGlobal, execScriptsHooks)
        };
    }

    /**
     * 处理 HTML 模板
     */
    private processTemplate(html: string): string {
        // 移除脚本标签，保留其他内容
        return html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<link\s+[^>]*rel=['"]stylesheet['"][^>]*>/gi, '');
    }

    /**
     * 获取资源公共路径
     */
    private getAssetPublicPath(url: string): string {
        const urlObj = new URL(url);
        return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname.replace(/\/[^/]*$/, '/')}/`;
    }

    /**
     * 提取脚本链接
     */
    private extractScripts(html: string, baseUrl: string): string[] {
        const scripts: string[] = [];
        const scriptRegex = /<script\b[^>]*src=['"]([^'"]*)['"]/gi;
        let match;

        while ((match = scriptRegex.exec(html)) !== null) {
            const src = match[1];
            if (src) {
                scripts.push(this.resolveUrl(src, baseUrl));
            }
        }

        return scripts;
    }

    /**
     * 提取样式链接
     */
    private extractStyles(html: string, baseUrl: string): string[] {
        const styles: string[] = [];
        const styleRegex = /<link\s+[^>]*rel=['"]stylesheet['"][^>]*href=['"]([^'"]*)['"]/gi;
        let match;

        while ((match = styleRegex.exec(html)) !== null) {
            const href = match[1];
            if (href) {
                styles.push(this.resolveUrl(href, baseUrl));
            }
        }

        return styles;
    }

    /**
     * 解析相对 URL
     */
    private resolveUrl(url: string, baseUrl: string): string {
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')) {
            return url;
        }

        if (url.startsWith('/')) {
            const base = new URL(baseUrl);
            return `${base.protocol}//${base.host}${url}`;
        }

        return new URL(url, baseUrl).href;
    }

    /**
     * 执行脚本
     */
    private async execScripts(
        scripts: string[],
        proxy?: any,
        strictGlobal?: boolean,
        execScriptsHooks?: { fetch?: typeof fetch }
    ): Promise<any> {
        const fetchFn = execScriptsHooks?.fetch || fetch;
        const exports: any = {};

        for (const scriptUrl of scripts) {
            try {
                const response = await fetchFn(scriptUrl);
                if (!response.ok) {
                    console.warn(`Failed to load script: ${scriptUrl}`);
                    continue;
                }

                const scriptContent = await response.text();

                // 在沙箱环境中执行脚本
                if (proxy && strictGlobal) {
                    this.execScriptInSandbox(scriptContent, proxy, exports);
                } else {
                    this.execScriptInGlobal(scriptContent, exports);
                }
            } catch (error) {
                console.error(`Error executing script ${scriptUrl}:`, error);
            }
        }

        return exports;
    }

    /**
     * 在沙箱中执行脚本
     */
    private execScriptInSandbox(scriptContent: string, proxy: any, exports: any): void {
        try {
            // 创建一个函数来执行脚本，并绑定到代理对象
            const scriptFunction = new Function('window', 'self', 'globalThis', 'exports', scriptContent);
            scriptFunction.call(proxy, proxy, proxy, proxy, exports);
        } catch (error) {
            console.error('Script execution error in sandbox:', error);
        }
    }

    /**
     * 在全局环境中执行脚本
     */
    private execScriptInGlobal(scriptContent: string, exports: any): void {
        try {
            // 在全局环境中执行脚本
            const scriptFunction = new Function('exports', scriptContent);
            scriptFunction.call(window, exports);
        } catch (error) {
            console.error('Script execution error in global:', error);
        }
    }

    /**
     * 清理缓存
     */
    public clearCache(): void {
        this.cache.clear();
    }
}

/**
 * 默认的 HTML Entry 处理器实例
 */
export const htmlEntryProcessor = new HTMLEntryProcessor();

/**
 * 导入 HTML 入口的便捷函数
 */
export function importHTML(url: string, opts?: { fetch?: typeof fetch }): Promise<ImportHTMLResult> {
    return htmlEntryProcessor.importHTML(url, opts);
}