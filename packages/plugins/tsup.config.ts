import { defineConfig } from 'tsup';

export default defineConfig({
    entry: {
        index: 'src/index.ts',
        'router/index': 'router/src/index.ts',
        'communication/index': 'communication/src/index.ts',
        'sandbox/index': 'sandbox-proxy/src/index.ts',
        'auth/index': 'plugin-auth/src/index.ts',
        'devtools/index': 'plugin-devtools/src/index.ts',
        'prefetch/index': 'plugin-prefetch/src/index.ts',
        'qiankun-compat/index': 'plugin-qiankun-compat/src/index.ts',
        'wujie-compat/index': 'plugin-wujie-compat/src/index.ts'
    },
    format: ['cjs', 'esm'],
    dts: true,
    clean: true,
    splitting: false,
    sourcemap: true,
    minify: false,
    external: [
        '@micro-core/shared',
        '@micro-core/core'
    ],
    esbuildOptions(options) {
        options.banner = {
            js: '"use client";'
        };
    }
});