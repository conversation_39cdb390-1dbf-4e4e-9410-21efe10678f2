/**
 * @micro-core/plugin-wujie-compat
 * Wujie 兼容插件 - 为 Wujie 用户提供基于 iframe 和 WebComponent 的兼容方案
 */

import { MicroApp, MicroCorePlugin, PluginContext } from '@micro-core/core';
import { IframeBridge } from './iframe-bridge';
import { PropsBridge } from './props-bridge';
import { WujieAppConfig, WujieCompatOptions } from './types';
import { WujieAdapter } from './wujie-adapter';

export class WujieCompatPlugin implements MicroCorePlugin {
    name = 'wujie-compat';
    version = '1.0.0';

    private adapter: WujieAdapter;
    private iframeBridge: IframeBridge;
    private propsBridge: PropsBridge;
    private options: WujieCompatOptions;

    constructor(options: WujieCompatOptions = {}) {
        this.options = {
            enableShadowDOM: true,
            enableIframeIsolation: true,
            enableEventBus: true,
            ...options
        };

        this.adapter = new WujieAdapter(this.options);
        this.iframeBridge = new IframeBridge();
        this.propsBridge = new PropsBridge();
    }

    async install(context: PluginContext): Promise<void> {
        // 注册 Wujie 兼容 API
        this.registerWujieAPI(context);

        // 初始化 iframe 沙箱
        if (this.options.enableIframeIsolation) {
            await this.iframeBridge.initialize();
        }

        // 初始化 props 通信桥
        if (this.options.enableEventBus) {
            this.propsBridge.initialize();
        }
    }

    async uninstall(): Promise<void> {
        this.iframeBridge.destroy();
        this.propsBridge.destroy();
    }

    private registerWujieAPI(context: PluginContext): void {
        // 注册全局 Wujie API
        (window as any).wujie = {
            startApp: this.startApp.bind(this),
            setupApp: this.setupApp.bind(this),
            destroyApp: this.destroyApp.bind(this),
            preloadApp: this.preloadApp.bind(this),
            bus: this.propsBridge.getEventBus()
        };
    }

    /**
     * 启动 Wujie 风格的微应用
     */
    async startApp(config: WujieAppConfig): Promise<void> {
        const microApp: MicroApp = {
            name: config.name,
            entry: config.url,
            container: config.el,
            activeWhen: () => true,
            props: config.props || {},
            sandbox: this.options.enableIframeIsolation ? 'iframe' : 'proxy'
        };

        await this.adapter.mountApp(microApp, config);
    }

    /**
     * 预设置微应用配置
     */
    async setupApp(config: WujieAppConfig): Promise<void> {
        await this.adapter.setupApp(config);
    }

    /**
     * 销毁指定的微应用
     */
    async destroyApp(name: string): Promise<void> {
        await this.adapter.destroyApp(name);
    }

    /**
     * 预加载微应用资源
     */
    async preloadApp(config: WujieAppConfig): Promise<void> {
        await this.adapter.preloadApp(config);
    }
}

// 导出兼容 API
export const startApp = (config: WujieAppConfig) => {
    const plugin = new WujieCompatPlugin();
    return plugin.startApp(config);
};

export const setupApp = (config: WujieAppConfig) => {
    const plugin = new WujieCompatPlugin();
    return plugin.setupApp(config);
};

export const destroyApp = (name: string) => {
    const plugin = new WujieCompatPlugin();
    return plugin.destroyApp(name);
};

export const preloadApp = (config: WujieAppConfig) => {
    const plugin = new WujieCompatPlugin();
    return plugin.preloadApp(config);
};

export * from './iframe-bridge';
export * from './props-bridge';
export * from './types';
export * from './wujie-adapter';
