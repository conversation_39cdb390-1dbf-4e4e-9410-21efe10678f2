/**
 * Wujie API 兼容层
 * 提供与 Wujie 完全兼容的 API 接口
 */

import type { MicroCoreKernel } from '@micro-core/core';
import type {
    WujieAppConfig,
    WujieAppInstance,
    WujieEventBus,
    WujieMessage,
    WujieSetupOptions,
    WujieStartOptions
} from './types';
import { WujieAdapter } from './wujie-adapter';

/**
 * Wujie 应用实例管理器
 */
class WujieAppManager {
    private apps = new Map<string, WujieAppInstance>();
    private kernel: MicroCoreKernel | null = null;
    private adapter: WujieAdapter | null = null;

    /**
     * 设置内核实例
     */
    setKernel(kernel: MicroCoreKernel): void {
        this.kernel = kernel;
        this.adapter = new WujieAdapter(kernel);
    }

    /**
     * 获取应用实例
     */
    getApp(name: string): WujieAppInstance | undefined {
        return this.apps.get(name);
    }

    /**
     * 注册应用实例
     */
    registerApp(app: WujieAppInstance): void {
        this.apps.set(app.name, app);
    }

    /**
     * 移除应用实例
     */
    removeApp(name: string): void {
        this.apps.delete(name);
    }

    /**
     * 获取所有应用实例
     */
    getAllApps(): WujieAppInstance[] {
        return Array.from(this.apps.values());
    }

    /**
     * 清空所有应用实例
     */
    clear(): void {
        this.apps.clear();
    }

    /**
     * 获取适配器
     */
    getAdapter(): WujieAdapter | null {
        return this.adapter;
    }
}

/**
 * Wujie 事件总线实现
 */
class WujieEventBusImpl implements WujieEventBus {
    private events = new Map<string, Array<(...args: any[]) => void>>();

    /**
     * 监听事件
     */
    $on(event: string, callback: (...args: any[]) => void): void {
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        this.events.get(event)!.push(callback);
    }

    /**
     * 移除事件监听
     */
    $off(event: string, callback?: (...args: any[]) => void): void {
        if (!this.events.has(event)) {
            return;
        }

        if (!callback) {
            this.events.delete(event);
            return;
        }

        const callbacks = this.events.get(event)!;
        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }

        if (callbacks.length === 0) {
            this.events.delete(event);
        }
    }

    /**
     * 触发事件
     */
    $emit(event: string, ...args: any[]): void {
        if (!this.events.has(event)) {
            return;
        }

        const callbacks = this.events.get(event)!;
        callbacks.forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`[Wujie EventBus] Error in event callback for "${event}":`, error);
            }
        });
    }

    /**
     * 监听一次事件
     */
    $once(event: string, callback: (...args: any[]) => void): void {
        const onceCallback = (...args: any[]) => {
            callback(...args);
            this.$off(event, onceCallback);
        };
        this.$on(event, onceCallback);
    }

    /**
     * 清空所有事件监听
     */
    $clear(): void {
        this.events.clear();
    }
}

// 全局实例
const appManager = new WujieAppManager();
const eventBus = new WujieEventBusImpl();

/**
 * 启动 Wujie 应用
 */
export async function startApp(options: WujieStartOptions): Promise<WujieAppInstance> {
    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized. Please call setupWujieCompat first.');
    }

    const adapter = appManager.getAdapter()!;

    // 创建应用实例
    const appInstance: WujieAppInstance = {
        name: options.name,
        config: options,
        status: 'not-loaded',
        alive: options.alive || false,
        props: options.props || {}
    };

    // 注册应用实例
    appManager.registerApp(appInstance);

    try {
        // 使用适配器启动应用
        await adapter.startApp(appInstance);

        // 触发启动事件
        eventBus.$emit('app:started', appInstance);

        return appInstance;
    } catch (error) {
        appInstance.status = 'error';
        appInstance.error = error as Error;

        // 触发错误事件
        eventBus.$emit('app:error', appInstance, error);

        throw error;
    }
}

/**
 * 设置 Wujie 应用
 */
export async function setupApp(options: WujieSetupOptions): Promise<WujieAppInstance> {
    const appInstance: WujieAppInstance = {
        name: options.name,
        config: options,
        status: 'not-loaded',
        alive: options.alive || false,
        props: options.props || {}
    };

    // 注册应用实例
    appManager.registerApp(appInstance);

    // 如果设置了立即启动，则启动应用
    if (options.start) {
        return await startApp(options);
    }

    return appInstance;
}

/**
 * 销毁 Wujie 应用
 */
export async function destroyApp(name: string): Promise<void> {
    const appInstance = appManager.getApp(name);
    if (!appInstance) {
        console.warn(`[Wujie Compat] App "${name}" not found`);
        return;
    }

    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized.');
    }

    const adapter = appManager.getAdapter()!;

    try {
        // 使用适配器销毁应用
        await adapter.destroyApp(appInstance);

        // 移除应用实例
        appManager.removeApp(name);

        // 触发销毁事件
        eventBus.$emit('app:destroyed', appInstance);
    } catch (error) {
        appInstance.status = 'error';
        appInstance.error = error as Error;

        // 触发错误事件
        eventBus.$emit('app:error', appInstance, error);

        throw error;
    }
}

/**
 * 预加载 Wujie 应用
 */
export async function preloadApp(options: WujieAppConfig): Promise<void> {
    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized.');
    }

    const adapter = appManager.getAdapter()!;

    try {
        await adapter.preloadApp(options);
        eventBus.$emit('app:preloaded', options);
    } catch (error) {
        eventBus.$emit('app:preload-error', options, error);
        throw error;
    }
}

/**
 * 获取应用实例
 */
export function getApp(name: string): WujieAppInstance | undefined {
    return appManager.getApp(name);
}

/**
 * 获取所有应用实例
 */
export function getAllApps(): WujieAppInstance[] {
    return appManager.getAllApps();
}

/**
 * 检查应用是否存在
 */
export function hasApp(name: string): boolean {
    return appManager.getApp(name) !== undefined;
}

/**
 * 激活应用
 */
export async function activateApp(name: string): Promise<void> {
    const appInstance = appManager.getApp(name);
    if (!appInstance) {
        throw new Error(`App "${name}" not found`);
    }

    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized.');
    }

    const adapter = appManager.getAdapter()!;
    await adapter.activateApp(appInstance);
    eventBus.$emit('app:activated', appInstance);
}

/**
 * 失活应用
 */
export async function deactivateApp(name: string): Promise<void> {
    const appInstance = appManager.getApp(name);
    if (!appInstance) {
        throw new Error(`App "${name}" not found`);
    }

    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized.');
    }

    const adapter = appManager.getAdapter()!;
    await adapter.deactivateApp(appInstance);
    eventBus.$emit('app:deactivated', appInstance);
}

/**
 * 获取事件总线
 */
export const bus: WujieEventBus = eventBus;

/**
 * 初始化 Wujie 兼容层
 */
export function setupWujieCompat(kernel: MicroCoreKernel): void {
    appManager.setKernel(kernel);
}

/**
 * 清理所有应用
 */
export async function clearApps(): Promise<void> {
    const apps = appManager.getAllApps();

    for (const app of apps) {
        try {
            await destroyApp(app.name);
        } catch (error) {
            console.error(`Failed to destroy app "${app.name}":`, error);
        }
    }

    appManager.clear();
    eventBus.$clear();
}

/**
 * 发送消息到指定应用
 */
export function sendMessage(to: string, type: string, data: any): void {
    const message: WujieMessage = {
        type,
        data,
        from: 'main',
        to,
        timestamp: Date.now()
    };

    eventBus.$emit('message', message);
    eventBus.$emit(`message:${to}`, message);
}

/**
 * 广播消息到所有应用
 */
export function broadcastMessage(type: string, data: any): void {
    const message: WujieMessage = {
        type,
        data,
        from: 'main',
        timestamp: Date.now()
    };

    eventBus.$emit('broadcast', message);

    const apps = appManager.getAllApps();
    apps.forEach(app => {
        eventBus.$emit(`message:${app.name}`, message);
    });
}

/**
 * 监听来自应用的消息
 */
export function onMessage(callback: (message: WujieMessage) => void): void {
    eventBus.$on('message', callback);
}

/**
 * 移除消息监听
 */
export function offMessage(callback?: (message: WujieMessage) => void): void {
    eventBus.$off('message', callback);
}

/**
 * 获取应用状态
 */
export function getAppStatus(name: string): string | undefined {
    const app = appManager.getApp(name);
    return app?.status;
}

/**
 * 检查应用是否已挂载
 */
export function isAppMounted(name: string): boolean {
    const app = appManager.getApp(name);
    return app?.status === 'mounted';
}

/**
 * 检查应用是否保活
 */
export function isAppAlive(name: string): boolean {
    const app = appManager.getApp(name);
    return app?.alive === true;
}

/**
 * 设置应用属性
 */
export function setAppProps(name: string, props: Record<string, any>): void {
    const app = appManager.getApp(name);
    if (app) {
        app.props = { ...app.props, ...props };
        eventBus.$emit('app:props-changed', app, props);
    }
}

/**
 * 获取应用属性
 */
export function getAppProps(name: string): Record<string, any> | undefined {
    const app = appManager.getApp(name);
    return app?.props;
}

/**
 * 重新加载应用
 */
export async function reloadApp(name: string): Promise<void> {
    const app = appManager.getApp(name);
    if (!app) {
        throw new Error(`App "${name}" not found`);
    }

    if (!appManager.getAdapter()) {
        throw new Error('Wujie adapter not initialized.');
    }

    const adapter = appManager.getAdapter()!;
    await adapter.reloadApp(app);
    eventBus.$emit('app:reloaded', app);
}

// 导出应用管理器和事件总线供内部使用
export { appManager, eventBus };
