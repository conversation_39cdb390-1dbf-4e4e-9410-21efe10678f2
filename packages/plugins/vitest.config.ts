import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./test/setup.ts'],
        include: [
            'plugin-*/tests/unit/**/*.test.ts',
            'plugin-*/tests/integration/**/*.test.ts',
            '__tests__/**/*.test.ts'
        ],
        exclude: [
            'node_modules/**',
            'dist/**',
            'build/**'
        ],
        testTimeout: 10000,
        hookTimeout: 10000,
        reporters: ['verbose'],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json'],
            exclude: [
                'node_modules/',
                'test/',
                'tests/',
                'dist/',
                '**/*.d.ts',
                '**/*.config.*',
                '**/index.ts'
            ],
            thresholds: {
                global: {
                    branches: 80,
                    functions: 80,
                    lines: 80,
                    statements: 80
                }
            }
        }
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@micro-core/shared': resolve(__dirname, '../shared/src'),
            '@micro-core/core': resolve(__dirname, '../core/src'),
            '@micro-core/plugin-auth': resolve(__dirname, 'plugin-auth/src'),
            '@micro-core/plugin-router': resolve(__dirname, 'plugin-router/src'),
            '@micro-core/plugin-communication': resolve(__dirname, 'plugin-communication/src'),
            '@micro-core/plugin-sandbox-proxy': resolve(__dirname, 'plugin-sandbox-proxy/src')
        }
    },
    define: {
        __DEV__: true,
        __TEST__: true,
        __VERSION__: JSON.stringify('0.1.0')
    }
});