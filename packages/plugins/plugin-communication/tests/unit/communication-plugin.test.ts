/**
 * @fileoverview 通信插件单元测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel, AppInfo } from '@micro-core/core';
import { CommunicationPlugin } from '../../src/communication-plugin';
import type { CommunicationConfig, MessageData, EventData } from '../../src/types';

// Mock MicroCore kernel
const mockKernel: Partial<MicroCoreKernel> = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  getApp: vi.fn(),
  getAllApps: vi.fn(),
};

describe('CommunicationPlugin', () => {
  let communicationPlugin: CommunicationPlugin;
  let mockConfig: CommunicationConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockConfig = {
      enableEventBus: true,
      enableGlobalState: true,
      enableMessageChannel: true,
      maxEventListeners: 100,
      maxStateSize: 1024 * 1024, // 1MB
      enablePersistence: false,
      storageKey: 'micro-core-state',
      enableDebug: false,
    };
    
    communicationPlugin = new CommunicationPlugin(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Plugin Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultPlugin = new CommunicationPlugin();
      expect(defaultPlugin.name).toBe('communication');
      expect(defaultPlugin.version).toBe('1.0.0');
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: Partial<CommunicationConfig> = {
        maxEventListeners: 200,
        enableDebug: true,
      };
      const plugin = new CommunicationPlugin(customConfig);
      expect(plugin).toBeDefined();
    });

    it('should install plugin successfully', async () => {
      await expect(communicationPlugin.install(mockKernel as MicroCoreKernel)).resolves.not.toThrow();
      expect(mockKernel.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
      expect(mockKernel.on).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
    });
  });

  describe('Event Bus Functionality', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should register and emit events correctly', () => {
      const mockListener = vi.fn();
      const eventData: EventData = {
        type: 'user-login',
        payload: { userId: '123', username: 'testuser' },
        source: 'app1',
        timestamp: Date.now(),
      };

      // Register event listener
      communicationPlugin.on('user-login', mockListener);

      // Emit event
      communicationPlugin.emit('user-login', eventData.payload, eventData.source);

      // Verify listener was called
      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'user-login',
          payload: eventData.payload,
          source: eventData.source,
        })
      );
    });

    it('should support multiple listeners for same event', () => {
      const listener1 = vi.fn();
      const listener2 = vi.fn();
      const listener3 = vi.fn();

      // Register multiple listeners
      communicationPlugin.on('test-event', listener1);
      communicationPlugin.on('test-event', listener2);
      communicationPlugin.on('test-event', listener3);

      // Emit event
      communicationPlugin.emit('test-event', { data: 'test' }, 'test-app');

      // All listeners should be called
      expect(listener1).toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
      expect(listener3).toHaveBeenCalled();
    });

    it('should remove event listeners correctly', () => {
      const mockListener = vi.fn();

      // Register and then remove listener
      communicationPlugin.on('test-event', mockListener);
      communicationPlugin.off('test-event', mockListener);

      // Emit event
      communicationPlugin.emit('test-event', { data: 'test' }, 'test-app');

      // Listener should not be called
      expect(mockListener).not.toHaveBeenCalled();
    });

    it('should support once listeners', () => {
      const mockListener = vi.fn();

      // Register once listener
      communicationPlugin.once('test-event', mockListener);

      // Emit event multiple times
      communicationPlugin.emit('test-event', { data: 'test1' }, 'test-app');
      communicationPlugin.emit('test-event', { data: 'test2' }, 'test-app');

      // Listener should only be called once
      expect(mockListener).toHaveBeenCalledTimes(1);
    });

    it('should handle event listener limits', () => {
      const listeners = Array.from({ length: 150 }, () => vi.fn());

      // Try to register more listeners than the limit
      listeners.forEach((listener, index) => {
        if (index < 100) {
          expect(() => communicationPlugin.on('test-event', listener)).not.toThrow();
        } else {
          expect(() => communicationPlugin.on('test-event', listener)).toThrow();
        }
      });
    });

    it('should support wildcard event listeners', () => {
      const wildcardListener = vi.fn();

      // Register wildcard listener
      communicationPlugin.on('*', wildcardListener);

      // Emit various events
      communicationPlugin.emit('user-login', { userId: '123' }, 'app1');
      communicationPlugin.emit('user-logout', { userId: '123' }, 'app1');
      communicationPlugin.emit('data-update', { data: 'test' }, 'app2');

      // Wildcard listener should be called for all events
      expect(wildcardListener).toHaveBeenCalledTimes(3);
    });
  });

  describe('Global State Management', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should set and get global state correctly', () => {
      const testData = { user: { id: '123', name: 'Test User' } };

      // Set state
      communicationPlugin.setState('user', testData.user);

      // Get state
      const retrievedState = communicationPlugin.getState('user');
      expect(retrievedState).toEqual(testData.user);
    });

    it('should support nested state paths', () => {
      const testData = { theme: 'dark', language: 'en' };

      // Set nested state
      communicationPlugin.setState('ui.theme', testData.theme);
      communicationPlugin.setState('ui.language', testData.language);

      // Get nested state
      expect(communicationPlugin.getState('ui.theme')).toBe(testData.theme);
      expect(communicationPlugin.getState('ui.language')).toBe(testData.language);
      expect(communicationPlugin.getState('ui')).toEqual({
        theme: testData.theme,
        language: testData.language,
      });
    });

    it('should notify watchers on state changes', () => {
      const mockWatcher = vi.fn();

      // Watch state changes
      communicationPlugin.watch('user', mockWatcher);

      // Change state
      const newUser = { id: '456', name: 'New User' };
      communicationPlugin.setState('user', newUser);

      // Watcher should be called
      expect(mockWatcher).toHaveBeenCalledWith(newUser, undefined, 'user');
    });

    it('should support multiple watchers for same state', () => {
      const watcher1 = vi.fn();
      const watcher2 = vi.fn();

      // Register multiple watchers
      communicationPlugin.watch('data', watcher1);
      communicationPlugin.watch('data', watcher2);

      // Change state
      const newData = { value: 'test' };
      communicationPlugin.setState('data', newData);

      // Both watchers should be called
      expect(watcher1).toHaveBeenCalledWith(newData, undefined, 'data');
      expect(watcher2).toHaveBeenCalledWith(newData, undefined, 'data');
    });

    it('should remove watchers correctly', () => {
      const mockWatcher = vi.fn();

      // Watch and then unwatch
      const unwatch = communicationPlugin.watch('data', mockWatcher);
      unwatch();

      // Change state
      communicationPlugin.setState('data', { value: 'test' });

      // Watcher should not be called
      expect(mockWatcher).not.toHaveBeenCalled();
    });

    it('should handle state size limits', () => {
      const largeData = 'x'.repeat(2 * 1024 * 1024); // 2MB string

      // Should throw error for data exceeding size limit
      expect(() => {
        communicationPlugin.setState('largeData', largeData);
      }).toThrow();
    });

    it('should support state persistence when enabled', () => {
      const persistentConfig: CommunicationConfig = {
        ...mockConfig,
        enablePersistence: true,
      };

      const persistentPlugin = new CommunicationPlugin(persistentConfig);

      // Mock localStorage
      const mockStorage = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
      };

      Object.defineProperty(window, 'localStorage', {
        value: mockStorage,
        writable: true,
      });

      // Set state
      persistentPlugin.setState('persistentData', { value: 'test' });

      // Should save to localStorage
      expect(mockStorage.setItem).toHaveBeenCalledWith(
        'micro-core-state',
        expect.stringContaining('persistentData')
      );
    });
  });

  describe('Message Channel Communication', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should send and receive messages between apps', () => {
      const mockReceiver = vi.fn();

      // Register message receiver
      communicationPlugin.onMessage('app2', mockReceiver);

      // Send message from app1 to app2
      const messageData: MessageData = {
        id: 'msg-123',
        type: 'data-request',
        payload: { query: 'users' },
        source: 'app1',
        target: 'app2',
        timestamp: Date.now(),
      };

      communicationPlugin.sendMessage('app2', messageData.type, messageData.payload, 'app1');

      // Receiver should be called
      expect(mockReceiver).toHaveBeenCalledWith(
        expect.objectContaining({
          type: messageData.type,
          payload: messageData.payload,
          source: 'app1',
          target: 'app2',
        })
      );
    });

    it('should support request-response pattern', async () => {
      // Mock response handler
      communicationPlugin.onMessage('app2', (message) => {
        if (message.type === 'data-request') {
          communicationPlugin.sendMessage(
            message.source,
            'data-response',
            { users: [{ id: '1', name: 'User 1' }] },
            'app2'
          );
        }
      });

      const mockResponseHandler = vi.fn();
      communicationPlugin.onMessage('app1', mockResponseHandler);

      // Send request
      communicationPlugin.sendMessage('app2', 'data-request', { query: 'users' }, 'app1');

      // Wait for response (simulate async)
      await new Promise(resolve => setTimeout(resolve, 0));

      // Response handler should be called
      expect(mockResponseHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'data-response',
          payload: { users: [{ id: '1', name: 'User 1' }] },
          source: 'app2',
          target: 'app1',
        })
      );
    });

    it('should support broadcast messages', () => {
      const receiver1 = vi.fn();
      const receiver2 = vi.fn();
      const receiver3 = vi.fn();

      // Register receivers for different apps
      communicationPlugin.onMessage('app1', receiver1);
      communicationPlugin.onMessage('app2', receiver2);
      communicationPlugin.onMessage('app3', receiver3);

      // Broadcast message
      communicationPlugin.broadcast('system-notification', { message: 'System update' }, 'system');

      // All receivers should be called
      expect(receiver1).toHaveBeenCalled();
      expect(receiver2).toHaveBeenCalled();
      expect(receiver3).toHaveBeenCalled();
    });

    it('should handle message routing correctly', () => {
      const receiver1 = vi.fn();
      const receiver2 = vi.fn();

      // Register receivers
      communicationPlugin.onMessage('app1', receiver1);
      communicationPlugin.onMessage('app2', receiver2);

      // Send targeted message
      communicationPlugin.sendMessage('app1', 'targeted-message', { data: 'test' }, 'app3');

      // Only targeted receiver should be called
      expect(receiver1).toHaveBeenCalled();
      expect(receiver2).not.toHaveBeenCalled();
    });
  });

  describe('Cross-App Integration', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle app mounting and unmounting', () => {
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Simulate app mounting
      const mountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:mounted'
      )[1];
      
      expect(() => mountHandler(mockAppInfo)).not.toThrow();

      // Simulate app unmounting
      const unmountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:unmounted'
      )[1];
      
      expect(() => unmountHandler(mockAppInfo)).not.toThrow();
    });

    it('should clean up app-specific resources on unmount', () => {
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Register app-specific listeners
      communicationPlugin.onMessage('test-app', vi.fn());
      communicationPlugin.on('test-event', vi.fn());

      // Simulate app unmounting
      const unmountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:unmounted'
      )[1];
      
      unmountHandler(mockAppInfo);

      // App-specific resources should be cleaned up
      // This would be verified by checking internal state in a real implementation
      expect(true).toBe(true); // Placeholder assertion
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle invalid event names gracefully', () => {
      const mockListener = vi.fn();

      // Should not throw for invalid event names
      expect(() => communicationPlugin.on('', mockListener)).not.toThrow();
      expect(() => communicationPlugin.on(null as any, mockListener)).not.toThrow();
      expect(() => communicationPlugin.on(undefined as any, mockListener)).not.toThrow();
    });

    it('should handle circular references in state data', () => {
      const circularData: any = { name: 'test' };
      circularData.self = circularData;

      // Should handle circular references gracefully
      expect(() => {
        communicationPlugin.setState('circular', circularData);
      }).not.toThrow();
    });

    it('should handle listener errors gracefully', () => {
      const errorListener = vi.fn(() => {
        throw new Error('Listener error');
      });
      const normalListener = vi.fn();

      // Register listeners
      communicationPlugin.on('test-event', errorListener);
      communicationPlugin.on('test-event', normalListener);

      // Emit event - should not throw and should call normal listener
      expect(() => {
        communicationPlugin.emit('test-event', { data: 'test' }, 'test-app');
      }).not.toThrow();

      expect(errorListener).toHaveBeenCalled();
      expect(normalListener).toHaveBeenCalled();
    });

    it('should handle memory cleanup on plugin uninstall', async () => {
      // Register various listeners and state
      communicationPlugin.on('test-event', vi.fn());
      communicationPlugin.setState('test-state', { data: 'test' });
      communicationPlugin.onMessage('test-app', vi.fn());

      // Uninstall plugin
      await communicationPlugin.uninstall();

      // Should clean up event listeners
      expect(mockKernel.off).toHaveBeenCalledWith('app:mounted', expect.any(Function));
      expect(mockKernel.off).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
    });
  });

  describe('Performance and Scalability', () => {
    beforeEach(async () => {
      await communicationPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle large numbers of event listeners efficiently', () => {
      const listeners = Array.from({ length: 50 }, () => vi.fn());

      const startTime = performance.now();

      // Register many listeners
      listeners.forEach(listener => {
        communicationPlugin.on('performance-test', listener);
      });

      // Emit event
      communicationPlugin.emit('performance-test', { data: 'test' }, 'test-app');

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(50);

      // All listeners should be called
      listeners.forEach(listener => {
        expect(listener).toHaveBeenCalled();
      });
    });

    it('should handle rapid state changes efficiently', () => {
      const watcher = vi.fn();
      communicationPlugin.watch('rapid-state', watcher);

      const startTime = performance.now();

      // Perform rapid state changes
      for (let i = 0; i < 1000; i++) {
        communicationPlugin.setState('rapid-state', { value: i });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(100);

      // Watcher should be called for each change
      expect(watcher).toHaveBeenCalledTimes(1000);
    });

    it('should handle large message payloads efficiently', () => {
      const receiver = vi.fn();
      communicationPlugin.onMessage('target-app', receiver);

      const largePayload = {
        data: Array.from({ length: 10000 }, (_, i) => ({ id: i, value: `item-${i}` }))
      };

      const startTime = performance.now();

      // Send large message
      communicationPlugin.sendMessage('target-app', 'large-data', largePayload, 'source-app');

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(50);

      // Receiver should be called with correct data
      expect(receiver).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: largePayload,
        })
      );
    });
  });

  describe('Debug and Development Features', () => {
    it('should provide debug information when enabled', () => {
      const debugConfig: CommunicationConfig = {
        ...mockConfig,
        enableDebug: true,
      };

      const debugPlugin = new CommunicationPlugin(debugConfig);

      // Mock console methods
      const originalLog = console.log;
      console.log = vi.fn();

      // Perform operations that should generate debug output
      debugPlugin.on('test-event', vi.fn());
      debugPlugin.emit('test-event', { data: 'test' }, 'test-app');

      // Should have debug output
      expect(console.log).toHaveBeenCalled();

      console.log = originalLog;
    });

    it('should provide communication statistics', () => {
      // Register listeners and send messages
      communicationPlugin.on('event1', vi.fn());
      communicationPlugin.on('event2', vi.fn());
      communicationPlugin.setState('state1', { data: 'test' });
      communicationPlugin.sendMessage('app1', 'message1', { data: 'test' }, 'app2');

      // Get statistics
      const stats = communicationPlugin.getStatistics();

      expect(stats).toEqual(
        expect.objectContaining({
          eventListeners: expect.any(Number),
          stateWatchers: expect.any(Number),
          messageHandlers: expect.any(Number),
          eventsEmitted: expect.any(Number),
          messagesExchanged: expect.any(Number),
        })
      );
    });
  });
});
