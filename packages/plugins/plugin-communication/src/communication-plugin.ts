import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import { logger } from '@micro-core/core';
import { EventBus } from './event-bus';
import { GlobalState } from './global-state';

/**
 * 通信插件配置
 */
export interface CommunicationPluginConfig {
    /** 是否启用全局状态管理 */
    enableGlobalState?: boolean;
    /** 是否启用事件总线 */
    enableEventBus?: boolean;
    /** 事件总线最大监听器数量 */
    maxListeners?: number;
    /** 是否启用调试模式 */
    debug?: boolean;
}

/**
 * 通信插件
 * 提供应用间通信能力，包括事件总线和全局状态管理
 */
export class CommunicationPlugin implements Plugin {
    name = 'communication';
    version = '0.1.0';
    private config: CommunicationPluginConfig;
    private eventBus?: EventBus;
    private globalState?: GlobalState;

    constructor(config: CommunicationPluginConfig = {}) {
        this.config = {
            enableGlobalState: true,
            enableEventBus: true,
            maxListeners: 100,
            debug: false,
            ...config
        };
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        // 初始化事件总线
        if (this.config.enableEventBus) {
            this.eventBus = new EventBus();
            if (this.config.maxListeners) {
                this.eventBus.setMaxListeners(this.config.maxListeners);
            }
        }

        // 初始化全局状态管理
        if (this.config.enableGlobalState) {
            this.globalState = new GlobalState();
        }

        // 扩展内核
        this.extendKernel(kernel);

        // 设置调试模式
        if (this.config.debug) {
            this.setupDebugMode();
        }

        logger.info('通信插件安装完成');
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 清理资源
        if (this.eventBus) {
            this.eventBus.clear();
            this.eventBus = undefined;
        }

        if (this.globalState) {
            this.globalState.clear();
            this.globalState = undefined;
        }

        // 移除内核扩展
        this.removeKernelExtensions(kernel);

        logger.info('通信插件卸载完成');
    }

    /**
     * 扩展内核
     */
    private extendKernel(kernel: MicroCoreKernel): void {
        // 添加通信相关方法到内核
        (kernel as any).communication = {
            // 事件总线方法
            on: (event: string, listener: (data: any) => void) => {
                this.eventBus?.on(event, listener);
            },
            once: (event: string, listener: (data: any) => void) => {
                this.eventBus?.once(event, listener);
            },
            off: (event: string, listener?: (data: any) => void) => {
                this.eventBus?.off(event, listener);
            },
            emit: (event: string, data?: any) => {
                return this.eventBus?.emit(event, data);
            },
            emitSync: (event: string, data?: any) => {
                this.eventBus?.emitSync(event, data);
            },

            // 全局状态方法
            setState: (key: string, value: any) => {
                this.globalState?.set(key, value);
            },
            getState: (key: string) => {
                return this.globalState?.get(key);
            },
            hasState: (key: string) => {
                return this.globalState?.has(key) || false;
            },
            deleteState: (key: string) => {
                return this.globalState?.delete(key) || false;
            },
            watchState: (key: string, listener: (newValue: any, oldValue: any) => void) => {
                this.globalState?.watch(key, listener);
            },
            unwatchState: (key: string, listener?: (newValue: any, oldValue: any) => void) => {
                this.globalState?.unwatch(key, listener);
            },
            getStateSnapshot: () => {
                return this.globalState?.getSnapshot() || {};
            },

            // 工具方法
            getEventBus: () => this.eventBus,
            getGlobalState: () => this.globalState,
            listenerCount: (event: string) => {
                return this.eventBus?.listenerCount(event) || 0;
            },
            eventNames: () => {
                return this.eventBus?.eventNames() || [];
            },
            stateKeys: () => {
                return this.globalState?.keys() || [];
            }
        };

        // 添加便捷的全局方法
        (kernel as any).on = (event: string, listener: (data: any) => void) => {
            this.eventBus?.on(event, listener);
        };

        (kernel as any).emit = (event: string, data?: any) => {
            return this.eventBus?.emit(event, data);
        };

        (kernel as any).setState = (key: string, value: any) => {
            this.globalState?.set(key, value);
        };

        (kernel as any).getState = (key: string) => {
            return this.globalState?.get(key);
        };
    }

    /**
     * 移除内核扩展
     */
    private removeKernelExtensions(kernel: MicroCoreKernel): void {
        delete (kernel as any).communication;
        delete (kernel as any).on;
        delete (kernel as any).emit;
        delete (kernel as any).setState;
        delete (kernel as any).getState;
    }

    /**
     * 设置调试模式
     */
    private setupDebugMode(): void {
        if (this.eventBus) {
            // 监听所有事件并打印日志
            const originalEmit = this.eventBus.emit.bind(this.eventBus);
            this.eventBus.emit = async (event: string, data?: any) => {
                logger.debug(`[EventBus] 触发事件: ${event}`, data);
                return originalEmit(event, data);
            };
        }

        if (this.globalState) {
            // 监听所有状态变化并打印日志
            this.globalState.on('state:changed', ({ key, newValue, oldValue }) => {
                logger.debug(`[GlobalState] 状态变化: ${key}`, { oldValue, newValue });
            });
        }
    }
}

/**
 * 创建通信插件实例
 */
export function createCommunicationPlugin(config?: CommunicationPluginConfig): CommunicationPlugin {
    return new CommunicationPlugin(config);
}

/**
 * 默认通信插件实例
 */
export const communicationPlugin = new CommunicationPlugin();