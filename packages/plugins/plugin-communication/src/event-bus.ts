import { logger } from '@micro-core/core';

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件总线
 * 提供应用间的事件通信能力
 */
export class EventBus {
    private listeners = new Map<string, Set<EventListener>>();
    private onceListeners = new Map<string, Set<EventListener>>();
    private maxListeners = 100;

    /**
     * 监听事件
     */
    on<T = any>(event: string, listener: EventListener<T>): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }

        const eventListeners = this.listeners.get(event)!;

        // 检查监听器数量限制
        if (eventListeners.size >= this.maxListeners) {
            logger.warn(`事件 ${event} 的监听器数量已达到上限 ${this.maxListeners}`);
            return;
        }

        eventListeners.add(listener);
        logger.debug(`添加事件监听器: ${event}`);
    }

    /**
     * 监听事件（仅一次）
     */
    once<T = any>(event: string, listener: EventListener<T>): void {
        if (!this.onceListeners.has(event)) {
            this.onceListeners.set(event, new Set());
        }

        const onceEventListeners = this.onceListeners.get(event)!;

        // 检查监听器数量限制
        if (onceEventListeners.size >= this.maxListeners) {
            logger.warn(`事件 ${event} 的一次性监听器数量已达到上限 ${this.maxListeners}`);
            return;
        }

        onceEventListeners.add(listener);
        logger.debug(`添加一次性事件监听器: ${event}`);
    }

    /**
     * 移除事件监听器
     */
    off<T = any>(event: string, listener?: EventListener<T>): void {
        if (!listener) {
            // 移除所有监听器
            this.listeners.delete(event);
            this.onceListeners.delete(event);
            logger.debug(`移除事件 ${event} 的所有监听器`);
            return;
        }

        // 移除指定监听器
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.delete(listener);
            if (eventListeners.size === 0) {
                this.listeners.delete(event);
            }
        }

        const onceEventListeners = this.onceListeners.get(event);
        if (onceEventListeners) {
            onceEventListeners.delete(listener);
            if (onceEventListeners.size === 0) {
                this.onceListeners.delete(event);
            }
        }

        logger.debug(`移除事件监听器: ${event}`);
    }

    /**
     * 触发事件
     */
    async emit<T = any>(event: string, data?: T): Promise<void> {
        const promises: Promise<void>[] = [];

        // 执行普通监听器
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            for (const listener of eventListeners) {
                try {
                    const result = listener(data);
                    if (result instanceof Promise) {
                        promises.push(result);
                    }
                } catch (error) {
                    logger.error(`事件监听器执行失败 (${event}):`, error);
                }
            }
        }

        // 执行一次性监听器
        const onceEventListeners = this.onceListeners.get(event);
        if (onceEventListeners) {
            for (const listener of onceEventListeners) {
                try {
                    const result = listener(data);
                    if (result instanceof Promise) {
                        promises.push(result);
                    }
                } catch (error) {
                    logger.error(`一次性事件监听器执行失败 (${event}):`, error);
                }
            }
            // 清除一次性监听器
            this.onceListeners.delete(event);
        }

        // 等待所有异步监听器完成
        if (promises.length > 0) {
            await Promise.allSettled(promises);
        }

        logger.debug(`触发事件: ${event}`, data);
    }

    /**
     * 同步触发事件
     */
    emitSync<T = any>(event: string, data?: T): void {
        // 执行普通监听器
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            for (const listener of eventListeners) {
                try {
                    listener(data);
                } catch (error) {
                    logger.error(`事件监听器执行失败 (${event}):`, error);
                }
            }
        }

        // 执行一次性监听器
        const onceEventListeners = this.onceListeners.get(event);
        if (onceEventListeners) {
            for (const listener of onceEventListeners) {
                try {
                    listener(data);
                } catch (error) {
                    logger.error(`一次性事件监听器执行失败 (${event}):`, error);
                }
            }
            // 清除一次性监听器
            this.onceListeners.delete(event);
        }

        logger.debug(`同步触发事件: ${event}`, data);
    }

    /**
     * 获取事件的监听器数量
     */
    listenerCount(event: string): number {
        const normalCount = this.listeners.get(event)?.size || 0;
        const onceCount = this.onceListeners.get(event)?.size || 0;
        return normalCount + onceCount;
    }

    /**
     * 获取所有事件名称
     */
    eventNames(): string[] {
        const normalEvents = Array.from(this.listeners.keys());
        const onceEvents = Array.from(this.onceListeners.keys());
        return [...new Set([...normalEvents, ...onceEvents])];
    }

    /**
     * 设置最大监听器数量
     */
    setMaxListeners(max: number): void {
        this.maxListeners = Math.max(1, max);
        logger.debug(`设置最大监听器数量: ${this.maxListeners}`);
    }

    /**
     * 获取最大监听器数量
     */
    getMaxListeners(): number {
        return this.maxListeners;
    }

    /**
     * 清除所有监听器
     */
    clear(): void {
        this.listeners.clear();
        this.onceListeners.clear();
        logger.debug('清除所有事件监听器');
    }

    /**
     * 检查是否有指定事件的监听器
     */
    hasListeners(event: string): boolean {
        return this.listenerCount(event) > 0;
    }
}