#!/usr/bin/env node

/**
 * 专用于 plugins 目录的测试运行器
 * 解决 mock kernel 和类型问题，确保插件测试通过
 */

import { spawn } from 'child_process';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const PLUGINS_DIR = __dirname;
const TEST_PATTERNS = [
  'tests/unit/**/*.test.ts',
  'tests/integration/**/*.test.ts'
];

console.log('🚀 启动插件测试运行器...');
console.log(`📁 测试目录: ${PLUGINS_DIR}`);

// 运行 vitest 测试
const vitestArgs = [
  'run',
  '--config', resolve(PLUGINS_DIR, 'vitest.config.ts'),
  '--reporter', 'verbose',
  '--no-coverage',
  ...TEST_PATTERNS
];

console.log('🧪 执行测试命令:', `vitest ${vitestArgs.join(' ')}`);

const testProcess = spawn('npx', ['vitest', ...vitestArgs], {
  cwd: PLUGINS_DIR,
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'test',
    VITEST_PLUGINS_TEST: 'true'
  }
});

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 所有插件测试通过！');
  } else {
    console.log(`❌ 测试失败，退出码: ${code}`);
    process.exit(code);
  }
});

testProcess.on('error', (error) => {
  console.error('❌ 测试运行器错误:', error);
  process.exit(1);
});
