/**
 * 联邦组件沙箱插件类型定义
 */

export interface FederationSandboxPluginOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 是否启用共享作用域 */
    enableSharedScope?: boolean;
    /** 是否启用模块缓存 */
    enableModuleCache?: boolean;
    /** 共享依赖列表 */
    sharedDependencies?: string[];
}

export interface FederationSandboxOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 是否启用共享作用域 */
    enableSharedScope?: boolean;
    /** 是否启用模块缓存 */
    enableModuleCache?: boolean;
    /** 共享依赖列表 */
    sharedDependencies?: string[];
    /** 远程入口地址 */
    remoteEntry?: string;
}

export interface ModuleInfo {
    /** 模块名称 */
    name: string;
    /** 模块版本 */
    version: string;
    /** 模块获取函数 */
    get: () => any;
    /** 是否已加载 */
    loaded: boolean;
    /** 模块依赖 */
    dependencies?: string[];
}

export interface SharedScope {
    /** 共享模块映射 */
    [moduleName: string]: ModuleInfo;
}

export interface RemoteEntry {
    /** 远程入口URL */
    url: string;
    /** 远程容器名称 */
    name: string;
    /** 是否已加载 */
    loaded: boolean;
    /** 可用模块映射 */
    modules: Map<string, any>;
}

export interface FederationContainer {
    /** 初始化容器 */
    init: (sharedScope: SharedScope) => Promise<void>;
    /** 获取模块 */
    get: (moduleName: string) => Promise<any>;
}

export interface WebpackShareScope {
    /** 共享作用域映射 */
    [scopeName: string]: {
        [moduleName: string]: ModuleInfo;
    };
}

export interface ModuleFederationConfig {
    /** 应用名称 */
    name: string;
    /** 远程入口 */
    remotes?: Record<string, string>;
    /** 暴露的模块 */
    exposes?: Record<string, string>;
    /** 共享依赖 */
    shared?: Record<string, any>;
}