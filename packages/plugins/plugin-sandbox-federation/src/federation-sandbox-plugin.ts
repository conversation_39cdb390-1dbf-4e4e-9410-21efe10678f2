import type { MicroCorePlugin, PluginContext } from '@micro-core/core';
import { FederationSandbox } from './federation-sandbox';
import type { FederationSandboxPluginOptions } from './types';

/**
 * 联邦组件沙箱插件
 * 提供基于模块联邦的沙箱隔离能力
 */
export class FederationSandboxPlugin implements MicroCorePlugin {
    public readonly name = 'federation-sandbox';
    public readonly version = '0.1.0';

    private options: FederationSandboxPluginOptions;
    private sandboxes = new Map<string, FederationSandbox>();

    constructor(options: FederationSandboxPluginOptions = {}) {
        this.options = {
            enableLogging: false,
            enableSharedScope: true,
            enableModuleCache: true,
            sharedDependencies: ['react', 'react-dom', 'vue', 'lodash'],
            ...options
        };
    }

    /**
     * 插件安装
     */
    install(context: PluginContext) {
        this.log('联邦组件沙箱插件正在安装...');

        // 注册沙箱创建钩子
        context.hooks.beforeAppMount.tap(this.name, (appConfig) => {
            this.createSandbox(appConfig.name, {
                enableLogging: this.options.enableLogging,
                enableSharedScope: this.options.enableSharedScope,
                enableModuleCache: this.options.enableModuleCache,
                sharedDependencies: this.options.sharedDependencies,
                remoteEntry: appConfig.entry
            });
        });

        // 注册沙箱激活钩子
        context.hooks.appMounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.activate();
                this.log(`应用 ${appConfig.name} 的联邦组件沙箱已激活`);
            }
        });

        // 注册沙箱停用钩子
        context.hooks.beforeAppUnmount.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.deactivate();
                this.log(`应用 ${appConfig.name} 的联邦组件沙箱已停用`);
            }
        });

        // 注册沙箱销毁钩子
        context.hooks.appUnmounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.destroy();
                this.sandboxes.delete(appConfig.name);
                this.log(`应用 ${appConfig.name} 的联邦组件沙箱已销毁`);
            }
        });

        this.log('联邦组件沙箱插件安装完成');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        this.log('联邦组件沙箱插件正在卸载...');

        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            try {
                sandbox.destroy();
                this.log(`应用 ${appName} 的联邦组件沙箱已销毁`);
            } catch (error) {
                console.error(`[FederationSandboxPlugin] 销毁应用 ${appName} 的沙箱失败:`, error);
            }
        }

        this.sandboxes.clear();
        this.log('联邦组件沙箱插件卸载完成');
    }

    /**
     * 创建沙箱
     */
    private createSandbox(appName: string, options: any) {
        try {
            const sandbox = new FederationSandbox(appName, options);
            this.sandboxes.set(appName, sandbox);
            this.log(`为应用 ${appName} 创建联邦组件沙箱成功`);
        } catch (error) {
            console.error(`[FederationSandboxPlugin] 为应用 ${appName} 创建沙箱失败:`, error);
        }
    }

    /**
     * 获取指定应用的沙箱
     */
    getSandbox(appName: string): FederationSandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): Map<string, FederationSandbox> {
        return new Map(this.sandboxes);
    }

    /**
     * 加载远程模块
     */
    async loadRemoteModule(appName: string, moduleName: string): Promise<any> {
        const sandbox = this.sandboxes.get(appName);
        if (!sandbox) {
            throw new Error(`应用 ${appName} 的沙箱不存在`);
        }

        return sandbox.loadRemoteModule(moduleName);
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[FederationSandboxPlugin] ${message}`);
        }
    }
}