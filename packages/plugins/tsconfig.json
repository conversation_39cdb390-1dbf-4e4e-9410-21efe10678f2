{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": ".", "baseUrl": ".", "paths": {"@micro-core/shared": ["../shared/src"], "@micro-core/shared/*": ["../shared/src/*"], "@micro-core/core": ["../core/src"], "@micro-core/core/*": ["../core/src/*"]}}, "include": ["src/**/*", "*/src/**/*", "plugin-*/src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}