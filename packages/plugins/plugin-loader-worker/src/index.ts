/**
 * Worker 加载器插件 - 主入口文件
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { CacheManager } from './cache-manager';
import type { LoadResult, WorkerLoaderOptions } from './types';
import { WorkerManager } from './worker-manager';

export { CacheManager } from './cache-manager';
export type { LoadResult, LoadTask, ProgressInfo, WorkerLoaderOptions } from './types';
export { WorkerManager } from './worker-manager';

/**
 * 插件接口
 */
export interface Plugin {
    name: string;
    version: string;
    install(kernel: MicroCoreKernel): void;
    uninstall?(kernel: MicroCoreKernel): void;
}

/**
 * Worker 加载器插件类
 */
export class WorkerLoaderPlugin implements Plugin {
    public name = 'worker-loader';
    public version = '0.1.0';

    private workerManager: WorkerManager;
    private cacheManager: CacheManager;
    private options: WorkerLoaderOptions;

    constructor(options: WorkerLoaderOptions = {}) {
        this.options = options;
        this.workerManager = new WorkerManager(options);
        this.cacheManager = new CacheManager(options.cacheStrategy);
    }

    /**
     * 插件安装方法
     */
    public install(kernel: MicroCoreKernel): void {
        // 注册插件到内核
        kernel.plugins.register(this.name, this);

        // 扩展内核功能
        this.extendKernel(kernel);
    }

    /**
     * 扩展内核功能
     */
    private extendKernel(kernel: MicroCoreKernel): void {
        // 添加 Worker 加载器方法到内核
        (kernel as any).workerLoader = {
            loadResource: this.loadResource.bind(this),
            loadResources: this.loadResources.bind(this),
            preloadResources: this.preloadResources.bind(this),
            getStats: this.getStats.bind(this),
            getCacheStats: this.getCacheStats.bind(this),
            clearCache: this.clearCache.bind(this)
        };
    }

    /**
     * 加载单个资源
     */
    public async loadResource(
        url: string,
        type: 'script' | 'style' | 'json' | 'text' | 'blob' = 'text',
        priority: 'high' | 'normal' | 'low' = 'normal'
    ): Promise<LoadResult> {
        // 先检查缓存
        const cachedItem = await this.cacheManager.get(url);
        if (cachedItem) {
            return {
                id: `cached_${Date.now()}`,
                success: true,
                content: cachedItem.content,
                loadTime: 0,
                size: cachedItem.size
            };
        }

        // 使用 Worker 加载
        const result = await this.workerManager.loadResource(url, type, priority);

        // 缓存结果
        if (result.success && result.content) {
            await this.cacheManager.set(url, result.content, type);
        }

        return result;
    }

    /**
     * 批量加载资源
     */
    public async loadResources(urls: string[]): Promise<LoadResult[]> {
        const promises = urls.map(url => this.loadResource(url));
        return Promise.all(promises);
    }

    /**
     * 预加载资源
     */
    public preloadResources(urls: string[]): void {
        this.workerManager.preloadResources(urls);
    }

    /**
     * 获取加载统计信息
     */
    public getStats() {
        return this.workerManager.getStats();
    }

    /**
     * 获取缓存统计信息
     */
    public getCacheStats() {
        return this.cacheManager.getCacheStats();
    }

    /**
     * 清理缓存
     */
    public async clearCache(): Promise<void> {
        await this.cacheManager.clear();
    }

    /**
     * 插件卸载方法
     */
    public uninstall(kernel: MicroCoreKernel): void {
        // 清理资源
        this.workerManager.destroy();
        this.clearCache();

        // 从内核移除扩展功能
        delete (kernel as any).workerLoader;

        // 从插件系统注销
        kernel.plugins.unregister(this.name);
    }
}

/**
 * 创建 Worker 加载器插件实例
 */
export function createWorkerLoaderPlugin(options: WorkerLoaderOptions = {}): WorkerLoaderPlugin {
    return new WorkerLoaderPlugin(options);
}

/**
 * 默认导出插件类
 */
export default WorkerLoaderPlugin;