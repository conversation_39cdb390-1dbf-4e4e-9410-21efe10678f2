/**
 * Worker 管理器 - 管理 Worker 池和任务调度
 */

import type {
    WorkerLoaderOptions,
    LoadTask,
    LoadResult,
    WorkerMessage,
    WorkerPool,
    LoaderStats
} from './types';

export class WorkerManager {
    private options: Required<WorkerLoaderOptions>;
    private workerPool: WorkerPool;
    private taskMap: Map<string, {
        resolve: (result: LoadResult) => void;
        reject: (error: Error) => void;
        task: LoadTask;
    }>;
    private stats: LoaderStats;
    private workerScript: string;

    constructor(options: WorkerLoaderOptions = {}) {
        this.options = {
            maxWorkers: options.maxWorkers || 4,
            cacheStrategy: options.cacheStrategy || 'memory',
            enableProgressTracking: options.enableProgressTracking || true,
            timeout: options.timeout || 30000,
            retryCount: options.retryCount || 3,
            concurrency: options.concurrency || 6
        };

        this.workerPool = {
            available: [],
            busy: new Map(),
            queue: []
        };

        this.taskMap = new Map();

        this.stats = {
            totalTasks: 0,
            successTasks: 0,
            failedTasks: 0,
            cacheHits: 0,
            averageLoadTime: 0,
            totalBytes: 0
        };

        this.workerScript = this.createWorkerScript();
        this.initializeWorkerPool();
    }

    /**
     * 创建 Worker 脚本
     */
    private createWorkerScript(): string {
        // 这里我们将 worker-script.ts 的内容内联
        return `
      // Worker 脚本内容
      const postMessage = (message) => self.postMessage(message);
      
      const sendProgress = (taskId, loaded, total) => {
        postMessage({
          type: 'progress',
          taskId,
          data: { taskId, loaded, total, percentage: total > 0 ? Math.round((loaded / total) * 100) : 0 }
        });
      };
      
      const sendError = (taskId, error) => {
        postMessage({ type: 'error', taskId, data: { error } });
      };
      
      const sendResult = (taskId, content, loadTime, size) => {
        postMessage({
          type: 'result',
          taskId,
          data: { id: taskId, success: true, content, loadTime, size }
        });
      };
      
      const loadResource = async (task) => {
        const startTime = Date.now();
        const controller = new AbortController();
        
        const timeoutId = setTimeout(() => controller.abort(), task.timeout || 30000);
        
        try {
          const response = await fetch(task.url, {
            signal: controller.signal,
            headers: { 'Cache-Control': 'no-cache' }
          });
          
          if (!response.ok) {
            throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
          }
          
          const contentLength = response.headers.get('content-length');
          const total = contentLength ? parseInt(contentLength, 10) : 0;
          let loaded = 0;
          
          const reader = response.body?.getReader();
          if (!reader) throw new Error('无法创建响应流读取器');
          
          const chunks = [];
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            chunks.push(value);
            loaded += value.length;
            
            if (total > 0) sendProgress(task.id, loaded, total);
          }
          
          const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const result = new Uint8Array(totalLength);
          let offset = 0;
          
          for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
          }
          
          let content;
          switch (task.type) {
            case 'script':
            case 'style':
            case 'json':
            case 'text':
              content = new TextDecoder().decode(result);
              break;
            default:
              content = result.buffer;
              break;
          }
          
          const loadTime = Date.now() - startTime;
          sendResult(task.id, content, loadTime, totalLength);
          
        } catch (error) {
          sendError(task.id, error.message || '加载失败');
        } finally {
          clearTimeout(timeoutId);
        }
      };
      
      self.addEventListener('message', (event) => {
        const { type, data } = event.data;
        if (type === 'load') {
          loadResource(data).catch(error => sendError(data.id, error.message || '加载失败'));
        }
      });
    `;
    }

    /**
     * 初始化 Worker 池
     */
    private initializeWorkerPool(): void {
        for (let i = 0; i < this.options.maxWorkers; i++) {
            const worker = this.createWorker();
            this.workerPool.available.push(worker);
        }
    }

    /**
     * 创建 Worker
     */
    private createWorker(): Worker {
        const blob = new Blob([this.workerScript], { type: 'application/javascript' });
        const workerUrl = URL.createObjectURL(blob);
        const worker = new Worker(workerUrl);

        worker.addEventListener('message', (event: MessageEvent<WorkerMessage>) => {
            this.handleWorkerMessage(worker, event.data);
        });

        worker.addEventListener('error', (error: ErrorEvent) => {
            console.error('Worker 错误:', error);
            this.handleWorkerError(worker, error);
        });

        return worker;
    }

    /**
     * 处理 Worker 消息
     */
    private handleWorkerMessage(worker: Worker, message: WorkerMessage): void {
        const { type, taskId, data } = message;

        if (!taskId) return;

        const taskInfo = this.taskMap.get(taskId);
        if (!taskInfo) return;

        switch (type) {
            case 'result':
                this.handleTaskResult(worker, taskInfo, data);
                break;
            case 'error':
                this.handleTaskError(worker, taskInfo, data.error);
                break;
            case 'progress':
                this.handleTaskProgress(taskInfo, data);
                break;
        }
    }

    /**
     * 处理任务结果
     */
    private handleTaskResult(worker: Worker, taskInfo: any, result: LoadResult): void {
        // 更新统计信息
        this.stats.successTasks++;
        this.stats.totalBytes += result.size;
        this.updateAverageLoadTime(result.loadTime);

        // 释放 Worker
        this.releaseWorker(worker);

        // 解析 Promise
        taskInfo.resolve(result);
        this.taskMap.delete(result.id);

        // 处理队列中的下一个任务
        this.processQueue();
    }

    /**
     * 处理任务错误
     */
    private handleTaskError(worker: Worker, taskInfo: any, error: string): void {
        const task = taskInfo.task;

        if (task.retryCount < this.options.retryCount) {
            // 重试任务
            task.retryCount++;
            this.workerPool.queue.unshift(task);
            this.releaseWorker(worker);
            this.processQueue();
        } else {
            // 任务失败
            this.stats.failedTasks++;
            this.releaseWorker(worker);
            taskInfo.reject(new Error(error));
            this.taskMap.delete(task.id);
            this.processQueue();
        }
    }

    /**
     * 处理任务进度
     */
    private handleTaskProgress(taskInfo: any, progressData: any): void {
        if (this.options.enableProgressTracking) {
            // 可以在这里触发进度事件
            console.log(`任务 ${taskInfo.task.id} 进度: ${progressData.percentage}%`);
        }
    }

    /**
     * 处理 Worker 错误
     */
    private handleWorkerError(worker: Worker, error: ErrorEvent): void {
        console.error('Worker 发生错误:', error);

        // 找到使用此 Worker 的任务
        const task = this.workerPool.busy.get(worker);
        if (task) {
            const taskInfo = this.taskMap.get(task.id);
            if (taskInfo) {
                this.handleTaskError(worker, taskInfo, error.message || 'Worker 错误');
            }
        }
    }

    /**
     * 释放 Worker
     */
    private releaseWorker(worker: Worker): void {
        this.workerPool.busy.delete(worker);
        this.workerPool.available.push(worker);
    }

    /**
     * 处理任务队列
     */
    private processQueue(): void {
        while (this.workerPool.queue.length > 0 && this.workerPool.available.length > 0) {
            const task = this.workerPool.queue.shift()!;
            const worker = this.workerPool.available.pop()!;

            this.workerPool.busy.set(worker, task);

            worker.postMessage({
                type: 'load',
                data: task
            });
        }
    }

    /**
     * 更新平均加载时间
     */
    private updateAverageLoadTime(loadTime: number): void {
        const totalTasks = this.stats.successTasks;
        this.stats.averageLoadTime =
            (this.stats.averageLoadTime * (totalTasks - 1) + loadTime) / totalTasks;
    }

    /**
     * 加载资源
     */
    public loadResource(
        url: string,
        type: LoadTask['type'] = 'text',
        priority: LoadTask['priority'] = 'normal'
    ): Promise<LoadResult> {
        return new Promise((resolve, reject) => {
            const task: LoadTask = {
                id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                url,
                type,
                priority,
                createdAt: Date.now(),
                retryCount: 0
            };

            this.stats.totalTasks++;

            this.taskMap.set(task.id, { resolve, reject, task });

            // 根据优先级插入队列
            if (priority === 'high') {
                this.workerPool.queue.unshift(task);
            } else {
                this.workerPool.queue.push(task);
            }

            this.processQueue();
        });
    }

    /**
     * 批量加载资源
     */
    public async loadResources(urls: string[]): Promise<LoadResult[]> {
        const promises = urls.map(url => this.loadResource(url));
        return Promise.all(promises);
    }

    /**
     * 预加载资源
     */
    public preloadResources(urls: string[]): void {
        urls.forEach(url => {
            this.loadResource(url, 'text', 'low').catch(() => {
                // 预加载失败不影响主流程
            });
        });
    }

    /**
     * 获取统计信息
     */
    public getStats(): LoaderStats {
        return { ...this.stats };
    }

    /**
     * 清理资源
     */
    public destroy(): void {
        // 终止所有 Worker
        [...this.workerPool.available, ...this.workerPool.busy.keys()].forEach(worker => {
            worker.terminate();
        });

        // 清理数据
        this.workerPool.available = [];
        this.workerPool.busy.clear();
        this.workerPool.queue = [];
        this.taskMap.clear();
    }
}