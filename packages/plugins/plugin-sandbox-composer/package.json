{"name": "@micro-core/plugin-sandbox-composer", "version": "0.1.0", "description": "沙箱组合器插件，允许将多种沙箱策略组合使用", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "sandbox", "composer", "plugin", "微前端"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0", "tsup": "^8.0.0", "vitest": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-sandbox-composer"}, "publishConfig": {"access": "public"}}