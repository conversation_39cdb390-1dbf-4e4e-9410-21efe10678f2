/**
 * 沙箱组合器类型定义
 */

export type SandboxStrategy =
    | 'proxy'
    | 'iframe'
    | 'webcomponent'
    | 'namespace'
    | 'defineproperty'
    | 'federation';

export interface SandboxComposerPluginOptions {
    /** 是否启用自动组合 */
    enableAutoCompose?: boolean;
    /** 默认沙箱策略 */
    defaultStrategy?: SandboxStrategy[];
    /** 是否启用日志 */
    enableLogging?: boolean;
}

export interface SandboxComposerOptions {
    /** 沙箱策略列表 */
    strategies: SandboxStrategy[];
    /** 是否启用日志 */
    enableLogging?: boolean;
}

export interface SandboxInstance {
    /** 沙箱类型 */
    type: SandboxStrategy;
    /** 沙箱实例 */
    instance: any;
    /** 激活沙箱 */
    activate: () => void;
    /** 停用沙箱 */
    deactivate: () => void;
    /** 销毁沙箱 */
    destroy: () => void;
}

export interface ProxySandboxInstance {
    fakeWindow: Record<string, any>;
    proxy: ProxyHandler<any>;
}

export interface IframeSandboxInstance {
    iframe: HTMLIFrameElement;
    contentWindow: Window | null;
}

export interface WebComponentSandboxInstance {
    container: HTMLElement;
    shadowRoot: ShadowRoot;
}

export interface NamespaceSandboxInstance {
    namespace: string;
    context: Record<string, any>;
}

export interface DefinePropertySandboxInstance {
    originalDescriptors: Map<string, PropertyDescriptor>;
    modifiedProps: Set<string>;
}

export interface FederationSandboxInstance {
    moduleCache: Map<string, any>;
    sharedScope: Record<string, any>;
}