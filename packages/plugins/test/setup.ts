/**
 * @fileoverview Vitest 测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { vi, afterEach } from 'vitest';

// Mock browser APIs
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/',
    search: '',
    hash: '',
    href: 'http://localhost:3000/',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
  writable: true,
});

Object.defineProperty(window, 'history', {
  value: {
    pushState: vi.fn(),
    replaceState: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    go: vi.fn(),
    length: 1,
    state: null,
  },
  writable: true,
});

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  },
  writable: true,
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  },
  writable: true,
});

// Mock fetch
global.fetch = vi.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
  } as Response)
);

// Mock console methods to avoid noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByName: vi.fn(() => []),
    getEntriesByType: vi.fn(() => []),
  },
  writable: true,
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn(() => []),
}));

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id));

// Mock MessageChannel
global.MessageChannel = vi.fn().mockImplementation(() => ({
  port1: {
    postMessage: vi.fn(),
    onmessage: null,
    close: vi.fn(),
  },
  port2: {
    postMessage: vi.fn(),
    onmessage: null,
    close: vi.fn(),
  },
}));

// Mock Worker
global.Worker = vi.fn().mockImplementation(() => ({
  postMessage: vi.fn(),
  terminate: vi.fn(),
  onmessage: null,
  onerror: null,
}));

// Mock WebAssembly
global.WebAssembly = {
  compile: vi.fn(() => Promise.resolve({})),
  instantiate: vi.fn(() => Promise.resolve({ instance: {}, module: {} })),
  validate: vi.fn(() => true),
  Module: vi.fn(),
  Instance: vi.fn(),
  Memory: vi.fn(),
  Table: vi.fn(),
  CompileError: Error,
  RuntimeError: Error,
  LinkError: Error,
} as any;

// Mock Proxy support check
if (typeof Proxy === 'undefined') {
  global.Proxy = class MockProxy {
    constructor(target: any, handler: any) {
      return new Proxy(target, handler);
    }
  } as any;
}

// Setup DOM environment
document.body.innerHTML = '<div id="app"></div>';

// Mock CSS support
Object.defineProperty(window, 'CSS', {
  value: {
    supports: vi.fn(() => true),
  },
  writable: true,
});

// Mock getComputedStyle
window.getComputedStyle = vi.fn(() => ({
  getPropertyValue: vi.fn(() => ''),
  setProperty: vi.fn(),
  removeProperty: vi.fn(),
})) as any;

// Mock matchMedia
window.matchMedia = vi.fn((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Mock crypto
Object.defineProperty(window, 'crypto', {
  value: {
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: vi.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9)),
  },
  writable: true,
});

// Mock URL
global.URL = class MockURL {
  constructor(public href: string, base?: string) {
    if (base) {
      this.href = new URL(href, base).href;
    }
  }
  
  get origin() { return 'http://localhost:3000'; }
  get protocol() { return 'http:'; }
  get host() { return 'localhost:3000'; }
  get hostname() { return 'localhost'; }
  get port() { return '3000'; }
  get pathname() { return '/'; }
  get search() { return ''; }
  get hash() { return ''; }
  
  toString() { return this.href; }
} as any;

// Mock URLSearchParams
global.URLSearchParams = class MockURLSearchParams {
  private params = new Map<string, string>();
  
  constructor(init?: string | string[][] | Record<string, string>) {
    if (typeof init === 'string') {
      init.split('&').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key) this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
      });
    } else if (Array.isArray(init)) {
      init.forEach(([key, value]) => this.params.set(key, value));
    } else if (init) {
      Object.entries(init).forEach(([key, value]) => this.params.set(key, value));
    }
  }
  
  get(name: string) { return this.params.get(name); }
  set(name: string, value: string) { this.params.set(name, value); }
  has(name: string) { return this.params.has(name); }
  delete(name: string) { this.params.delete(name); }
  toString() { 
    return Array.from(this.params.entries())
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
  }
} as any;

// Mock MicroCore kernel with comprehensive API
global.mockKernel = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  getEventBus: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    once: vi.fn(),
    removeAllListeners: vi.fn()
  })),
  getApp: vi.fn(),
  getAllApps: vi.fn(),
  registerHook: vi.fn((hookName: string, callback: Function) => {
    // Store hooks for potential execution in tests
    if (!global.mockKernel._hooks) {
      global.mockKernel._hooks = new Map();
    }
    if (!global.mockKernel._hooks.has(hookName)) {
      global.mockKernel._hooks.set(hookName, []);
    }
    global.mockKernel._hooks.get(hookName).push(callback);
    return Promise.resolve();
  }),
  unregisterHook: vi.fn(),
  executeHook: vi.fn(),
  getPlugin: vi.fn(),
  hasPlugin: vi.fn(),
  installPlugin: vi.fn(),
  uninstallPlugin: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
  getState: vi.fn(),
  setState: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  loadApp: vi.fn(),
  unloadApp: vi.fn(),
  mountApp: vi.fn(),
  unmountApp: vi.fn(),
  startApp: vi.fn(),
  stopApp: vi.fn(),
  getAppStatus: vi.fn(),
  isAppActive: vi.fn(),
  getActiveApps: vi.fn(),
  prefetchApp: vi.fn(),
  preloadApp: vi.fn(),
  createSandbox: vi.fn(),
  destroySandbox: vi.fn(),
  getSandbox: vi.fn(),
  activateSandbox: vi.fn(),
  deactivateSandbox: vi.fn(),
  registerRoute: vi.fn(),
  unregisterRoute: vi.fn(),
  navigate: vi.fn(),
  getCurrentRoute: vi.fn(),
  getRoutes: vi.fn(),
  matchRoute: vi.fn(),
  addGuard: vi.fn(),
  removeGuard: vi.fn(),
  broadcast: vi.fn(),
  sendMessage: vi.fn(),
  onMessage: vi.fn(),
  offMessage: vi.fn(),
  createChannel: vi.fn(),
  destroyChannel: vi.fn(),
  getChannel: vi.fn(),
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  startPerformanceMonitoring: vi.fn(),
  stopPerformanceMonitoring: vi.fn(),
  getPerformanceMetrics: vi.fn(),
  clearPerformanceMetrics: vi.fn(),
  addMetric: vi.fn(),
  removeMetric: vi.fn(),
  getMetrics: vi.fn(),
  exportMetrics: vi.fn(),
  version: '1.0.0',
  name: 'micro-core',
  status: 'ready',
  plugins: new Map(),
  apps: new Map(),
  routes: new Map(),
  sandboxes: new Map(),
  channels: new Map(),
  hooks: new Map(),
  guards: [],
  config: {},
  state: {},
  subscribers: new Map(),
  metrics: new Map(),
  performance: {
    startTime: Date.now(),
    endTime: null,
    duration: 0,
    memory: 0,
    cpu: 0,
  },
};

// Setup cleanup after each test
afterEach(() => {
  vi.clearAllMocks();
  document.body.innerHTML = '<div id="app"></div>';
  
  // Reset location
  Object.assign(window.location, {
    pathname: '/',
    search: '',
    hash: '',
    href: 'http://localhost:3000/',
  });
  
  // Clear storage
  (window.localStorage.clear as any)();
  (window.sessionStorage.clear as any)();
  
  // Reset mock kernel
  if (global.mockKernel) {
    Object.keys(global.mockKernel).forEach(key => {
      if (typeof global.mockKernel[key] === 'function') {
        global.mockKernel[key].mockClear();
      } else if (global.mockKernel[key] instanceof Map) {
        global.mockKernel[key].clear();
      } else if (Array.isArray(global.mockKernel[key])) {
        global.mockKernel[key].length = 0;
      } else if (typeof global.mockKernel[key] === 'object' && global.mockKernel[key] !== null) {
        Object.keys(global.mockKernel[key]).forEach(subKey => {
          delete global.mockKernel[key][subKey];
        });
      }
    });
  }
});
