/**
 * History API 适配器 - 统一处理浏览器历史记录
 */

export interface HistoryAdapterOptions {
    mode?: 'hash' | 'history' | 'memory';
    base?: string;
}

export type RouteChangeListener = (location: Location) => void;

export class HistoryAdapter {
    private options: HistoryAdapterOptions;
    private listeners = new Set<RouteChangeListener>();
    private originalPushState?: typeof history.pushState;
    private originalReplaceState?: typeof history.replaceState;
    private memoryHistory: string[] = [];
    private memoryIndex = -1;

    constructor(options: HistoryAdapterOptions = {}) {
        this.options = {
            mode: 'history',
            base: '/',
            ...options
        };

        this.init();
    }

    /**
     * 初始化历史记录适配器
     */
    private init(): void {
        if (this.options.mode === 'memory') {
            this.initMemoryMode();
        } else {
            this.initBrowserMode();
        }

        console.log(`[HistoryAdapter] 历史记录适配器已初始化，模式: ${this.options.mode}`);
    }

    /**
     * 初始化浏览器模式
     */
    private initBrowserMode(): void {
        // 保存原始方法
        this.originalPushState = history.pushState;
        this.originalReplaceState = history.replaceState;

        // 重写 pushState
        history.pushState = (state: any, title: string, url?: string | URL | null) => {
            this.originalPushState!.call(history, state, title, url);
            this.notifyListeners();
        };

        // 重写 replaceState
        history.replaceState = (state: any, title: string, url?: string | URL | null) => {
            this.originalReplaceState!.call(history, state, title, url);
            this.notifyListeners();
        };

        // 监听 popstate 事件
        window.addEventListener('popstate', this.handlePopState);

        // 监听 hashchange 事件（hash 模式）
        if (this.options.mode === 'hash') {
            window.addEventListener('hashchange', this.handleHashChange);
        }
    }

    /**
     * 初始化内存模式
     */
    private initMemoryMode(): void {
        // 内存模式下，初始化历史记录
        this.memoryHistory = [this.getCurrentPath()];
        this.memoryIndex = 0;
    }

    /**
     * 处理 popstate 事件
     */
    private handlePopState = (): void => {
        this.notifyListeners();
    };

    /**
     * 处理 hashchange 事件
     */
    private handleHashChange = (): void => {
        this.notifyListeners();
    };

    /**
     * 通知监听器
     */
    private notifyListeners(): void {
        const location = this.options.mode === 'memory'
            ? this.createMemoryLocation()
            : window.location;

        for (const listener of this.listeners) {
            try {
                listener(location);
            } catch (error) {
                console.error('[HistoryAdapter] 路由监听器执行失败:', error);
            }
        }
    }

    /**
     * 创建内存模式的 location 对象
     */
    private createMemoryLocation(): Location {
        const path = this.memoryHistory[this.memoryIndex] || '/';
        const url = new URL(path, 'http://localhost');

        return {
            href: url.href,
            origin: url.origin,
            protocol: url.protocol,
            host: url.host,
            hostname: url.hostname,
            port: url.port,
            pathname: url.pathname,
            search: url.search,
            hash: url.hash,
            assign: () => { },
            replace: () => { },
            reload: () => { }
        } as Location;
    }

    /**
     * 导航到指定路径
     */
    navigate(path: string, replace = false): void {
        const fullPath = this.resolvePath(path);

        if (this.options.mode === 'memory') {
            this.navigateMemory(fullPath, replace);
        } else if (this.options.mode === 'hash') {
            this.navigateHash(fullPath, replace);
        } else {
            this.navigateHistory(fullPath, replace);
        }
    }

    /**
     * 内存模式导航
     */
    private navigateMemory(path: string, replace: boolean): void {
        if (replace) {
            this.memoryHistory[this.memoryIndex] = path;
        } else {
            this.memoryIndex++;
            this.memoryHistory = this.memoryHistory.slice(0, this.memoryIndex);
            this.memoryHistory.push(path);
        }

        this.notifyListeners();
    }

    /**
     * Hash 模式导航
     */
    private navigateHash(path: string, replace: boolean): void {
        const hash = '#' + path.replace(/^#/, '');

        if (replace) {
            window.location.replace(window.location.pathname + window.location.search + hash);
        } else {
            window.location.hash = hash;
        }
    }

    /**
     * History 模式导航
     */
    private navigateHistory(path: string, replace: boolean): void {
        if (replace) {
            history.replaceState(null, '', path);
        } else {
            history.pushState(null, '', path);
        }
    }

    /**
     * 解析路径
     */
    private resolvePath(path: string): string {
        const base = this.options.base || '/';

        // 如果是绝对路径，直接返回
        if (path.startsWith('/')) {
            return path;
        }

        // 相对路径，拼接 base
        return base.replace(/\/$/, '') + '/' + path.replace(/^\//, '');
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        if (this.options.mode === 'memory') {
            return this.memoryHistory[this.memoryIndex] || '/';
        } else if (this.options.mode === 'hash') {
            return window.location.hash.replace(/^#/, '') || '/';
        } else {
            return window.location.pathname + window.location.search + window.location.hash;
        }
    }

    /**
     * 添加路由变化监听器
     */
    onRouteChange(listener: RouteChangeListener): () => void {
        this.listeners.add(listener);

        // 返回取消监听的函数
        return () => {
            this.listeners.delete(listener);
        };
    }

    /**
     * 后退
     */
    back(): void {
        if (this.options.mode === 'memory') {
            if (this.memoryIndex > 0) {
                this.memoryIndex--;
                this.notifyListeners();
            }
        } else {
            history.back();
        }
    }

    /**
     * 前进
     */
    forward(): void {
        if (this.options.mode === 'memory') {
            if (this.memoryIndex < this.memoryHistory.length - 1) {
                this.memoryIndex++;
                this.notifyListeners();
            }
        } else {
            history.forward();
        }
    }

    /**
     * 获取历史记录长度
     */
    getHistoryLength(): number {
        if (this.options.mode === 'memory') {
            return this.memoryHistory.length;
        } else {
            return history.length;
        }
    }

    /**
     * 销毁适配器
     */
    destroy(): void {
        // 恢复原始方法
        if (this.originalPushState) {
            history.pushState = this.originalPushState;
        }
        if (this.originalReplaceState) {
            history.replaceState = this.originalReplaceState;
        }

        // 移除事件监听器
        window.removeEventListener('popstate', this.handlePopState);
        window.removeEventListener('hashchange', this.handleHashChange);

        // 清理监听器
        this.listeners.clear();

        // 清理内存历史记录
        this.memoryHistory = [];
        this.memoryIndex = -1;

        console.log('[HistoryAdapter] 历史记录适配器已销毁');
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        mode: string;
        currentPath: string;
        historyLength: number;
        listenersCount: number;
    } {
        return {
            mode: this.options.mode || 'history',
            currentPath: this.getCurrentPath(),
            historyLength: this.getHistoryLength(),
            listenersCount: this.listeners.size
        };
    }
}