import type { MicroCoreKernel } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * 路由管理器
 * 负责管理微前端应用的路由状态和导航
 */
export class RouterManager {
    private kernel: MicroCoreKernel;
    private currentRoute: string = '';
    private routeHistory: string[] = [];
    private routeGuards: RouteGuard[] = [];
    private isListening = false;

    constructor(kernel: MicroCoreKernel) {
        this.kernel = kernel;
        this.currentRoute = this.getCurrentPath();
        this.routeHistory.push(this.currentRoute);
    }

    /**
     * 开始监听路由变化
     */
    startListening(): void {
        if (this.isListening) {
            return;
        }

        this.isListening = true;
        this.setupRouteListener();
        logger.info('路由管理器开始监听');
    }

    /**
     * 停止监听路由变化
     */
    stopListening(): void {
        if (!this.isListening) {
            return;
        }

        this.isListening = false;
        // 移除事件监听器
        window.removeEventListener('popstate', this.handleRouteChange);
        logger.info('路由管理器停止监听');
    }

    /**
     * 导航到指定路径
     */
    async navigate(path: string, replace = false): Promise<void> {
        const from = this.currentRoute;

        // 执行路由守卫
        const canNavigate = await this.executeRouteGuards(from, path);
        if (!canNavigate) {
            logger.warn(`路由导航被守卫阻止: ${from} -> ${path}`);
            return;
        }

        // 更新浏览器历史
        if (replace) {
            history.replaceState(null, '', path);
        } else {
            history.pushState(null, '', path);
        }

        // 更新内部状态
        this.updateRoute(path, from);
    }

    /**
     * 返回上一页
     */
    goBack(): void {
        if (this.routeHistory.length > 1) {
            history.back();
        }
    }

    /**
     * 前进到下一页
     */
    goForward(): void {
        history.forward();
    }

    /**
     * 添加路由守卫
     */
    addGuard(guard: RouteGuard): void {
        this.routeGuards.push(guard);
        logger.debug(`添加路由守卫: ${guard.name || 'anonymous'}`);
    }

    /**
     * 移除路由守卫
     */
    removeGuard(guard: RouteGuard): void {
        const index = this.routeGuards.indexOf(guard);
        if (index > -1) {
            this.routeGuards.splice(index, 1);
            logger.debug(`移除路由守卫: ${guard.name || 'anonymous'}`);
        }
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        return window.location.pathname + window.location.search + window.location.hash;
    }

    /**
     * 获取路由历史
     */
    getHistory(): string[] {
        return [...this.routeHistory];
    }

    /**
     * 设置路由监听器
     */
    private setupRouteListener(): void {
        // 监听 popstate 事件
        window.addEventListener('popstate', this.handleRouteChange);

        // 拦截 pushState 和 replaceState
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = (...args) => {
            originalPushState.apply(history, args);
            this.handleRouteChange();
        };

        history.replaceState = (...args) => {
            originalReplaceState.apply(history, args);
            this.handleRouteChange();
        };
    }

    /**
     * 处理路由变化
     */
    private handleRouteChange = (): void => {
        const newPath = this.getCurrentPath();
        const oldPath = this.currentRoute;

        if (newPath !== oldPath) {
            this.updateRoute(newPath, oldPath);
        }
    };

    /**
     * 更新路由状态
     */
    private updateRoute(newPath: string, oldPath: string): void {
        this.currentRoute = newPath;
        this.routeHistory.push(newPath);

        // 限制历史记录长度
        if (this.routeHistory.length > 100) {
            this.routeHistory.shift();
        }

        // 触发路由变化事件
        this.kernel.emit('route:changed', { from: oldPath, to: newPath });

        logger.debug(`路由变化: ${oldPath} -> ${newPath}`);
    }

    /**
     * 执行路由守卫
     */
    private async executeRouteGuards(from: string, to: string): Promise<boolean> {
        for (const guard of this.routeGuards) {
            try {
                const result = await guard.canActivate(from, to);
                if (!result) {
                    return false;
                }
            } catch (error) {
                logger.error(`路由守卫执行失败:`, error);
                return false;
            }
        }
        return true;
    }

    /**
     * 销毁路由管理器
     */
    destroy(): void {
        this.stopListening();
        this.routeGuards.length = 0;
        this.routeHistory.length = 0;
        logger.info('路由管理器已销毁');
    }
}

/**
 * 路由守卫接口
 */
export interface RouteGuard {
    name?: string;
    canActivate(from: string, to: string): boolean | Promise<boolean>;
}

/**
 * 路由配置接口
 */
export interface RouteConfig {
    path: string;
    component?: any;
    children?: RouteConfig[];
    meta?: Record<string, any>;
    beforeEnter?: RouteGuard;
}