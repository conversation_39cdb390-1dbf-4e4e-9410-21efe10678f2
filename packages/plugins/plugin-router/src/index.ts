/**
 * 路由插件
 * 提供微前端应用的路由管理功能
 */

import type { Plugin } from '@micro-core/core';
import { EventEmitter } from 'events';

/**
 * 路由配置接口
 */
export interface RouteConfig {
    /** 路由路径 */
    path: string;
    /** 对应的应用名称 */
    app: string;
    /** 路由参数 */
    params?: Record<string, any>;
    /** 路由元数据 */
    meta?: Record<string, any>;
}

/**
 * 路由插件选项
 */
export interface RouterPluginOptions {
    /** 路由模式 */
    mode?: 'hash' | 'history' | 'memory';
    /** 基础路径 */
    base?: string;
    /** 是否启用路由守卫 */
    enableGuards?: boolean;
}

/**
 * 路由管理器
 */
export class RouterManager extends EventEmitter {
    private routes = new Map<string, RouteConfig>();
    private currentRoute: RouteConfig | null = null;
    private mode: 'hash' | 'history' | 'memory' = 'history';
    private basePath = '';
    private isStarted = false;

    constructor(options: RouterPluginOptions = {}) {
        super();
        this.mode = options.mode || 'history';
        this.basePath = options.base || '';
    }

    /**
     * 启动路由管理器
     */
    start(): void {
        if (this.isStarted) {
            return;
        }

        this.isStarted = true;

        // 监听路由变化
        if (this.mode === 'hash') {
            window.addEventListener('hashchange', this.handleRouteChange.bind(this));
        } else if (this.mode === 'history') {
            window.addEventListener('popstate', this.handleRouteChange.bind(this));
        }

        // 处理初始路由
        this.handleRouteChange();
    }

    /**
     * 停止路由管理器
     */
    stop(): void {
        if (!this.isStarted) {
            return;
        }

        this.isStarted = false;

        // 移除事件监听
        if (this.mode === 'hash') {
            window.removeEventListener('hashchange', this.handleRouteChange.bind(this));
        } else if (this.mode === 'history') {
            window.removeEventListener('popstate', this.handleRouteChange.bind(this));
        }
    }

    /**
     * 注册路由
     */
    registerRoute(config: RouteConfig): void {
        this.routes.set(config.path, config);
        this.emit('route:registered', config);
    }

    /**
     * 取消注册路由
     */
    unregisterRoute(path: string): void {
        const route = this.routes.get(path);
        if (route) {
            this.routes.delete(path);
            this.emit('route:unregistered', route);
        }
    }

    /**
     * 导航到指定路径
     */
    navigate(path: string, replace = false): void {
        const fullPath = this.getFullPath(path);

        if (this.mode === 'hash') {
            if (replace) {
                window.location.replace(`#${fullPath}`);
            } else {
                window.location.hash = fullPath;
            }
        } else if (this.mode === 'history') {
            if (replace) {
                window.history.replaceState(null, '', fullPath);
            } else {
                window.history.pushState(null, '', fullPath);
            }
            this.handleRouteChange();
        } else if (this.mode === 'memory') {
            // 内存模式，直接触发路由变化
            this.handleRouteChange(fullPath);
        }
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        if (this.mode === 'hash') {
            return window.location.hash.slice(1) || '/';
        } else if (this.mode === 'history') {
            return window.location.pathname + window.location.search;
        } else {
            return this.currentRoute?.path || '/';
        }
    }

    /**
     * 获取当前路由
     */
    getCurrentRoute(): RouteConfig | null {
        return this.currentRoute;
    }

    /**
     * 匹配路由
     */
    matchRoute(path: string): RouteConfig | null {
        // 精确匹配
        if (this.routes.has(path)) {
            return this.routes.get(path)!;
        }

        // 模糊匹配
        for (const [routePath, route] of this.routes) {
            if (this.isPathMatch(path, routePath)) {
                return route;
            }
        }

        return null;
    }

    /**
     * 处理路由变化
     */
    private handleRouteChange(customPath?: string): void {
        const currentPath = customPath || this.getCurrentPath();
        const matchedRoute = this.matchRoute(currentPath);

        if (matchedRoute !== this.currentRoute) {
            const previousRoute = this.currentRoute;
            this.currentRoute = matchedRoute;

            this.emit('route:changed', {
                from: previousRoute,
                to: matchedRoute,
                path: currentPath
            });
        }
    }

    /**
     * 检查路径是否匹配
     */
    private isPathMatch(currentPath: string, routePath: string): boolean {
        // 简单的通配符匹配
        if (routePath.includes('*')) {
            const regexPattern = routePath.replace(/\*/g, '.*');
            const regex = new RegExp(`^${regexPattern}$`);
            return regex.test(currentPath);
        }

        // 参数匹配 (例如: /user/:id)
        if (routePath.includes(':')) {
            const routeSegments = routePath.split('/');
            const pathSegments = currentPath.split('/');

            if (routeSegments.length !== pathSegments.length) {
                return false;
            }

            return routeSegments.every((segment, index) => {
                return segment.startsWith(':') || segment === pathSegments[index];
            });
        }

        // 前缀匹配
        return currentPath.startsWith(routePath);
    }

    /**
     * 获取完整路径
     */
    private getFullPath(path: string): string {
        if (path.startsWith('/')) {
            return this.basePath + path;
        }
        return this.basePath + '/' + path;
    }

    /**
     * 获取所有路由
     */
    getRoutes(): RouteConfig[] {
        return Array.from(this.routes.values());
    }

    /**
     * 清空所有路由
     */
    clear(): void {
        this.routes.clear();
        this.currentRoute = null;
    }
}

/**
 * 路由插件类
 */
export class RouterPlugin implements Plugin {
    name = 'router';
    version = '1.0.0';
    private manager: RouterManager;

    constructor(private options: RouterPluginOptions = {}) {
        this.manager = new RouterManager(options);
    }

    /**
     * 安装插件
     */
    install(kernel: any): void {
        // 将路由管理器注册到内核
        kernel.routerManager = this.manager;

        // 监听应用注册事件，自动注册路由
        if (kernel.on) {
            kernel.on('app:registered', (app: any) => {
                if (typeof app.config.activeWhen === 'string') {
                    this.manager.registerRoute({
                        path: app.config.activeWhen,
                        app: app.name
                    });
                }
            });

            // 监听应用卸载事件，自动取消注册路由
            kernel.on('app:unregistered', (app: any) => {
                if (typeof app.config.activeWhen === 'string') {
                    this.manager.unregisterRoute(app.config.activeWhen);
                }
            });
        }

        // 启动路由管理器
        this.manager.start();
    }

    /**
     * 卸载插件
     */
    uninstall(): void {
        this.manager.stop();
        this.manager.clear();
    }

    /**
     * 获取路由管理器
     */
    getManager(): RouterManager {
        return this.manager;
    }
}

// 默认导出
export default RouterPlugin;