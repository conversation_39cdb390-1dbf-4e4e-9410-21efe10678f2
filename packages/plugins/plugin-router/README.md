# @micro-core/plugin-router

微前端路由管理插件，提供统一的路由注册、导航和守卫功能。

## 功能特性

- 🛣️ **统一路由管理**: 跨应用的统一路由注册和管理
- 🧭 **智能导航**: 支持编程式和声明式导航
- 🛡️ **路由守卫**: 全局和局部路由守卫支持
- 🔄 **多模式支持**: Hash 模式和 History 模式
- 📱 **响应式**: 路由状态的响应式更新
- 🎯 **精确匹配**: 支持精确匹配和模糊匹配
- 🔗 **嵌套路由**: 支持多层嵌套路由结构
- ⚡ **懒加载**: 支持路由组件的懒加载

## 安装

```bash
pnpm add @micro-core/plugin-router
```

## 基础用法

### 插件注册

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';

const kernel = new MicroCoreKernel();

// 注册路由插件
await kernel.installPlugin(new RouterPlugin({
  mode: 'hash', // 'hash' | 'history'
  base: '/',
  enableGuards: true,
  enableDebug: false,
  fallback: '/404'
}));
```

### 路由注册

```typescript
// 注册应用路由
routerPlugin.registerRoutes('app1', [
  {
    path: '/',
    name: 'home',
    component: 'HomeComponent',
    meta: { title: '首页' }
  },
  {
    path: '/users/:id',
    name: 'user-detail',
    component: 'UserDetailComponent',
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    component: 'AdminLayout',
    children: [
      {
        path: 'dashboard',
        name: 'admin-dashboard',
        component: 'DashboardComponent'
      },
      {
        path: 'users',
        name: 'admin-users',
        component: 'UsersComponent'
      }
    ]
  }
]);
```

### 路由导航

```typescript
// 编程式导航
await routerPlugin.push('/users/123');
await routerPlugin.push({ name: 'user-detail', params: { id: '123' } });
await routerPlugin.replace('/login');

// 历史导航
routerPlugin.go(-1); // 后退
routerPlugin.go(1);  // 前进
routerPlugin.back(); // 后退
routerPlugin.forward(); // 前进

// 获取当前路由
const currentRoute = routerPlugin.getCurrentRoute();
console.log('当前路由:', currentRoute);
```

### 路由守卫

```typescript
// 全局前置守卫
routerPlugin.beforeEach(async (to, from, next) => {
  console.log(`导航从 ${from.path} 到 ${to.path}`);
  
  // 检查认证
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  // 检查权限
  if (to.meta.requiredRoles && !hasRoles(to.meta.requiredRoles)) {
    next('/unauthorized');
    return;
  }
  
  next();
});

// 全局后置守卫
routerPlugin.afterEach((to, from) => {
  // 更新页面标题
  document.title = to.meta.title || 'Micro App';
  
  // 发送页面浏览统计
  analytics.track('page_view', {
    path: to.path,
    name: to.name
  });
});

// 路由级守卫
const route = {
  path: '/admin',
  component: 'AdminComponent',
  beforeEnter: (to, from, next) => {
    if (!isAdmin()) {
      next('/unauthorized');
      return;
    }
    next();
  }
};
```

## 配置选项

```typescript
interface RouterConfig {
  // 基础配置
  mode?: 'hash' | 'history';           // 路由模式
  base?: string;                       // 基础路径
  fallback?: string;                   // 404 页面路径
  
  // 功能开关
  enableGuards?: boolean;              // 启用路由守卫
  enableDebug?: boolean;               // 启用调试模式
  enableLazyLoading?: boolean;         // 启用懒加载
  
  // 导航配置
  scrollBehavior?: ScrollBehavior;     // 滚动行为
  linkActiveClass?: string;            // 激活链接类名
  linkExactActiveClass?: string;       // 精确激活链接类名
  
  // 性能配置
  enableCache?: boolean;               // 启用路由缓存
  maxCacheSize?: number;               // 最大缓存大小
  
  // 安全配置
  enableCSRFProtection?: boolean;      // 启用 CSRF 保护
  allowedOrigins?: string[];           // 允许的来源
}
```

## API 参考

### 路由管理

#### `registerRoutes(appId: string, routes: RouteConfig[]): void`
注册应用路由

#### `unregisterRoutes(appId: string): void`
注销应用路由

#### `getRoutes(appId?: string): RouteConfig[]`
获取路由配置

#### `hasRoute(name: string): boolean`
检查路由是否存在

### 导航方法

#### `push(location: RouteLocation): Promise<void>`
导航到新路由

#### `replace(location: RouteLocation): Promise<void>`
替换当前路由

#### `go(delta: number): void`
历史导航

#### `back(): void`
后退

#### `forward(): void`
前进

### 路由信息

#### `getCurrentRoute(): RouteLocationNormalized`
获取当前路由

#### `resolve(location: RouteLocation): RouteLocationResolved`
解析路由位置

#### `getMatchedRoutes(path: string): RouteRecord[]`
获取匹配的路由记录

### 守卫方法

#### `beforeEach(guard: NavigationGuard): UnregisterFunction`
注册全局前置守卫

#### `afterEach(guard: NavigationHookAfter): UnregisterFunction`
注册全局后置守卫

#### `beforeResolve(guard: NavigationGuard): UnregisterFunction`
注册全局解析守卫

## 高级用法

### 动态路由

```typescript
// 动态添加路由
routerPlugin.addRoute({
  path: '/dynamic/:id',
  name: 'dynamic-route',
  component: 'DynamicComponent'
});

// 动态移除路由
routerPlugin.removeRoute('dynamic-route');

// 检查路由是否存在
if (routerPlugin.hasRoute('dynamic-route')) {
  console.log('路由存在');
}
```

### 路由元信息

```typescript
const routes = [
  {
    path: '/admin',
    component: 'AdminComponent',
    meta: {
      title: '管理后台',
      requiresAuth: true,
      requiredRoles: ['admin'],
      breadcrumb: ['首页', '管理后台'],
      keepAlive: true
    }
  }
];

// 在守卫中使用元信息
routerPlugin.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  
  // 更新面包屑
  if (to.meta.breadcrumb) {
    updateBreadcrumb(to.meta.breadcrumb);
  }
  
  next();
});
```

### 路由懒加载

```typescript
// 组件懒加载
const routes = [
  {
    path: '/lazy',
    name: 'lazy-component',
    component: () => import('./LazyComponent.vue')
  },
  {
    path: '/admin',
    component: () => import('./AdminLayout.vue'),
    children: [
      {
        path: 'dashboard',
        component: () => import('./Dashboard.vue')
      }
    ]
  }
];

// 路由级代码分割
routerPlugin.registerRoutes('app1', routes);
```

### 路由缓存

```typescript
// 启用路由缓存
const plugin = new RouterPlugin({
  enableCache: true,
  maxCacheSize: 10
});

// 缓存特定路由
const route = {
  path: '/cached',
  component: 'CachedComponent',
  meta: {
    keepAlive: true,
    cacheKey: 'cached-page'
  }
};
```

## 路由模式

### Hash 模式

```typescript
const plugin = new RouterPlugin({
  mode: 'hash',
  base: '/'
});

// URL 格式: http://example.com/#/users/123
```

### History 模式

```typescript
const plugin = new RouterPlugin({
  mode: 'history',
  base: '/app/'
});

// URL 格式: http://example.com/app/users/123
// 需要服务器配置支持
```

## 滚动行为

```typescript
const plugin = new RouterPlugin({
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置，恢复滚动位置
    if (savedPosition) {
      return savedPosition;
    }
    
    // 如果有锚点，滚动到锚点
    if (to.hash) {
      return { el: to.hash };
    }
    
    // 默认滚动到顶部
    return { top: 0 };
  }
});
```

## 类型定义

```typescript
interface RouteConfig {
  path: string;
  name?: string;
  component?: string | (() => Promise<any>);
  children?: RouteConfig[];
  meta?: RouteMeta;
  beforeEnter?: NavigationGuard;
}

interface RouteMeta {
  title?: string;
  requiresAuth?: boolean;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  breadcrumb?: string[];
  keepAlive?: boolean;
  [key: string]: any;
}

interface RouteLocation {
  path?: string;
  name?: string;
  params?: Record<string, string>;
  query?: Record<string, string>;
  hash?: string;
}

interface NavigationGuard {
  (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): void | Promise<void>;
}
```

## 最佳实践

### 1. 路由结构设计

```typescript
// 推荐的路由结构
const routes = [
  // 根路由
  { path: '/', redirect: '/dashboard' },
  
  // 功能模块路由
  {
    path: '/users',
    component: 'UsersLayout',
    children: [
      { path: '', name: 'users-list', component: 'UsersList' },
      { path: ':id', name: 'user-detail', component: 'UserDetail' },
      { path: ':id/edit', name: 'user-edit', component: 'UserEdit' }
    ]
  },
  
  // 错误页面
  { path: '/404', name: 'not-found', component: 'NotFound' },
  { path: '/:pathMatch(.*)*', redirect: '/404' }
];
```

### 2. 守卫组织

```typescript
// 认证守卫
const authGuard = (to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  next();
};

// 权限守卫
const permissionGuard = (to, from, next) => {
  if (to.meta.requiredPermissions && !hasPermissions(to.meta.requiredPermissions)) {
    next('/unauthorized');
    return;
  }
  next();
};

// 注册守卫
routerPlugin.beforeEach(authGuard);
routerPlugin.beforeEach(permissionGuard);
```

### 3. 错误处理

```typescript
// 导航错误处理
routerPlugin.onError((error) => {
  console.error('路由错误:', error);
  
  if (error.type === 'NavigationDuplicated') {
    // 重复导航，忽略
    return;
  }
  
  if (error.type === 'NavigationAborted') {
    // 导航被中止
    showMessage('导航被取消');
    return;
  }
  
  // 其他错误，跳转到错误页面
  routerPlugin.push('/error');
});
```

## 调试和监控

### 1. 调试模式

```typescript
const plugin = new RouterPlugin({
  enableDebug: true
});

// 监听路由变化
plugin.onRouteChange((to, from) => {
  console.log('路由变化:', { to, from });
});

// 监听导航事件
plugin.onNavigationStart((to) => {
  console.log('开始导航到:', to.path);
});

plugin.onNavigationEnd((to) => {
  console.log('导航完成:', to.path);
});
```

### 2. 性能监控

```typescript
// 获取路由性能指标
const metrics = plugin.getMetrics();
console.log('导航次数:', metrics.navigationCount);
console.log('平均导航时间:', metrics.averageNavigationTime);
console.log('缓存命中率:', metrics.cacheHitRate);
```

## 故障排除

### 常见问题

**Q: History 模式下刷新页面 404**
A: 需要配置服务器支持 SPA，将所有路由请求重定向到 index.html。

**Q: 路由守卫不生效**
A: 检查守卫注册顺序和 next() 函数调用。

**Q: 动态路由参数获取不到**
A: 确保路由路径格式正确，使用 `:param` 语法定义参数。

### 调试技巧

```typescript
// 启用详细日志
plugin.setLogLevel('verbose');

// 检查路由状态
console.log('当前路由:', plugin.getCurrentRoute());
console.log('所有路由:', plugin.getRoutes());

// 检查守卫状态
console.log('注册的守卫:', plugin.getGuards());
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 Hash 和 History 模式
- 支持路由守卫
- 支持嵌套路由
- 支持懒加载
- 支持路由缓存
