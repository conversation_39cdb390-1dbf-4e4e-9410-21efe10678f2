import { resolve } from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'MicroCorePluginRouter',
            fileName: 'index',
            formats: ['es']
        },
        rollupOptions: {
            external: ['@micro-core/core'],
            output: {
                globals: {
                    '@micro-core/core': 'MicroCore'
                }
            }
        },
        sourcemap: true,
        minify: 'esbuild'
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src')
        }
    }
});