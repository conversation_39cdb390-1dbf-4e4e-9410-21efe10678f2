{"name": "@micro-core/plugins", "version": "0.1.0", "description": "Micro-Core 插件集合", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "microfrontend", "plugin", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@types/node": "^20.0.0", "rimraf": "^5.0.0", "tsup": "^8.0.0", "typescript": "^5.3.0"}, "publishConfig": {"access": "public"}}