# @micro-core/plugin-loader-wasm

Micro-Core WebAssembly 加载器插件 - 支持 WebAssembly 模块的高性能加载和执行

## 特性

- 🚀 **流式编译**: 支持 WebAssembly 模块的流式编译和实例化
- 💾 **内存管理**: 智能的 WASM 内存管理和垃圾回收
- 🏊 **实例池**: WASM 实例池管理，复用实例提升性能
- ⚡ **优化引擎**: 内置优化引擎，自动优化 WASM 模块执行
- 🔒 **类型安全**: 完整的 TypeScript 类型支持，确保类型安全

## 安装

```bash
npm install @micro-core/plugin-loader-wasm
# 或
pnpm add @micro-core/plugin-loader-wasm
```

## 使用方法

### 基本使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { WasmLoaderPlugin } from '@micro-core/plugin-loader-wasm';

const kernel = new MicroCoreKernel();

// 注册 WebAssembly 加载器插件
const wasmLoader = new WasmLoaderPlugin({
  enableStreaming: true,
  instancePoolSize: 10,
  memoryOptimization: true
});

kernel.use(wasmLoader);

// 加载 WebAssembly 模块
const result = await kernel.wasmLoader.loadModule('/path/to/module.wasm');
if (result.success && result.module) {
  // 执行 WASM 函数
  const executeResult = await kernel.wasmLoader.execute(result.module, 'calculatePi', [1000]);
  console.log('计算结果:', executeResult.result);
}
```

### 配置选项

```typescript
interface WasmLoaderOptions {
  /** 启用流式编译 */
  enableStreaming?: boolean; // 默认: true
  /** 实例池大小 */
  instancePoolSize?: number; // 默认: 10
  /** 内存优化 */
  memoryOptimization?: boolean; // 默认: true
  /** 超时时间 (毫秒) */
  timeout?: number; // 默认: 30000
  /** 缓存策略 */
  cacheStrategy?: 'memory' | 'localStorage' | 'indexedDB'; // 默认: 'memory'
  /** 最大内存使用量 (字节) */
  maxMemoryUsage?: number; // 默认: 100MB
}
```

### API 方法

#### loadModule(url, name?)

加载 WebAssembly 模块

```typescript
const result = await wasmLoader.loadModule('/path/to/math.wasm', 'math-module');
if (result.success) {
  console.log('模块加载成功:', result.module);
}
```

#### execute(moduleOrName, functionName, args?)

执行 WebAssembly 函数

```typescript
// 使用模块实例执行
const result1 = await wasmLoader.execute(module, 'add', [1, 2]);

// 使用模块名称执行
const result2 = await wasmLoader.execute('math-module', 'multiply', [3, 4]);
```

#### getStats()

获取统计信息

```typescript
const stats = wasmLoader.getStats();
console.log('WASM 统计:', stats);
```

## 性能优势

- **流式编译**: 支持 WebAssembly 模块的流式编译，提升加载速度
- **实例池管理**: 复用 WASM 实例，减少创建开销
- **内存优化**: 智能内存管理，避免内存泄漏
- **原生性能**: WebAssembly 提供接近原生的执行性能

## WebAssembly 模块示例

### C/C++ 源码

```c
// math.c
int add(int a, int b) {
    return a + b;
}

int multiply(int a, int b) {
    return a * b;
}

double calculatePi(int iterations) {
    double pi = 0.0;
    for (int i = 0; i < iterations; i++) {
        pi += (i % 2 == 0 ? 1.0 : -1.0) / (2 * i + 1);
    }
    return pi * 4;
}
```

### 编译为 WebAssembly

```bash
# 使用 Emscripten 编译
emcc math.c -o math.wasm -s EXPORTED_FUNCTIONS='["_add","_multiply","_calculatePi"]' -s WASM=1
```

### 在微前端中使用

```typescript
// 加载数学模块
const mathModule = await kernel.wasmLoader.loadModule('/assets/math.wasm', 'math');

if (mathModule.success && mathModule.module) {
  // 执行加法运算
  const addResult = await kernel.wasmLoader.execute(mathModule.module, 'add', [10, 20]);
  console.log('10 + 20 =', addResult.result); // 30
  
  // 计算 π 值
  const piResult = await kernel.wasmLoader.execute(mathModule.module, 'calculatePi', [10000]);
  console.log('π ≈', piResult.result); // 3.14159...
}
```

## 浏览器兼容性

- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## 许可证

MIT License