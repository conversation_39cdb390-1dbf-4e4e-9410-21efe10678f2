/**
 * WebAssembly 管理器 - 管理 WASM 模块的加载、编译和执行
 */

import type {
    WasmCacheItem,
    WasmExecuteResult,
    WasmInstancePool,
    WasmLoaderOptions,
    WasmLoadResult,
    WasmModule,
    WasmStats
} from './types';

export class WasmManager {
    private options: Required<WasmLoaderOptions>;
    private instancePool: WasmInstancePool;
    private moduleCache: Map<string, WasmCacheItem>;
    private stats: WasmStats;

    constructor(options: WasmLoaderOptions = {}) {
        this.options = {
            enableStreaming: options.enableStreaming ?? true,
            instancePoolSize: options.instancePoolSize ?? 10,
            memoryOptimization: options.memoryOptimization ?? true,
            timeout: options.timeout ?? 30000,
            cacheStrategy: options.cacheStrategy ?? 'memory',
            maxMemoryUsage: options.maxMemoryUsage ?? 100 * 1024 * 1024 // 100MB
        };

        this.instancePool = {
            available: [],
            busy: new Map(),
            maxSize: this.options.instancePoolSize,
            currentSize: 0
        };

        this.moduleCache = new Map();

        this.stats = {
            totalModules: 0,
            activeModules: 0,
            totalMemoryUsage: 0,
            averageLoadTime: 0,
            averageExecuteTime: 0,
            cacheHitRate: 0
        };
    }

    /**
     * 加载 WebAssembly 模块
     */
    public async loadModule(url: string, name?: string): Promise<WasmLoadResult> {
        const startTime = Date.now();
        const moduleName = name || this.extractModuleName(url);

        try {
            // 检查缓存
            const cachedModule = this.moduleCache.get(url);
            if (cachedModule && !this.isCacheExpired(cachedModule)) {
                const instance = await this.createInstance(cachedModule.module, moduleName, url);
                const loadTime = Date.now() - startTime;

                this.updateStats(loadTime, 0, true);

                return {
                    success: true,
                    module: instance,
                    loadTime,
                    size: cachedModule.size
                };
            }

            // 加载和编译模块
            const { module, bytes } = await this.fetchAndCompileModule(url);

            // 缓存模块
            this.cacheModule(url, module, bytes);

            // 创建实例
            const instance = await this.createInstance(module, moduleName, url);
            const loadTime = Date.now() - startTime;

            this.updateStats(loadTime, bytes.byteLength, false);

            return {
                success: true,
                module: instance,
                loadTime,
                size: bytes.byteLength
            };

        } catch (error) {
            const loadTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误',
                loadTime,
                size: 0
            };
        }
    }

    /**
     * 获取和编译 WebAssembly 模块
     */
    private async fetchAndCompileModule(url: string): Promise<{ module: WebAssembly.Module; bytes: ArrayBuffer }> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);

        try {
            const response = await fetch(url, { signal: controller.signal });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const bytes = await response.arrayBuffer();

            // 使用流式编译（如果支持）
            let module: WebAssembly.Module;
            if (this.options.enableStreaming && WebAssembly.compileStreaming) {
                try {
                    const streamResponse = await fetch(url);
                    module = await WebAssembly.compileStreaming(streamResponse);
                } catch {
                    // 回退到普通编译
                    module = await WebAssembly.compile(bytes);
                }
            } else {
                module = await WebAssembly.compile(bytes);
            }

            return { module, bytes };

        } finally {
            clearTimeout(timeoutId);
        }
    }

    /**
     * 创建 WebAssembly 实例
     */
    private async createInstance(module: WebAssembly.Module, name: string, url: string): Promise<WasmModule> {
        const instance = await WebAssembly.instantiate(module);
        const memoryUsage = this.calculateMemoryUsage(instance);

        const wasmModule: WasmModule = {
            name,
            url,
            instance,
            module,
            exports: instance.exports,
            memoryUsage,
            createdAt: Date.now(),
            lastUsedAt: Date.now()
        };

        // 添加到实例池
        this.addToPool(wasmModule);

        return wasmModule;
    }

    /**
     * 执行 WebAssembly 函数
     */
    public async execute(moduleOrName: WasmModule | string, functionName: string, args: any[] = []): Promise<WasmExecuteResult> {
        const startTime = Date.now();

        try {
            let module: WasmModule;

            if (typeof moduleOrName === 'string') {
                // 从池中获取模块
                const foundModule = this.getFromPool(moduleOrName);
                if (!foundModule) {
                    throw new Error(`模块 ${moduleOrName} 未找到`);
                }
                module = foundModule;
            } else {
                module = moduleOrName;
            }

            // 检查函数是否存在
            const func = module.exports[functionName];
            if (typeof func !== 'function') {
                throw new Error(`函数 ${functionName} 不存在`);
            }

            // 执行函数
            const result = func(...args);
            const executeTime = Date.now() - startTime;

            // 更新使用时间
            module.lastUsedAt = Date.now();

            this.updateExecuteStats(executeTime);

            return {
                success: true,
                result,
                executeTime
            };

        } catch (error) {
            const executeTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : '执行错误',
                executeTime
            };
        }
    }

    /**
     * 添加模块到实例池
     */
    private addToPool(module: WasmModule): void {
        if (this.instancePool.currentSize < this.instancePool.maxSize) {
            this.instancePool.available.push(module);
            this.instancePool.currentSize++;
            this.stats.activeModules++;
            this.stats.totalMemoryUsage += module.memoryUsage;
        } else if (this.options.memoryOptimization) {
            // 移除最旧的模块
            this.evictOldestModule();
            this.instancePool.available.push(module);
            this.stats.activeModules++;
            this.stats.totalMemoryUsage += module.memoryUsage;
        }
    }

    /**
     * 从实例池获取模块
     */
    private getFromPool(name: string): WasmModule | null {
        const index = this.instancePool.available.findIndex(m => m.name === name);
        if (index !== -1) {
            return this.instancePool.available[index];
        }

        // 检查忙碌的实例
        for (const [key, module] of this.instancePool.busy) {
            if (module.name === name) {
                return module;
            }
        }

        return null;
    }

    /**
     * 移除最旧的模块
     */
    private evictOldestModule(): void {
        if (this.instancePool.available.length === 0) return;

        let oldestIndex = 0;
        let oldestTime = this.instancePool.available[0].lastUsedAt;

        for (let i = 1; i < this.instancePool.available.length; i++) {
            if (this.instancePool.available[i].lastUsedAt < oldestTime) {
                oldestTime = this.instancePool.available[i].lastUsedAt;
                oldestIndex = i;
            }
        }

        const removedModule = this.instancePool.available.splice(oldestIndex, 1)[0];
        this.stats.activeModules--;
        this.stats.totalMemoryUsage -= removedModule.memoryUsage;
    }

    /**
     * 缓存模块
     */
    private cacheModule(url: string, module: WebAssembly.Module, bytes: ArrayBuffer): void {
        const cacheItem: WasmCacheItem = {
            bytes,
            module,
            timestamp: Date.now(),
            expireTime: Date.now() + 3600000, // 1小时过期
            size: bytes.byteLength
        };

        this.moduleCache.set(url, cacheItem);
    }

    /**
     * 检查缓存是否过期
     */
    private isCacheExpired(cacheItem: WasmCacheItem): boolean {
        return Date.now() > cacheItem.expireTime;
    }

    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage(instance: WebAssembly.Instance): number {
        let memoryUsage = 0;

        // 检查 WebAssembly 内存
        if (instance.exports.memory && instance.exports.memory instanceof WebAssembly.Memory) {
            memoryUsage += instance.exports.memory.buffer.byteLength;
        }

        return memoryUsage;
    }

    /**
     * 提取模块名称
     */
    private extractModuleName(url: string): string {
        const parts = url.split('/');
        const filename = parts[parts.length - 1];
        return filename.replace(/\.(wasm|wat)$/, '');
    }

    /**
     * 更新统计信息
     */
    private updateStats(loadTime: number, size: number, fromCache: boolean): void {
        this.stats.totalModules++;

        if (fromCache) {
            this.stats.cacheHitRate = (this.stats.cacheHitRate * (this.stats.totalModules - 1) + 1) / this.stats.totalModules;
        } else {
            this.stats.cacheHitRate = (this.stats.cacheHitRate * (this.stats.totalModules - 1)) / this.stats.totalModules;
        }

        this.stats.averageLoadTime = (this.stats.averageLoadTime * (this.stats.totalModules - 1) + loadTime) / this.stats.totalModules;
    }

    /**
     * 更新执行统计信息
     */
    private updateExecuteStats(executeTime: number): void {
        const totalExecutions = this.stats.totalModules; // 简化统计
        this.stats.averageExecuteTime = (this.stats.averageExecuteTime * (totalExecutions - 1) + executeTime) / totalExecutions;
    }

    /**
     * 获取统计信息
     */
    public getStats(): WasmStats {
        return { ...this.stats };
    }

    /**
     * 清理资源
     */
    public destroy(): void {
        // 清理实例池
        this.instancePool.available = [];
        this.instancePool.busy.clear();
        this.instancePool.currentSize = 0;

        // 清理缓存
        this.moduleCache.clear();

        // 重置统计信息
        this.stats = {
            totalModules: 0,
            activeModules: 0,
            totalMemoryUsage: 0,
            averageLoadTime: 0,
            averageExecuteTime: 0,
            cacheHitRate: 0
        };
    }
}