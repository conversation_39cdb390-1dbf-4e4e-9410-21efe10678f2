/**
 * WASM 模块管理器
 * 管理 WebAssembly 模块的加载、编译和缓存
 */

import type {
    WasmCacheItem,
    WasmImport,
    WasmImportObject,
    WasmLoaderConfig,
    WasmLoadProgress,
    WasmMemoryInfo,
    WasmModuleInfo
} from './types';

/**
 * WASM 模块管理器类
 */
export class WasmModuleManager {
    private config: Required<WasmLoaderConfig>;
    private modules = new Map<string, WasmModuleInfo>();
    private cache = new Map<string, WasmCacheItem>();
    private loadingPromises = new Map<string, Promise<WasmModuleInfo>>();
    private db: IDBDatabase | null = null;

    constructor(config: WasmLoaderConfig) {
        this.config = {
            enableStreaming: true,
            instancePoolSize: 10,
            memoryOptimization: true,
            maxMemoryPages: 256,
            enableCache: true,
            cacheStrategy: 'memory',
            cacheSize: 100,
            timeout: 30000,
            enableProfiling: false,
            debug: false,
            ...config
        };

        if (this.config.enableCache && this.config.cacheStrategy === 'indexeddb') {
            this.initIndexedDB();
        }
    }

    /**
     * 初始化 IndexedDB
     */
    private async initIndexedDB(): Promise<void> {
        if (!('indexedDB' in window)) {
            console.warn('浏览器不支持 IndexedDB，将使用内存缓存');
            return;
        }

        return new Promise((resolve, reject) => {
            const request = indexedDB.open('micro-core-wasm-cache', 1);

            request.onerror = () => {
                console.error('打开 IndexedDB 失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;

                if (!db.objectStoreNames.contains('wasm-modules')) {
                    const store = db.createObjectStore('wasm-modules', { keyPath: 'key' });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    store.createIndex('expireTime', 'expireTime', { unique: false });
                }
            };
        });
    }

    /**
     * 加载 WASM 模块
     */
    async loadModule(
        url: string,
        options: {
            moduleId?: string;
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        } = {}
    ): Promise<WasmModuleInfo> {
        const moduleId = options.moduleId || this.generateModuleId(url);

        // 检查是否已经在加载中
        const existingPromise = this.loadingPromises.get(moduleId);
        if (existingPromise) {
            return existingPromise;
        }

        // 检查缓存
        if (this.config.enableCache) {
            const cachedModule = await this.getFromCache(moduleId);
            if (cachedModule) {
                this.modules.set(moduleId, cachedModule.moduleInfo);
                return cachedModule.moduleInfo;
            }
        }

        // 创建加载 Promise
        const loadPromise = this.doLoadModule(url, moduleId, options);
        this.loadingPromises.set(moduleId, loadPromise);

        try {
            const moduleInfo = await loadPromise;
            this.loadingPromises.delete(moduleId);
            return moduleInfo;
        } catch (error) {
            this.loadingPromises.delete(moduleId);
            throw error;
        }
    }

    /**
     * 执行模块加载
     */
    private async doLoadModule(
        url: string,
        moduleId: string,
        options: {
            importObject?: WasmImportObject;
            onProgress?: (progress: WasmLoadProgress) => void;
        }
    ): Promise<WasmModuleInfo> {
        const startTime = Date.now();
        const { onProgress } = options;

        try {
            // 报告开始下载
            onProgress?.({
                moduleId,
                stage: 'downloading',
                percentage: 0,
                loaded: 0,
                total: 0,
                message: '开始下载 WASM 模块'
            });

            // 下载模块
            const downloadStartTime = Date.now();
            const response = await this.fetchWithTimeout(url);
            const downloadTime = Date.now() - downloadStartTime;

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentLength = response.headers.get('content-length');
            const total = contentLength ? parseInt(contentLength, 10) : 0;

            // 读取响应数据
            const arrayBuffer = await this.readResponseWithProgress(
                response,
                moduleId,
                total,
                onProgress
            );

            // 报告开始编译
            onProgress?.({
                moduleId,
                stage: 'compiling',
                percentage: 50,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: '编译 WASM 模块'
            });

            // 编译模块
            const compileStartTime = Date.now();
            const module = this.config.enableStreaming && 'compileStreaming' in WebAssembly
                ? await this.compileStreaming(url)
                : await WebAssembly.compile(arrayBuffer);
            const compileTime = Date.now() - compileStartTime;

            // 报告开始实例化
            onProgress?.({
                moduleId,
                stage: 'instantiating',
                percentage: 75,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: '实例化 WASM 模块'
            });

            // 创建临时实例以获取模块信息
            const instantiateStartTime = Date.now();
            const instance = await WebAssembly.instantiate(module, options.importObject || {});
            const instantiateTime = Date.now() - instantiateStartTime;

            // 分析模块信息
            const moduleInfo: WasmModuleInfo = {
                id: moduleId,
                url,
                size: arrayBuffer.byteLength,
                compileTime,
                instantiateTime,
                exports: this.extractExports(instance),
                imports: this.extractImports(module),
                memory: this.extractMemoryInfo(instance),
                fromCache: false,
                createdAt: Date.now()
            };

            // 存储模块信息
            this.modules.set(moduleId, moduleInfo);

            // 缓存模块
            if (this.config.enableCache) {
                await this.saveToCache(moduleId, module, moduleInfo);
            }

            // 报告完成
            onProgress?.({
                moduleId,
                stage: 'complete',
                percentage: 100,
                loaded: arrayBuffer.byteLength,
                total: arrayBuffer.byteLength,
                message: 'WASM 模块加载完成'
            });

            if (this.config.debug) {
                console.log(`WASM 模块 ${moduleId} 加载完成:`, {
                    downloadTime,
                    compileTime,
                    instantiateTime,
                    totalTime: Date.now() - startTime,
                    size: arrayBuffer.byteLength
                });
            }

            return moduleInfo;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            if (this.config.debug) {
                console.error(`WASM 模块 ${moduleId} 加载失败:`, error);
            }

            throw new Error(`加载 WASM 模块失败: ${errorMessage}`);
        }
    }

    /**
     * 带超时的 fetch
     */
    private async fetchWithTimeout(url: string): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        try {
            const response = await fetch(url, { signal: controller.signal });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * 读取响应并跟踪进度
     */
    private async readResponseWithProgress(
        response: Response,
        moduleId: string,
        total: number,
        onProgress?: (progress: WasmLoadProgress) => void
    ): Promise<ArrayBuffer> {
        if (!response.body || !onProgress) {
            return await response.arrayBuffer();
        }

        const reader = response.body.getReader();
        const chunks: Uint8Array[] = [];
        let loaded = 0;

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                chunks.push(value);
                loaded += value.length;

                onProgress({
                    moduleId,
                    stage: 'downloading',
                    percentage: total > 0 ? (loaded / total) * 40 : 0, // 下载占总进度的 40%
                    loaded,
                    total,
                    message: `下载中: ${this.formatBytes(loaded)}${total > 0 ? ` / ${this.formatBytes(total)}` : ''}`
                });
            }
        } finally {
            reader.releaseLock();
        }

        // 合并所有块
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return result.buffer;
    }

    /**
     * 流式编译
     */
    private async compileStreaming(url: string): Promise<WebAssembly.Module> {
        if ('compileStreaming' in WebAssembly) {
            return await WebAssembly.compileStreaming(fetch(url));
        } else {
            // 降级到普通编译
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            return await WebAssembly.compile(arrayBuffer);
        }
    }

    /**
     * 提取导出信息
     */
    private extractExports(instance: WebAssembly.Instance): string[] {
        return Object.keys(instance.exports);
    }

    /**
     * 提取导入信息
     */
    private extractImports(module: WebAssembly.Module): WasmImport[] {
        const imports: WasmImport[] = [];

        try {
            const moduleImports = WebAssembly.Module.imports(module);
            for (const imp of moduleImports) {
                imports.push({
                    module: imp.module,
                    field: imp.name,
                    kind: imp.kind as any
                });
            }
        } catch (error) {
            if (this.config.debug) {
                console.warn('无法提取模块导入信息:', error);
            }
        }

        return imports;
    }

    /**
     * 提取内存信息
     */
    private extractMemoryInfo(instance: WebAssembly.Instance): WasmMemoryInfo | undefined {
        const memory = instance.exports.memory as WebAssembly.Memory;
        if (!memory) {
            return undefined;
        }

        return {
            initial: memory.buffer.byteLength / 65536, // 页大小为 64KB
            current: memory.buffer.byteLength / 65536,
            shared: memory.buffer instanceof SharedArrayBuffer
        };
    }

    /**
     * 从缓存获取模块
     */
    private async getFromCache(moduleId: string): Promise<WasmCacheItem | null> {
        switch (this.config.cacheStrategy) {
            case 'memory':
                return this.getFromMemoryCache(moduleId);
            case 'indexeddb':
                return await this.getFromIndexedDBCache(moduleId);
            default:
                return null;
        }
    }

    /**
     * 从内存缓存获取
     */
    private getFromMemoryCache(moduleId: string): WasmCacheItem | null {
        const item = this.cache.get(moduleId);
        if (!item) {
            return null;
        }

        // 检查是否过期
        if (item.expireTime && Date.now() > item.expireTime) {
            this.cache.delete(moduleId);
            return null;
        }

        // 更新访问信息
        item.accessCount++;
        item.lastAccessed = Date.now();

        return item;
    }

    /**
     * 从 IndexedDB 缓存获取
     */
    private async getFromIndexedDBCache(moduleId: string): Promise<WasmCacheItem | null> {
        if (!this.db) {
            return null;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readonly');
            const store = transaction.objectStore('wasm-modules');
            const request = store.get(moduleId);

            request.onsuccess = () => {
                const item = request.result as WasmCacheItem | undefined;
                if (!item) {
                    resolve(null);
                    return;
                }

                // 检查是否过期
                if (item.expireTime && Date.now() > item.expireTime) {
                    this.deleteFromIndexedDBCache(moduleId);
                    resolve(null);
                    return;
                }

                // 更新访问信息
                item.accessCount++;
                item.lastAccessed = Date.now();

                resolve(item);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 保存到缓存
     */
    private async saveToCache(
        moduleId: string,
        module: WebAssembly.Module,
        moduleInfo: WasmModuleInfo
    ): Promise<void> {
        const cacheItem: WasmCacheItem = {
            key: moduleId,
            module,
            moduleInfo,
            timestamp: Date.now(),
            accessCount: 0,
            lastAccessed: Date.now()
        };

        switch (this.config.cacheStrategy) {
            case 'memory':
                await this.saveToMemoryCache(moduleId, cacheItem);
                break;
            case 'indexeddb':
                await this.saveToIndexedDBCache(moduleId, cacheItem);
                break;
        }
    }

    /**
     * 保存到内存缓存
     */
    private async saveToMemoryCache(moduleId: string, item: WasmCacheItem): Promise<void> {
        // 检查缓存大小限制
        await this.ensureCacheSize();
        this.cache.set(moduleId, item);
    }

    /**
     * 保存到 IndexedDB 缓存
     */
    private async saveToIndexedDBCache(moduleId: string, item: WasmCacheItem): Promise<void> {
        if (!this.db) {
            return;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
            const store = transaction.objectStore('wasm-modules');
            const request = store.put(item);

            request.onsuccess = () => {
                resolve();
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 从 IndexedDB 删除缓存项
     */
    private async deleteFromIndexedDBCache(moduleId: string): Promise<void> {
        if (!this.db) {
            return;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
            const store = transaction.objectStore('wasm-modules');
            const request = store.delete(moduleId);

            request.onsuccess = () => {
                resolve();
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 确保缓存大小不超过限制
     */
    private async ensureCacheSize(): Promise<void> {
        if (this.cache.size < this.config.cacheSize) {
            return;
        }

        // 使用 LRU 策略清理缓存
        const items = Array.from(this.cache.entries());
        items.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

        const removeCount = Math.ceil(this.cache.size * 0.2); // 清理 20%
        for (let i = 0; i < removeCount && i < items.length; i++) {
            this.cache.delete(items[i][0]);
        }
    }

    /**
     * 生成模块 ID
     */
    private generateModuleId(url: string): string {
        return `wasm-${Date.now()}-${url.split('/').pop()?.replace(/\W/g, '') || 'module'}`;
    }

    /**
     * 格式化字节数
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取模块信息
     */
    getModuleInfo(moduleId: string): WasmModuleInfo | undefined {
        return this.modules.get(moduleId);
    }

    /**
     * 获取所有模块信息
     */
    getAllModules(): WasmModuleInfo[] {
        return Array.from(this.modules.values());
    }

    /**
     * 删除模块
     */
    async deleteModule(moduleId: string): Promise<void> {
        this.modules.delete(moduleId);
        this.cache.delete(moduleId);

        if (this.config.cacheStrategy === 'indexeddb') {
            await this.deleteFromIndexedDBCache(moduleId);
        }
    }

    /**
     * 清空所有模块和缓存
     */
    async clearAll(): Promise<void> {
        this.modules.clear();
        this.cache.clear();
        this.loadingPromises.clear();

        if (this.config.cacheStrategy === 'indexeddb' && this.db) {
            return new Promise((resolve, reject) => {
                const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
                const store = transaction.objectStore('wasm-modules');
                const request = store.clear();

                request.onsuccess = () => {
                    resolve();
                };

                request.onerror = () => {
                    reject(request.error);
                };
            });
        }
    }

    /**
     * 获取缓存统计信息
     */
    async getCacheStats(): Promise<{
        totalModules: number;
        cacheSize: number;
        memoryUsage: number;
        hitRate: number;
    }> {
        const totalModules = this.modules.size;
        const cacheSize = this.cache.size;

        // 计算内存使用量
        let memoryUsage = 0;
        for (const moduleInfo of this.modules.values()) {
            memoryUsage += moduleInfo.size;
        }

        return {
            totalModules,
            cacheSize,
            memoryUsage,
            hitRate: 0 // 这里可以实现命中率统计
        };
    }

    /**
     * 检查浏览器支持
     */
    static isSupported(): boolean {
        return typeof WebAssembly !== 'undefined' &&
            typeof WebAssembly.compile === 'function' &&
            typeof WebAssembly.instantiate === 'function';
    }

    /**
     * 销毁管理器
     */
    destroy(): void {
        this.clearAll();

        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}
