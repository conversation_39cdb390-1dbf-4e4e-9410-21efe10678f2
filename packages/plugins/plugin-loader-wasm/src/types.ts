/**
 * WebAssembly 加载器插件类型定义
 */

export interface WasmLoaderOptions {
    /** 启用流式编译 */
    enableStreaming?: boolean;
    /** 实例池大小 */
    instancePoolSize?: number;
    /** 内存优化 */
    memoryOptimization?: boolean;
    /** 超时时间 (毫秒) */
    timeout?: number;
    /** 缓存策略 */
    cacheStrategy?: 'memory' | 'localStorage' | 'indexedDB';
    /** 最大内存使用量 (字节) */
    maxMemoryUsage?: number;
}

export interface WasmModule {
    /** 模块名称 */
    name: string;
    /** 模块URL */
    url: string;
    /** WebAssembly 模块实例 */
    instance: WebAssembly.Instance;
    /** WebAssembly 模块 */
    module: WebAssembly.Module;
    /** 导出的函数 */
    exports: WebAssembly.Exports;
    /** 内存使用量 */
    memoryUsage: number;
    /** 创建时间 */
    createdAt: number;
    /** 最后使用时间 */
    lastUsedAt: number;
}

export interface WasmInstancePool {
    /** 可用实例 */
    available: WasmModule[];
    /** 使用中的实例 */
    busy: Map<string, WasmModule>;
    /** 最大实例数 */
    maxSize: number;
    /** 当前实例数 */
    currentSize: number;
}

export interface WasmLoadResult {
    /** 是否成功 */
    success: boolean;
    /** 模块实例 */
    module?: WasmModule;
    /** 错误信息 */
    error?: string;
    /** 加载时间 */
    loadTime: number;
    /** 模块大小 */
    size: number;
}

export interface WasmExecuteResult {
    /** 是否成功 */
    success: boolean;
    /** 执行结果 */
    result?: any;
    /** 错误信息 */
    error?: string;
    /** 执行时间 */
    executeTime: number;
}

export interface WasmStats {
    /** 总模块数 */
    totalModules: number;
    /** 活跃模块数 */
    activeModules: number;
    /** 总内存使用量 */
    totalMemoryUsage: number;
    /** 平均加载时间 */
    averageLoadTime: number;
    /** 平均执行时间 */
    averageExecuteTime: number;
    /** 缓存命中率 */
    cacheHitRate: number;
}

export interface WasmCacheItem {
    /** 模块字节码 */
    bytes: ArrayBuffer;
    /** 编译后的模块 */
    module: WebAssembly.Module;
    /** 缓存时间 */
    timestamp: number;
    /** 过期时间 */
    expireTime: number;
    /** 模块大小 */
    size: number;
}