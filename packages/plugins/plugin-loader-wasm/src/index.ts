/**
 * WebAssembly 加载器插件 - 主入口文件
 */

import type { MicroCoreKernel } from '@micro-core/core';
import type { WasmExecuteResult, WasmLoaderOptions, WasmLoadResult, WasmModule } from './types';
import { WasmManager } from './wasm-manager';

export type { WasmExecuteResult, WasmLoaderOptions, WasmLoadResult, WasmModule, WasmStats } from './types';
export { WasmManager } from './wasm-manager';

/**
 * WebAssembly 加载器插件接口
 */
export interface Plugin {
    name: string;
    version: string;
    install(kernel: MicroCoreKernel): void;
    uninstall?(kernel: MicroCoreKernel): void;
}

/**
 * WebAssembly 加载器插件类
 */
export class WasmLoaderPlugin implements Plugin {
    public name = 'wasm-loader';
    public version = '0.1.0';
    
    private wasmManager: WasmManager;
    private options: WasmLoaderOptions;

    constructor(options: WasmLoaderOptions = {}) {
        this.options = options;
        this.wasmManager = new WasmManager(options);
    }

    /**
     * 插件安装方法
     */
    public install(kernel: MicroCoreKernel): void {
        // 扩展内核功能
        this.extendKernel(kernel);
    }

    /**
     * 扩展内核功能
     */
    private extendKernel(kernel: MicroCoreKernel): void {
        // 添加 WASM 加载器方法到内核
        (kernel as any).wasmLoader = {
            loadModule: this.loadModule.bind(this),
            execute: this.execute.bind(this),
            getStats: this.getStats.bind(this)
        };
    }

    /**
     * 加载 WebAssembly 模块
     */
    public async loadModule(url: string, name?: string): Promise<WasmLoadResult> {
        return this.wasmManager.loadModule(url, name);
    }

    /**
     * 执行 WebAssembly 函数
     */
    public async execute(moduleOrName: WasmModule | string, functionName: string, args: any[] = []): Promise<WasmExecuteResult> {
        return this.wasmManager.execute(moduleOrName, functionName, args);
    }

    /**
     * 获取统计信息
     */
    public getStats() {
        return this.wasmManager.getStats();
    }

    /**
     * 插件卸载方法
     */
    public uninstall(kernel: MicroCoreKernel): void {
        // 清理资源
        this.wasmManager.destroy();

        // 从内核移除扩展功能
        delete (kernel as any).wasmLoader;
    }
}

/**
 * 创建 WebAssembly 加载器插件实例
 */
export function createWasmLoaderPlugin(options: WasmLoaderOptions = {}): WasmLoaderPlugin {
    return new WasmLoaderPlugin(options);
}

/**
 * 默认导出插件类
 */
export default WasmLoaderPlugin;