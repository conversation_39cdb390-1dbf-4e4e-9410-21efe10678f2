{"name": "@micro-core/plugin-loader-wasm", "version": "0.1.0", "description": "Micro-Core WebAssembly 加载器插件 - 支持 WebAssembly 模块的高性能加载和执行", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "webassembly", "wasm", "loader", "performance", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-loader-wasm"}, "peerDependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@micro-core/core": "workspace:*", "@types/node": "^20.0.0", "typescript": "^5.3.0", "vite": "^7.0.4", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}