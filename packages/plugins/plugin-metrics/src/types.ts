/**
 * 性能监控插件类型定义
 */

export interface MetricsPluginOptions {
    /** 是否启用性能监控 */
    enablePerformance?: boolean;
    /** 是否启用内存监控 */
    enableMemory?: boolean;
    /** 是否启用网络监控 */
    enableNetwork?: boolean;
    /** 上报间隔（毫秒） */
    reportInterval?: number;
    /** 最大指标缓存数量 */
    maxMetricsSize?: number;
    /** 远程上报地址 */
    reportUrl?: string;
}

export interface MetricsCollectorOptions extends MetricsPluginOptions {
    enablePerformance: boolean;
    enableMemory: boolean;
    enableNetwork: boolean;
    reportInterval: number;
    maxMetricsSize: number;
}

export interface MetricEntry {
    /** 指标名称 */
    name: string;
    /** 指标值 */
    value: number;
    /** 指标类型 */
    type: string;
    /** 标签信息 */
    tags: Record<string, any>;
    /** 时间戳 */
    timestamp: number;
}

export interface PerformanceMetrics {
    /** DOM内容加载完成时间 */
    domContentLoaded: number;
    /** 页面完全加载时间 */
    loadComplete: number;
    /** 首次绘制时间 */
    firstPaint: number;
    /** 首次内容绘制时间 */
    firstContentfulPaint: number;
    /** 最大内容绘制时间 */
    largestContentfulPaint: number;
}

export interface MemoryMetrics {
    /** 已使用的JS堆大小 */
    usedJSHeapSize: number;
    /** 总JS堆大小 */
    totalJSHeapSize: number;
    /** JS堆大小限制 */
    jsHeapSizeLimit: number;
}

export interface NetworkMetrics {
    /** 请求数量 */
    requestCount: number;
    /** 平均响应时间 */
    avgResponseTime: number;
    /** 错误请求数量 */
    errorCount: number;
}

export interface MetricsReport {
    /** 报告时间戳 */
    timestamp: number;
    /** 指标数据 */
    metrics: MetricEntry[];
    /** 汇总信息 */
    summary: {
        totalMetrics: number;
        recentMetrics: number;
        avgLoadTime: number;
        avgMountTime: number;
        errorCount: number;
    };
}