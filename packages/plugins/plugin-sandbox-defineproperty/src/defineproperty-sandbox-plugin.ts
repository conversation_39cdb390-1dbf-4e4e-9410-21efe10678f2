import type { MicroCorePlugin, PluginContext } from '@micro-core/core';
import { DefinePropertySandbox } from './defineproperty-sandbox';
import type { DefinePropertySandboxPluginOptions } from './types';

/**
 * DefineProperty 沙箱插件
 * 提供基于 Object.defineProperty 的沙箱隔离能力
 */
export class DefinePropertySandboxPlugin implements MicroCorePlugin {
    public readonly name = 'defineproperty-sandbox';
    public readonly version = '0.1.0';

    private options: DefinePropertySandboxPluginOptions;
    private sandboxes = new Map<string, DefinePropertySandbox>();

    constructor(options: DefinePropertySandboxPluginOptions = {}) {
        this.options = {
            enableLogging: false,
            enableStrictMode: true,
            enablePropertyValidation: true,
            ...options
        };
    }

    /**
     * 插件安装
     */
    install(context: PluginContext) {
        this.log('DefineProperty 沙箱插件正在安装...');

        // 注册沙箱创建钩子
        context.hooks.beforeAppMount.tap(this.name, (appConfig) => {
            this.createSandbox(appConfig.name, {
                enableLogging: this.options.enableLogging,
                enableStrictMode: this.options.enableStrictMode,
                enablePropertyValidation: this.options.enablePropertyValidation
            });
        });

        // 注册沙箱激活钩子
        context.hooks.appMounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.activate();
                this.log(`应用 ${appConfig.name} 的 DefineProperty 沙箱已激活`);
            }
        });

        // 注册沙箱停用钩子
        context.hooks.beforeAppUnmount.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.deactivate();
                this.log(`应用 ${appConfig.name} 的 DefineProperty 沙箱已停用`);
            }
        });

        // 注册沙箱销毁钩子
        context.hooks.appUnmounted.tap(this.name, (appConfig) => {
            const sandbox = this.sandboxes.get(appConfig.name);
            if (sandbox) {
                sandbox.destroy();
                this.sandboxes.delete(appConfig.name);
                this.log(`应用 ${appConfig.name} 的 DefineProperty 沙箱已销毁`);
            }
        });

        this.log('DefineProperty 沙箱插件安装完成');
    }

    /**
     * 插件卸载
     */
    uninstall() {
        this.log('DefineProperty 沙箱插件正在卸载...');

        // 销毁所有沙箱
        for (const [appName, sandbox] of this.sandboxes) {
            try {
                sandbox.destroy();
                this.log(`应用 ${appName} 的 DefineProperty 沙箱已销毁`);
            } catch (error) {
                console.error(`[DefinePropertySandboxPlugin] 销毁应用 ${appName} 的沙箱失败:`, error);
            }
        }

        this.sandboxes.clear();
        this.log('DefineProperty 沙箱插件卸载完成');
    }

    /**
     * 创建沙箱
     */
    private createSandbox(appName: string, options: any) {
        try {
            const sandbox = new DefinePropertySandbox(appName, options);
            this.sandboxes.set(appName, sandbox);
            this.log(`为应用 ${appName} 创建 DefineProperty 沙箱成功`);
        } catch (error) {
            console.error(`[DefinePropertySandboxPlugin] 为应用 ${appName} 创建沙箱失败:`, error);
        }
    }

    /**
     * 获取指定应用的沙箱
     */
    getSandbox(appName: string): DefinePropertySandbox | undefined {
        return this.sandboxes.get(appName);
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): Map<string, DefinePropertySandbox> {
        return new Map(this.sandboxes);
    }

    /**
     * 记录日志
     */
    private log(message: string) {
        if (this.options.enableLogging) {
            console.log(`[DefinePropertySandboxPlugin] ${message}`);
        }
    }
}