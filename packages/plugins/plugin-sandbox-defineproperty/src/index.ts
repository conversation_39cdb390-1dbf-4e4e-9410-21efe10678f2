/**
 * DefineProperty 沙箱插件
 * 基于 Object.defineProperty 实现的沙箱策略，提供对旧版浏览器的支持
 */

export { DefinePropertySandbox } from './defineproperty-sandbox';
export { DefinePropertySandboxPlugin } from './defineproperty-sandbox-plugin';
export type {
    DefinePropertySandboxOptions, DefinePropertySandboxPluginOptions, PropertyDescriptorInfo,
    SandboxContext
} from './types';

// 默认导出插件
export { DefinePropertySandboxPlugin as default } from './defineproperty-sandbox-plugin';
