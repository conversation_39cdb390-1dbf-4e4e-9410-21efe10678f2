/**
 * DefineProperty 沙箱插件类型定义
 */

export interface DefinePropertySandboxPluginOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 是否启用严格模式 */
    enableStrictMode?: boolean;
    /** 是否启用属性验证 */
    enablePropertyValidation?: boolean;
}

export interface DefinePropertySandboxOptions {
    /** 是否启用日志 */
    enableLogging?: boolean;
    /** 是否启用严格模式 */
    enableStrictMode?: boolean;
    /** 是否启用属性验证 */
    enablePropertyValidation?: boolean;
}

export interface PropertyDescriptorInfo {
    /** 属性名 */
    property: string;
    /** 原始属性描述符 */
    originalDescriptor?: PropertyDescriptor;
    /** 新属性描述符 */
    newDescriptor: PropertyDescriptor;
    /** 是否已修改 */
    isModified: boolean;
}

export interface SandboxContext {
    /** 沙箱化的 window 对象 */
    window: Window;
    /** 沙箱化的 document 对象 */
    document: Document;
    /** 沙箱化的 location 对象 */
    location: Location;
    /** 沙箱化的 history 对象 */
    history: History;
}

export interface GlobalPropertyPatch {
    /** 属性名 */
    name: string;
    /** 获取器 */
    getter: () => any;
    /** 设置器 */
    setter: (value: any) => void;
    /** 是否可配置 */
    configurable?: boolean;
    /** 是否可枚举 */
    enumerable?: boolean;
}

export interface SandboxState {
    /** 是否激活 */
    isActive: boolean;
    /** 修改的属性列表 */
    modifiedProperties: Set<string>;
    /** 原始属性描述符映射 */
    originalDescriptors: Map<string, PropertyDescriptor | undefined>;
    /** 沙箱上下文 */
    context: SandboxContext;
}