import type { LogEntry, LogLevel, LoggerOptions } from './types';

/**
 * 创建日志器实例
 */
export function createLogger(options: LoggerOptions) {
    const logs: LogEntry[] = [];
    const { level, enableConsole, enableRemote, maxLogSize, remoteUrl } = options;

    // 日志级别优先级
    const levelPriority: Record<LogLevel, number> = {
        debug: 0,
        info: 1,
        warn: 2,
        error: 3
    };

    /**
     * 记录日志
     */
    function log(logLevel: LogLevel, message: string, ...args: any[]) {
        // 检查日志级别
        if (levelPriority[logLevel] < levelPriority[level]) {
            return;
        }

        const logEntry: LogEntry = {
            level: logLevel,
            message,
            args,
            timestamp: new Date().toISOString(),
            source: 'micro-core'
        };

        // 添加到日志缓存
        logs.push(logEntry);

        // 限制日志缓存大小
        if (logs.length > maxLogSize) {
            logs.shift();
        }

        // 控制台输出
        if (enableConsole) {
            const consoleMethod = console[logLevel] || console.log;
            consoleMethod(`[${logEntry.timestamp}] [${logLevel.toUpperCase()}]`, message, ...args);
        }

        // 远程日志上报
        if (enableRemote && remoteUrl) {
            sendRemoteLog(logEntry, remoteUrl);
        }
    }

    /**
     * 发送远程日志
     */
    async function sendRemoteLog(logEntry: LogEntry, url: string) {
        try {
            await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logEntry)
            });
        } catch (error) {
            console.warn('[Logger] 远程日志发送失败:', error);
        }
    }

    return {
        debug: (message: string, ...args: any[]) => log('debug', message, ...args),
        info: (message: string, ...args: any[]) => log('info', message, ...args),
        warn: (message: string, ...args: any[]) => log('warn', message, ...args),
        error: (message: string, ...args: any[]) => log('error', message, ...args),

        /**
         * 获取所有日志
         */
        getLogs: () => [...logs],

        /**
         * 清空日志
         */
        clear: () => {
            logs.length = 0;
        },

        /**
         * 导出日志
         */
        export: () => {
            return JSON.stringify(logs, null, 2);
        },

        /**
         * 设置日志级别
         */
        setLevel: (newLevel: LogLevel) => {
            options.level = newLevel;
        }
    };
}