{"name": "@micro-core/plugin-devtools", "version": "0.1.0", "description": "Micro-Core 开发者工具插件 - 提供调试和监控微前端应用的开发工具", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "vite build && tsc --emitDeclarationOnly", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["micro-frontend", "devtools", "debug", "monitor", "plugin"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-devtools"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "homepage": "https://github.com/echo008/micro-core/tree/main/packages/plugins/plugin-devtools#readme", "peerDependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "@types/node": "^20.10.5", "typescript": "^5.3.3", "vite": "^5.0.10", "vitest": "^1.1.0"}}