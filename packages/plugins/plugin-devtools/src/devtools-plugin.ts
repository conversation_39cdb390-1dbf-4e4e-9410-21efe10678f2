/**
 * 开发者工具插件
 * 提供调试和监控微前端应用的开发工具
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import type { AppInfo, DevToolsConfig, DevToolsEvents, LogEntry } from './types';

/**
 * 开发者工具插件类
 */
export class DevToolsPlugin implements Plugin {
    name = 'devtools';
    version = '0.1.0';

    private config: Required<DevToolsConfig>;
    private kernel: MicroCoreKernel | null = null;
    private panel: HTMLElement | null = null;
    private isVisible = false;
    private apps = new Map<string, AppInfo>();
    private logs: LogEntry[] = [];
    private eventListeners: Partial<DevToolsEvents> = {};
    private performanceObserver: PerformanceObserver | null = null;

    constructor(config: DevToolsConfig = {}) {
        this.config = {
            enabled: true,
            showPanel: false,
            panelPosition: 'bottom',
            enablePerformanceMonitor: true,
            enableLogger: true,
            logLevel: 'info',
            enableInspector: true,
            hotkeys: {
                togglePanel: 'Ctrl+Shift+D',
                toggleInspector: 'Ctrl+Shift+I'
            },
            ...config
        };
    }

    /**
     * 插件安装
     */
    install(kernel: MicroCoreKernel): void {
        if (!this.config.enabled) {
            return;
        }

        this.kernel = kernel;

        // 注册到内核
        kernel.registerPlugin(this);

        // 监听应用生命周期事件
        this.setupLifecycleListeners();

        // 创建开发工具面板
        if (this.config.showPanel) {
            this.createPanel();
        }

        // 设置性能监控
        if (this.config.enablePerformanceMonitor) {
            this.setupPerformanceMonitor();
        }

        // 设置快捷键
        this.setupHotkeys();

        // 添加方法到内核
        (kernel as any).devtools = {
            show: () => this.showPanel(),
            hide: () => this.hidePanel(),
            toggle: () => this.togglePanel(),
            getApps: () => this.getApps(),
            getLogs: () => this.getLogs(),
            clearLogs: () => this.clearLogs()
        };

        this.log('info', 'DevTools 插件已启用');
    }

    /**
     * 插件卸载
     */
    uninstall(kernel: MicroCoreKernel): void {
        if (this.panel) {
            this.panel.remove();
            this.panel = null;
        }

        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
            this.performanceObserver = null;
        }

        // 移除快捷键监听
        document.removeEventListener('keydown', this.handleKeydown);

        // 从内核移除方法
        delete (kernel as any).devtools;

        this.kernel = null;
        this.apps.clear();
        this.logs = [];
        this.eventListeners = {};
    }

    /**
     * 设置生命周期监听器
     */
    private setupLifecycleListeners(): void {
        if (!this.kernel) return;

        // 监听应用注册
        this.kernel.on('app:registered', (appConfig) => {
            const appInfo: AppInfo = {
                name: appConfig.name,
                status: 'registered',
                version: appConfig.version,
                entry: appConfig.entry,
                container: appConfig.container,
                activeWhen: appConfig.activeWhen
            };
            this.apps.set(appConfig.name, appInfo);
            this.updatePanel();
            this.log('info', `应用 ${appConfig.name} 已注册`);
        });

        // 监听应用加载
        this.kernel.on('app:loading', (appName) => {
            const app = this.apps.get(appName);
            if (app) {
                app.status = 'loading';
                this.updatePanel();
                this.log('info', `应用 ${appName} 开始加载`);
            }
        });

        // 监听应用挂载
        this.kernel.on('app:mounted', (appName) => {
            const app = this.apps.get(appName);
            if (app) {
                app.status = 'mounted';
                this.updatePanel();
                this.log('info', `应用 ${appName} 已挂载`);
            }
        });

        // 监听应用卸载
        this.kernel.on('app:unmounted', (appName) => {
            const app = this.apps.get(appName);
            if (app) {
                app.status = 'unmounted';
                this.updatePanel();
                this.log('info', `应用 ${appName} 已卸载`);
            }
        });

        // 监听错误
        this.kernel.on('app:error', (appName, error) => {
            const app = this.apps.get(appName);
            if (app) {
                app.status = 'error';
                this.updatePanel();
                this.log('error', `应用 ${appName} 发生错误: ${error.message}`, error);
            }
        });
    }

    /**
     * 创建开发工具面板
     */
    private createPanel(): void {
        if (this.panel) return;

        this.panel = document.createElement('div');
        this.panel.id = 'micro-core-devtools';
        this.panel.innerHTML = this.getPanelHTML();
        this.panel.style.cssText = this.getPanelCSS();

        document.body.appendChild(this.panel);

        // 绑定事件
        this.bindPanelEvents();

        if (this.config.showPanel) {
            this.showPanel();
        }
    }

    /**
     * 获取面板 HTML
     */
    private getPanelHTML(): string {
        return `
            <div class="devtools-header">
                <h3>Micro-Core DevTools</h3>
                <div class="devtools-controls">
                    <button class="devtools-btn" data-action="refresh">刷新</button>
                    <button class="devtools-btn" data-action="clear">清空日志</button>
                    <button class="devtools-btn" data-action="close">关闭</button>
                </div>
            </div>
            <div class="devtools-content">
                <div class="devtools-tabs">
                    <button class="devtools-tab active" data-tab="apps">应用</button>
                    <button class="devtools-tab" data-tab="logs">日志</button>
                    <button class="devtools-tab" data-tab="performance">性能</button>
                </div>
                <div class="devtools-panels">
                    <div class="devtools-panel active" data-panel="apps">
                        <div class="apps-list"></div>
                    </div>
                    <div class="devtools-panel" data-panel="logs">
                        <div class="logs-list"></div>
                    </div>
                    <div class="devtools-panel" data-panel="performance">
                        <div class="performance-metrics"></div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取面板 CSS
     */
    private getPanelCSS(): string {
        const position = this.config.panelPosition;
        const positionStyles = {
            top: 'top: 0; left: 0; right: 0; height: 300px;',
            bottom: 'bottom: 0; left: 0; right: 0; height: 300px;',
            left: 'top: 0; left: 0; bottom: 0; width: 400px;',
            right: 'top: 0; right: 0; bottom: 0; width: 400px;'
        };

        return `
            position: fixed;
            ${positionStyles[position]}
            background: #1e1e1e;
            color: #ffffff;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            z-index: 999999;
            border: 1px solid #333;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: none;
            flex-direction: column;
        `;
    }

    /**
     * 绑定面板事件
     */
    private bindPanelEvents(): void {
        if (!this.panel) return;

        // 控制按钮事件
        this.panel.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const action = target.getAttribute('data-action');

            switch (action) {
                case 'refresh':
                    this.updatePanel();
                    break;
                case 'clear':
                    this.clearLogs();
                    break;
                case 'close':
                    this.hidePanel();
                    break;
            }
        });

        // 标签切换事件
        this.panel.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            if (target.classList.contains('devtools-tab')) {
                const tabName = target.getAttribute('data-tab');
                this.switchTab(tabName!);
            }
        });
    }

    /**
     * 切换标签
     */
    private switchTab(tabName: string): void {
        if (!this.panel) return;

        // 更新标签状态
        const tabs = this.panel.querySelectorAll('.devtools-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.getAttribute('data-tab') === tabName);
        });

        // 更新面板状态
        const panels = this.panel.querySelectorAll('.devtools-panel');
        panels.forEach(panel => {
            panel.classList.toggle('active', panel.getAttribute('data-panel') === tabName);
        });

        // 更新内容
        this.updatePanelContent(tabName);
    }

    /**
     * 更新面板内容
     */
    private updatePanelContent(tabName: string): void {
        if (!this.panel) return;

        const panel = this.panel.querySelector(`[data-panel="${tabName}"]`);
        if (!panel) return;

        switch (tabName) {
            case 'apps':
                this.updateAppsPanel(panel);
                break;
            case 'logs':
                this.updateLogsPanel(panel);
                break;
            case 'performance':
                this.updatePerformancePanel(panel);
                break;
        }
    }

    /**
     * 更新应用面板
     */
    private updateAppsPanel(panel: Element): void {
        const appsList = panel.querySelector('.apps-list');
        if (!appsList) return;

        const appsHTML = Array.from(this.apps.values()).map(app => `
            <div class="app-item ${app.status}">
                <div class="app-name">${app.name}</div>
                <div class="app-status">${app.status}</div>
                <div class="app-details">
                    ${app.entry ? `<div>入口: ${app.entry}</div>` : ''}
                    ${app.container ? `<div>容器: ${app.container}</div>` : ''}
                    ${app.activeWhen ? `<div>激活规则: ${app.activeWhen}</div>` : ''}
                </div>
            </div>
        `).join('');

        appsList.innerHTML = appsHTML || '<div class="empty">暂无应用</div>';
    }

    /**
     * 更新日志面板
     */
    private updateLogsPanel(panel: Element): void {
        const logsList = panel.querySelector('.logs-list');
        if (!logsList) return;

        const logsHTML = this.logs.slice(-100).map(log => `
            <div class="log-item ${log.level}">
                <span class="log-time">${new Date(log.timestamp).toLocaleTimeString()}</span>
                <span class="log-level">[${log.level.toUpperCase()}]</span>
                ${log.appName ? `<span class="log-app">[${log.appName}]</span>` : ''}
                <span class="log-message">${log.message}</span>
            </div>
        `).join('');

        logsList.innerHTML = logsHTML || '<div class="empty">暂无日志</div>';
        logsList.scrollTop = logsList.scrollHeight;
    }

    /**
     * 更新性能面板
     */
    private updatePerformancePanel(panel: Element): void {
        const metricsContainer = panel.querySelector('.performance-metrics');
        if (!metricsContainer) return;

        const appsWithMetrics = Array.from(this.apps.values()).filter(app => app.metrics);

        const metricsHTML = appsWithMetrics.map(app => `
            <div class="metrics-item">
                <h4>${app.name}</h4>
                <div class="metrics-grid">
                    <div class="metric">
                        <label>加载时间</label>
                        <value>${app.metrics!.loadTime}ms</value>
                    </div>
                    <div class="metric">
                        <label>挂载时间</label>
                        <value>${app.metrics!.mountTime}ms</value>
                    </div>
                    <div class="metric">
                        <label>内存使用</label>
                        <value>${(app.metrics!.memoryUsage / 1024 / 1024).toFixed(2)}MB</value>
                    </div>
                    <div class="metric">
                        <label>错误数量</label>
                        <value>${app.metrics!.errorCount}</value>
                    </div>
                </div>
            </div>
        `).join('');

        metricsContainer.innerHTML = metricsHTML || '<div class="empty">暂无性能数据</div>';
    }

    /**
     * 设置性能监控
     */
    private setupPerformanceMonitor(): void {
        if (!('PerformanceObserver' in window)) {
            this.log('warn', '浏览器不支持 PerformanceObserver');
            return;
        }

        this.performanceObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
                if (entry.entryType === 'navigation') {
                    this.log('info', `页面加载时间: ${entry.duration.toFixed(2)}ms`);
                }
            });
        });

        this.performanceObserver.observe({ entryTypes: ['navigation', 'measure'] });
    }

    /**
     * 设置快捷键
     */
    private setupHotkeys(): void {
        document.addEventListener('keydown', this.handleKeydown);
    }

    /**
     * 处理键盘事件
     */
    private handleKeydown = (e: KeyboardEvent): void => {
        const { togglePanel, toggleInspector } = this.config.hotkeys;

        if (this.matchHotkey(e, togglePanel!)) {
            e.preventDefault();
            this.togglePanel();
        } else if (this.matchHotkey(e, toggleInspector!)) {
            e.preventDefault();
            this.toggleInspector();
        }
    };

    /**
     * 匹配快捷键
     */
    private matchHotkey(e: KeyboardEvent, hotkey: string): boolean {
        const parts = hotkey.split('+').map(part => part.trim());
        const key = parts.pop()!.toLowerCase();

        const modifiers = {
            ctrl: e.ctrlKey || e.metaKey,
            shift: e.shiftKey,
            alt: e.altKey
        };

        const requiredModifiers = parts.map(part => part.toLowerCase());

        return e.key.toLowerCase() === key &&
            requiredModifiers.every(mod => modifiers[mod as keyof typeof modifiers]) &&
            Object.keys(modifiers).filter(mod => modifiers[mod as keyof typeof modifiers]).length === requiredModifiers.length;
    }

    /**
     * 显示面板
     */
    showPanel(): void {
        if (!this.panel) {
            this.createPanel();
        }

        if (this.panel) {
            this.panel.style.display = 'flex';
            this.isVisible = true;
            this.updatePanel();
            this.emit('panelToggle', true);
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel(): void {
        if (this.panel) {
            this.panel.style.display = 'none';
            this.isVisible = false;
            this.emit('panelToggle', false);
        }
    }

    /**
     * 切换面板显示状态
     */
    togglePanel(): void {
        if (this.isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }

    /**
     * 切换检查器
     */
    toggleInspector(): void {
        // 这里可以实现应用检查器功能
        this.log('info', '应用检查器功能待实现');
    }

    /**
     * 更新面板
     */
    private updatePanel(): void {
        if (!this.panel || !this.isVisible) return;

        const activeTab = this.panel.querySelector('.devtools-tab.active');
        if (activeTab) {
            const tabName = activeTab.getAttribute('data-tab');
            if (tabName) {
                this.updatePanelContent(tabName);
            }
        }
    }

    /**
     * 记录日志
     */
    private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): void {
        if (!this.config.enableLogger) return;

        const logLevels = ['debug', 'info', 'warn', 'error'];
        const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
        const messageLevelIndex = logLevels.indexOf(level);

        if (messageLevelIndex < currentLevelIndex) return;

        const entry: LogEntry = {
            timestamp: Date.now(),
            level,
            message,
            data
        };

        this.logs.push(entry);

        // 限制日志数量
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-500);
        }

        // 触发事件
        this.emit('logEntry', entry);

        // 更新面板
        if (this.isVisible) {
            const activeTab = this.panel?.querySelector('.devtools-tab.active');
            if (activeTab?.getAttribute('data-tab') === 'logs') {
                this.updatePanelContent('logs');
            }
        }

        // 输出到控制台
        console[level](`[Micro-Core DevTools] ${message}`, data);
    }

    /**
     * 获取应用列表
     */
    getApps(): AppInfo[] {
        return Array.from(this.apps.values());
    }

    /**
     * 获取日志列表
     */
    getLogs(): LogEntry[] {
        return [...this.logs];
    }

    /**
     * 清空日志
     */
    clearLogs(): void {
        this.logs = [];
        if (this.isVisible) {
            this.updatePanelContent('logs');
        }
    }

    /**
     * 添加事件监听器
     */
    on<K extends keyof DevToolsEvents>(event: K, listener: DevToolsEvents[K]): void {
        this.eventListeners[event] = listener;
    }

    /**
     * 移除事件监听器
     */
    off<K extends keyof DevToolsEvents>(event: K): void {
        delete this.eventListeners[event];
    }

    /**
     * 触发事件
     */
    private emit<K extends keyof DevToolsEvents>(event: K, ...args: Parameters<DevToolsEvents[K]>): void {
        const listener = this.eventListeners[event];
        if (listener) {
            try {
                (listener as any)(...args);
            } catch (error) {
                console.error(`DevTools 事件监听器 ${event} 执行失败:`, error);
            }
        }
    }

    /**
     * 检查是否启用
     */
    static isEnabled(): boolean {
        return process.env.NODE_ENV === 'development' ||
            typeof window !== 'undefined' && window.location.search.includes('devtools=true');
    }

    /**
     * 创建插件实例
     */
    static create(config?: DevToolsConfig): DevToolsPlugin {
        return new DevToolsPlugin({
            enabled: DevToolsPlugin.isEnabled(),
            ...config
        });
    }
}