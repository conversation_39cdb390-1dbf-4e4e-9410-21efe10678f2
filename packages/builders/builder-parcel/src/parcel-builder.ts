/**
 * @fileoverview Parcel Builder - Optimized Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { 
  BaseBuilder, 
  BaseBuilderConfig, 
  BaseBuilderOptions, 
  BuildResult, 
  DevServerConfig,
  Config<PERSON>erger,
  Logger,
  PerformanceMonitor
} from '../../shared';

// Parcel 类型定义（避免直接依赖@parcel/core包）
interface ParcelTransformer {
  name: string;
  transform?: (asset: any) => Promise<any>;
}

interface ParcelConfig {
  entries?: string[];
  defaultConfig?: string;
  targets?: Record<string, {
    distDir?: string;
    publicUrl?: string;
    optimize?: boolean;
    engines?: {
      browsers?: string[];
      node?: string;
    };
  }>;
  mode?: 'development' | 'production';
  env?: Record<string, string>;
  transformers?: Record<string, string[]>;
  resolvers?: string[];
  bundler?: string;
  namers?: string[];
  runtimes?: string[];
  optimizers?: Record<string, string[]>;
  packagers?: Record<string, string>;
  reporters?: string[];
  validators?: Record<string, string[]>;
}

/**
 * Parcel构建器选项接口
 */
export interface ParcelBuilderOptions extends BaseBuilderOptions {
  /** Parcel特定配置 */
  parcelConfig?: Partial<ParcelConfig>;
  /** 目标环境 */
  target?: 'browser' | 'node' | 'electron-main' | 'electron-renderer';
  /** 是否优化输出 */
  optimize?: boolean;
  /** 是否启用热更新 */
  hmr?: boolean;
  /** 浏览器兼容性目标 */
  browsers?: string[];
}

/**
 * Parcel构建器配置接口
 */
export interface ParcelBuilderConfig extends BaseBuilderConfig {
  /** Parcel特定配置 */
  parcelConfig?: Partial<ParcelConfig>;
  /** 开发服务器配置 */
  devServer?: DevServerConfig;
}

/**
 * 创建Parcel微前端转换器
 */
export function createMicroCoreParcelTransformer(options: ParcelBuilderOptions = {}): ParcelTransformer {
  return {
    name: 'micro-core-parcel-transformer',
    
    async transform(asset: any) {
      Logger.debug('Transforming asset for micro-frontend', asset.filePath);
      
      // 生成微前端清单文件
      if (options.microApp && asset.filePath.endsWith('index.html')) {
        const manifest = {
          name: options.microApp.name,
          version: '1.0.0',
          entry: options.microApp.entry,
          routes: options.microApp.routes || [],
          permissions: options.microApp.permissions || [],
          externals: options.externals || {},
          timestamp: Date.now(),
          buildTool: 'parcel',
          buildToolVersion: '2.x'
        };
        
        Logger.debug('Generated micro-app manifest', manifest);
      }
      
      return asset;
    }
  };
}

/**
 * Parcel构建器类
 */
export class ParcelBuilder extends BaseBuilder {
  readonly name = 'builder-parcel';
  readonly version = '1.0.0';
  
  constructor(options: ParcelBuilderOptions = {}) {
    super(options);
    this.validateOptions();
  }
  
  /**
   * 创建Parcel配置
   */
  protected createBuilderConfig(config: ParcelBuilderConfig): ParcelConfig {
    Logger.debug('Creating Parcel config', config);
    
    const parcelOptions = this.options as ParcelBuilderOptions;
    const microApp = parcelOptions.microApp;
    
    const baseConfig: ParcelConfig = {
      entries: [config.entry || './src/index.html'],
      defaultConfig: '@parcel/config-default',
      targets: {
        [parcelOptions.target || 'browser']: {
          distDir: config.outDir || parcelOptions.outDir || 'dist',
          publicUrl: config.publicPath || parcelOptions.publicPath || './',
          optimize: !config.dev && parcelOptions.optimize !== false,
          engines: {
            browsers: parcelOptions.browsers || ['> 0.5%, last 2 versions, not dead']
          }
        }
      },
      mode: config.dev ? 'development' : 'production',
      env: {
        NODE_ENV: config.dev ? 'development' : 'production',
        __MICRO_APP_NAME__: microApp?.name || 'unknown',
        __MICRO_APP_VERSION__: '1.0.0'
      },
      transformers: {
        '*.{js,mjs,jsm,jsx,es6,cjs,ts,tsx}': ['@parcel/transformer-js'],
        '*.{css,less,sass,scss,stylus,styl}': ['@parcel/transformer-css'],
        '*.{html,htm,xhtml}': ['@parcel/transformer-html']
      },
      resolvers: ['@parcel/resolver-default'],
      bundler: '@parcel/bundler-default',
      namers: ['@parcel/namer-default'],
      runtimes: ['@parcel/runtime-js', '@parcel/runtime-browser-hmr'],
      optimizers: {
        '*.{js,mjs,cjs}': ['@parcel/optimizer-terser'],
        '*.{css,scss,sass,less,styl,stylus}': ['@parcel/optimizer-css']
      },
      packagers: {
        '*.{html,htm,xhtml}': '@parcel/packager-html',
        '*.{js,mjs,cjs}': '@parcel/packager-js',
        '*.{css,scss,sass,less,styl,stylus}': '@parcel/packager-css'
      },
      reporters: ['@parcel/reporter-dev-server']
    };
    
    // 合并用户自定义配置
    const finalConfig = ConfigMerger.deepMerge(
      baseConfig, 
      config.parcelConfig || {},
      parcelOptions.parcelConfig || {}
    );
    
    Logger.debug('Final Parcel config created', finalConfig);
    return finalConfig;
  }
  
  /**
   * 执行构建
   */
  protected async executeBuild(parcelConfig: ParcelConfig): Promise<BuildResult> {
    try {
      Logger.info('Starting Parcel build process');
      
      // 模拟构建过程（实际应调用 Parcel build API）
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const outputs = [
        {
          type: 'chunk' as const,
          fileName: 'index.js',
          size: 23456,
          code: '// Generated by Parcel\nconsole.log("Hello from micro-app");'
        },
        {
          type: 'asset' as const,
          fileName: 'index.html',
          size: 1234,
          source: '<!DOCTYPE html>\n<html>\n<head><title>Micro App</title></head>\n<body><div id="app"></div></body>\n</html>'
        },
        {
          type: 'asset' as const,
          fileName: 'style.css',
          size: 2345,
          source: '/* Generated styles by Parcel */\n.app { margin: 0; padding: 20px; }'
        }
      ];
      
      const totalSize = outputs.reduce((sum, output) => sum + output.size, 0);
      
      Logger.info(`Parcel build completed. Generated ${outputs.length} files (${PerformanceMonitor.formatSize(totalSize)})`);
      
      return {
        success: true,
        outputs,
        stats: {
          duration: 0, // Will be set by BaseBuilder
          fileCount: outputs.length,
          totalSize,
          errors: 0,
          warnings: 0
        }
      };
    } catch (error) {
      Logger.error('Parcel build failed', error);
      throw error;
    }
  }
  
  /**
   * 启动开发服务器
   */
  protected async startDevServer(parcelConfig: ParcelConfig, devServerConfig?: DevServerConfig): Promise<any> {
    try {
      Logger.info('Starting Parcel dev server');
      
      // 模拟开发服务器（实际应使用 Parcel dev server API）
      const mockServer = {
        config: parcelConfig,
        listening: true,
        port: devServerConfig?.port || 1234,
        host: devServerConfig?.host || 'localhost',
        close: async () => {
          Logger.info('Parcel dev server closed');
        },
        reload: () => {
          Logger.debug('Parcel dev server reloaded');
        },
        hmr: {
          broadcast: (data: any) => {
            Logger.debug('Parcel HMR broadcast', data);
          }
        }
      };
      
      Logger.info(`Parcel dev server started on http://${mockServer.host}:${mockServer.port}`);
      return mockServer;
    } catch (error) {
      Logger.error('Failed to start Parcel dev server', error);
      throw error;
    }
  }
  
  /**
   * 停止开发服务器
   */
  protected async stopDevServer(server: any): Promise<void> {
    try {
      Logger.info('Stopping Parcel dev server');
      await server.close();
    } catch (error) {
      Logger.error('Failed to stop Parcel dev server', error);
      throw error;
    }
  }
}

/**
 * 创建Parcel构建器实例
 */
export function createParcelBuilder(options?: ParcelBuilderOptions): ParcelBuilder {
  return new ParcelBuilder(options);
}

/**
 * 默认导出
 */
export default ParcelBuilder;