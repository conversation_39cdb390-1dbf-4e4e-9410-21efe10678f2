/**
 * Parcel 构建器类型定义
 */

export interface ParcelBuilderOptions {
    /** 应用名称 */
    appName: string;
    /** 入口文件路径 */
    entry: string;
    /** 输出目录 */
    outdir?: string;
    /** 目标环境 */
    target?: string;
    /** 构建模式 */
    mode?: 'development' | 'production';
    /** 是否生成清单文件 */
    generateManifest?: boolean;
}

export interface ParcelTransformerOptions {
    /** 应用名称 */
    appName: string;
    /** 是否启用开发模式 */
    isDev?: boolean;
}

export interface MicroAppManifest {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 入口文件 */
    entry: string;
    /** 资源文件 */
    assets: {
        js: string[];
        css: string[];
    };
    /** 构建工具 */
    buildTool: string;
    /** 构建时间 */
    buildTime: string;
}