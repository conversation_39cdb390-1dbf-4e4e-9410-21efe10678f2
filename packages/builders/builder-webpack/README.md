# @micro-core/builder-webpack

A comprehensive Webpack builder implementation for the Micro-Core ecosystem, providing seamless integration with Webpack's powerful bundling capabilities.

## 🚀 Features

- **Full Webpack Integration**: Complete support for Webpack 5+ features
- **Micro-Frontend Ready**: Built-in support for module federation
- **Development Server**: Hot module replacement and live reloading
- **Production Optimization**: Advanced code splitting and minification
- **TypeScript Support**: First-class TypeScript integration
- **Plugin Ecosystem**: Extensive plugin support and customization
- **Performance Monitoring**: Built-in build performance tracking
- **Error Handling**: Comprehensive error reporting and recovery

## 📦 Installation

```bash
pnpm add @micro-core/builder-webpack
# or
npm install @micro-core/builder-webpack
# or
yarn add @micro-core/builder-webpack
```

## 🔧 Quick Start

```typescript
import { WebpackBuilder } from '@micro-core/builder-webpack';

// Create builder instance
const builder = new WebpackBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist'
});

// Build project
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
```

## ⚙️ Configuration

### Basic Configuration

```typescript
const builder = new WebpackBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist',
  target: 'web',
  devtool: 'source-map'
});
```

### Advanced Configuration

```typescript
const builder = new WebpackBuilder({
  mode: 'production',
  entry: {
    main: './src/index.ts',
    vendor: './src/vendor.ts'
  },
  outDir: './dist',
  webpackConfig: {
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: 'ts-loader',
          exclude: /node_modules/
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    plugins: [
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify('production')
      })
    ],
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
});
```

## 🔌 API Reference

### WebpackBuilder Class

#### Constructor

```typescript
constructor(options: WebpackBuilderOptions)
```

**Parameters:**
- `options.mode`: Build mode ('development' | 'production')
- `options.entry`: Entry point(s) for the build
- `options.outDir`: Output directory
- `options.target`: Build target ('web' | 'node' | 'electron-main' | 'electron-renderer')
- `options.webpackConfig`: Custom Webpack configuration

#### Methods

##### `build(config: WebpackBuilderConfig): Promise<BuildResult>`

Execute the build process.

```typescript
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});
```

##### `serve(config: WebpackBuilderConfig, devServerConfig?: DevServerConfig): Promise<WebpackDevServer>`

Start the development server.

```typescript
const server = await builder.serve({
  entry: './src/index.ts',
  mode: 'development'
}, {
  port: 3000,
  host: 'localhost',
  hot: true
});
```

##### `stop(): Promise<void>`

Stop the development server.

```typescript
await builder.stop();
```

### Configuration Types

#### WebpackBuilderConfig

```typescript
interface WebpackBuilderConfig extends BaseBuilderConfig {
  target?: 'web' | 'node' | 'electron-main' | 'electron-renderer';
  webpackConfig?: webpack.Configuration;
  plugins?: webpack.WebpackPluginInstance[];
  rules?: webpack.RuleSetRule[];
  alias?: Record<string, string>;
  externals?: webpack.ExternalsElement;
}
```

#### DevServerConfig

```typescript
interface DevServerConfig {
  port?: number;
  host?: string;
  hot?: boolean;
  open?: boolean;
  https?: boolean;
  proxy?: Record<string, string>;
}
```

## 🏗️ Advanced Usage

### Module Federation

```typescript
const builder = new WebpackBuilder({
  mode: 'production',
  entry: './src/index.ts',
  webpackConfig: {
    plugins: [
      new ModuleFederationPlugin({
        name: 'shell',
        filename: 'remoteEntry.js',
        remotes: {
          mfe1: 'mfe1@http://localhost:3001/remoteEntry.js'
        },
        shared: {
          react: { singleton: true },
          'react-dom': { singleton: true }
        }
      })
    ]
  }
});
```

### Custom Loaders and Plugins

```typescript
const builder = new WebpackBuilder({
  mode: 'development',
  entry: './src/index.ts',
  webpackConfig: {
    module: {
      rules: [
        {
          test: /\.vue$/,
          loader: 'vue-loader'
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/,
          type: 'asset/resource'
        }
      ]
    },
    plugins: [
      new VueLoaderPlugin(),
      new webpack.HotModuleReplacementPlugin()
    ]
  }
});
```

### Environment-Specific Configuration

```typescript
const isDevelopment = process.env.NODE_ENV === 'development';

const builder = new WebpackBuilder({
  mode: isDevelopment ? 'development' : 'production',
  entry: './src/index.ts',
  webpackConfig: {
    devtool: isDevelopment ? 'eval-source-map' : 'source-map',
    optimization: {
      minimize: !isDevelopment,
      splitChunks: {
        chunks: 'all'
      }
    }
  }
});
```

## 🧪 Testing

The builder includes comprehensive test coverage:

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## 📊 Performance Tips

1. **Enable Caching**: Use Webpack's built-in caching for faster rebuilds
2. **Code Splitting**: Implement dynamic imports for better loading performance
3. **Tree Shaking**: Enable tree shaking to eliminate dead code
4. **Bundle Analysis**: Use webpack-bundle-analyzer to optimize bundle size

```typescript
const builder = new WebpackBuilder({
  webpackConfig: {
    cache: {
      type: 'filesystem'
    },
    optimization: {
      usedExports: true,
      sideEffects: false
    }
  }
});
```

## 🔍 Troubleshooting

### Common Issues

**Build fails with TypeScript errors:**
```bash
# Ensure TypeScript is properly configured
pnpm add -D typescript ts-loader
```

**Module not found errors:**
```typescript
// Check resolve configuration
webpackConfig: {
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  }
}
```

**Memory issues during build:**
```bash
# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=4096" pnpm build
```

### Debug Mode

Enable debug logging for detailed build information:

```typescript
const builder = new WebpackBuilder({
  debug: true,
  // ... other options
});
```

## 🔄 Migration Guide

### From Webpack CLI

```typescript
// Before (webpack.config.js)
module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js'
  }
};

// After (Micro-Core)
const builder = new WebpackBuilder({
  entry: './src/index.js',
  outDir: './dist'
});
```

### From Other Builders

```typescript
// Easy migration from other builders
const builder = new WebpackBuilder({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});
```

## 📄 License

MIT © 2025 Micro-Core

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 📖 Documentation: [Micro-Core Docs](https://micro-core.dev)
