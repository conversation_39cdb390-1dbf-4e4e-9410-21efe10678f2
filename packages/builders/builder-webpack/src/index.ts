/**
 * @fileoverview Webpack Builder - Optimized Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { 
  BaseBuilder, 
  BaseBuilderConfig, 
  BaseBuilderOptions, 
  BuildResult, 
  DevServerConfig,
  <PERSON><PERSON>g<PERSON>er<PERSON>,
  <PERSON>gger,
  PerformanceMonitor
} from '../../shared';

// Webpack 类型定义（避免直接依赖webpack包）
interface WebpackConfig {
    mode?: 'development' | 'production' | 'none';
    entry?: string | string[] | Record<string, string | string[]>;
    output?: {
        path?: string;
        filename?: string;
        publicPath?: string;
        library?: string;
        libraryTarget?: string;
        chunkFilename?: string;
    };
    module?: {
        rules?: Array<{
            test?: RegExp;
            use?: string | Array<string | { loader: string; options?: any }>;
            exclude?: RegExp;
        }>;
    };
    plugins?: any[];
    resolve?: {
        extensions?: string[];
        alias?: Record<string, string>;
    };
    externals?: Record<string, string> | Array<string | Record<string, string>>;
    devServer?: {
        port?: number;
        host?: string;
        hot?: boolean;
        headers?: Record<string, string>;
    };
}

export interface WebpackBuilderOptions {
    /** 是否启用开发模式 */
    dev?: boolean;
    /** 构建输出目录 */
    outDir?: string;
    /** 公共路径 */
    publicPath?: string;
    /** 是否启用模块联邦 */
    enableModuleFederation?: boolean;
    /** 微前端配置 */
    microConfig?: {
        /** 应用名称 */
        name: string;
        /** 入口文件 */
        entry?: string;
        /** 暴露的模块 */
        exposes?: Record<string, string>;
        /** 远程模块 */
        remotes?: Record<string, string>;
        /** 共享依赖 */
        shared?: Record<string, any>;
    };
    /** 自定义 Webpack 配置 */
    webpackConfig?: Partial<WebpackConfig>;
    /** 是否启用日志 */
    enableLogging?: boolean;
}

/**
 * Webpack 微前端插件
 */
export class MicroCoreWebpackPlugin {
    private options: WebpackBuilderOptions;

    constructor(options: WebpackBuilderOptions = {}) {
        this.options = options;
    }

    apply(compiler: any): void {
        const { microConfig, enableLogging } = this.options;

        // 在编译开始时执行
        compiler.hooks.beforeCompile.tapAsync('MicroCoreWebpackPlugin', (params: any, callback: any) => {
            if (enableLogging) {
                console.log('[MicroCoreWebpackPlugin] 开始编译微前端应用');
            }
            callback();
        });

        // 在生成资源时执行
        compiler.hooks.emit.tapAsync('MicroCoreWebpackPlugin', (compilation: any, callback: any) => {
            if (microConfig) {
                // 生成微前端清单文件
                const manifest = {
                    name: microConfig.name,
                    entry: `${microConfig.name}.js`,
                    exposes: microConfig.exposes || {},
                    remotes: microConfig.remotes || {},
                    shared: microConfig.shared || {},
                    version: '1.0.0',
                    buildTime: new Date().toISOString()
                };

                const manifestContent = JSON.stringify(manifest, null, 2);

                // 添加清单文件到编译输出
                compilation.assets['micro-manifest.json'] = {
                    source: () => manifestContent,
                    size: () => manifestContent.length
                };

                if (enableLogging) {
                    console.log(`[MicroCoreWebpackPlugin] 已生成微前端清单: ${microConfig.name}`);
                }
            }

            callback();
        });

        // 编译完成时执行
        compiler.hooks.done.tap('MicroCoreWebpackPlugin', (stats: any) => {
            if (enableLogging) {
                const { errors, warnings } = stats.compilation;
                if (errors.length > 0) {
                    console.error('[MicroCoreWebpackPlugin] 编译出现错误:', errors);
                }
                if (warnings.length > 0) {
                    console.warn('[MicroCoreWebpackPlugin] 编译出现警告:', warnings);
                }
                console.log('[MicroCoreWebpackPlugin] 微前端应用编译完成');
            }
        });
    }
}

/**
 * 创建 Webpack 配置
 */
export function createMicroCoreWebpackConfig(options: WebpackBuilderOptions = {}): WebpackConfig {
    const {
        dev = false,
        outDir = 'dist',
        publicPath = '/',
        enableModuleFederation = false,
        microConfig,
        webpackConfig = {}
    } = options;

    const isDev = dev;

    // 基础配置
    const baseConfig: WebpackConfig = {
        mode: isDev ? 'development' : 'production',
        entry: microConfig?.entry || './src/index.ts',
        output: {
            path: outDir,
            filename: isDev ? '[name].js' : '[name].[contenthash].js',
            publicPath: publicPath,
            chunkFilename: isDev ? '[name].chunk.js' : '[name].[contenthash].chunk.js'
        },
        module: {
            rules: [
                {
                    test: /\.(ts|tsx)$/,
                    use: [
                        {
                            loader: 'ts-loader',
                            options: {
                                transpileOnly: true
                            }
                        }
                    ],
                    exclude: /node_modules/
                },
                {
                    test: /\.(js|jsx)$/,
                    use: [
                        {
                            loader: 'babel-loader',
                            options: {
                                presets: ['@babel/preset-env', '@babel/preset-react']
                            }
                        }
                    ],
                    exclude: /node_modules/
                },
                {
                    test: /\.css$/,
                    use: ['style-loader', 'css-loader']
                },
                {
                    test: /\.(png|jpg|jpeg|gif|svg)$/,
                    use: [
                        {
                            loader: 'file-loader',
                            options: {
                                name: '[name].[hash].[ext]',
                                outputPath: 'images/'
                            }
                        }
                    ]
                }
            ]
        },
        resolve: {
            extensions: ['.ts', '.tsx', '.js', '.jsx', '.json']
        },
        plugins: [
            new MicroCoreWebpackPlugin(options)
        ]
    };

    // 微前端特定配置
    if (microConfig) {
        // 库模式配置（用于子应用）
        baseConfig.output = {
            ...baseConfig.output,
            library: microConfig.name,
            libraryTarget: 'umd'
        };

        // 外部化依赖
        baseConfig.externals = {
            'react': 'React',
            'react-dom': 'ReactDOM',
            'vue': 'Vue',
            '@angular/core': 'ng.core',
            '@angular/common': 'ng.common'
        };
    }

    // 模块联邦配置
    if (enableModuleFederation && microConfig) {
        try {
            // 动态导入 ModuleFederationPlugin（避免编译时错误）
            const ModuleFederationPlugin = require('@module-federation/webpack');

            baseConfig.plugins = baseConfig.plugins || [];
            baseConfig.plugins.push(
                new ModuleFederationPlugin({
                    name: microConfig.name,
                    filename: 'remoteEntry.js',
                    exposes: microConfig.exposes || {},
                    remotes: microConfig.remotes || {},
                    shared: {
                        react: { singleton: true },
                        'react-dom': { singleton: true },
                        vue: { singleton: true },
                        ...microConfig.shared
                    }
                })
            );
        } catch (error) {
            console.warn('[MicroCoreWebpackConfig] 模块联邦插件未找到，跳过模块联邦配置');
        }
    }

    // 开发模式配置
    if (isDev) {
        baseConfig.devServer = {
            port: 3000,
            host: '0.0.0.0',
            hot: true,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
                'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
            }
        };
    }

    // 合并自定义配置
    return mergeWebpackConfig(baseConfig, webpackConfig);
}

/**
 * 合并 Webpack 配置
 */
function mergeWebpackConfig(base: WebpackConfig, custom: Partial<WebpackConfig>): WebpackConfig {
    return {
        ...base,
        ...custom,
        output: {
            ...base.output,
            ...custom.output
        },
        module: {
            rules: [
                ...(base.module?.rules || []),
                ...(custom.module?.rules || [])
            ]
        },
        plugins: [
            ...(base.plugins || []),
            ...(custom.plugins || [])
        ],
        resolve: {
            ...base.resolve,
            ...custom.resolve,
            extensions: [
                ...(base.resolve?.extensions || []),
                ...(custom.resolve?.extensions || [])
            ]
        }
    };
}

/**
 * Webpack 构建器类
 */
export class WebpackBuilder implements Plugin {
    name = 'builder-webpack';
    version = '1.0.0';

    private kernel?: MicroCoreKernel;
    private options: WebpackBuilderOptions;

    constructor(options: WebpackBuilderOptions = {}) {
        this.options = options;
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        this.kernel = kernel;

        // 注册构建器
        kernel.registerBuilder?.('webpack', {
            createConfig: (config: any) => {
                return this.createConfig(config);
            },
            build: (config: any) => {
                return this.build(config);
            }
        });

        if (this.options.enableLogging) {
            console.log('[WebpackBuilder] Webpack 构建适配器已安装');
        }
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 注销构建器
        kernel.unregisterBuilder?.('webpack');

        this.kernel = undefined;

        if (this.options.enableLogging) {
            console.log('[WebpackBuilder] Webpack 构建适配器已卸载');
        }
    }

    /**
     * 创建 Webpack 配置
     */
    createConfig(config: any): WebpackConfig {
        return createMicroCoreWebpackConfig({
            ...this.options,
            ...config
        });
    }

    /**
     * 执行构建
     */
    async build(config: any): Promise<void> {
        try {
            // 动态导入 webpack（避免编译时错误）
            const webpack = require('webpack');

            const webpackConfig = this.createConfig(config);
            const compiler = webpack(webpackConfig);

            return new Promise((resolve, reject) => {
                compiler.run((err: any, stats: any) => {
                    if (err) {
                        console.error('[WebpackBuilder] 构建失败:', err);
                        reject(err);
                        return;
                    }

                    if (stats.hasErrors()) {
                        const errors = stats.toJson().errors;
                        console.error('[WebpackBuilder] 构建出现错误:', errors);
                        reject(new Error('构建出现错误'));
                        return;
                    }

                    if (this.options.enableLogging) {
                        console.log('[WebpackBuilder] 构建完成');
                        console.log(stats.toString({ colors: true }));
                    }

                    resolve();
                });
            });
        } catch (error) {
            console.error('[WebpackBuilder] 构建器执行失败:', error);
            throw error;
        }
    }

    /**
     * 启动开发服务器
     */
    async serve(config: any): Promise<void> {
        try {
            // 动态导入 webpack-dev-server（避免编译时错误）
            const webpack = require('webpack');
            const WebpackDevServer = require('webpack-dev-server');

            const webpackConfig = this.createConfig({ ...config, dev: true });
            const compiler = webpack(webpackConfig);

            const devServerOptions = webpackConfig.devServer || {};
            const server = new WebpackDevServer(devServerOptions, compiler);

            await server.start();

            if (this.options.enableLogging) {
                console.log(`[WebpackBuilder] 开发服务器已启动: http://${devServerOptions.host || 'localhost'}:${devServerOptions.port || 3000}`);
            }
        } catch (error) {
            console.error('[WebpackBuilder] 开发服务器启动失败:', error);
            throw error;
        }
    }

    /**
     * 获取构建器状态
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            options: this.options
        };
    }
}

/**
 * 创建 Webpack 构建器实例
 */
export function createWebpackBuilder(options?: WebpackBuilderOptions): WebpackBuilder {
    return new WebpackBuilder(options);
}

/**
 * 默认导出
 */
export default WebpackBuilder;