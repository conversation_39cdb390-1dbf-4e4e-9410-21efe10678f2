/**
 * @fileoverview Webpack插件集合
 * <AUTHOR> <<EMAIL>>
 */

import webpack from 'webpack';
import type { WebpackBuilderConfig, WebpackPluginOptions } from './types';

/**
 * 微前端专用Webpack插件
 */
export class MicroFrontendPlugin implements webpack.WebpackPluginInstance {
    private options: any;

    constructor(options: any = {}) {
        this.options = options;
    }

    apply(compiler: webpack.Compiler): void {
        const pluginName = 'MicroFrontendPlugin';

        compiler.hooks.compilation.tap(pluginName, (compilation) => {
            // 在这里添加微前端特定的编译逻辑
            compilation.hooks.processAssets.tap(
                {
                    name: pluginName,
                    stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE
                },
                () => {
                    // 处理微前端资源
                    this.processMicroFrontendAssets(compilation);
                }
            );
        });
    }

    private processMicroFrontendAssets(compilation: webpack.Compilation): void {
        // 添加微前端元数据
        const manifest = {
            name: this.options.name || 'micro-app',
            version: this.options.version || '1.0.0',
            entry: this.options.entry || 'index.js',
            assets: Object.keys(compilation.assets)
        };

        const manifestJson = JSON.stringify(manifest, null, 2);
        compilation.emitAsset('micro-manifest.json', new webpack.sources.RawSource(manifestJson));
    }
}

/**
 * 获取默认插件配置
 */
export function getDefaultPlugins(config: WebpackBuilderConfig): WebpackPluginOptions[] {
    const plugins: WebpackPluginOptions[] = [];

    // HTML插件
    if (config.microApp?.entry) {
        plugins.push({
            name: 'HtmlWebpackPlugin',
            plugin: require('html-webpack-plugin'),
            options: {
                template: 'public/index.html',
                inject: true
            },
            enabled: true
        });
    }

    // 微前端插件
    plugins.push({
        name: 'MicroFrontendPlugin',
        plugin: MicroFrontendPlugin,
        options: {
            name: config.microApp?.library?.name || 'micro-app',
            version: '1.0.0',
            entry: config.microApp?.entry
        },
        enabled: true
    });

    // 模块联邦插件
    if (config.microApp?.federation) {
        plugins.push({
            name: 'ModuleFederationPlugin',
            plugin: webpack.container.ModuleFederationPlugin,
            options: {
                name: config.microApp.library?.name || 'micro-app',
                filename: 'remoteEntry.js',
                shared: config.microApp.shared || {}
            },
            enabled: true
        });
    }

    return plugins;
}

/**
 * 创建插件实例
 */
export function createPluginInstances(plugins: WebpackPluginOptions[]): webpack.WebpackPluginInstance[] {
    return plugins
        .filter(plugin => plugin.enabled !== false)
        .filter(plugin => !plugin.condition || plugin.condition({} as WebpackBuilderConfig))
        .map(plugin => new plugin.plugin(plugin.options || {}));
}

/**
 * 合并插件配置
 */
export function mergePlugins(
    defaultPlugins: WebpackPluginOptions[],
    userPlugins: WebpackPluginOptions[] = []
): WebpackPluginOptions[] {
    const pluginMap = new Map<string, WebpackPluginOptions>();

    // 添加默认插件
    defaultPlugins.forEach(plugin => {
        pluginMap.set(plugin.name, plugin);
    });

    // 覆盖或添加用户插件
    userPlugins.forEach(plugin => {
        pluginMap.set(plugin.name, plugin);
    });

    return Array.from(pluginMap.values());
}