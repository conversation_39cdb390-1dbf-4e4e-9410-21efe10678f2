/**
 * @fileoverview Webpack构建器类型定义
 * <AUTHOR> <<EMAIL>>
 */

import type webpack from 'webpack';
import type WebpackDevServer from 'webpack-dev-server';
import type { BaseBuilderConfig, BuildResult } from '../../src/types';

/**
 * Webpack构建器配置
 */
export interface WebpackBuilderConfig extends BaseBuilderConfig {
    /** Webpack原生配置 */
    webpack?: webpack.Configuration;
    /** 开发服务器配置 */
    devServer?: WebpackDevServerConfig;
    /** 微应用特定配置 */
    microApp?: WebpackMicroAppConfig;
    /** 插件配置 */
    plugins?: WebpackPluginOptions[];
}

/**
 * Webpack开发服务器配置
 */
export interface WebpackDevServerConfig extends WebpackDevServer.Configuration {
    /** 端口号 */
    port?: number;
    /** 主机地址 */
    host?: string;
    /** 是否启用HTTPS */
    https?: boolean;
    /** 是否启用热更新 */
    hot?: boolean;
    /** 是否自动打开浏览器 */
    open?: boolean;
    /** 代理配置 */
    proxy?: Record<string, any>;
    /** CORS配置 */
    allowedHosts?: string[] | 'all' | 'auto';
}

/**
 * 微应用配置
 */
export interface WebpackMicroAppConfig {
    /** 入口文件 */
    entry?: string | string[] | Record<string, string>;
    /** 输出目录 */
    outDir?: string;
    /** 公共路径 */
    publicPath?: string;
    /** 是否启用模块联邦 */
    federation?: boolean;
    /** 外部依赖 */
    externals?: string[] | Record<string, string>;
    /** 共享模块 */
    shared?: Record<string, any>;
    /** 是否启用代码分割 */
    codeSplitting?: boolean;
    /** 库配置 */
    library?: {
        name?: string;
        type?: string;
        export?: string;
    };
    /** 运行时chunk名称 */
    runtimeChunk?: boolean | string;
    /** 优化配置 */
    optimization?: {
        minimize?: boolean;
        splitChunks?: boolean | webpack.Configuration['optimization']['splitChunks'];
        usedExports?: boolean;
        sideEffects?: boolean;
    };
}

/**
 * Webpack插件选项
 */
export interface WebpackPluginOptions {
    /** 插件名称 */
    name: string;
    /** 插件构造函数 */
    plugin: new (...args: any[]) => webpack.WebpackPluginInstance;
    /** 插件参数 */
    options?: any;
    /** 是否启用 */
    enabled?: boolean;
    /** 应用条件 */
    condition?: (config: WebpackBuilderConfig) => boolean;
}

/**
 * Webpack构建结果
 */
export interface WebpackBuildResult extends BuildResult {
    /** Webpack统计信息 */
    stats?: webpack.Stats;
    /** 编译信息 */
    compilation?: webpack.Compilation;
    /** 资源信息 */
    assets?: WebpackAssetInfo[];
    /** 模块信息 */
    modules?: WebpackModuleInfo[];
    /** 性能信息 */
    performance?: WebpackPerformanceInfo;
}

/**
 * Webpack资源信息
 */
export interface WebpackAssetInfo {
    /** 资源名称 */
    name: string;
    /** 资源大小 */
    size: number;
    /** 资源类型 */
    type: 'javascript' | 'stylesheet' | 'html' | 'json' | 'asset';
    /** 是否为入口资源 */
    isEntry?: boolean;
    /** 是否为初始资源 */
    isInitial?: boolean;
    /** 相关chunks */
    chunks?: string[];
    /** 源映射文件 */
    sourceMap?: string;
}

/**
 * Webpack模块信息
 */
export interface WebpackModuleInfo {
    /** 模块ID */
    id: string | number;
    /** 模块名称 */
    name: string;
    /** 模块大小 */
    size: number;
    /** 模块类型 */
    type: string;
    /** 是否为入口模块 */
    isEntry?: boolean;
    /** 依赖模块 */
    dependencies?: string[];
    /** 所属chunks */
    chunks?: string[];
}

/**
 * Webpack性能信息
 */
export interface WebpackPerformanceInfo {
    /** 构建时间 */
    buildTime: number;
    /** 编译时间 */
    compileTime: number;
    /** 总资源大小 */
    totalSize: number;
    /** 入口资源大小 */
    entrySize: number;
    /** chunk数量 */
    chunkCount: number;
    /** 模块数量 */
    moduleCount: number;
    /** 缓存命中率 */
    cacheHitRate?: number;
}

/**
 * Webpack加载器配置
 */
export interface WebpackLoaderConfig {
    /** 测试规则 */
    test: RegExp;
    /** 加载器名称或配置 */
    use: string | string[] | webpack.RuleSetUseItem[];
    /** 排除规则 */
    exclude?: RegExp | string | string[];
    /** 包含规则 */
    include?: RegExp | string | string[];
    /** 加载器选项 */
    options?: any;
}

/**
 * Webpack模块联邦配置
 */
export interface WebpackModuleFederationConfig {
    /** 应用名称 */
    name: string;
    /** 文件名 */
    filename?: string;
    /** 暴露的模块 */
    exposes?: Record<string, string>;
    /** 远程模块 */
    remotes?: Record<string, string>;
    /** 共享依赖 */
    shared?: Record<string, any>;
    /** 库配置 */
    library?: {
        type: string;
        name?: string;
    };
}

/**
 * Webpack环境变量
 */
export interface WebpackEnvironment {
    /** 构建模式 */
    mode: 'development' | 'production' | 'test';
    /** 是否为开发模式 */
    isDev: boolean;
    /** 是否为生产模式 */
    isProd: boolean;
    /** 是否为测试模式 */
    isTest: boolean;
    /** Node环境 */
    nodeEnv: string;
    /** 自定义环境变量 */
    custom?: Record<string, string>;
}

/**
 * Webpack钩子上下文
 */
export interface WebpackHookContext {
    /** 构建器配置 */
    config: WebpackBuilderConfig;
    /** 编译器实例 */
    compiler?: webpack.Compiler;
    /** 编译信息 */
    compilation?: webpack.Compilation;
    /** 环境信息 */
    environment: WebpackEnvironment;
    /** 是否为开发模式 */
    isDev: boolean;
    /** 是否为生产模式 */
    isProd: boolean;
}