/**
 * @file webpack-builder.ts
 * @description Webpack构建器实现
 * <AUTHOR>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { BaseBuilder } from '../../shared/base-builder';
import { BaseBuilderConfig, BaseBuilderOptions, BuildResult, DevServerConfig } from '../../shared/types';
import { Lo<PERSON>, ConfigMerger, PerformanceMonitor } from '../../shared/utils';

// Webpack相关接口定义（避免直接依赖）
interface WebpackBuilderOptions extends BaseBuilderOptions {
  webpackConfig?: any; // Webpack配置对象
  alias?: Record<string, string>;
  externals?: Record<string, string>;
}

interface WebpackBuilderConfig extends BaseBuilderConfig {
  webpackConfig?: any; // 用户自定义Webpack配置
}

interface WebpackConfig {
  mode?: 'development' | 'production';
  entry?: string | Record<string, string>;
  output?: {
    path?: string;
    filename?: string;
    publicPath?: string;
    library?: string;
    libraryTarget?: string;
    chunkFilename?: string;
    clean?: boolean;
  };
  resolve?: {
    extensions?: string[];
    alias?: Record<string, string>;
  };
  externals?: Record<string, string>;
  module?: {
    rules?: any[];
  };
  plugins?: any[];
  devServer?: {
    port?: number;
    host?: string;
    hot?: boolean;
    headers?: Record<string, string>;
  };
}

/**
 * Webpack构建器实现
 * 提供基于Webpack的微应用构建能力
 */
export class WebpackBuilder extends BaseBuilder {
  readonly name = 'builder-webpack';
  readonly version = '1.0.0';

  constructor(options: WebpackBuilderOptions = {}) {
    super(options);
    this.validateOptions();
    Logger.info(`Initialized ${this.name} v${this.version}`);
  }

  /**
   * 验证构建器选项
   */
  protected validateOptions(): void {
    const webpackOptions = this.options as WebpackBuilderOptions;
    
    if (webpackOptions.webpackConfig && typeof webpackOptions.webpackConfig !== 'object') {
      throw new Error('Webpack config must be an object');
    }
    
    Logger.debug('WebpackBuilder options validated successfully');
  }

  /**
   * 创建构建器配置（实现BaseBuilder的抽象方法）
   */
  protected createBuilderConfig(config: WebpackBuilderConfig): WebpackConfig {
    Logger.debug('Creating Webpack config', config);
    
    const webpackOptions = this.options as WebpackBuilderOptions;
    
    const baseConfig: WebpackConfig = {
      mode: config.dev ? 'development' : 'production',
      entry: config.entry || './src/index.ts',
      output: {
        path: config.outDir || 'dist',
        filename: config.dev ? '[name].js' : '[name].[contenthash].js',
        publicPath: config.publicPath || '/',
        clean: true
      },
      resolve: {
        extensions: ['.js', '.ts', '.jsx', '.tsx', '.json'],
        alias: webpackOptions.alias || {}
      },
      externals: webpackOptions.externals || {},
      module: {
        rules: [
          {
            test: /\.(js|jsx|ts|tsx)$/,
            exclude: /node_modules/,
            use: [
              {
                loader: 'babel-loader',
                options: {
                  presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript']
                }
              }
            ]
          },
          {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
          }
        ]
      },
      plugins: [],
      devServer: {
        port: config.devServer?.port || 3000,
        host: config.devServer?.host || '0.0.0.0',
        hot: config.devServer?.hot !== false,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          ...(config.devServer?.headers || {})
        }
      }
    };
    
    // 合并用户自定义配置
    const finalConfig = ConfigMerger.deepMerge(
      baseConfig, 
      config.webpackConfig || {},
      webpackOptions.webpackConfig || {}
    );
    
    Logger.debug('Final Webpack config created', finalConfig);
    return finalConfig;
  }

  /**
   * 执行构建（实现BaseBuilder的抽象方法）
   */
  protected async executeBuild(webpackConfig: WebpackConfig): Promise<BuildResult> {
    try {
      Logger.info('Starting Webpack build process');
      
      // 模拟构建过程（实际应调用 Webpack build API）
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const outputs = [
        {
          type: 'chunk' as const,
          fileName: 'main.js',
          size: 45678,
          code: '// Generated by Webpack\nconsole.log("Hello from webpack micro-app");'
        },
        {
          type: 'asset' as const,
          fileName: 'main.css',
          size: 3456,
          source: '/* Generated styles */\n.webpack-app { padding: 20px; }'
        }
      ];
      
      const totalSize = outputs.reduce((sum, output) => sum + output.size, 0);
      
      Logger.info(`Webpack build completed. Generated ${outputs.length} files (${PerformanceMonitor.formatSize(totalSize)})`);
      
      return {
        success: true,
        outputs,
        stats: {
          duration: 0, // Will be set by BaseBuilder
          fileCount: outputs.length,
          totalSize,
          errors: 0,
          warnings: 0
        }
      };
    } catch (error) {
      Logger.error('Webpack build failed', error);
      throw error;
    }
  }

  /**
   * 启动开发服务器（实现BaseBuilder的抽象方法）
   */
  protected async startDevServer(webpackConfig: WebpackConfig, devServerConfig?: DevServerConfig): Promise<any> {
    try {
      Logger.info('Starting Webpack dev server');
      
      // 模拟开发服务器（实际应调用 Webpack DevServer API）
      const mockServer = {
        config: webpackConfig,
        listen: async (port?: number) => {
          Logger.info(`Webpack dev server listening on port ${port || webpackConfig.devServer?.port || 3000}`);
          return mockServer;
        },
        close: async () => {
          Logger.info('Webpack dev server closed');
        },
        sockWrite: (type: string, data: any) => {
          Logger.debug(`WebSocket message sent: ${type}`, data);
        },
        use: (path: string, handler: any) => {
          Logger.debug(`Middleware registered for ${path}`);
        }
      };
      
      await mockServer.listen(devServerConfig?.port || webpackConfig.devServer?.port);
      return mockServer;
    } catch (error) {
      Logger.error('Failed to start Webpack dev server', error);
      throw error;
    }
  }

  /**
   * 停止开发服务器（实现BaseBuilder的抽象方法）
   */
  protected async stopDevServer(server: any): Promise<void> {
    try {
      Logger.info('Stopping Webpack dev server');
      await server.close();
    } catch (error) {
      Logger.error('Failed to stop Webpack dev server', error);
      throw error;
    }
  }
}

/**
 * 创建Webpack构建器实例的工厂函数
 */
export function createWebpackBuilder(options?: WebpackBuilderOptions): WebpackBuilder {
  return new WebpackBuilder(options);
}
