/**
 * @fileoverview Webpack构建器工具函数
 * <AUTHOR> <<EMAIL>>
 */

import path from 'path';
import webpack from 'webpack';
import type { WebpackBuilderConfig, WebpackEnvironment, WebpackLoaderConfig } from './types';

/**
 * 创建环境变量对象
 */
export function createEnvironment(mode: string = 'development'): WebpackEnvironment {
    const isDev = mode === 'development';
    const isProd = mode === 'production';
    const isTest = mode === 'test';

    return {
        mode: mode as any,
        isDev,
        isProd,
        isTest,
        nodeEnv: process.env.NODE_ENV || mode,
        custom: {
            ...process.env
        }
    };
}

/**
 * 解析入口配置
 */
export function resolveEntry(entry: string | string[] | Record<string, string>): Record<string, string> {
    if (typeof entry === 'string') {
        return { main: entry };
    }

    if (Array.isArray(entry)) {
        return { main: entry[0] };
    }

    return entry;
}

/**
 * 解析输出配置
 */
export function resolveOutput(config: WebpackBuilderConfig): webpack.Configuration['output'] {
    const microApp = config.microApp || {};
    const isDev = config.mode === 'development';

    return {
        path: path.resolve(process.cwd(), microApp.outDir || 'dist'),
        filename: isDev ? '[name].js' : '[name].[contenthash:8].js',
        chunkFilename: isDev ? '[name].chunk.js' : '[name].[contenthash:8].chunk.js',
        publicPath: microApp.publicPath || '/',
        clean: true,
        library: microApp.library?.name,
        libraryTarget: microApp.library?.type as any || 'umd',
        libraryExport: microApp.library?.export,
        globalObject: 'this'
    };
}

/**
 * 获取默认加载器配置
 */
export function getDefaultLoaders(): WebpackLoaderConfig[] {
    return [
        // TypeScript加载器
        {
            test: /\.(ts|tsx)$/,
            use: [
                {
                    loader: 'ts-loader',
                    options: {
                        transpileOnly: true,
                        compilerOptions: {
                            module: 'esnext'
                        }
                    }
                }
            ],
            exclude: /node_modules/
        },

        // JavaScript加载器
        {
            test: /\.(js|jsx)$/,
            use: [
                {
                    loader: 'babel-loader',
                    options: {
                        presets: [
                            ['@babel/preset-env', { modules: false }],
                            '@babel/preset-react'
                        ],
                        plugins: [
                            '@babel/plugin-proposal-class-properties',
                            '@babel/plugin-syntax-dynamic-import'
                        ]
                    }
                }
            ],
            exclude: /node_modules/
        },

        // CSS加载器
        {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
        },

        // SCSS加载器
        {
            test: /\.(scss|sass)$/,
            use: ['style-loader', 'css-loader', 'sass-loader']
        },

        // Less加载器
        {
            test: /\.less$/,
            use: ['style-loader', 'css-loader', 'less-loader']
        },

        // 图片加载器
        {
            test: /\.(png|jpe?g|gif|svg)$/i,
            use: [
                {
                    loader: 'file-loader',
                    options: {
                        name: '[name].[hash:8].[ext]',
                        outputPath: 'images'
                    }
                }
            ]
        },

        // 字体加载器
        {
            test: /\.(woff|woff2|eot|ttf|otf)$/i,
            use: [
                {
                    loader: 'file-loader',
                    options: {
                        name: '[name].[hash:8].[ext]',
                        outputPath: 'fonts'
                    }
                }
            ]
        }
    ];
}

/**
 * 创建优化配置
 */
export function createOptimization(config: WebpackBuilderConfig): webpack.Configuration['optimization'] {
    const microApp = config.microApp || {};
    const isDev = config.mode === 'development';
    const optimization = microApp.optimization || {};

    const baseOptimization: webpack.Configuration['optimization'] = {
        minimize: !isDev && (optimization.minimize !== false),
        usedExports: optimization.usedExports !== false,
        sideEffects: optimization.sideEffects !== false,
        runtimeChunk: microApp.runtimeChunk ? {
            name: typeof microApp.runtimeChunk === 'string' ? microApp.runtimeChunk : 'runtime'
        } : false
    };

    // 代码分割配置
    if (microApp.codeSplitting && optimization.splitChunks !== false) {
        baseOptimization.splitChunks = {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5,
                    reuseExistingChunk: true
                }
            }
        };
    }

    return baseOptimization;
}

/**
 * 创建解析配置
 */
export function createResolve(): webpack.Configuration['resolve'] {
    return {
        extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        alias: {
            '@': path.resolve(process.cwd(), 'src')
        },
        modules: ['node_modules', path.resolve(process.cwd(), 'src')]
    };
}

/**
 * 格式化文件大小
 */
export function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化构建时间
 */
export function formatTime(ms: number): string {
    if (ms < 1000) {
        return `${ms}ms`;
    }

    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    }

    return `${seconds}s`;
}

/**
 * 检查Webpack版本兼容性
 */
export function checkWebpackVersion(): { compatible: boolean; version: string; message?: string } {
    try {
        const webpackVersion = require('webpack/package.json').version;
        const majorVersion = parseInt(webpackVersion.split('.')[0]);

        if (majorVersion >= 5) {
            return {
                compatible: true,
                version: webpackVersion
            };
        }

        return {
            compatible: false,
            version: webpackVersion,
            message: `需要Webpack 5.x版本，当前版本: ${webpackVersion}`
        };
    } catch (error) {
        return {
            compatible: false,
            version: 'unknown',
            message: 'Webpack未安装或版本检测失败'
        };
    }
}

/**
 * 合并Webpack配置
 */
export function mergeWebpackConfig(
    base: webpack.Configuration,
    override: webpack.Configuration
): webpack.Configuration {
    const merged = { ...base, ...override };

    // 合并模块规则
    if (base.module?.rules && override.module?.rules) {
        merged.module = {
            ...base.module,
            ...override.module,
            rules: [...base.module.rules, ...override.module.rules]
        };
    }

    // 合并插件
    if (base.plugins && override.plugins) {
        merged.plugins = [...base.plugins, ...override.plugins];
    }

    // 合并解析配置
    if (base.resolve && override.resolve) {
        merged.resolve = {
            ...base.resolve,
            ...override.resolve,
            extensions: [
                ...(base.resolve.extensions || []),
                ...(override.resolve.extensions || [])
            ],
            alias: {
                ...base.resolve.alias,
                ...override.resolve.alias
            }
        };
    }

    return merged;
}

/**
 * 验证配置有效性
 */
export function validateConfig(config: WebpackBuilderConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查入口配置
    if (!config.microApp?.entry && !config.webpack?.entry) {
        errors.push('必须指定入口文件');
    }

    // 检查输出目录
    if (config.microApp?.outDir && !path.isAbsolute(config.microApp.outDir)) {
        // 相对路径是允许的
    }

    // 检查模块联邦配置
    if (config.microApp?.federation && !config.microApp?.library?.name) {
        errors.push('启用模块联邦时必须指定库名称');
    }

    return {
        valid: errors.length === 0,
        errors
    };
}