import type { Plugin } from 'esbuild';
import type { EsbuildPluginOptions } from './types';

/**
 * 创建 esbuild 微前端插件
 */
export function createEsbuildPlugin(options: EsbuildPluginOptions): Plugin {
    return {
        name: 'micro-core-esbuild-plugin',
        setup(build) {
            const { appName, entry, generateManifest = true } = options;

            // 处理微前端入口模块
            build.onResolve({ filter: /^@micro-core\/entry$/ }, () => ({
                path: entry,
                namespace: 'micro-app-entry'
            }));

            // 注入微前端生命周期
            build.onLoad({ filter: /.*/, namespace: 'micro-app-entry' }, async (args) => {
                const fs = await import('fs/promises');
                const originalContent = await fs.readFile(args.path, 'utf8');

                const injectedContent = `
// 微前端生命周期注入
window.__MICRO_APP_NAME__ = '${appName}';
window.__POWERED_BY_MICRO_CORE__ = true;

${originalContent}

// 导出微前端生命周期函数
if (typeof bootstrap === 'function') {
    window.__MICRO_APP_BOOTSTRAP__ = bootstrap;
}
if (typeof mount === 'function') {
    window.__MICRO_APP_MOUNT__ = mount;
}
if (typeof unmount === 'function') {
    window.__MICRO_APP_UNMOUNT__ = unmount;
}
`;

                return {
                    contents: injectedContent,
                    loader: 'js'
                };
            });

            // 构建完成后生成清单
            if (generateManifest) {
                build.onEnd(async (result) => {
                    if (result.errors.length === 0) {
                        await generateAppManifest(options, result);
                    }
                });
            }
        }
    };
}

/**
 * 生成应用清单文件
 */
async function generateAppManifest(options: EsbuildPluginOptions, buildResult: any) {
    const fs = await import('fs/promises');
    const path = await import('path');

    const manifest = {
        name: options.appName,
        version: '1.0.0',
        entry: './index.js',
        assets: {
            js: buildResult.outputFiles?.filter((file: any) => file.path.endsWith('.js')).map((file: any) => path.basename(file.path)) || [],
            css: buildResult.outputFiles?.filter((file: any) => file.path.endsWith('.css')).map((file: any) => path.basename(file.path)) || []
        },
        buildTool: 'esbuild',
        buildTime: new Date().toISOString()
    };

    const manifestPath = path.join(options.outdir || 'dist', 'micro-app-manifest.json');
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));

    console.log('[EsbuildPlugin] 应用清单已生成:', manifestPath);
}