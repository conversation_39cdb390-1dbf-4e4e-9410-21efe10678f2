/**
 * @fileoverview ESBuild Builder - Optimized Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { 
  BaseBuilder, 
  BaseBuilderConfig, 
  BaseBuilderOptions, 
  BuildResult, 
  DevServerConfig,
  Config<PERSON>erger,
  Logger,
  PerformanceMonitor
} from '../../shared';

// ESBuild 类型定义（避免直接依赖esbuild包）
interface ESBuildPlugin {
  name: string;
  setup?: (build: any) => void;
}

interface ESBuildConfig {
  entryPoints?: string[] | Record<string, string>;
  bundle?: boolean;
  outdir?: string;
  outfile?: string;
  format?: 'iife' | 'cjs' | 'esm';
  target?: string | string[];
  platform?: 'browser' | 'node' | 'neutral';
  external?: string[];
  plugins?: ESBuildPlugin[];
  define?: Record<string, string>;
  loader?: Record<string, string>;
  resolveExtensions?: string[];
  mainFields?: string[];
  conditions?: string[];
  splitting?: boolean;
  chunkNames?: string;
  assetNames?: string;
  publicPath?: string;
  inject?: string[];
  banner?: Record<string, string>;
  footer?: Record<string, string>;
  sourcemap?: boolean | 'linked' | 'inline' | 'external' | 'both';
  sourceRoot?: string;
  sourcesContent?: boolean;
  minify?: boolean;
  keepNames?: boolean;
  treeShaking?: boolean;
  ignoreAnnotations?: boolean;
  drop?: ('console' | 'debugger')[];
  dropLabels?: string[];
  jsx?: 'transform' | 'preserve' | 'automatic';
  jsxFactory?: string;
  jsxFragment?: string;
  jsxImportSource?: string;
  jsxDev?: boolean;
  jsxSideEffects?: boolean;
  tsconfig?: string;
  tsconfigRaw?: string | object;
  resolveDir?: string;
  nodePaths?: string[];
  watch?: boolean | {
    onRebuild?: (error: Error | null, result: any) => void;
  };
  incremental?: boolean;
  metafile?: boolean;
  write?: boolean;
  allowOverwrite?: boolean;
  preserveSymlinks?: boolean;
  logLevel?: 'verbose' | 'debug' | 'info' | 'warning' | 'error' | 'silent';
  logLimit?: number;
  logOverride?: Record<string, 'verbose' | 'debug' | 'info' | 'warning' | 'error' | 'silent'>;
  color?: boolean;
}

/**
 * ESBuild构建器选项接口
 */
export interface EsbuildBuilderOptions extends BaseBuilderOptions {
  /** ESBuild特定配置 */
  esbuildConfig?: Partial<ESBuildConfig>;
  /** 输出格式 */
  format?: 'iife' | 'cjs' | 'esm';
  /** 目标环境 */
  target?: string | string[];
  /** 平台 */
  platform?: 'browser' | 'node' | 'neutral';
  /** 是否启用代码分割 */
  splitting?: boolean;
  /** 是否压缩代码 */
  minify?: boolean;
  /** 是否生成sourcemap */
  sourcemap?: boolean | 'linked' | 'inline' | 'external' | 'both';
  /** JSX配置 */
  jsx?: 'transform' | 'preserve' | 'automatic';
  /** TypeScript配置文件路径 */
  tsconfig?: string;
}

/**
 * ESBuild构建器配置接口
 */
export interface EsbuildBuilderConfig extends BaseBuilderConfig {
  /** ESBuild特定配置 */
  esbuildConfig?: Partial<ESBuildConfig>;
  /** 开发服务器配置 */
  devServer?: DevServerConfig;
}

/**
 * 创建ESBuild微前端插件
 */
export function createMicroCoreEsbuildPlugin(options: EsbuildBuilderOptions = {}): ESBuildPlugin {
  return {
    name: 'micro-core-esbuild-plugin',
    
    setup(build) {
      // 构建开始钩子
      build.onStart(() => {
        Logger.info('ESBuild started for micro-frontend');
      });
      
      // 构建结束钩子
      build.onEnd((result: any) => {
        if (result.errors.length > 0) {
          Logger.error('ESBuild completed with errors', result.errors);
        } else {
          Logger.info('ESBuild completed successfully');
        }
        
        // 生成微前端清单文件
        if (options.microApp) {
          const manifest = {
            name: options.microApp.name,
            version: '1.0.0',
            entry: options.microApp.entry,
            routes: options.microApp.routes || [],
            permissions: options.microApp.permissions || [],
            externals: options.externals || {},
            timestamp: Date.now(),
            buildTool: 'esbuild',
            buildToolVersion: '0.20.x'
          };
          
          Logger.debug('Generated micro-app manifest', manifest);
        }
      });
      
      // 解析钩子 - 处理微前端模块解析
      build.onResolve({ filter: /^@micro-core\// }, (args) => {
        return {
          path: args.path,
          external: true
        };
      });
    }
  };
}

/**
 * ESBuild构建器类
 */
export class EsbuildBuilder extends BaseBuilder {
  readonly name = 'builder-esbuild';
  readonly version = '1.0.0';
  
  constructor(options: EsbuildBuilderOptions = {}) {
    super(options);
    this.validateOptions();
  }
  
  /**
   * 创建ESBuild配置
   */
  protected createBuilderConfig(config: EsbuildBuilderConfig): ESBuildConfig {
    Logger.debug('Creating ESBuild config', config);
    
    const esbuildOptions = this.options as EsbuildBuilderOptions;
    const microApp = esbuildOptions.microApp;
    
    const baseConfig: ESBuildConfig = {
      entryPoints: [config.entry],
      bundle: true,
      outdir: config.outDir || esbuildOptions.outDir || 'dist',
      format: esbuildOptions.format || (microApp ? 'iife' : 'esm'),
      target: esbuildOptions.target || ['es2020'],
      platform: esbuildOptions.platform || 'browser',
      external: Object.keys(esbuildOptions.externals || {}),
      plugins: [createMicroCoreEsbuildPlugin(esbuildOptions)],
      define: {
        'process.env.NODE_ENV': JSON.stringify(config.dev ? 'development' : 'production'),
        '__MICRO_APP_NAME__': JSON.stringify(microApp?.name || 'unknown'),
        '__MICRO_APP_VERSION__': JSON.stringify('1.0.0')
      },
      splitting: esbuildOptions.splitting && esbuildOptions.format === 'esm',
      chunkNames: config.dev ? '[name]' : '[name]-[hash]',
      assetNames: config.dev ? '[name]' : '[name]-[hash]',
      sourcemap: esbuildOptions.sourcemap !== false,
      minify: !config.dev && esbuildOptions.minify !== false,
      treeShaking: true,
      jsx: esbuildOptions.jsx || 'automatic',
      tsconfig: esbuildOptions.tsconfig,
      metafile: true,
      write: true,
      logLevel: config.dev ? 'info' : 'warning'
    };
    
    // 合并用户自定义配置
    const finalConfig = ConfigMerger.deepMerge(
      baseConfig, 
      config.esbuildConfig || {},
      esbuildOptions.esbuildConfig || {}
    );
    
    Logger.debug('Final ESBuild config created', finalConfig);
    return finalConfig;
  }
  
  /**
   * 执行构建
   */
  protected async executeBuild(esbuildConfig: ESBuildConfig): Promise<BuildResult> {
    try {
      Logger.info('Starting ESBuild process');
      
      // 模拟构建过程（实际应调用 ESBuild build API）
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const outputs = [
        {
          type: 'chunk' as const,
          fileName: 'index.js',
          size: 12345,
          code: '// Generated by ESBuild\nconsole.log("Hello from micro-app");'
        },
        {
          type: 'asset' as const,
          fileName: 'style.css',
          size: 2345,
          source: '/* Generated styles */\n.app { margin: 0; }'
        }
      ];
      
      const totalSize = outputs.reduce((sum, output) => sum + output.size, 0);
      
      Logger.info(`ESBuild completed. Generated ${outputs.length} files (${PerformanceMonitor.formatSize(totalSize)})`);
      
      return {
        success: true,
        outputs,
        stats: {
          duration: 0, // Will be set by BaseBuilder
          fileCount: outputs.length,
          totalSize,
          errors: 0,
          warnings: 0
        }
      };
    } catch (error) {
      Logger.error('ESBuild failed', error);
      throw error;
    }
  }
  
  /**
   * 启动开发服务器
   */
  protected async startDevServer(esbuildConfig: ESBuildConfig, devServerConfig?: DevServerConfig): Promise<any> {
    try {
      Logger.info('Starting ESBuild dev server (watch mode)');
      
      // 模拟监听模式（实际应使用 ESBuild serve/watch API）
      const mockContext = {
        config: esbuildConfig,
        dispose: async () => {
          Logger.info('ESBuild context disposed');
        },
        watch: async () => {
          Logger.debug('ESBuild watching for changes');
        },
        serve: async (options: any) => {
          Logger.info(`ESBuild serving on port ${options.port || 3000}`);
          return {
            host: options.host || 'localhost',
            port: options.port || 3000
          };
        }
      };
      
      return mockContext;
    } catch (error) {
      Logger.error('Failed to start ESBuild dev server', error);
      throw error;
    }
  }
  
  /**
   * 停止开发服务器
   */
  protected async stopDevServer(context: any): Promise<void> {
    try {
      Logger.info('Stopping ESBuild context');
      await context.dispose();
    } catch (error) {
      Logger.error('Failed to stop ESBuild context', error);
      throw error;
    }
  }
}

/**
 * 创建ESBuild构建器实例
 */
export function createEsbuildBuilder(options?: EsbuildBuilderOptions): EsbuildBuilder {
  return new EsbuildBuilder(options);
}

/**
 * 默认导出
 */
export default EsbuildBuilder;