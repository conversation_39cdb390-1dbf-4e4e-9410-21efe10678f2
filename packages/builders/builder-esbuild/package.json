{"name": "@micro-core/builder-esbuild", "version": "0.1.0", "description": "esbuild 构建工具适配器，用于微前端应用的快速构建", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "esbuild", "builder", "微前端"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"esbuild": "^0.19.0", "typescript": "^5.3.0", "tsup": "^8.0.0", "vitest": "^1.0.0"}, "peerDependencies": {"esbuild": "^0.19.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/builders/builder-esbuild"}, "publishConfig": {"access": "public"}}