# @micro-core/builder-vite

Micro-Core Vite 构建适配器 - 支持 Vite 构建工具的微前端集成

## 特性

- 🚀 **Vite 集成**: 完整支持 Vite 7.0.4+ 版本
- 📦 **自动配置**: 自动配置微前端构建选项
- 🔧 **生命周期注入**: 自动注入微前端生命周期函数
- 📄 **应用清单**: 自动生成应用清单文件
- 🌐 **CORS 支持**: 开发环境自动配置 CORS
- 🔗 **模块联邦**: 支持模块联邦配置

## 安装

```bash
npm install @micro-core/builder-vite
# 或
pnpm add @micro-core/builder-vite
```

## 使用方法

### 微应用配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCoreVitePlugin({
      appName: 'my-micro-app',
      appType: 'micro',
      entry: './src/main.ts',
      generateManifest: true,
      transformOptions: {
        injectLifecycles: true,
        wrapWithMicroCore: true
      }
    })
  ]
});
```

### 主应用配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { createMainAppConfig } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    createMainAppConfig({
      appName: 'main-app',
      generateManifest: true
    })
  ]
});
```

### 模块联邦配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCoreVitePlugin({
      appName: 'federated-app',
      enableFederation: true,
      federationConfig: {
        name: 'federatedApp',
        filename: 'remoteEntry.js',
        exposes: {
          './Button': './src/components/Button.vue',
          './utils': './src/utils/index.ts'
        },
        shared: {
          vue: { singleton: true },
          'vue-router': { singleton: true }
        }
      }
    })
  ]
});
```

## 配置选项

```typescript
interface MicroCoreViteOptions {
  /** 应用名称 */
  appName: string;
  /** 应用类型 */
  appType?: 'main' | 'micro'; // 默认: 'micro'
  /** 应用入口文件 */
  entry?: string; // 默认: 'src/main.ts'
  /** 输出目录 */
  outputDir?: string; // 默认: 'dist'
  /** 是否生成应用清单 */
  generateManifest?: boolean; // 默认: true
  /** 清单文件名 */
  manifestFileName?: string; // 默认: 'micro-app-manifest.json'
  /** 是否启用模块联邦 */
  enableFederation?: boolean; // 默认: false
  /** 联邦配置 */
  federationConfig?: {
    name: string;
    filename?: string;
    exposes?: Record<string, string>;
    remotes?: Record<string, string>;
    shared?: Record<string, any>;
  };
  /** 自定义转换选项 */
  transformOptions?: {
    injectLifecycles?: boolean; // 默认: false
    wrapWithMicroCore?: boolean; // 默认: true
  };
}
```

## 应用清单

插件会自动生成应用清单文件，包含以下信息：

```json
{
  "name": "my-micro-app",
  "type": "micro",
  "version": "1.0.0",
  "buildTime": "2024-01-01T00:00:00.000Z",
  "entry": {
    "js": ["my-micro-app.js"],
    "css": ["style.css"]
  },
  "assets": ["logo.png", "favicon.ico"],
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0"
  },
  "buildInfo": {
    "viteVersion": "7.0.4",
    "nodeVersion": "v18.17.0",
    "buildMode": "production"
  }
}
```

## 生命周期注入

当启用 `transformOptions.injectLifecycles` 时，插件会自动为你的应用注入微前端生命周期函数：

```typescript
// 自动生成的生命周期函数
export async function bootstrap(props) {
  console.log('应用启动中...', props);
  return Promise.resolve();
}

export async function mount(props) {
  console.log('应用挂载中...', props);
  // 自动检测框架并挂载应用
  return Promise.resolve();
}

export async function unmount(props) {
  console.log('应用卸载中...', props);
  // 自动检测框架并卸载应用
  return Promise.resolve();
}
```

## 开发环境

在开发环境中，插件会自动：

1. 为微应用配置 CORS 头
2. 启用热更新
3. 提供开发服务器配置

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev
```

## 构建生产版本

```bash
# 构建生产版本
npm run build
# 或
pnpm build
```

构建完成后，会在输出目录生成：
- 应用主文件 (如 `my-micro-app.js`)
- 样式文件 (如 `style.css`)
- 应用清单文件 (`micro-app-manifest.json`)
- 其他静态资源

## 最佳实践

### 1. 外部依赖配置

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      external: ['vue', 'vue-router', 'react', 'react-dom'],
      output: {
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter',
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    }
  }
});
```

### 2. 环境变量配置

```typescript
// .env.production
VITE_APP_NAME=my-micro-app
VITE_API_BASE_URL=https://api.example.com
VITE_PUBLIC_PATH=/micro-apps/my-app/
```

### 3. 路径配置

```typescript
// vite.config.ts
export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? '/micro-apps/my-app/' : '/',
  build: {
    assetsDir: 'assets'
  }
});
```

## 故障排除

### 常见问题

1. **CORS 错误**
   - 确保在开发环境中启用了 CORS 配置
   - 检查主应用的安全策略

2. **资源加载失败**
   - 检查 `base` 配置是否正确
   - 确认静态资源路径配置

3. **生命周期函数未生效**
   - 确保启用了 `transformOptions.injectLifecycles`
   - 检查入口文件路径配置

## 许可证

MIT License