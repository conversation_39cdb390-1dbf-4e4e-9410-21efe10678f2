/**
 * 应用清单生成器
 */

import type { OutputBundle, ResolvedConfig } from 'vite';

/**
 * 应用清单接口
 */
export interface MicroAppManifest {
    /** 应用名称 */
    name: string;
    /** 应用类型 */
    type: 'main' | 'micro';
    /** 应用版本 */
    version: string;
    /** 构建时间 */
    buildTime: string;
    /** 入口文件 */
    entry: {
        js: string[];
        css: string[];
    };
    /** 静态资源 */
    assets: string[];
    /** 依赖信息 */
    dependencies: Record<string, string>;
    /** 构建信息 */
    buildInfo: {
        viteVersion: string;
        nodeVersion: string;
        buildMode: string;
    };
}

/**
 * 生成应用清单
 */
export function generateManifest(options: {
    appName: string;
    appType: 'main' | 'micro';
    bundle: OutputBundle;
    config: ResolvedConfig;
}): MicroAppManifest {
    const { appName, appType, bundle, config } = options;

    // 提取入口文件
    const jsFiles: string[] = [];
    const cssFiles: string[] = [];
    const assets: string[] = [];

    Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];

        if (chunk.type === 'chunk') {
            if (chunk.isEntry) {
                jsFiles.push(fileName);
            }
        } else if (chunk.type === 'asset') {
            if (fileName.endsWith('.css')) {
                cssFiles.push(fileName);
            } else {
                assets.push(fileName);
            }
        }
    });

    // 获取依赖信息
    const packageJson = require(process.cwd() + '/package.json');
    const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.peerDependencies
    };

    return {
        name: appName,
        type: appType,
        version: packageJson.version || '1.0.0',
        buildTime: new Date().toISOString(),
        entry: {
            js: jsFiles,
            css: cssFiles
        },
        assets,
        dependencies,
        buildInfo: {
            viteVersion: config.plugins.find(p => p.name === 'vite:build')?.name || 'unknown',
            nodeVersion: process.version,
            buildMode: config.mode
        }
    };
}