/**
 * 代码转换器
 */

/**
 * 转换选项
 */
export interface TransformOptions {
    /** 应用名称 */
    appName: string;
    /** 是否包装为 Micro-Core 应用 */
    wrapWithMicroCore?: boolean;
}

/**
 * 转换入口文件，注入生命周期函数
 */
export function transformEntry(code: string, options: TransformOptions): string {
    const { appName, wrapWithMicroCore = true } = options;

    if (!wrapWithMicroCore) {
        return code;
    }

    // 检查是否已经有生命周期函数
    if (code.includes('export async function bootstrap') ||
        code.includes('export async function mount') ||
        code.includes('export async function unmount')) {
        return code;
    }

    // 注入生命周期函数
    const lifecycleCode = `
// Micro-Core 生命周期函数 - 自动生成
let app = null;

export async function bootstrap(props) {
    console.log('${appName} 应用启动中...', props);
    return Promise.resolve();
}

export async function mount(props) {
    console.log('${appName} 应用挂载中...', props);
    
    // 这里需要根据具体框架实现挂载逻辑
    // 示例：React 应用挂载
    if (typeof createApp === 'function') {
        // Vue 3 应用
        app = createApp(App);
        app.mount(props.container || '#app');
    } else if (typeof ReactDOM !== 'undefined' && ReactDOM.render) {
        // React 应用
        ReactDOM.render(React.createElement(App), props.container || document.getElementById('app'));
    } else {
        // 其他框架或原生应用
        console.warn('请手动实现 mount 生命周期函数');
    }
    
    return Promise.resolve();
}

export async function unmount(props) {
    console.log('${appName} 应用卸载中...', props);
    
    if (app && app.unmount) {
        app.unmount();
        app = null;
    } else if (typeof ReactDOM !== 'undefined' && ReactDOM.unmountComponentAtNode) {
        ReactDOM.unmountComponentAtNode(props.container || document.getElementById('app'));
    }
    
    return Promise.resolve();
}

// 独立运行时的启动逻辑
if (!window.__POWERED_BY_MICRO_CORE__) {
    // 原有的启动代码
    ${code}
}
`;

    return lifecycleCode;
}

/**
 * 转换模块导出
 */
export function transformModuleExports(code: string, appName: string): string {
    // 将默认导出转换为命名导出
    if (code.includes('export default')) {
        return code.replace(
            /export default/g,
            `export { default as ${appName}Module }`
        );
    }

    return code;
}

/**
 * 注入环境变量
 */
export function injectEnvironmentVariables(code: string, env: Record<string, any>): string {
    let transformedCode = code;

    Object.keys(env).forEach(key => {
        const value = JSON.stringify(env[key]);
        transformedCode = transformedCode.replace(
            new RegExp(`process\\.env\\.${key}`, 'g'),
            value
        );
    });

    return transformedCode;
}