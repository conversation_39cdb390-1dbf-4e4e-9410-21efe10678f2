/**
 * Vite Builder Types
 */

import type { UserConfig, Plugin, ViteDevServer as ViteDevServerType } from 'vite';
import type { BuildConfig } from '@micro-core/core';

export interface ViteBuilderConfig extends BuildConfig {
  /** Vite configuration */
  vite?: UserConfig;
  /** Micro-app specific Vite options */
  microApp?: ViteMicroAppConfig;
  /** Development server configuration */
  devServer?: ViteDevServerConfig;
  /** Build options */
  build?: ViteBuildOptions;
}

export interface ViteMicroAppConfig {
  /** Entry point for the micro-app */
  entry?: string;
  /** Output directory */
  outDir?: string;
  /** Public path */
  publicPath?: string;
  /** Enable module federation */
  federation?: boolean;
  /** Federation configuration */
  federationConfig?: ModuleFederationConfig;
  /** Enable code splitting */
  codeSplitting?: boolean;
  /** External dependencies */
  externals?: string[] | Record<string, string>;
  /** Shared dependencies */
  shared?: SharedDependencies;
  /** Asset optimization */
  assets?: AssetConfig;
}

export interface ViteDevServerConfig {
  /** Development server port */
  port?: number;
  /** Development server host */
  host?: string | boolean;
  /** Enable HTTPS */
  https?: boolean;
  /** CORS configuration */
  cors?: boolean | object;
  /** Proxy configuration */
  proxy?: Record<string, string | object>;
  /** Enable hot module replacement */
  hmr?: boolean | object;
  /** Open browser on server start */
  open?: boolean | string;
}

export interface ViteBuildOptions {
  /** Target environment */
  target?: string | string[];
  /** Output format */
  format?: 'es' | 'cjs' | 'umd' | 'iife';
  /** Minification */
  minify?: boolean | 'terser' | 'esbuild';
  /** Source maps */
  sourcemap?: boolean | 'inline' | 'hidden';
  /** Rollup options */
  rollupOptions?: any;
  /** Library mode */
  lib?: LibraryConfig;
  /** Asset inlining threshold */
  assetsInlineLimit?: number;
}

export interface LibraryConfig {
  /** Library entry point */
  entry: string;
  /** Library name */
  name?: string;
  /** Output formats */
  formats?: ('es' | 'cjs' | 'umd' | 'iife')[];
  /** File name pattern */
  fileName?: string | ((format: string) => string);
}

export interface ModuleFederationConfig {
  /** Application name */
  name: string;
  /** Exposed modules */
  exposes?: Record<string, string>;
  /** Remote applications */
  remotes?: Record<string, string>;
  /** Shared dependencies */
  shared?: SharedDependencies;
  /** Runtime */
  runtime?: string | boolean;
}

export interface SharedDependencies {
  [key: string]: {
    /** Package version */
    version?: string;
    /** Singleton mode */
    singleton?: boolean;
    /** Strict version */
    strictVersion?: boolean;
    /** Required version */
    requiredVersion?: string;
    /** Eager loading */
    eager?: boolean;
  };
}

export interface AssetConfig {
  /** Asset file name pattern */
  fileName?: string;
  /** Asset directory */
  dir?: string;
  /** Inline threshold */
  inlineLimit?: number;
  /** Public URL */
  publicUrl?: string;
}

export interface VitePluginOptions {
  /** Micro-app configuration */
  microApp?: ViteMicroAppConfig;
  /** Enable development mode */
  dev?: boolean;
  /** Enable production optimizations */
  production?: boolean;
  /** Custom transformations */
  transforms?: TransformConfig[];
}

export interface TransformConfig {
  /** File pattern to match */
  test: RegExp;
  /** Transform function */
  transform: (code: string, id: string) => string | Promise<string>;
  /** Transform order */
  order?: 'pre' | 'post';
}

export interface ViteBuildResult {
  /** Build success status */
  success: boolean;
  /** Output files */
  outputs: BuildOutput[];
  /** Build stats */
  stats: BuildStats;
  /** Error information */
  error?: Error;
}

export interface BuildOutput {
  /** File path */
  path: string;
  /** File size */
  size: number;
  /** File type */
  type: 'js' | 'css' | 'html' | 'asset';
  /** Source map path */
  map?: string;
}

export interface BuildStats {
  /** Build duration */
  duration: number;
  /** Total size */
  totalSize: number;
  /** Chunk count */
  chunkCount: number;
  /** Asset count */
  assetCount: number;
  /** Bundle analysis */
  analysis?: BundleAnalysis;
}

export interface BundleAnalysis {
  /** Module sizes */
  modules: ModuleSize[];
  /** Dependency tree */
  dependencies: DependencyNode[];
  /** Unused exports */
  unusedExports: string[];
}

export interface ModuleSize {
  /** Module path */
  path: string;
  /** Module size */
  size: number;
  /** Gzipped size */
  gzipSize: number;
}

export interface DependencyNode {
  /** Module path */
  path: string;
  /** Dependencies */
  dependencies: string[];
  /** Dependents */
  dependents: string[];
}

export interface ViteServerContext {
  /** Vite dev server instance */
  server: ViteDevServerType;
  /** Server configuration */
  config: ViteDevServerConfig;
  /** Middleware functions */
  middlewares: ServerMiddleware[];
  /** WebSocket connections */
  ws: WebSocketServer;
}

export interface ServerMiddleware {
  /** Middleware path */
  path: string;
  /** Middleware function */
  handler: (req: any, res: any, next: any) => void;
  /** Middleware order */
  order?: number;
}

export interface WebSocketServer {
  /** Send message to all clients */
  send: (event: string, data?: any) => void;
  /** Send message to specific client */
  sendTo: (clientId: string, event: string, data?: any) => void;
  /** Register event handler */
  on: (event: string, handler: (data: any, client: any) => void) => void;
}

export interface ViteHMRContext {
  /** File path */
  file: string;
  /** Update timestamp */
  timestamp: number;
  /** Modules to update */
  modules: Set<any>;
  /** Read function */
  read: () => string | Promise<string>;
}

export type ViteHookContext = {
  /** Build configuration */
  config: ViteBuilderConfig;
  /** Development mode */
  isDev: boolean;
  /** Production mode */
  isProd: boolean;
  /** Command being executed */
  command: 'build' | 'serve';
};
