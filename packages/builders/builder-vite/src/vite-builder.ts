/**
 * Vite Builder Implementation
 * Main builder class for Vite integration with Micro-Core
 */

import { build, createServer, mergeConfig, defineConfig } from 'vite';
import type { UserConfig, ViteDevServer } from 'vite';
import { BaseBuilder } from '../../shared/base-builder';
import type { BaseBuilderConfig, BaseBuilderOptions, BuildResult, DevServerConfig } from '../../shared/types';
import { VitePlugin } from './vite-plugin';
// import { ViteDevServer as MicroCoreViteDevServer } from './dev-server';
import type {
  ViteBuilderConfig,
  ViteBuildResult,
  ViteServerContext,
  ViteHookContext
} from './types';

export class ViteBuilder extends BaseBuilder {
  public readonly name = 'vite';
  public readonly version = '1.0.0';
  
  private config: ViteBuilderConfig;
  private viteServer: ViteDevServer | null = null;
  private devServer: any = null;

  constructor(options: BaseBuilderOptions = {}) {
    super(options);
    this.config = this.normalizeConfig({});
  }

  /**
   * Create Vite-specific builder configuration
   */
  protected createBuilderConfig(config: BaseBuilderConfig): UserConfig {
    return this.createViteConfig('build');
  }

  /**
   * Execute build using Vite
   */
  protected async executeBuild(builderConfig: UserConfig): Promise<BuildResult> {
    try {
      const startTime = Date.now();
      const result = await build(builderConfig);
      const duration = Date.now() - startTime;

      return {
        success: true,
        outputs: this.extractOutputs(result).map(output => ({
          type: output.type as 'chunk' | 'asset',
          fileName: output.path,
          size: output.size,
          code: output.type === 'js' ? 'compiled code' : undefined
        })),
        stats: {
          duration,
          fileCount: this.extractOutputs(result).length,
          totalSize: this.calculateTotalSize(result),
          errors: 0,
          warnings: 0
        }
      };
    } catch (error) {
      return {
        success: false,
        outputs: [],
        errors: [{ message: (error as Error).message }],
        stats: {
          duration: 0,
          fileCount: 0,
          totalSize: 0,
          errors: 1,
          warnings: 0
        }
      };
    }
  }

  /**
   * Start Vite development server
   */
  protected async startDevServer(builderConfig: UserConfig, devServerConfig?: DevServerConfig): Promise<ViteDevServer> {
    const viteConfig = {
      ...builderConfig,
      server: {
        port: devServerConfig?.port || 3000,
        host: devServerConfig?.host || 'localhost',
        https: devServerConfig?.https || false,
        cors: devServerConfig?.cors ?? true,
        hmr: true,
        open: false
      }
    };
    
    this.viteServer = await createServer(viteConfig);
    await this.viteServer.listen();
    return this.viteServer;
  }

  /**
   * Stop Vite development server
   */
  protected async stopDevServer(server: ViteDevServer): Promise<void> {
    await server.close();
    this.viteServer = null;
  }

  /**
   * Build the micro-app for production (legacy method)
   */
  async build(): Promise<ViteBuildResult> {
    try {
      const startTime = Date.now();
      const viteConfig = this.createViteConfig('build');

      console.log('🔨 Building micro-app with Vite...');
      
      const result = await build(viteConfig);
      const duration = Date.now() - startTime;

      const buildResult: ViteBuildResult = {
        success: true,
        outputs: this.extractOutputs(result),
        stats: {
          duration,
          totalSize: this.calculateTotalSize(result),
          chunkCount: this.getChunkCount(result),
          assetCount: this.getAssetCount(result)
        }
      };

      console.log(`✅ Build completed in ${duration}ms`);
      this.logBuildStats(buildResult.stats);

      return buildResult;
    } catch (error) {
      console.error('❌ Build failed:', error);
      return {
        success: false,
        outputs: [],
        stats: {
          duration: 0,
          totalSize: 0,
          chunkCount: 0,
          assetCount: 0
        },
        error: error as Error
      };
    }
  }

  /**
   * Start development server
   */
  async serve(): Promise<ViteServerContext> {
    try {
      const viteConfig = this.createViteConfig('serve');
      
      console.log('🚀 Starting Vite development server...');
      
      this.viteServer = await createServer(viteConfig);
      await this.viteServer.listen();

      const serverInfo = this.viteServer.config.server;
      const url = `${serverInfo.https ? 'https' : 'http'}://${serverInfo.host || 'localhost'}:${serverInfo.port}`;
      
      console.log(`🌐 Development server running at: ${url}`);

      // Create micro-core dev server wrapper
      this.devServer = { server: this.viteServer };

      const context: ViteServerContext = {
        server: this.viteServer,
        config: this.config.devServer || {},
        middlewares: [],
        ws: {
          send: (event: string, data?: any) => {
            this.viteServer?.ws.send(event, data);
          },
          sendTo: (clientId: string, event: string, data?: any) => {
            // Implementation for targeted WebSocket messages
            this.viteServer?.ws.send(event, data);
          },
          on: (event: string, handler: (data: any, client: any) => void) => {
            this.viteServer?.ws.on(event, handler);
          }
        }
      };

      return context;
    } catch (error) {
      console.error('❌ Failed to start development server:', error);
      throw error;
    }
  }

  /**
   * Stop development server
   */
  async stop(): Promise<void> {
    if (this.viteServer) {
      await this.viteServer.close();
      this.viteServer = null;
      console.log('🛑 Development server stopped');
    }

    if (this.devServer) {
      await this.devServer.stop();
      this.devServer = null;
    }
  }

  /**
   * Watch for file changes
   */
  async watch(callback: (event: string, file: string) => void): Promise<void> {
    if (!this.viteServer) {
      throw new Error('Development server not started');
    }

    this.viteServer.ws.on('file-changed', (data) => {
      callback('change', data.file);
    });

    this.viteServer.ws.on('file-added', (data) => {
      callback('add', data.file);
    });

    this.viteServer.ws.on('file-removed', (data) => {
      callback('unlink', data.file);
    });
  }

  /**
   * Get build configuration
   */
  getConfig(): ViteBuilderConfig {
    return this.config;
  }

  /**
   * Update build configuration
   */
  updateConfig(newConfig: Partial<ViteBuilderConfig>): void {
    this.config = this.normalizeConfig({
      ...this.config,
      ...newConfig
    });
  }

  /**
   * Create Vite configuration
   */
  private createViteConfig(command: 'build' | 'serve'): UserConfig {
    const isDev = command === 'serve';
    const isProd = command === 'build';

    const hookContext: ViteHookContext = {
      config: this.config,
      isDev,
      isProd,
      command
    };

    const baseConfig: UserConfig = {
      plugins: [
        VitePlugin({
          microApp: this.config.microApp,
          dev: isDev,
          production: isProd
        })
      ],
      build: {
        outDir: this.config.microApp?.outDir || 'dist',
        lib: this.config.build?.lib ? {
          entry: this.config.build.lib.entry,
          name: this.config.build.lib.name,
          formats: this.config.build.lib.formats || ['es', 'cjs'],
          fileName: this.config.build.lib.fileName
        } : undefined,
        rollupOptions: {
          external: this.config.microApp?.externals || [],
          ...this.config.build?.rollupOptions
        },
        minify: this.config.build?.minify ?? true,
        sourcemap: this.config.build?.sourcemap ?? true,
        target: this.config.build?.target || 'esnext'
      },
      server: isDev ? {
        port: this.config.devServer?.port || 3000,
        host: this.config.devServer?.host || 'localhost',
        https: this.config.devServer?.https || false,
        cors: this.config.devServer?.cors ?? true,
        proxy: this.config.devServer?.proxy,
        hmr: this.config.devServer?.hmr ?? true,
        open: this.config.devServer?.open || false
      } : undefined,
      define: {
        __MICRO_CORE_DEV__: isDev,
        __MICRO_CORE_PROD__: isProd
      },
      resolve: {
        alias: {
          '@': this.config.microApp?.entry ? 
            this.config.microApp.entry.replace(/\/[^\/]*$/, '') : 
            'src'
        }
      }
    };

    // Merge with user-provided Vite config
    const finalConfig = this.config.vite ? 
      mergeConfig(baseConfig, this.config.vite) : 
      baseConfig;

    return finalConfig;
  }

  /**
   * Normalize configuration
   */
  private normalizeConfig(config: ViteBuilderConfig): ViteBuilderConfig {
    return {
      ...config,
      microApp: {
        entry: 'src/index.ts',
        outDir: 'dist',
        publicPath: '/',
        federation: false,
        codeSplitting: true,
        externals: [],
        shared: {},
        ...config.microApp
      },
      devServer: {
        port: 3000,
        host: 'localhost',
        https: false,
        cors: true,
        hmr: true,
        open: false,
        ...config.devServer
      },
      build: {
        target: 'esnext',
        format: 'es',
        minify: true,
        sourcemap: true,
        ...config.build
      }
    };
  }

  /**
   * Extract build outputs from Vite build result
   */
  private extractOutputs(result: any): any[] {
    if (!result || !result.output) {
      return [];
    }

    return result.output.map((chunk: any) => ({
      path: chunk.fileName,
      size: chunk.code ? chunk.code.length : 0,
      type: this.getFileType(chunk.fileName),
      map: chunk.map ? `${chunk.fileName}.map` : undefined
    }));
  }

  /**
   * Calculate total build size
   */
  private calculateTotalSize(result: any): number {
    if (!result || !result.output) {
      return 0;
    }

    return result.output.reduce((total: number, chunk: any) => {
      return total + (chunk.code ? chunk.code.length : 0);
    }, 0);
  }

  /**
   * Get chunk count from build result
   */
  private getChunkCount(result: any): number {
    if (!result || !result.output) {
      return 0;
    }

    return result.output.filter((chunk: any) => chunk.isEntry || chunk.isDynamicEntry).length;
  }

  /**
   * Get asset count from build result
   */
  private getAssetCount(result: any): number {
    if (!result || !result.output) {
      return 0;
    }

    return result.output.filter((chunk: any) => chunk.type === 'asset').length;
  }

  /**
   * Get file type from filename
   */
  private getFileType(fileName: string): 'js' | 'css' | 'html' | 'asset' {
    const ext = fileName.split('.').pop()?.toLowerCase();
    
    switch (ext) {
      case 'js':
      case 'mjs':
      case 'ts':
        return 'js';
      case 'css':
      case 'scss':
      case 'sass':
      case 'less':
        return 'css';
      case 'html':
      case 'htm':
        return 'html';
      default:
        return 'asset';
    }
  }

  /**
   * Log build statistics
   */
  private logBuildStats(stats: any): void {
    console.log('\n📊 Build Statistics:');
    console.log(`   Duration: ${stats.duration}ms`);
    console.log(`   Total Size: ${this.formatBytes(stats.totalSize)}`);
    console.log(`   Chunks: ${stats.chunkCount}`);
    console.log(`   Assets: ${stats.assetCount}`);
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
