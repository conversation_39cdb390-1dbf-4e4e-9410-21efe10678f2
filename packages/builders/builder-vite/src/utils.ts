/**
 * Vite Builder Utilities
 * Helper functions for Vite integration with Micro-Core
 */

import { mergeConfig } from 'vite';
import type { UserConfig } from 'vite';
import type { ViteBuilder } from './vite-builder';
import type { ViteBuilderConfig, VitePluginOptions, ViteMicroAppConfig } from './types';
import { VitePlugin } from './vite-plugin';

/**
 * Create a Vite builder instance with default configuration
 */
export function createViteBuilder(config: ViteBuilderConfig = {}): ViteBuilder {
  return new ViteBuilder(config);
}

/**
 * Create Vite plugin with micro-app configuration
 */
export function createVitePlugin(options: VitePluginOptions = {}) {
  return VitePlugin(options);
}

/**
 * Merge Vite configurations with micro-app defaults
 */
export function mergeViteConfig(
  baseConfig: UserConfig,
  microAppConfig: ViteMicroAppConfig,
  userConfig: UserConfig = {}
): UserConfig {
  const microCoreConfig: UserConfig = {
    plugins: [
      VitePlugin({
        microApp: microAppConfig,
        dev: process.env.NODE_ENV === 'development',
        production: process.env.NODE_ENV === 'production'
      })
    ],
    build: {
      lib: microAppConfig.federation ? undefined : {
        entry: microAppConfig.entry || 'src/index.ts',
        name: microAppConfig.entry?.split('/').pop()?.replace(/\.[^/.]+$/, '') || 'MicroApp',
        formats: ['es', 'cjs']
      },
      outDir: microAppConfig.outDir || 'dist',
      rollupOptions: {
        external: microAppConfig.externals || [],
        output: {
          globals: microAppConfig.externals?.reduce((acc, ext) => {
            acc[ext] = ext.replace(/[@\/]/g, '').replace(/-/g, '');
            return acc;
          }, {} as Record<string, string>) || {}
        }
      }
    },
    define: {
      __MICRO_APP_NAME__: JSON.stringify(microAppConfig.entry || 'micro-app'),
      __MICRO_APP_VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0'),
      __MICRO_APP_FEDERATION__: JSON.stringify(microAppConfig.federation || false)
    }
  };

  // Merge configurations in order: base -> micro-core -> user
  return mergeConfig(mergeConfig(baseConfig, microCoreConfig), userConfig);
}

/**
 * Create default Vite configuration for micro-apps
 */
export function createDefaultViteConfig(microAppConfig: ViteMicroAppConfig = {}): UserConfig {
  return {
    plugins: [
      VitePlugin({
        microApp: microAppConfig
      })
    ],
    build: {
      lib: {
        entry: microAppConfig.entry || 'src/index.ts',
        name: 'MicroApp',
        formats: ['es', 'cjs'],
        fileName: (format) => `index.${format === 'es' ? 'esm' : format}.js`
      },
      rollupOptions: {
        external: ['vue', 'react', 'react-dom', '@micro-core/core'],
        output: {
          globals: {
            'vue': 'Vue',
            'react': 'React',
            'react-dom': 'ReactDOM',
            '@micro-core/core': 'MicroCoreCore'
          }
        }
      },
      sourcemap: true,
      minify: 'terser'
    },
    resolve: {
      alias: {
        '@': microAppConfig.entry ? 
          microAppConfig.entry.replace(/\/[^\/]*$/, '') : 
          'src'
      }
    }
  };
}

/**
 * Validate Vite builder configuration
 */
export function validateViteConfig(config: ViteBuilderConfig): void {
  if (!config.microApp?.entry) {
    throw new Error('Micro-app entry point is required');
  }

  if (config.microApp.federation && !config.microApp.federationConfig) {
    throw new Error('Federation configuration is required when federation is enabled');
  }

  if (config.microApp.federationConfig?.exposes) {
    Object.values(config.microApp.federationConfig.exposes).forEach(exposePath => {
      if (!exposePath || typeof exposePath !== 'string') {
        throw new Error('Invalid federation expose path');
      }
    });
  }
}

/**
 * Generate Vite configuration for different environments
 */
export function generateViteConfig(
  environment: 'development' | 'production' | 'test',
  microAppConfig: ViteMicroAppConfig
): UserConfig {
  const baseConfig = createDefaultViteConfig(microAppConfig);

  switch (environment) {
    case 'development':
      return mergeConfig(baseConfig, {
        mode: 'development',
        server: {
          port: 3000,
          host: 'localhost',
          cors: true,
          hmr: true
        },
        define: {
          __DEV__: true,
          __PROD__: false,
          __TEST__: false
        }
      });

    case 'production':
      return mergeConfig(baseConfig, {
        mode: 'production',
        build: {
          minify: 'terser',
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true
            }
          },
          rollupOptions: {
            output: {
              manualChunks: {
                vendor: ['vue', 'react', 'react-dom']
              }
            }
          }
        },
        define: {
          __DEV__: false,
          __PROD__: true,
          __TEST__: false
        }
      });

    case 'test':
      return mergeConfig(baseConfig, {
        mode: 'test',
        test: {
          environment: 'jsdom',
          globals: true,
          setupFiles: ['./test/setup.ts']
        },
        define: {
          __DEV__: false,
          __PROD__: false,
          __TEST__: true
        }
      });

    default:
      return baseConfig;
  }
}

/**
 * Extract build information from Vite build result
 */
export function extractBuildInfo(buildResult: any): any {
  if (!buildResult || !buildResult.output) {
    return {
      chunks: [],
      assets: [],
      totalSize: 0
    };
  }

  const chunks = buildResult.output.filter((item: any) => item.type === 'chunk');
  const assets = buildResult.output.filter((item: any) => item.type === 'asset');

  const totalSize = buildResult.output.reduce((total: number, item: any) => {
    return total + (item.code?.length || 0);
  }, 0);

  return {
    chunks: chunks.map((chunk: any) => ({
      fileName: chunk.fileName,
      size: chunk.code?.length || 0,
      isEntry: chunk.isEntry,
      isDynamicEntry: chunk.isDynamicEntry,
      imports: chunk.imports || [],
      exports: chunk.exports || []
    })),
    assets: assets.map((asset: any) => ({
      fileName: asset.fileName,
      size: asset.source?.length || 0,
      type: getAssetType(asset.fileName)
    })),
    totalSize
  };
}

/**
 * Get asset type from file name
 */
function getAssetType(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'css':
    case 'scss':
    case 'sass':
    case 'less':
      return 'stylesheet';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return 'image';
    case 'woff':
    case 'woff2':
    case 'ttf':
    case 'eot':
      return 'font';
    case 'json':
      return 'data';
    default:
      return 'unknown';
  }
}

/**
 * Create federation configuration
 */
export function createFederationConfig(options: {
  name: string;
  exposes?: Record<string, string>;
  remotes?: Record<string, string>;
  shared?: Record<string, any>;
}) {
  return {
    name: options.name,
    exposes: options.exposes || {},
    remotes: options.remotes || {},
    shared: {
      vue: {
        singleton: true,
        strictVersion: false
      },
      react: {
        singleton: true,
        strictVersion: false
      },
      'react-dom': {
        singleton: true,
        strictVersion: false
      },
      '@micro-core/core': {
        singleton: true,
        strictVersion: true
      },
      ...options.shared
    }
  };
}

/**
 * Optimize Vite configuration for micro-apps
 */
export function optimizeViteConfig(config: UserConfig): UserConfig {
  return mergeConfig(config, {
    build: {
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Vendor chunk for external dependencies
            if (id.includes('node_modules')) {
              if (id.includes('vue') || id.includes('react')) {
                return 'framework';
              }
              return 'vendor';
            }
            
            // Micro-core chunk
            if (id.includes('@micro-core')) {
              return 'micro-core';
            }
            
            // Utils chunk for utility modules
            if (id.includes('/utils/') || id.includes('/helpers/')) {
              return 'utils';
            }
          }
        }
      }
    },
    optimizeDeps: {
      include: [
        '@micro-core/core',
        '@micro-core/shared'
      ],
      exclude: [
        '@micro-core/plugin-*',
        '@micro-core/adapter-*'
      ]
    }
  });
}

/**
 * Create Vite configuration for library mode
 */
export function createLibraryConfig(options: {
  entry: string;
  name: string;
  formats?: ('es' | 'cjs' | 'umd' | 'iife')[];
  external?: string[];
  globals?: Record<string, string>;
}): UserConfig {
  return {
    build: {
      lib: {
        entry: options.entry,
        name: options.name,
        formats: options.formats || ['es', 'cjs'],
        fileName: (format) => `${options.name.toLowerCase()}.${format === 'es' ? 'esm' : format}.js`
      },
      rollupOptions: {
        external: options.external || [],
        output: {
          globals: options.globals || {}
        }
      },
      sourcemap: true,
      minify: false // Let the consumer decide on minification
    }
  };
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if Vite is available
 */
export function isViteAvailable(): boolean {
  try {
    require.resolve('vite');
    return true;
  } catch {
    return false;
  }
}

/**
 * Get Vite version
 */
export function getViteVersion(): string | null {
  try {
    const vitePackage = require('vite/package.json');
    return vitePackage.version;
  } catch {
    return null;
  }
}

/**
 * Create Vite configuration for micro-app development
 */
export function createDevConfig(microAppConfig: ViteMicroAppConfig): UserConfig {
  return generateViteConfig('development', microAppConfig);
}

/**
 * Create Vite configuration for micro-app production build
 */
export function createProdConfig(microAppConfig: ViteMicroAppConfig): UserConfig {
  return generateViteConfig('production', microAppConfig);
}
