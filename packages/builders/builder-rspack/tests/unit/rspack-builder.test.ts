/**
 * @fileoverview Rspack Builder Unit Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RspackBuilder } from '../../src/rspack-builder';
import type { RspackBuilderConfig, RspackBuilderOptions } from '../../src/types';
import type { BaseBuilderConfig, DevServerConfig } from '../../../shared/types';

// Mock Rspack (since it may not be available)
vi.mock('@rspack/core', () => ({
  rspack: vi.fn(),
  Configuration: {},
  DefinePlugin: vi.fn()
}), { virtual: true });

// Mock shared utilities
vi.mock('../../../shared', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn()
  },
  PerformanceMonitor: {
    startTimer: vi.fn().mockReturnValue('timer-id'),
    endTimer: vi.fn().mockReturnValue(1500)
  },
  ConfigMerger: {
    deepMerge: vi.fn().mockImplementation((base, override) => ({ ...base, ...override }))
  }
}));

describe('RspackBuilder', () => {
  let builder: RspackBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new RspackBuilder({
      mode: 'development',
      entry: './src/index.ts',
      outDir: './dist'
    });

    mockConfig = {
      entry: './src/index.ts',
      outDir: './dist',
      mode: 'development'
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default options', () => {
      const defaultBuilder = new RspackBuilder();
      expect(defaultBuilder.name).toBe('rspack');
      expect(defaultBuilder.version).toBe('1.0.0');
    });

    it('should initialize with custom options', () => {
      const options: RspackBuilderOptions = {
        mode: 'production',
        entry: './custom/entry.ts',
        outDir: './custom/dist'
      };
      const customBuilder = new RspackBuilder(options);
      expect(customBuilder.name).toBe('rspack');
      expect(customBuilder.version).toBe('1.0.0');
    });
  });

  describe('createBuilderConfig', () => {
    it('should create basic Rspack configuration', () => {
      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      
      expect(rspackConfig).toMatchObject({
        entry: './src/index.ts',
        output: {
          path: './dist',
          filename: '[name].[contenthash].js'
        },
        mode: 'development'
      });
    });

    it('should handle production mode configuration', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const rspackConfig = (builder as any).createBuilderConfig(prodConfig);
      
      expect(rspackConfig.mode).toBe('production');
      expect(rspackConfig.optimization.minimize).toBe(true);
    });

    it('should handle custom Rspack configuration', () => {
      const configWithCustom: RspackBuilderConfig = {
        ...mockConfig,
        rspackConfig: {
          target: 'node',
          externals: ['express'],
          resolve: {
            extensions: ['.ts', '.js']
          }
        }
      };
      
      const rspackConfig = (builder as any).createBuilderConfig(configWithCustom);
      expect(rspackConfig.target).toBe('node');
      expect(rspackConfig.externals).toEqual(['express']);
    });

    it('should handle module rules configuration', () => {
      const configWithRules: RspackBuilderConfig = {
        ...mockConfig,
        rules: [
          {
            test: /\.tsx?$/,
            use: 'builtin:swc-loader'
          }
        ]
      };
      
      const rspackConfig = (builder as any).createBuilderConfig(configWithRules);
      expect(rspackConfig.module.rules).toHaveLength(1);
      expect(rspackConfig.module.rules[0].test).toEqual(/\.tsx?$/);
    });
  });

  describe('executeBuild', () => {
    it('should execute successful build', async () => {
      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => false,
        toJson: () => ({
          assets: [
            { name: 'main.js', size: 1500 },
            { name: 'style.css', size: 800 }
          ],
          errors: [],
          warnings: []
        })
      };

      // Mock the build process
      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rspackConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.duration).toBe(1500);
      expect(result.stats.totalSize).toBe(2300);
    });

    it('should handle build errors', async () => {
      const buildError = new Error('Rspack build failed');
      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(buildError, null);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rspackConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Rspack build failed');
    });

    it('should handle compilation errors', async () => {
      const mockStats = {
        hasErrors: () => true,
        hasWarnings: () => false,
        toJson: () => ({
          assets: [],
          errors: [
            { message: 'TypeScript error' },
            { message: 'Module not found' }
          ],
          warnings: []
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rspackConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.stats.errors).toBe(2);
    });

    it('should handle compilation warnings', async () => {
      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => true,
        toJson: () => ({
          assets: [{ name: 'main.js', size: 1000 }],
          errors: [],
          warnings: [
            { message: 'Deprecated API usage' }
          ]
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rspackConfig);

      expect(result.success).toBe(true);
      expect(result.warnings).toHaveLength(1);
      expect(result.stats.warnings).toBe(1);
    });
  });

  describe('startDevServer', () => {
    it('should start development server successfully', async () => {
      const mockDevServer = {
        start: vi.fn().mockResolvedValue(undefined),
        stop: vi.fn().mockResolvedValue(undefined)
      };

      (builder as any).mockRspackDevServer = vi.fn().mockReturnValue(mockDevServer);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const devServerConfig: DevServerConfig = {
        port: 3000,
        host: 'localhost',
        hot: true
      };

      const server = await (builder as any).startDevServer(rspackConfig, devServerConfig);

      expect(server).toBe(mockDevServer);
      expect(mockDevServer.start).toHaveBeenCalled();
    });

    it('should handle dev server start errors', async () => {
      const serverError = new Error('Failed to start Rspack dev server');
      const mockDevServer = {
        start: vi.fn().mockRejectedValue(serverError)
      };

      (builder as any).mockRspackDevServer = vi.fn().mockReturnValue(mockDevServer);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      
      await expect((builder as any).startDevServer(rspackConfig)).rejects.toThrow('Failed to start Rspack dev server');
    });

    it('should use default dev server configuration', async () => {
      const mockDevServer = {
        start: vi.fn().mockResolvedValue(undefined)
      };

      (builder as any).mockRspackDevServer = vi.fn().mockReturnValue(mockDevServer);

      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      await (builder as any).startDevServer(rspackConfig);

      expect((builder as any).mockRspackDevServer).toHaveBeenCalledWith(
        expect.objectContaining({
          port: 8080,
          host: 'localhost'
        })
      );
    });
  });

  describe('stopDevServer', () => {
    it('should stop development server', async () => {
      const mockServer = {
        stop: vi.fn().mockResolvedValue(undefined)
      };

      await (builder as any).stopDevServer(mockServer);
      expect(mockServer.stop).toHaveBeenCalled();
    });

    it('should handle server stop errors', async () => {
      const mockServer = {
        stop: vi.fn().mockRejectedValue(new Error('Stop failed'))
      };

      await expect((builder as any).stopDevServer(mockServer)).rejects.toThrow('Stop failed');
    });
  });

  describe('Build Integration', () => {
    it('should perform complete build lifecycle', async () => {
      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => false,
        toJson: () => ({
          assets: [{ name: 'bundle.js', size: 2500 }],
          errors: [],
          warnings: []
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const result = await builder.build(mockConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(1);
      expect(result.outputs[0].fileName).toBe('bundle.js');
      expect(result.outputs[0].size).toBe(2500);
    });

    it('should handle multiple entry points', async () => {
      const multiEntryConfig = {
        ...mockConfig,
        entry: {
          main: './src/index.ts',
          admin: './src/admin.ts'
        }
      };

      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => false,
        toJson: () => ({
          assets: [
            { name: 'main.js', size: 1800 },
            { name: 'admin.js', size: 1200 }
          ],
          errors: [],
          warnings: []
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const result = await builder.build(multiEntryConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.totalSize).toBe(3000);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of assets efficiently', async () => {
      const largeAssets = Array.from({ length: 500 }, (_, i) => ({
        name: `chunk-${i}.js`,
        size: 1000
      }));

      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => false,
        toJson: () => ({
          assets: largeAssets,
          errors: [],
          warnings: []
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const startTime = Date.now();
      const rspackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rspackConfig);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(500);
      expect(endTime - startTime).toBeLessThan(3000); // Should complete within 3 seconds
    });

    it('should handle rapid successive builds', async () => {
      const mockStats = {
        hasErrors: () => false,
        hasWarnings: () => false,
        toJson: () => ({
          assets: [{ name: 'main.js', size: 1000 }],
          errors: [],
          warnings: []
        })
      };

      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          callback(null, mockStats);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const promises = Array.from({ length: 5 }, () => builder.build(mockConfig));
      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...mockConfig,
        entry: null, // Invalid entry
        outDir: undefined // Invalid output
      };

      // Should not throw during config creation
      expect(() => (builder as any).createBuilderConfig(invalidConfig)).not.toThrow();
    });

    it('should handle build process interruption', async () => {
      const mockCompiler = {
        run: vi.fn().mockImplementation((callback) => {
          setTimeout(() => callback(new Error('Build interrupted'), null), 100);
        })
      };

      (builder as any).mockRspackCompiler = vi.fn().mockReturnValue(mockCompiler);

      const result = await builder.build(mockConfig);
      expect(result.success).toBe(false);
      expect(result.errors[0].message).toBe('Build interrupted');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate entry point configuration', () => {
      const configWithStringEntry = { ...mockConfig, entry: './src/main.ts' };
      const rspackConfig = (builder as any).createBuilderConfig(configWithStringEntry);
      expect(rspackConfig.entry).toBe('./src/main.ts');
    });

    it('should validate output configuration', () => {
      const configWithCustomOut = { ...mockConfig, outDir: './custom-dist' };
      const rspackConfig = (builder as any).createBuilderConfig(configWithCustomOut);
      expect(rspackConfig.output.path).toBe('./custom-dist');
    });

    it('should handle external dependencies', () => {
      const configWithExternals: RspackBuilderConfig = {
        ...mockConfig,
        externals: ['react', 'react-dom']
      };
      
      const rspackConfig = (builder as any).createBuilderConfig(configWithExternals);
      expect(rspackConfig.externals).toEqual(['react', 'react-dom']);
    });

    it('should handle alias configuration', () => {
      const configWithAlias: RspackBuilderConfig = {
        ...mockConfig,
        alias: {
          '@': './src',
          '~': './node_modules'
        }
      };
      
      const rspackConfig = (builder as any).createBuilderConfig(configWithAlias);
      expect(rspackConfig.resolve.alias).toEqual({
        '@': './src',
        '~': './node_modules'
      });
    });
  });

  describe('getBuildStatus', () => {
    it('should return current build status', () => {
      const status = builder.getBuildStatus();
      expect(status).toHaveProperty('name', 'rspack');
      expect(status).toHaveProperty('version', '1.0.0');
      expect(status).toHaveProperty('hasActiveCompiler', false);
      expect(status).toHaveProperty('isDevServerRunning', false);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', async () => {
      await builder.cleanup();
      // Should complete without errors
      expect(true).toBe(true);
    });
  });
});
