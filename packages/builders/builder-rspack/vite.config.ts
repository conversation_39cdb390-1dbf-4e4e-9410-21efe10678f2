/**
 * @fileoverview Vite configuration for @micro-core/builder-rspack
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MicroCoreBuilderRspack',
      fileName: (format) => `micro-core-builder-rspack.${format}.js`,
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        '@rspack/core',
        '@rspack/cli',
        '@micro-core/core',
        '@micro-core/shared',
        /^@rspack\/.*/,
        /^@micro-core\/.*/
      ],
      output: {
        globals: {
          '@rspack/core': 'Rspack',
          '@rspack/cli': 'RspackCli',
          '@micro-core/core': 'MicroCore',
          '@micro-core/shared': 'MicroCoreShared'
        }
      }
    },
    target: 'node14',
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@shared': resolve(__dirname, '../shared'),
      '@core': resolve(__dirname, '../../core/src')
    }
  },
  test: {
    globals: true,
    environment: 'node',
    include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        'dist/',
        '**/*.d.ts'
      ]
    }
  }
});
