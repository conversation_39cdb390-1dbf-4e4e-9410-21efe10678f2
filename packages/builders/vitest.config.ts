/**
 * @fileoverview Vitest Configuration for Builders Package
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // Test environment configuration
    environment: 'node',
    
    // Test file patterns
    include: [
      '**/tests/**/*.{test,spec}.{js,ts,tsx}',
      '**/__tests__/**/*.{test,spec}.{js,ts,tsx}',
      '**/*.{test,spec}.{js,ts,tsx}'
    ],
    
    // Exclude patterns
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/.nuxt/**',
      '**/coverage/**'
    ],
    
    // Global test configuration
    globals: true,
    
    // Test timeout configuration
    testTimeout: 30000,
    hookTimeout: 10000,
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'coverage/**',
        'dist/**',
        '**/node_modules/**',
        '**/tests/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/vite.config.ts',
        '**/vitest.config.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Setup files
    setupFiles: ['./tests/setup.ts'],
    
    // Mock configuration
    clearMocks: true,
    restoreMocks: true,
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // Reporter configuration
    reporters: process.env.CI 
      ? ['verbose', 'junit', 'json'] 
      : ['verbose'],
    
    outputFile: {
      junit: './test-results/junit.xml',
      json: './test-results/results.json'
    },
    
    // Watch configuration
    watch: !process.env.CI,
    
    // Retry configuration for flaky tests
    retry: process.env.CI ? 2 : 0,
    
    // Bail configuration
    bail: process.env.CI ? 1 : 0
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@shared': resolve(__dirname, './shared'),
      '@tests': resolve(__dirname, './tests')
    }
  },
  
  // Define global constants
  define: {
    __TEST__: true,
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production'
  }
});
