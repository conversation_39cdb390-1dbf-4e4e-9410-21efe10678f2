# @micro-core/builder-turbopack

A cutting-edge Turbopack builder implementation for the Micro-Core ecosystem, providing next-generation bundling with Rust-powered performance.

## 🚀 Features

- **Rust-Powered Speed**: Built on Turbopack's high-performance Rust engine
- **Incremental Compilation**: Lightning-fast rebuilds with intelligent caching
- **Hot Module Replacement**: Instant updates during development
- **TypeScript First**: Native TypeScript support without configuration
- **React Fast Refresh**: Seamless React development experience
- **Code Splitting**: Automatic and manual code splitting strategies
- **Tree Shaking**: Advanced dead code elimination
- **Future-Ready**: Built for the next generation of web development

## 📦 Installation

```bash
pnpm add @micro-core/builder-turbopack
# or
npm install @micro-core/builder-turbopack
# or
yarn add @micro-core/builder-turbopack
```

## 🔧 Quick Start

```typescript
import { TurbopackBuilder } from '@micro-core/builder-turbopack';

// Create builder instance
const builder = new TurbopackBuilder({
  mode: 'development',
  entry: './src/index.tsx',
  outDir: './dist'
});

// Build project
const result = await builder.build({
  entry: './src/index.tsx',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
```

## ⚙️ Configuration

### Basic Configuration

```typescript
const builder = new TurbopackBuilder({
  mode: 'development',
  entry: './src/index.tsx',
  outDir: './dist',
  target: 'browser'
});
```

### Advanced Configuration

```typescript
const builder = new TurbopackBuilder({
  mode: 'production',
  entry: {
    main: './src/index.tsx',
    admin: './src/admin.tsx'
  },
  outDir: './dist',
  turbopackConfig: {
    resolve: {
      alias: {
        '@': './src',
        '~': './node_modules'
      },
      extensions: ['.tsx', '.ts', '.jsx', '.js', '.json']
    },
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: 'swc-loader',
          options: {
            jsc: {
              parser: {
                syntax: 'typescript',
                tsx: true
              },
              transform: {
                react: {
                  runtime: 'automatic'
                }
              }
            }
          }
        }
      ]
    },
    optimization: {
      minimize: true,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
});
```

## 🔌 API Reference

### TurbopackBuilder Class

#### Constructor

```typescript
constructor(options: TurbopackBuilderOptions)
```

**Parameters:**
- `options.mode`: Build mode ('development' | 'production')
- `options.entry`: Entry point(s) for the build
- `options.outDir`: Output directory
- `options.target`: Build target ('browser' | 'node')
- `options.turbopackConfig`: Custom Turbopack configuration

#### Methods

##### `build(config: TurbopackBuilderConfig): Promise<BuildResult>`

Execute the build process.

```typescript
const result = await builder.build({
  entry: './src/index.tsx',
  outDir: './dist',
  mode: 'production'
});
```

##### `serve(config: TurbopackBuilderConfig, devServerConfig?: DevServerConfig): Promise<any>`

Start the development server.

```typescript
const server = await builder.serve({
  entry: './src/index.tsx',
  mode: 'development'
}, {
  port: 3000,
  host: 'localhost',
  hot: true
});
```

##### `stop(): Promise<void>`

Stop the development server.

```typescript
await builder.stop();
```

### Configuration Types

#### TurbopackBuilderConfig

```typescript
interface TurbopackBuilderConfig extends BaseBuilderConfig {
  target?: 'browser' | 'node';
  turbopackConfig?: TurbopackConfig;
  plugins?: TurbopackPlugin[];
  rules?: ModuleRule[];
  alias?: Record<string, string>;
  externals?: string[];
}
```

## 🏗️ Advanced Usage

### React Fast Refresh

```typescript
const builder = new TurbopackBuilder({
  entry: './src/index.tsx',
  turbopackConfig: {
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: 'swc-loader',
          options: {
            jsc: {
              transform: {
                react: {
                  runtime: 'automatic',
                  refresh: true // Enable Fast Refresh
                }
              }
            }
          }
        }
      ]
    }
  }
});
```

### Custom Loaders

```typescript
const builder = new TurbopackBuilder({
  turbopackConfig: {
    module: {
      rules: [
        {
          test: /\.svg$/,
          use: '@svgr/webpack'
        },
        {
          test: /\.(png|jpe?g|gif)$/,
          type: 'asset/resource'
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader', 'postcss-loader']
        }
      ]
    }
  }
});
```

### Environment Configuration

```typescript
const builder = new TurbopackBuilder({
  turbopackConfig: {
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      'process.env.API_URL': JSON.stringify(process.env.API_URL),
      __DEV__: process.env.NODE_ENV === 'development'
    }
  }
});
```

### Code Splitting

```typescript
const builder = new TurbopackBuilder({
  turbopackConfig: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all'
          }
        }
      }
    }
  }
});
```

## 🧪 Testing

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## 📊 Performance Tips

1. **Enable Incremental Compilation**: Turbopack's incremental compilation provides massive speedups
2. **Use SWC**: Leverage SWC for ultra-fast TypeScript/JavaScript transformation
3. **Optimize Bundle Splitting**: Configure intelligent chunk splitting for better caching
4. **Enable Tree Shaking**: Remove unused code automatically

```typescript
const builder = new TurbopackBuilder({
  turbopackConfig: {
    optimization: {
      usedExports: true,
      sideEffects: false,
      minimize: true
    },
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename]
      }
    }
  }
});
```

## 🔍 Troubleshooting

### Common Issues

**Slow initial build:**
```typescript
// Enable persistent caching
turbopackConfig: {
  cache: {
    type: 'filesystem'
  }
}
```

**React Fast Refresh not working:**
```typescript
// Ensure Fast Refresh is properly configured
turbopackConfig: {
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'swc-loader',
        options: {
          jsc: {
            transform: {
              react: {
                refresh: true
              }
            }
          }
        }
      }
    ]
  }
}
```

**Module resolution errors:**
```typescript
// Configure resolve options
turbopackConfig: {
  resolve: {
    extensions: ['.tsx', '.ts', '.jsx', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  }
}
```

## 🔄 Migration Guide

### From Next.js

Turbopack is the future of Next.js bundling:

```typescript
// Next.js with Turbopack
// next.config.js
module.exports = {
  experimental: {
    turbo: {}
  }
};

// Micro-Core equivalent
const builder = new TurbopackBuilder({
  entry: './src/index.tsx',
  mode: 'development'
});
```

### From Webpack

```typescript
// Webpack config
module.exports = {
  entry: './src/index.js',
  module: {
    rules: [
      {
        test: /\.js$/,
        use: 'babel-loader'
      }
    ]
  }
};

// Turbopack equivalent
const builder = new TurbopackBuilder({
  entry: './src/index.js',
  turbopackConfig: {
    module: {
      rules: [
        {
          test: /\.js$/,
          use: 'swc-loader' // Much faster than Babel
        }
      ]
    }
  }
});
```

## 📄 License

MIT © 2025 Micro-Core

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 📖 Documentation: [Micro-Core Docs](https://micro-core.dev)
