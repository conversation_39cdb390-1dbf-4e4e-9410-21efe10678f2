/**
 * @fileoverview Turbopack Builder Unit Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TurbopackBuilder } from '../../src/turbopack-builder';
import type { TurbopackBuilderConfig, TurbopackBuilderOptions } from '../../src/types';
import type { BaseBuilderConfig, DevServerConfig } from '../../../shared/types';

// Mock shared utilities
vi.mock('../../../shared', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn()
  },
  PerformanceMonitor: {
    startTimer: vi.fn().mockReturnValue('timer-id'),
    endTimer: vi.fn().mockReturnValue(800)
  },
  ConfigMerger: {
    deepMerge: vi.fn().mockImplementation((base, override) => ({ ...base, ...override }))
  }
}));

describe('TurbopackBuilder', () => {
  let builder: TurbopackBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new TurbopackBuilder({
      mode: 'development',
      entry: './src/index.tsx',
      outDir: './dist'
    });

    mockConfig = {
      entry: './src/index.tsx',
      outDir: './dist',
      mode: 'development'
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default options', () => {
      const defaultBuilder = new TurbopackBuilder();
      expect(defaultBuilder.name).toBe('turbopack');
      expect(defaultBuilder.version).toBe('1.0.0');
    });

    it('should initialize with custom options', () => {
      const options: TurbopackBuilderOptions = {
        mode: 'production',
        entry: './custom/entry.tsx',
        outDir: './custom/dist'
      };
      const customBuilder = new TurbopackBuilder(options);
      expect(customBuilder.name).toBe('turbopack');
      expect(customBuilder.version).toBe('1.0.0');
    });
  });

  describe('createBuilderConfig', () => {
    it('should create basic Turbopack configuration', () => {
      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      
      expect(turbopackConfig).toMatchObject({
        entry: './src/index.tsx',
        output: {
          path: './dist',
          filename: '[name].[hash].js'
        },
        resolve: {
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
        }
      });
    });

    it('should handle production mode configuration', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const turbopackConfig = (builder as any).createBuilderConfig(prodConfig);
      
      expect(turbopackConfig.optimization.minimize).toBe(true);
      expect(turbopackConfig.devtool).toBe('source-map');
    });

    it('should handle development mode configuration', () => {
      const devConfig = { ...mockConfig, mode: 'development' as const };
      const turbopackConfig = (builder as any).createBuilderConfig(devConfig);
      
      expect(turbopackConfig.optimization.minimize).toBe(false);
      expect(turbopackConfig.devtool).toBe('eval-source-map');
    });

    it('should handle custom Turbopack configuration', () => {
      const configWithCustom: TurbopackBuilderConfig = {
        ...mockConfig,
        turbopackConfig: {
          resolve: {
            alias: {
              '@': './src',
              '~': './node_modules'
            }
          },
          module: {
            rules: [
              {
                test: /\.svg$/,
                use: '@svgr/webpack'
              }
            ]
          }
        }
      };
      
      const turbopackConfig = (builder as any).createBuilderConfig(configWithCustom);
      expect(turbopackConfig.resolve.alias).toEqual({
        '@': './src',
        '~': './node_modules'
      });
      expect(turbopackConfig.module.rules).toHaveLength(1);
    });

    it('should handle alias configuration', () => {
      const configWithAlias: TurbopackBuilderConfig = {
        ...mockConfig,
        alias: {
          '@components': './src/components',
          '@utils': './src/utils'
        }
      };
      
      const turbopackConfig = (builder as any).createBuilderConfig(configWithAlias);
      expect(turbopackConfig.resolve.alias).toEqual({
        '@components': './src/components',
        '@utils': './src/utils'
      });
    });
  });

  describe('executeBuild', () => {
    it('should execute successful build', async () => {
      // Mock successful build result
      const mockBuildResult = {
        assets: [
          { name: 'main.js', type: 'chunk', size: 2500, source: '// Turbopack compiled code' },
          { name: 'styles.css', type: 'asset', size: 1200, source: '/* Turbopack compiled styles */' }
        ]
      };

      // Mock the build process
      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(turbopackConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.duration).toBe(800);
      expect(result.stats.totalSize).toBe(3700);
    });

    it('should handle build errors', async () => {
      const buildError = new Error('Turbopack build failed');
      (builder as any).mockTurbopackBuild = vi.fn().mockRejectedValue(buildError);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(turbopackConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Turbopack build failed');
    });

    it('should handle empty build result', async () => {
      const mockBuildResult = {
        assets: []
      };

      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(turbopackConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(0);
      expect(result.stats.totalSize).toBe(0);
    });
  });

  describe('startDevServer', () => {
    it('should start development server successfully', async () => {
      const mockServer = {
        port: 3000,
        host: 'localhost',
        close: vi.fn().mockResolvedValue(undefined)
      };

      (builder as any).mockTurbopackDevServer = vi.fn().mockResolvedValue(mockServer);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const devServerConfig: DevServerConfig = {
        port: 3000,
        host: 'localhost',
        hot: true
      };

      const server = await (builder as any).startDevServer(turbopackConfig, devServerConfig);

      expect(server).toBe(mockServer);
      expect(server.port).toBe(3000);
      expect(server.host).toBe('localhost');
    });

    it('should handle dev server start errors', async () => {
      const serverError = new Error('Failed to start Turbopack dev server');
      (builder as any).mockTurbopackDevServer = vi.fn().mockRejectedValue(serverError);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      
      await expect((builder as any).startDevServer(turbopackConfig)).rejects.toThrow('Failed to start Turbopack dev server');
    });

    it('should use default dev server configuration', async () => {
      const mockServer = {
        port: 3000,
        host: 'localhost',
        close: vi.fn()
      };

      (builder as any).mockTurbopackDevServer = vi.fn().mockResolvedValue(mockServer);

      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const server = await (builder as any).startDevServer(turbopackConfig);

      expect(server.port).toBe(3000);
      expect(server.host).toBe('localhost');
    });
  });

  describe('stopDevServer', () => {
    it('should stop development server', async () => {
      const mockServer = {
        close: vi.fn().mockResolvedValue(undefined)
      };

      await (builder as any).stopDevServer(mockServer);
      expect(mockServer.close).toHaveBeenCalled();
    });

    it('should handle server stop errors', async () => {
      const mockServer = {
        close: vi.fn().mockRejectedValue(new Error('Stop failed'))
      };

      await expect((builder as any).stopDevServer(mockServer)).rejects.toThrow('Stop failed');
    });
  });

  describe('Build Integration', () => {
    it('should perform complete build lifecycle', async () => {
      const mockBuildResult = {
        assets: [
          { name: 'bundle.js', type: 'chunk', size: 3500, source: '// Turbopack bundle' },
          { name: 'main.css', type: 'asset', size: 1500, source: '/* Turbopack styles */' }
        ]
      };

      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const result = await builder.build(mockConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.outputs[0].fileName).toBe('bundle.js');
      expect(result.outputs[0].size).toBe(3500);
      expect(result.outputs[1].fileName).toBe('main.css');
      expect(result.outputs[1].size).toBe(1500);
      expect(result.stats.totalSize).toBe(5000);
    });

    it('should handle multiple entry points', async () => {
      const multiEntryConfig = {
        ...mockConfig,
        entry: {
          main: './src/index.tsx',
          admin: './src/admin.tsx'
        }
      };

      const mockBuildResult = {
        assets: [
          { name: 'main.js', type: 'chunk', size: 2000, source: '// Main bundle' },
          { name: 'admin.js', type: 'chunk', size: 1800, source: '// Admin bundle' },
          { name: 'shared.js', type: 'chunk', size: 1200, source: '// Shared bundle' }
        ]
      };

      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const result = await builder.build(multiEntryConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(3);
      expect(result.stats.totalSize).toBe(5000);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of assets efficiently', async () => {
      const largeAssets = Array.from({ length: 100 }, (_, i) => ({
        name: `chunk-${i}.js`,
        type: 'chunk',
        size: 1000,
        source: `// Chunk ${i}`
      }));

      const mockBuildResult = {
        assets: largeAssets
      };

      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const startTime = Date.now();
      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(turbopackConfig);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should be very fast
    });

    it('should handle rapid successive builds', async () => {
      const mockBuildResult = {
        assets: [
          { name: 'main.js', type: 'chunk', size: 1000, source: '// Code' }
        ]
      };

      (builder as any).mockTurbopackBuild = vi.fn().mockResolvedValue(mockBuildResult);

      const promises = Array.from({ length: 5 }, () => builder.build(mockConfig));
      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...mockConfig,
        entry: undefined, // Invalid entry
        outDir: '' // Invalid output
      };

      // Should not throw during config creation
      expect(() => (builder as any).createBuilderConfig(invalidConfig)).not.toThrow();
    });

    it('should handle build process interruption', async () => {
      (builder as any).mockTurbopackBuild = vi.fn().mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Build interrupted')), 50);
        });
      });

      const result = await builder.build(mockConfig);
      expect(result.success).toBe(false);
      expect(result.errors[0].message).toBe('Build interrupted');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate entry point configuration', () => {
      const configWithStringEntry = { ...mockConfig, entry: './src/main.tsx' };
      const turbopackConfig = (builder as any).createBuilderConfig(configWithStringEntry);
      expect(turbopackConfig.entry).toBe('./src/main.tsx');
    });

    it('should validate output configuration', () => {
      const configWithCustomOut = { ...mockConfig, outDir: './custom-dist' };
      const turbopackConfig = (builder as any).createBuilderConfig(configWithCustomOut);
      expect(turbopackConfig.output.path).toBe('./custom-dist');
    });

    it('should handle external dependencies', () => {
      const configWithExternals: TurbopackBuilderConfig = {
        ...mockConfig,
        externals: ['react', 'react-dom']
      };
      
      const turbopackConfig = (builder as any).createBuilderConfig(configWithExternals);
      expect(turbopackConfig.externals).toEqual(['react', 'react-dom']);
    });

    it('should handle plugins configuration', () => {
      const configWithPlugins: TurbopackBuilderConfig = {
        ...mockConfig,
        plugins: [
          { name: 'custom-plugin', setup: vi.fn() }
        ]
      };
      
      const turbopackConfig = (builder as any).createBuilderConfig(configWithPlugins);
      expect(turbopackConfig.plugins).toHaveLength(1);
      expect(turbopackConfig.plugins[0].name).toBe('custom-plugin');
    });
  });

  describe('React Fast Refresh', () => {
    it('should enable Fast Refresh in development mode', () => {
      const devConfig = { ...mockConfig, mode: 'development' as const };
      const turbopackConfig = (builder as any).createBuilderConfig(devConfig);
      
      // Check if Fast Refresh related configuration is present
      expect(turbopackConfig.module.rules).toBeDefined();
    });

    it('should disable Fast Refresh in production mode', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const turbopackConfig = (builder as any).createBuilderConfig(prodConfig);
      
      expect(turbopackConfig.optimization.minimize).toBe(true);
    });
  });

  describe('Code Splitting', () => {
    it('should configure code splitting', () => {
      const turbopackConfig = (builder as any).createBuilderConfig(mockConfig);
      
      expect(turbopackConfig.optimization.splitChunks).toBeDefined();
      expect(turbopackConfig.optimization.splitChunks.chunks).toBe('all');
      expect(turbopackConfig.optimization.splitChunks.cacheGroups.vendor).toBeDefined();
    });

    it('should handle custom chunk splitting', () => {
      const configWithSplitting: TurbopackBuilderConfig = {
        ...mockConfig,
        turbopackConfig: {
          optimization: {
            splitChunks: {
              chunks: 'async',
              minSize: 30000,
              cacheGroups: {
                commons: {
                  test: /[\\/]node_modules[\\/]/,
                  name: 'commons',
                  chunks: 'all'
                }
              }
            }
          }
        }
      };
      
      const turbopackConfig = (builder as any).createBuilderConfig(configWithSplitting);
      expect(turbopackConfig.optimization.splitChunks.chunks).toBe('async');
      expect(turbopackConfig.optimization.splitChunks.minSize).toBe(30000);
    });
  });

  describe('getBuildStatus', () => {
    it('should return current build status', () => {
      const status = builder.getBuildStatus();
      expect(status).toHaveProperty('name', 'turbopack');
      expect(status).toHaveProperty('version', '1.0.0');
      expect(status).toHaveProperty('hasActiveCompiler', false);
      expect(status).toHaveProperty('isDevServerRunning', false);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', async () => {
      await builder.cleanup();
      // Should complete without errors
      expect(true).toBe(true);
    });

    it('should cleanup active compiler and dev server', async () => {
      // Simulate active resources
      (builder as any).turbopackCompiler = { close: vi.fn() };
      (builder as any).devServer = { close: vi.fn().mockResolvedValue(undefined) };

      await builder.cleanup();

      expect((builder as any).turbopackCompiler).toBeNull();
      expect((builder as any).devServer).toBeNull();
    });
  });

  describe('Mock Implementation Tests', () => {
    it('should use mock build implementation', async () => {
      const result = await (builder as any).mockTurbopackBuild({});
      
      expect(result).toHaveProperty('assets');
      expect(result.assets).toHaveLength(2);
      expect(result.assets[0].name).toBe('main.js');
      expect(result.assets[1].name).toBe('styles.css');
    });

    it('should use mock dev server implementation', async () => {
      const server = await (builder as any).mockTurbopackDevServer({}, { port: 4000 });
      
      expect(server).toHaveProperty('port', 4000);
      expect(server).toHaveProperty('host', 'localhost');
      expect(server).toHaveProperty('close');
    });
  });
});
