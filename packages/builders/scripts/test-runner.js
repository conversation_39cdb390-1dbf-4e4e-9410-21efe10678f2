#!/usr/bin/env node

/**
 * @fileoverview CI Test Runner for Builders Package
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const CONFIG = {
  testTimeout: 300000, // 5 minutes
  retries: 2,
  parallel: true,
  coverage: true,
  verbose: true
};

// ANSI colors for output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logger utility
const logger = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}▶${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.magenta}${msg}${colors.reset}\n`)
};

// Test runner class
class TestRunner {
  constructor() {
    this.startTime = Date.now();
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      coverage: null
    };
  }

  async run() {
    try {
      logger.header('🚀 Starting Builders Package Test Suite');
      
      // Pre-test validation
      await this.validateEnvironment();
      
      // Run tests
      await this.runTests();
      
      // Generate reports
      await this.generateReports();
      
      // Summary
      this.printSummary();
      
      // Exit with appropriate code
      process.exit(this.results.failed > 0 ? 1 : 0);
      
    } catch (error) {
      logger.error(`Test runner failed: ${error.message}`);
      process.exit(1);
    }
  }

  async validateEnvironment() {
    logger.step('Validating test environment...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    logger.info(`Node.js version: ${nodeVersion}`);
    
    // Check if vitest is available
    try {
      require.resolve('vitest');
      logger.success('Vitest is available');
    } catch (error) {
      throw new Error('Vitest is not installed. Run: pnpm install');
    }
    
    // Check test directories
    const testDirs = [
      'builder-vite/tests',
      'builder-webpack/tests',
      'builder-rollup/tests',
      'builder-esbuild/tests',
      'builder-rspack/tests',
      'builder-parcel/tests',
      'builder-turbopack/tests'
    ];
    
    for (const dir of testDirs) {
      const fullPath = path.join(process.cwd(), dir);
      if (!fs.existsSync(fullPath)) {
        logger.warning(`Test directory not found: ${dir}`);
      } else {
        logger.success(`Test directory found: ${dir}`);
      }
    }
    
    // Create output directories
    this.ensureDirectories([
      'coverage',
      'test-results'
    ]);
  }

  async runTests() {
    logger.step('Running test suites...');
    
    const vitestArgs = [
      'run',
      '--config', './vitest.config.ts',
      '--reporter=verbose',
      '--reporter=junit',
      '--reporter=json',
      '--outputFile.junit=./test-results/junit.xml',
      '--outputFile.json=./test-results/results.json'
    ];
    
    if (CONFIG.coverage) {
      vitestArgs.push('--coverage');
    }
    
    if (CONFIG.parallel) {
      vitestArgs.push('--pool=threads');
    }
    
    const result = await this.runCommand('npx', ['vitest', ...vitestArgs]);
    
    if (result.code !== 0) {
      logger.error('Some tests failed');
      this.results.failed = 1;
    } else {
      logger.success('All tests passed');
      this.results.passed = 1;
    }
    
    // Parse test results
    await this.parseTestResults();
  }

  async parseTestResults() {
    try {
      const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
      if (fs.existsSync(resultsPath)) {
        const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
        this.results.total = results.numTotalTests || 0;
        this.results.passed = results.numPassedTests || 0;
        this.results.failed = results.numFailedTests || 0;
        this.results.skipped = results.numPendingTests || 0;
        
        logger.info(`Tests: ${this.results.total} total, ${this.results.passed} passed, ${this.results.failed} failed, ${this.results.skipped} skipped`);
      }
    } catch (error) {
      logger.warning('Could not parse test results');
    }
  }

  async generateReports() {
    logger.step('Generating test reports...');
    
    // Coverage report
    if (CONFIG.coverage) {
      const coveragePath = path.join(process.cwd(), 'coverage');
      if (fs.existsSync(coveragePath)) {
        logger.success('Coverage report generated');
        
        // Try to read coverage summary
        const summaryPath = path.join(coveragePath, 'coverage-summary.json');
        if (fs.existsSync(summaryPath)) {
          try {
            const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
            const total = summary.total;
            if (total) {
              logger.info(`Coverage: Lines ${total.lines.pct}%, Functions ${total.functions.pct}%, Branches ${total.branches.pct}%, Statements ${total.statements.pct}%`);
              this.results.coverage = total;
            }
          } catch (error) {
            logger.warning('Could not parse coverage summary');
          }
        }
      }
    }
    
    // JUnit report
    const junitPath = path.join(process.cwd(), 'test-results', 'junit.xml');
    if (fs.existsSync(junitPath)) {
      logger.success('JUnit report generated');
    }
  }

  printSummary() {
    const duration = Date.now() - this.startTime;
    const durationStr = `${(duration / 1000).toFixed(2)}s`;
    
    logger.header('📊 Test Summary');
    
    if (this.results.failed > 0) {
      logger.error(`Tests failed in ${durationStr}`);
      logger.error(`${this.results.failed} test suite(s) failed`);
    } else {
      logger.success(`All tests passed in ${durationStr}`);
    }
    
    if (this.results.total > 0) {
      logger.info(`Total tests: ${this.results.total}`);
      logger.info(`Passed: ${this.results.passed}`);
      logger.info(`Failed: ${this.results.failed}`);
      logger.info(`Skipped: ${this.results.skipped}`);
    }
    
    if (this.results.coverage) {
      const { lines, functions, branches, statements } = this.results.coverage;
      logger.info(`Coverage: Lines ${lines.pct}%, Functions ${functions.pct}%, Branches ${branches.pct}%, Statements ${statements.pct}%`);
      
      // Check coverage thresholds
      const threshold = 80;
      const coverageItems = [
        { name: 'Lines', value: lines.pct },
        { name: 'Functions', value: functions.pct },
        { name: 'Branches', value: branches.pct },
        { name: 'Statements', value: statements.pct }
      ];
      
      const belowThreshold = coverageItems.filter(item => item.value < threshold);
      if (belowThreshold.length > 0) {
        logger.warning(`Coverage below ${threshold}% threshold:`);
        belowThreshold.forEach(item => {
          logger.warning(`  ${item.name}: ${item.value}%`);
        });
      } else {
        logger.success(`All coverage metrics above ${threshold}% threshold`);
      }
    }
  }

  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options
      });
      
      child.on('close', (code) => {
        resolve({ code });
      });
      
      child.on('error', (error) => {
        reject(error);
      });
      
      // Handle timeout
      const timeout = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error(`Command timed out after ${CONFIG.testTimeout}ms`));
      }, CONFIG.testTimeout);
      
      child.on('close', () => {
        clearTimeout(timeout);
      });
    });
  }

  ensureDirectories(dirs) {
    dirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        logger.info(`Created directory: ${dir}`);
      }
    });
  }
}

// CLI handling
if (require.main === module) {
  const runner = new TestRunner();
  runner.run().catch(error => {
    logger.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = TestRunner;
