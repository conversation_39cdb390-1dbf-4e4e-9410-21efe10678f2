/**
 * @fileoverview Unit tests for RollupBuilder
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RollupBuilder } from '../../src/rollup-builder';
import { BaseBuilderConfig } from '../../../shared/types';

// Mock rollup
vi.mock('rollup', () => ({
  rollup: vi.fn(),
  watch: vi.fn()
}));

// Mock shared utilities
vi.mock('../../../shared/utils', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  },
  ConfigMerger: {
    deepMerge: vi.fn((a, b) => ({ ...a, ...b }))
  },
  PerformanceMonitor: {
    startTimer: vi.fn(),
    endTimer: vi.fn(() => 150)
  }
}));

describe('RollupBuilder', () => {
  let builder: RollupBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new RollupBuilder();
    mockConfig = {
      id: 'test-rollup-app',
      name: 'test-rollup-app',
      entry: './src/index.js',
      outDir: './dist',
      mode: 'development'
    };
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should create RollupBuilder instance with correct name and version', () => {
      expect(builder.name).toBe('rollup');
      expect(builder.version).toBeDefined();
      expect(typeof builder.version).toBe('string');
    });

    it('should initialize with default options', () => {
      const builderWithOptions = new RollupBuilder({
        logLevel: 'info'
      });
      expect(builderWithOptions).toBeInstanceOf(RollupBuilder);
    });
  });

  describe('createBuilderConfig', () => {
    it('should create valid rollup configuration', () => {
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      
      expect(rollupConfig).toBeDefined();
      expect(rollupConfig.input).toBe(mockConfig.entry);
      expect(rollupConfig.output).toBeDefined();
      expect(rollupConfig.output.dir).toBe(mockConfig.outDir);
      expect(rollupConfig.output.format).toBe('es');
    });

    it('should handle custom format configuration', () => {
      const configWithFormat = {
        ...mockConfig,
        format: 'cjs'
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithFormat);
      expect(rollupConfig.output.format).toBe('cjs');
    });

    it('should include plugins and external configuration', () => {
      const configWithPlugins = {
        ...mockConfig,
        plugins: [{ name: 'test-plugin' }],
        externals: ['react', 'react-dom']
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithPlugins);
      expect(rollupConfig.plugins).toBeDefined();
      expect(rollupConfig.external).toEqual(['react', 'react-dom']);
    });

    it('should merge with custom rollup configuration', () => {
      const configWithCustomRollup = {
        ...mockConfig,
        rollupConfig: {
          treeshake: false,
          output: {
            format: 'umd',
            name: 'MyLibrary'
          }
        }
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithCustomRollup);
      expect(rollupConfig.treeshake).toBe(false);
      expect(rollupConfig.output.name).toBe('MyLibrary');
    });
  });

  describe('executeBuild', () => {
    it('should execute rollup build successfully', async () => {
      const mockRollupBuild = {
        generate: vi.fn().mockResolvedValue({
          output: [
            {
              type: 'chunk',
              fileName: 'main.js',
              code: 'console.log("Hello World");'
            },
            {
              type: 'asset',
              fileName: 'style.css',
              source: 'body { margin: 0; }'
            }
          ]
        }),
        write: vi.fn().mockResolvedValue({}),
        close: vi.fn().mockResolvedValue({})
      };

      const { rollup } = await import('rollup');
      (rollup as any).mockResolvedValue(mockRollupBuild);
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rollupConfig);
      
      expect(result.success).toBe(true);
      expect(result.outputs).toBeDefined();
      expect(result.outputs.length).toBe(2);
      expect(result.stats).toBeDefined();
      expect(result.stats.duration).toBeGreaterThan(0);
      expect(mockRollupBuild.close).toHaveBeenCalled();
    });

    it('should handle build errors gracefully', async () => {
      const { rollup } = await import('rollup');
      (rollup as any).mockRejectedValue(new Error('Build failed'));
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rollupConfig);
      
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0].message).toBe('Build failed');
    });

    it('should calculate build statistics correctly', async () => {
      const mockOutput = [
        { type: 'chunk', fileName: 'main.js', code: 'a'.repeat(1000) },
        { type: 'asset', fileName: 'style.css', source: 'b'.repeat(500) }
      ];

      const mockRollupBuild = {
        generate: vi.fn().mockResolvedValue({ output: mockOutput }),
        write: vi.fn().mockResolvedValue({}),
        close: vi.fn().mockResolvedValue({})
      };

      const { rollup } = await import('rollup');
      (rollup as any).mockResolvedValue(mockRollupBuild);
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(rollupConfig);
      
      expect(result.success).toBe(true);
      expect(result.stats.fileCount).toBe(2);
      expect(result.stats.totalSize).toBe(1500);
    });
  });

  describe('startDevServer', () => {
    it('should start rollup watch mode successfully', async () => {
      const mockWatcher = {
        on: vi.fn((event, callback) => {
          if (event === 'event') {
            // Simulate successful build event
            setTimeout(() => callback({ code: 'END' }), 10);
          }
        }),
        close: vi.fn(),
        resolved: false
      };

      const { watch } = await import('rollup');
      (watch as any).mockReturnValue(mockWatcher);
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      const devServerConfig = { port: 3000, host: 'localhost' };
      
      const watcher = await (builder as any).startDevServer(rollupConfig, devServerConfig);
      
      expect(watcher).toBeDefined();
      expect(mockWatcher.on).toHaveBeenCalledWith('event', expect.any(Function));
    });

    it('should handle watch mode errors', async () => {
      const mockWatcher = {
        on: vi.fn((event, callback) => {
          if (event === 'event') {
            setTimeout(() => callback({ 
              code: 'ERROR', 
              error: new Error('Watch failed') 
            }), 10);
          }
        }),
        resolved: false
      };

      const { watch } = await import('rollup');
      (watch as any).mockReturnValue(mockWatcher);
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      
      await expect((builder as any).startDevServer(rollupConfig))
        .rejects.toThrow('Watch failed');
    });
  });

  describe('stopDevServer', () => {
    it('should stop watch mode successfully', async () => {
      const mockWatcher = {
        close: vi.fn()
      };
      
      await expect((builder as any).stopDevServer(mockWatcher)).resolves.not.toThrow();
      expect(mockWatcher.close).toHaveBeenCalled();
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large number of output files efficiently', async () => {
      const largeOutput = Array.from({ length: 1000 }, (_, i) => ({
        type: 'chunk',
        fileName: `chunk-${i}.js`,
        code: `console.log(${i});`
      }));

      const mockRollupBuild = {
        generate: vi.fn().mockResolvedValue({ output: largeOutput }),
        write: vi.fn().mockResolvedValue({}),
        close: vi.fn().mockResolvedValue({})
      };

      const { rollup } = await import('rollup');
      (rollup as any).mockResolvedValue(mockRollupBuild);
      
      const rollupConfig = (builder as any).createBuilderConfig(mockConfig);
      const startTime = Date.now();
      const result = await (builder as any).executeBuild(rollupConfig);
      const duration = Date.now() - startTime;
      
      expect(result.success).toBe(true);
      expect(result.outputs.length).toBe(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle empty configuration gracefully', () => {
      const emptyConfig = {
        id: '',
        name: '',
        entry: '',
        outDir: '',
        mode: 'development' as const
      };
      
      expect(() => (builder as any).createBuilderConfig(emptyConfig)).not.toThrow();
    });

    it('should validate external dependencies', () => {
      const configWithExternals = {
        ...mockConfig,
        externals: ['react', 'lodash', '@types/node']
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithExternals);
      expect(rollupConfig.external).toEqual(['react', 'lodash', '@types/node']);
    });
  });

  describe('Integration with BaseBuilder', () => {
    it('should properly extend BaseBuilder functionality', () => {
      expect(builder).toHaveProperty('name');
      expect(builder).toHaveProperty('version');
      expect(builder).toHaveProperty('build');
      expect(builder).toHaveProperty('serve');
      expect(builder).toHaveProperty('stop');
    });

    it('should provide build status information', () => {
      const status = builder.getBuildStatus();
      expect(status).toHaveProperty('name', 'rollup');
      expect(status).toHaveProperty('version');
      expect(status).toHaveProperty('hasActiveBuild');
      expect(status).toHaveProperty('isWatching');
    });

    it('should clean up resources properly', async () => {
      await expect(builder.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Configuration Validation', () => {
    it('should handle missing entry point', () => {
      const configWithoutEntry = {
        ...mockConfig,
        entry: undefined as any
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithoutEntry);
      expect(rollupConfig.input).toBeDefined(); // Should use default
    });

    it('should handle different output formats', () => {
      const formats = ['es', 'cjs', 'umd', 'iife'];
      
      formats.forEach(format => {
        const configWithFormat = {
          ...mockConfig,
          format
        };
        
        const rollupConfig = (builder as any).createBuilderConfig(configWithFormat);
        expect(rollupConfig.output.format).toBe(format);
      });
    });

    it('should handle tree shaking configuration', () => {
      const configWithTreeShaking = {
        ...mockConfig,
        treeShaking: false
      };
      
      const rollupConfig = (builder as any).createBuilderConfig(configWithTreeShaking);
      expect(rollupConfig.treeshake).toBeDefined();
    });
  });
});
