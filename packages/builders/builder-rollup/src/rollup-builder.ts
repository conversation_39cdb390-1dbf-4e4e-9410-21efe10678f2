/**
 * @fileoverview Rollup Builder Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { rollup, watch, RollupOptions, RollupBuild, OutputOptions } from 'rollup';
import { BaseBuilder } from '../../shared/base-builder';
import type { BaseBuilderConfig, BaseBuilderOptions, BuildResult, DevServerConfig } from '../../shared/types';
import { Logger, ConfigMerger, PerformanceMonitor } from '../../shared/utils';

// Rollup-specific interfaces
interface RollupBuilderOptions extends BaseBuilderOptions {
  rollupConfig?: RollupOptions;
  alias?: Record<string, string>;
  externals?: string[];
}

interface RollupBuilderConfig extends BaseBuilderConfig {
  rollupConfig?: RollupOptions;
  plugins?: any[];
  format?: 'es' | 'cjs' | 'umd' | 'iife';
  target?: string;
}

/**
 * Rollup Builder for Micro-Core
 * Provides Rollup build tool integration with micro-frontend capabilities
 */
export class RollupBuilder extends BaseBuilder {
  public readonly name = 'rollup';
  public readonly version = '1.0.0';

  private rollupBuild: RollupBuild | null = null;
  private watcher: any = null;

  constructor(options: RollupBuilderOptions = {}) {
    super(options);
    Logger.info(`Initialized ${this.name} builder v${this.version}`);
  }

  /**
   * Create Rollup-specific builder configuration
   */
  protected createBuilderConfig(config: BaseBuilderConfig): RollupOptions {
    const rollupConfig = config as RollupBuilderConfig;
    
    const baseConfig: RollupOptions = {
      input: config.entry || './src/index.js',
      output: {
        dir: config.outDir || './dist',
        format: rollupConfig.format || 'es',
        sourcemap: true,
        entryFileNames: '[name].[hash].js',
        chunkFileNames: '[name].[hash].js',
        assetFileNames: '[name].[hash][extname]'
      },
      plugins: [
        // Add micro-frontend specific plugins here
        ...(rollupConfig.plugins || [])
      ],
      external: rollupConfig.externals || [],
      treeshake: {
        moduleSideEffects: false
      }
    };

    // Merge with user-provided Rollup config
    if (rollupConfig.rollupConfig) {
      return ConfigMerger.deepMerge(baseConfig, rollupConfig.rollupConfig);
    }

    return baseConfig;
  }

  /**
   * Execute build using Rollup
   */
  protected async executeBuild(builderConfig: RollupOptions): Promise<BuildResult> {
    const timerId = `rollup-build-${Date.now()}`;
    PerformanceMonitor.startTimer(timerId);

    try {
      Logger.info('Starting Rollup build...');

      // Create Rollup build
      this.rollupBuild = await rollup(builderConfig);
      
      // Generate output
      const { output } = await this.rollupBuild.generate(builderConfig.output as OutputOptions);
      
      // Write files
      await this.rollupBuild.write(builderConfig.output as OutputOptions);

      const duration = PerformanceMonitor.endTimer(timerId);

      // Process outputs
      const outputs = output.map(chunk => ({
        type: chunk.type as 'chunk' | 'asset',
        fileName: chunk.fileName,
        size: chunk.type === 'chunk' ? chunk.code.length : 
              chunk.type === 'asset' ? (chunk.source as string).length : 0,
        code: chunk.type === 'chunk' ? chunk.code : undefined
      }));

      const result: BuildResult = {
        success: true,
        outputs,
        stats: {
          duration,
          fileCount: outputs.length,
          totalSize: outputs.reduce((sum, output) => sum + output.size, 0),
          errors: 0,
          warnings: 0
        }
      };

      Logger.info(`Rollup build completed successfully in ${duration}ms`);
      return result;

    } catch (error) {
      const duration = PerformanceMonitor.endTimer(timerId);
      const errorMessage = (error as Error).message;
      
      Logger.error(`Rollup build failed: ${errorMessage}`);

      return {
        success: false,
        outputs: [],
        errors: [{ message: errorMessage }],
        stats: {
          duration,
          fileCount: 0,
          totalSize: 0,
          errors: 1,
          warnings: 0
        }
      };
    } finally {
      // Clean up
      if (this.rollupBuild) {
        await this.rollupBuild.close();
        this.rollupBuild = null;
      }
    }
  }

  /**
   * Start Rollup development server (watch mode)
   */
  protected async startDevServer(builderConfig: RollupOptions, devServerConfig?: DevServerConfig): Promise<any> {
    try {
      Logger.info('Starting Rollup watch mode...');

      const watchOptions = {
        ...builderConfig,
        watch: {
          include: 'src/**',
          exclude: 'node_modules/**'
        }
      };

      this.watcher = watch(watchOptions);

      return new Promise((resolve, reject) => {
        this.watcher.on('event', (event: any) => {
          switch (event.code) {
            case 'START':
              Logger.info('Rollup watch: Build started');
              break;
            case 'BUNDLE_START':
              Logger.info(`Rollup watch: Bundling ${event.input}`);
              break;
            case 'BUNDLE_END':
              Logger.info(`Rollup watch: Bundle completed in ${event.duration}ms`);
              break;
            case 'END':
              Logger.info('Rollup watch: Build completed');
              if (!this.watcher.resolved) {
                this.watcher.resolved = true;
                resolve(this.watcher);
              }
              break;
            case 'ERROR':
              Logger.error(`Rollup watch error: ${event.error.message}`);
              if (!this.watcher.resolved) {
                this.watcher.resolved = true;
                reject(event.error);
              }
              break;
          }
        });

        // Resolve after a short delay if no immediate errors
        setTimeout(() => {
          if (!this.watcher.resolved) {
            this.watcher.resolved = true;
            resolve(this.watcher);
          }
        }, 1000);
      });

    } catch (error) {
      Logger.error(`Failed to start Rollup watch mode: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Stop Rollup development server (watch mode)
   */
  protected async stopDevServer(server: any): Promise<void> {
    try {
      if (this.watcher) {
        this.watcher.close();
        this.watcher = null;
        Logger.info('Rollup watch mode stopped');
      }
    } catch (error) {
      Logger.error(`Failed to stop Rollup watch mode: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Get current build status
   */
  public getBuildStatus() {
    return {
      ...super.getStatus(),
      hasActiveBuild: !!this.rollupBuild,
      isWatching: !!this.watcher
    };
  }

  /**
   * Clean up resources
   */
  public async cleanup(): Promise<void> {
    if (this.rollupBuild) {
      await this.rollupBuild.close();
      this.rollupBuild = null;
    }

    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }

    Logger.info('Rollup builder cleanup completed');
  }
}
