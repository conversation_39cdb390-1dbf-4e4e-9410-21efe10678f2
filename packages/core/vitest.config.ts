import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./test/setup.ts'],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            exclude: [
                'node_modules/',
                'test/',
                'dist/',
                '**/*.d.ts',
                '**/*.config.*',
                '**/index.ts'
            ]
        }
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@micro-core/shared': resolve(__dirname, '../shared/src')
        }
    }
});