# @micro-core/core

Micro-Core 核心运行时 - 微前端应用的核心引擎，提供应用注册、生命周期管理、沙箱隔离等核心功能。

## 📦 安装

```bash
npm install @micro-core/core
# 或
pnpm add @micro-core/core
# 或
yarn add @micro-core/core
```

## 🚀 快速开始

### 基础使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';

// 创建内核实例
const kernel = new MicroCoreKernel({
  development: true,
  defaultSandbox: 'proxy'
});

// 注册微前端应用
kernel.registerApplication({
  name: 'my-app',
  entry: 'https://example.com/app.js',
  container: '#app-container',
  activeWhen: '/my-app'
});

// 启动内核
await kernel.start();
```

### 高级配置

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel({
  development: process.env.NODE_ENV === 'development',
  logLevel: 'INFO',
  defaultSandbox: 'proxy',
  errorHandler: (error) => {
    console.error('微前端错误:', error);
    // 发送错误到监控系统
  },
  plugins: [
    // 插件列表
  ]
});
```

## 📚 核心概念

### 内核 (Kernel)

内核是整个微前端系统的核心，负责协调各个管理器和插件的工作。

```typescript
// 内核生命周期
await kernel.start();    // 启动内核
await kernel.stop();     // 停止内核
await kernel.destroy();  // 销毁内核
```

### 应用注册

```typescript
// 基础应用配置
kernel.registerApplication({
  name: 'react-app',
  entry: 'https://cdn.example.com/react-app.js',
  container: '#react-container',
  activeWhen: '/react-app',
  props: {
    theme: 'dark',
    apiUrl: 'https://api.example.com'
  }
});

// 高级应用配置
kernel.registerApplication({
  name: 'vue-app',
  entry: {
    scripts: ['https://cdn.example.com/vue-app.js'],
    styles: ['https://cdn.example.com/vue-app.css']
  },
  container: document.getElementById('vue-container'),
  activeWhen: (location) => location.pathname.startsWith('/vue'),
  sandbox: {
    type: 'iframe',
    styleIsolation: true,
    scriptIsolation: true
  },
  lifecycle: {
    beforeMount: async (app) => {
      console.log(`准备挂载应用: ${app.name}`);
    },
    mounted: async (app) => {
      console.log(`应用已挂载: ${app.name}`);
    }
  }
});
```

### 应用生命周期管理

```typescript
// 手动控制应用生命周期
await kernel.loadApplication('my-app');      // 加载应用
await kernel.mountApplication('my-app');     // 挂载应用
await kernel.unmountApplication('my-app');   // 卸载应用
await kernel.unregisterApplication('my-app'); // 注销应用

// 获取应用信息
const appInfo = kernel.getApplication('my-app');
console.log('应用状态:', appInfo?.status);
console.log('加载时间:', appInfo?.loadTime);
console.log('挂载时间:', appInfo?.mountTime);
```

### 插件系统

```typescript
// 使用插件
kernel.use(MyPlugin, {
  option1: 'value1',
  option2: 'value2'
});

// 获取插件
const plugin = kernel.getPlugin('my-plugin');

// 自定义插件
const MyPlugin = {
  name: 'my-plugin',
  version: '1.0.0',
  install(core, options) {
    // 注册钩子
    core.registerHook('beforeMount', async (appInfo) => {
      console.log('应用即将挂载:', appInfo.name);
    });
    
    // 监听事件
    core.getEventBus().on('app:mounted', (appInfo) => {
      console.log('应用已挂载:', appInfo.name);
    });
  },
  uninstall(core) {
    // 清理资源
  }
};
```

### 事件系统

```typescript
const eventBus = kernel.getEventBus();

// 监听应用事件
eventBus.on('app:registered', (appInfo) => {
  console.log('应用已注册:', appInfo.name);
});

eventBus.on('app:mounted', (appInfo) => {
  console.log('应用已挂载:', appInfo.name);
});

eventBus.on('app:error', (error, appInfo) => {
  console.error('应用错误:', error, appInfo);
});

// 监听生命周期事件
eventBus.on('lifecycle:beforeMount', (appInfo) => {
  console.log('准备挂载:', appInfo.name);
});

// 发射自定义事件
eventBus.emit('custom:event', { data: 'value' });
```

### 全局状态管理

```typescript
const globalState = kernel.getGlobalState();

// 设置全局状态
globalState.set('theme', 'dark');
globalState.set('user', { id: 1, name: 'John' });

// 获取全局状态
const theme = globalState.get('theme');
const user = globalState.get('user');

// 监听状态变化
const unsubscribe = globalState.onChange((change) => {
  console.log('状态变化:', change);
});

// 监听特定键的变化
const unsubscribeTheme = globalState.onKeyChange('theme', (change) => {
  console.log('主题变化:', change.newValue);
});

// 批量设置状态
globalState.setMultiple({
  theme: 'light',
  language: 'zh-CN'
});
```

## 🔧 API 参考

### MicroCoreKernel

#### 构造函数

```typescript
new MicroCoreKernel(options?: MicroCoreOptions)
```

#### 配置选项

```typescript
interface MicroCoreOptions {
  development?: boolean;           // 是否开发模式
  logLevel?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'SILENT';
  defaultSandbox?: SandboxType;    // 默认沙箱类型
  errorHandler?: (error: MicroCoreError) => void;
  plugins?: Plugin[];              // 插件列表
}
```

#### 主要方法

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `registerApplication(config)` | 注册微前端应用 | `void` |
| `unregisterApplication(name)` | 注销微前端应用 | `void` |
| `getApplication(name)` | 获取应用信息 | `AppInfo \| null` |
| `getApplications()` | 获取所有应用信息 | `AppInfo[]` |
| `start()` | 启动内核 | `Promise<void>` |
| `stop()` | 停止内核 | `Promise<void>` |
| `destroy()` | 销毁内核 | `Promise<void>` |
| `loadApplication(name)` | 加载应用 | `Promise<void>` |
| `mountApplication(name)` | 挂载应用 | `Promise<void>` |
| `unmountApplication(name)` | 卸载应用 | `Promise<void>` |
| `use(plugin, options?)` | 使用插件 | `void` |
| `getPlugin(name)` | 获取插件 | `Plugin \| null` |
| `getEventBus()` | 获取事件总线 | `EventBus` |
| `getGlobalState()` | 获取全局状态 | `GlobalState` |

### 应用配置

```typescript
interface AppConfig {
  name: string;                    // 应用名称
  entry: string | AppEntry;        // 应用入口
  container: string | HTMLElement; // 容器
  activeWhen?: string | string[] | ((location: Location) => boolean);
  props?: Record<string, any>;     // 自定义属性
  sandbox?: SandboxConfig;         // 沙箱配置
  loader?: LoaderConfig;           // 加载器配置
  lifecycle?: LifecycleHooks;      // 生命周期钩子
}
```

### 应用状态

| 状态 | 描述 |
|------|------|
| `NOT_LOADED` | 未加载 |
| `LOADING_SOURCE_CODE` | 正在加载源码 |
| `NOT_BOOTSTRAPPED` | 未启动 |
| `BOOTSTRAPPING` | 正在启动 |
| `NOT_MOUNTED` | 未挂载 |
| `MOUNTING` | 正在挂载 |
| `MOUNTED` | 已挂载 |
| `UPDATING` | 正在更新 |
| `UNMOUNTING` | 正在卸载 |
| `UNLOADING` | 正在卸载 |
| `LOAD_ERROR` | 加载错误 |
| `SKIP_BECAUSE_BROKEN` | 跳过（已损坏） |

## 🧪 测试

```bash
# 运行测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行测试 UI
pnpm test:ui
```

## 📝 开发

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 代码格式化
pnpm lint:fix
```

## 🔗 相关包

- [@micro-core/shared](../shared) - 共享工具包
- [@micro-core/plugins](../plugins) - 插件集合
- [@micro-core/adapters](../adapters) - 框架适配器
- [@micro-core/sidecar](../sidecar) - 边车模式

## 📄 许可证

MIT License - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详情。

## 📞 支持

- 📖 [文档](https://micro-core.dev)
- 🐛 [问题反馈](https://github.com/echo008/micro-core/issues)
- 💬 [讨论](https://github.com/echo008/micro-core/discussions)