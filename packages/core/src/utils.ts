/**
 * @fileoverview 核心工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { LogLevel } from './constants';

/**
 * 生成唯一ID
 */
export function generateId(prefix = 'micro-core'): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}`;
}

/**
 * 验证URL是否有效
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 深度合并对象
 */
export function merge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();

    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} });
                merge(target[key], source[key]);
            } else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }

    return merge(target, ...sources);
}

/**
 * 检查是否为对象
 */
export function isObject(item: any): item is Record<string, any> {
    return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * 检查是否为函数
 */
export function isFunction(item: any): item is Function {
    return typeof item === 'function';
}

/**
 * 检查是否为字符串
 */
export function isString(item: any): item is string {
    return typeof item === 'string';
}

/**
 * 检查是否为数字
 */
export function isNumber(item: any): item is number {
    return typeof item === 'number' && !isNaN(item);
}

/**
 * 检查是否为布尔值
 */
export function isBoolean(item: any): item is boolean {
    return typeof item === 'boolean';
}

/**
 * 检查是否为数组
 */
export function isArray(item: any): item is any[] {
    return Array.isArray(item);
}

/**
 * 检查是否为Promise
 */
export function isPromise(item: any): item is Promise<any> {
    return item && typeof item.then === 'function';
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}

/**
 * 创建日志记录器
 */
export function createLogger(namespace: string) {
    const log = (level: LogLevel, message: string, ...args: any[]) => {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${namespace}] [${level.toUpperCase()}]`;

        switch (level) {
            case LogLevel.DEBUG:
                console.debug(prefix, message, ...args);
                break;
            case LogLevel.INFO:
                console.info(prefix, message, ...args);
                break;
            case LogLevel.WARN:
                console.warn(prefix, message, ...args);
                break;
            case LogLevel.ERROR:
                console.error(prefix, message, ...args);
                break;
        }
    };

    return {
        debug: (message: string, ...args: any[]) => log(LogLevel.DEBUG, message, ...args),
        info: (message: string, ...args: any[]) => log(LogLevel.INFO, message, ...args),
        warn: (message: string, ...args: any[]) => log(LogLevel.WARN, message, ...args),
        error: (message: string, ...args: any[]) => log(LogLevel.ERROR, message, ...args),
        log: (message: string, ...args: any[]) => log(LogLevel.INFO, message, ...args)
    };
}

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000
): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;
            if (i < maxRetries) {
                await delay(delayMs * Math.pow(2, i)); // 指数退避
            }
        }
    }

    throw lastError!;
}

/**
 * 获取DOM元素
 */
export function getElement(selector: string | Element): Element | null {
    if (typeof selector === 'string') {
        return document.querySelector(selector);
    }
    return selector;
}

/**
 * 创建DOM元素
 */
export function createElement(
    tagName: string,
    attributes?: Record<string, string>,
    children?: (string | Element)[]
): Element {
    const element = document.createElement(tagName);

    if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
    }

    if (children) {
        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });
    }

    return element;
}

/**
 * 移除DOM元素
 */
export function removeElement(element: Element | null): void {
    if (element && element.parentNode) {
        element.parentNode.removeChild(element);
    }
}

/**
 * 加载脚本
 */
export function loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
    });
}

/**
 * 加载样式
 */
export function loadStyle(href: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = () => resolve();
        link.onerror = () => reject(new Error(`Failed to load style: ${href}`));
        document.head.appendChild(link);
    });
}

/**
 * 获取查询参数
 */
export function getQueryParams(url?: string): Record<string, string> {
    const searchParams = new URLSearchParams(url ? new URL(url).search : window.location.search);
    const params: Record<string, string> = {};

    for (const [key, value] of searchParams) {
        params[key] = value;
    }

    return params;
}

/**
 * 设置查询参数
 */
export function setQueryParams(params: Record<string, string>, url?: string): string {
    const targetUrl = new URL(url || window.location.href);

    Object.entries(params).forEach(([key, value]) => {
        targetUrl.searchParams.set(key, value);
    });

    return targetUrl.toString();
}

/**
 * 格式化字节大小
 */
export function formatBytes(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 */
export function formatTime(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(2)}m`;
    return `${(ms / 3600000).toFixed(2)}h`;
}

/**
 * 获取性能指标
 */
export function getPerformanceMetrics(): Record<string, number> {
    if (!window.performance) {
        return {};
    }

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

    return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        domInteractive: navigation.domInteractive - navigation.navigationStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
    };
}

/**
 * 检查浏览器支持
 */
export function checkBrowserSupport(): {
    proxy: boolean;
    webComponents: boolean;
    es6: boolean;
    modules: boolean;
} {
    return {
        proxy: typeof Proxy !== 'undefined',
        webComponents: 'customElements' in window,
        es6: typeof Symbol !== 'undefined',
        modules: 'noModule' in document.createElement('script')
    };
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as any;
    }

    if (obj instanceof Array) {
        return obj.map(item => deepClone(item)) as any;
    }

    if (typeof obj === 'object') {
        const cloned = {} as any;
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                cloned[key] = deepClone((obj as any)[key]);
            }
        }
        return cloned;
    }

    return obj;
}

/**
 * 获取错误信息
 */
export function getErrorMessage(error: any): string {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return String(error);
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T = any>(json: string, defaultValue: T): T {
    try {
        return JSON.parse(json);
    } catch {
        return defaultValue;
    }
}

/**
 * 安全的JSON字符串化
 */
export function safeJsonStringify(obj: any, space?: number): string {
    try {
        return JSON.stringify(obj, null, space);
    } catch {
        return '{}';
    }
}

/**
 * 获取对象的类型
 */
export function getType(obj: any): string {
    return Object.prototype.toString.call(obj).slice(8, -1).toLowerCase();
}

/**
 * 检查对象是否为空
 */
export function isEmpty(obj: any): boolean {
    if (obj == null) return true;
    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
}

/**
 * 获取嵌套对象的值
 */
export function get(obj: any, path: string, defaultValue?: any): any {
    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
        if (result == null || typeof result !== 'object') {
            return defaultValue;
        }
        result = result[key];
    }

    return result !== undefined ? result : defaultValue;
}

/**
 * 设置嵌套对象的值
 */
export function set(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    let current = obj;

    for (const key of keys) {
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }

    current[lastKey] = value;
}

/**
 * 转换为驼峰命名
 */
export function toCamelCase(str: string): string {
    return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 转换为短横线命名
 */
export function toKebabCase(str: string): string {
    return str.replace(/([A-Z])/g, '-$1').toLowerCase();
}

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 随机字符串
 */
export function randomString(length = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 去重数组
 */
export function unique<T>(arr: T[]): T[] {
    return Array.from(new Set(arr));
}

/**
 * 扁平化数组
 */
export function flatten<T>(arr: (T | T[])[]): T[] {
    return arr.reduce<T[]>((acc, val) => {
        return acc.concat(Array.isArray(val) ? flatten(val) : val);
    }, []);
}

/**
 * 分组数组
 */
export function groupBy<T, K extends string | number | symbol>(
    arr: T[],
    keyFn: (item: T) => K
): Record<K, T[]> {
    return arr.reduce((groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {} as Record<K, T[]>);
}