/**
 * @fileoverview 核心类型定义 - 向后兼容导出
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 重新导出所有类型，保持向后兼容
export * from './types/index';

// 为了向后兼容，重新导出一些常用类型
export type {
    ActiveWhen,
    AppConfig,
    AppInfo,
    AppStatus,
    ErrorCodes,
    HookFn,
    LifecycleFn,
    LifecycleFunction,
    LoadOptions,
    LogLevelType,
    MicroApp,
    MicroCoreOptions,
    Plugin,
    ResourceInfo,
    SandboxType,
    StateChangeCallback
} from './types/index';
