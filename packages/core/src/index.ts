/**
 * @fileoverview Micro-Core 核心包入口文件
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 导出核心类
export { AppRegistry } from './runtime/app-registry';
export { MicroCoreKernel } from './runtime/kernel';
export { LifecycleManager } from './runtime/lifecycle-manager';
export { PluginSystem } from './runtime/plugin-system';
export { ResourceManager } from './runtime/resource-manager';
export { SandboxManager } from './sandbox/sandbox-manager';

// 导出通信模块
export { CommunicationManager } from './communication/communication-manager';
export { EventBus } from './communication/event-bus';
export { GlobalState } from './communication/global-state';

// 导出路由模块
export { RouteGuards } from './router/route-guards';
export { RouterManager } from './router/router-manager';
export type { RouteConfig, RouteMatch, RouteChangeEvent, HistoryAdapter } from './router/router-manager';

// 导出沙箱实现
export { DefinePropertySandbox } from './sandbox/define-property-sandbox';
export { FederationSandbox } from './sandbox/federation-sandbox';
export { IframeSandbox } from './sandbox/iframe-sandbox';
export { NamespaceSandbox } from './sandbox/namespace-sandbox';
export { ProxySandbox } from './sandbox/proxy-sandbox';
export { WebComponentSandbox } from './sandbox/web-component-sandbox';
export { BaseSandbox } from './sandbox/base-sandbox';

// 导出错误处理
export { ErrorHandler } from './runtime/error-handler';
export { MicroCoreError } from './runtime/errors';
export { MicroCoreError as CoreError } from './errors';

// 导出常量和枚举
export * from './constants';

// 导出类型定义
export type * from './types';

// 导出工具函数
export * from './utils';

// 便捷函数
export function createKernel(config?: import('./types').MicroCoreOptions) {
    return new MicroCoreKernel(config);
}

// 默认导出
export { MicroCoreKernel as default };