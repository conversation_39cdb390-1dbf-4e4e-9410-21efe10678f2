/**
 * @fileoverview 常量定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 应用状态枚举
 */
export const APP_STATUS = {
    /** 未加载 */
    NOT_LOADED: 'NOT_LOADED',
    /** 正在加载源码 */
    LOADING_SOURCE_CODE: 'LOADING_SOURCE_CODE',
    /** 未启动 */
    NOT_BOOTSTRAPPED: 'NOT_BOOTSTRAPPED',
    /** 正在启动 */
    BOOTSTRAPPING: 'BOOTSTRAPPING',
    /** 未挂载 */
    NOT_MOUNTED: 'NOT_MOUNTED',
    /** 正在挂载 */
    MOUNTING: 'MOUNTING',
    /** 已挂载 */
    MOUNTED: 'MOUNTED',
    /** 正在更新 */
    UPDATING: 'UPDATING',
    /** 正在卸载 */
    UNMOUNTING: 'UNMOUNTING',
    /** 正在卸载源码 */
    UNLOADING: 'UNLOADING',
    /** 加载错误 */
    LOAD_ERROR: 'LOAD_ERROR',
    /** 跳过（已损坏） */
    SKIP_BECAUSE_BROKEN: 'SKIP_BECAUSE_BROKEN'
} as const;

/**
 * 错误代码枚举
 */
export const ERROR_CODES = {
    /** 未知错误 */
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    /** 无效参数 */
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    /** 操作失败 */
    OPERATION_FAILED: 'OPERATION_FAILED',
    /** 应用错误 */
    APPLICATION_ERROR: 'APPLICATION_ERROR',
    /** 应用未找到 */
    APPLICATION_NOT_FOUND: 'APPLICATION_NOT_FOUND',
    /** 应用已存在 */
    APPLICATION_ALREADY_EXISTS: 'APPLICATION_ALREADY_EXISTS',
    /** 应用加载失败 */
    APPLICATION_LOAD_FAILED: 'APPLICATION_LOAD_FAILED',
    /** 应用挂载失败 */
    APPLICATION_MOUNT_FAILED: 'APPLICATION_MOUNT_FAILED',
    /** 应用卸载失败 */
    APPLICATION_UNMOUNT_FAILED: 'APPLICATION_UNMOUNT_FAILED',
    /** 应用启动失败 */
    APPLICATION_BOOTSTRAP_FAILED: 'APPLICATION_BOOTSTRAP_FAILED',
    /** 应用更新失败 */
    APPLICATION_UPDATE_FAILED: 'APPLICATION_UPDATE_FAILED',
    /** 插件错误 */
    PLUGIN_ERROR: 'PLUGIN_ERROR',
    /** 插件未找到 */
    PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
    /** 插件已存在 */
    PLUGIN_ALREADY_EXISTS: 'PLUGIN_ALREADY_EXISTS',
    /** 插件安装失败 */
    PLUGIN_INSTALL_FAILED: 'PLUGIN_INSTALL_FAILED',
    /** 插件卸载失败 */
    PLUGIN_UNINSTALL_FAILED: 'PLUGIN_UNINSTALL_FAILED',
    /** 沙箱错误 */
    SANDBOX_ERROR: 'SANDBOX_ERROR',
    /** 沙箱创建失败 */
    SANDBOX_CREATE_FAILED: 'SANDBOX_CREATE_FAILED',
    /** 沙箱销毁失败 */
    SANDBOX_DESTROY_FAILED: 'SANDBOX_DESTROY_FAILED',
    /** 沙箱激活失败 */
    SANDBOX_ACTIVATE_FAILED: 'SANDBOX_ACTIVATE_FAILED',
    /** 沙箱停用失败 */
    SANDBOX_DEACTIVATE_FAILED: 'SANDBOX_DEACTIVATE_FAILED',
    /** 沙箱脚本执行失败 */
    SANDBOX_SCRIPT_EXECUTION_FAILED: 'SANDBOX_SCRIPT_EXECUTION_FAILED',
    /** 资源错误 */
    RESOURCE_ERROR: 'RESOURCE_ERROR',
    /** 资源加载失败 */
    RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
    /** 资源解析失败 */
    RESOURCE_PARSE_FAILED: 'RESOURCE_PARSE_FAILED',
    /** 资源缓存失败 */
    RESOURCE_CACHE_FAILED: 'RESOURCE_CACHE_FAILED',
    /** 路由错误 */
    ROUTER_ERROR: 'ROUTER_ERROR',
    /** 路由匹配失败 */
    ROUTER_MATCH_FAILED: 'ROUTER_MATCH_FAILED',
    /** 路由导航失败 */
    ROUTER_NAVIGATION_FAILED: 'ROUTER_NAVIGATION_FAILED',
    /** 通信错误 */
    COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
    /** 事件发送失败 */
    EVENT_EMIT_FAILED: 'EVENT_EMIT_FAILED',
    /** 状态更新失败 */
    STATE_UPDATE_FAILED: 'STATE_UPDATE_FAILED',
    /** 生命周期错误 */
    LIFECYCLE_ERROR: 'LIFECYCLE_ERROR',
    /** 生命周期超时 */
    LIFECYCLE_TIMEOUT: 'LIFECYCLE_TIMEOUT'
} as const;

/**
 * 沙箱类型枚举
 */
export const SANDBOX_TYPE = {
    /** Proxy 沙箱 */
    PROXY: 'proxy',
    /** DefineProperty 沙箱 */
    DEFINE_PROPERTY: 'defineProperty',
    /** WebComponent 沙箱 */
    WEB_COMPONENT: 'webComponent',
    /** Iframe 沙箱 */
    IFRAME: 'iframe',
    /** 命名空间沙箱 */
    NAMESPACE: 'namespace',
    /** 联邦组件沙箱 */
    FEDERATION: 'federation'
} as const;

/**
 * 资源类型枚举
 */
export const RESOURCE_TYPE = {
    /** JavaScript 脚本 */
    SCRIPT: 'script',
    /** CSS 样式 */
    STYLE: 'style',
    /** HTML 模板 */
    HTML: 'html',
    /** 图片资源 */
    IMAGE: 'image',
    /** 字体资源 */
    FONT: 'font',
    /** 其他资源 */
    OTHER: 'other'
} as const;

/**
 * 资源状态枚举
 */
export const RESOURCE_STATUS = {
    /** 未加载 */
    NOT_LOADED: 'NOT_LOADED',
    /** 正在加载 */
    LOADING: 'LOADING',
    /** 已加载 */
    LOADED: 'LOADED',
    /** 加载失败 */
    LOAD_FAILED: 'LOAD_FAILED',
    /** 已缓存 */
    CACHED: 'CACHED'
} as const;

/**
 * 日志级别枚举
 */
export const LOG_LEVEL = {
    /** 调试 */
    DEBUG: 'DEBUG',
    /** 信息 */
    INFO: 'INFO',
    /** 警告 */
    WARN: 'WARN',
    /** 错误 */
    ERROR: 'ERROR',
    /** 静默 */
    SILENT: 'SILENT'
} as const;

/**
 * 生命周期钩子名称
 */
export const LIFECYCLE_HOOKS = {
    /** 启动前 */
    BEFORE_BOOTSTRAP: 'beforeBootstrap',
    /** 启动后 */
    AFTER_BOOTSTRAP: 'afterBootstrap',
    /** 挂载前 */
    BEFORE_MOUNT: 'beforeMount',
    /** 挂载后 */
    AFTER_MOUNT: 'afterMount',
    /** 卸载前 */
    BEFORE_UNMOUNT: 'beforeUnmount',
    /** 卸载后 */
    AFTER_UNMOUNT: 'afterUnmount',
    /** 更新前 */
    BEFORE_UPDATE: 'beforeUpdate',
    /** 更新后 */
    AFTER_UPDATE: 'afterUpdate'
} as const;

/**
 * 事件名称常量
 */
export const EVENT_NAMES = {
    /** 内核启动中 */
    KERNEL_STARTING: 'kernel:starting',
    /** 内核已启动 */
    KERNEL_STARTED: 'kernel:started',
    /** 内核停止中 */
    KERNEL_STOPPING: 'kernel:stopping',
    /** 内核已停止 */
    KERNEL_STOPPED: 'kernel:stopped',
    /** 内核销毁中 */
    KERNEL_DESTROYING: 'kernel:destroying',
    /** 内核已销毁 */
    KERNEL_DESTROYED: 'kernel:destroyed',
    /** 应用已注册 */
    APP_REGISTERED: 'app:registered',
    /** 应用已注销 */
    APP_UNREGISTERED: 'app:unregistered',
    /** 应用已加载 */
    APP_LOADED: 'app:loaded',
    /** 应用已挂载 */
    APP_MOUNTED: 'app:mounted',
    /** 应用已卸载 */
    APP_UNMOUNTED: 'app:unmounted',
    /** 应用错误 */
    APP_ERROR: 'app:error',
    /** 插件已安装 */
    PLUGIN_INSTALLED: 'plugin:installed',
    /** 插件已卸载 */
    PLUGIN_UNINSTALLED: 'plugin:uninstalled',
    /** 路由变化 */
    ROUTE_CHANGED: 'route:changed',
    /** 状态变化 */
    STATE_CHANGED: 'state:changed'
} as const;

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
    /** 默认日志级别 */
    LOG_LEVEL: LOG_LEVEL.INFO,
    /** 默认沙箱类型 */
    SANDBOX_TYPE: SANDBOX_TYPE.PROXY,
    /** 默认生命周期超时时间（毫秒） */
    LIFECYCLE_TIMEOUT: 10000,
    /** 默认资源加载超时时间（毫秒） */
    RESOURCE_TIMEOUT: 30000
} as const;

// 类型导出
export type AppStatusType = typeof APP_STATUS[keyof typeof APP_STATUS];
export type ErrorCodeType = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type SandboxTypeType = typeof SANDBOX_TYPE[keyof typeof SANDBOX_TYPE];
export type ResourceTypeType = typeof RESOURCE_TYPE[keyof typeof RESOURCE_TYPE];
export type ResourceStatusType = typeof RESOURCE_STATUS[keyof typeof RESOURCE_STATUS];
export type LogLevelType = typeof LOG_LEVEL[keyof typeof LOG_LEVEL];
export type LifecycleHookType = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventNameType = typeof EVENT_NAMES[keyof typeof EVENT_NAMES];
