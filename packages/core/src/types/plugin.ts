/**
 * @fileoverview 插件相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 插件钩子映射
 */
export interface PluginHooks {
    // 内核钩子
    beforeStart: (kernel: any) => void | Promise<void>;
    afterStart: (kernel: any) => void | Promise<void>;
    beforeStop: (kernel: any) => void | Promise<void>;
    afterStop: (kernel: any) => void | Promise<void>;
    beforeDestroy: (kernel: any) => void | Promise<void>;

    // 应用钩子
    beforeRegister: (config: any) => any | Promise<any>;
    afterRegister: (app: any) => void | Promise<void>;
    beforeUnregister: (app: any) => void | Promise<void>;
    afterUnregister: (name: string) => void | Promise<void>;

    // 生命周期钩子
    beforeBootstrap: (app: any) => void | Promise<void>;
    afterBootstrap: (app: any) => void | Promise<void>;
    beforeMount: (app: any) => void | Promise<void>;
    afterMount: (app: any) => void | Promise<void>;
    beforeUnmount: (app: any) => void | Promise<void>;
    afterUnmount: (app: any) => void | Promise<void>;
    beforeUpdate: (app: any) => void | Promise<void>;
    afterUpdate: (app: any) => void | Promise<void>;

    // 资源钩子
    beforeLoadResource: (url: string, type: string) => string | Promise<string>;
    afterLoadResource: (url: string, type: string, content: string) => string | Promise<string>;

    // 路由钩子
    beforeRouteChange: (from: string, to: string) => boolean | Promise<boolean>;
    afterRouteChange: (from: string, to: string) => void | Promise<void>;
}

/**
 * 钩子回调函数
 */
export interface HookCallback {
    (...args: any[]): any | Promise<any>;
}