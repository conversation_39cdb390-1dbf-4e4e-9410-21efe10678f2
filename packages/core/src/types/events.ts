/**
 * @fileoverview 事件相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 事件映射
 */
export interface EventMap {
    // 内核事件
    'kernel:starting': [];
    'kernel:started': [];
    'kernel:stopping': [];
    'kernel:stopped': [];
    'kernel:destroying': [];
    'kernel:destroyed': [];

    // 应用事件
    'app:registered': [{ name: string; config: any; timestamp: number }];
    'app:unregistered': [{ name: string; timestamp: number }];
    'app:loaded': [{ name: string; timestamp: number }];
    'app:mounted': [{ name: string; timestamp: number }];
    'app:unmounted': [{ name: string; timestamp: number }];
    'app:error': [{ name: string; error: Error; timestamp: number }];

    // 插件事件
    'plugin:installed': [{ name: string; version: string; options?: any; timestamp: number }];
    'plugin:uninstalled': [{ name: string; timestamp: number }];

    // 生命周期事件
    'lifecycle:beforeBootstrap': [any];
    'lifecycle:afterBootstrap': [any];
    'lifecycle:beforeMount': [any];
    'lifecycle:afterMount': [any];
    'lifecycle:beforeUnmount': [any];
    'lifecycle:afterUnmount': [any];
    'lifecycle:beforeUpdate': [any];
    'lifecycle:afterUpdate': [any];
    'lifecycle:error': [{ app: any; lifecycle: string; error: Error }];

    // 路由事件
    'route:changed': [{ from: string; to: string; timestamp: number }];
    'route:beforeChange': [{ from: string; to: string }];
    'route:afterChange': [{ from: string; to: string }];

    // 状态事件
    'state:changed': [{ key: string; oldValue: any; newValue: any; timestamp: number }];

    // 资源事件
    'resource:loading': [{ url: string; type: string }];
    'resource:loaded': [{ url: string; type: string; size: number; loadTime: number }];
    'resource:error': [{ url: string; type: string; error: Error }];
}

/**
 * 路由事件类型
 */
export type RouterEventType = 'beforeChange' | 'afterChange' | 'changed';

/**
 * 生命周期事件类型
 */
export type LifecycleEventType =
    | 'beforeBootstrap'
    | 'afterBootstrap'
    | 'beforeMount'
    | 'afterMount'
    | 'beforeUnmount'
    | 'afterUnmount'
    | 'beforeUpdate'
    | 'afterUpdate'
    | 'error';