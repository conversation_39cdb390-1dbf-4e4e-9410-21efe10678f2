/**
 * @fileoverview 通信相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 事件监听器
 */
export type EventListener = (...args: any[]) => void;

/**
 * 事件映射（向后兼容）
 */
export interface EventMap {
    [event: string]: any[];
}

/**
 * 状态变化事件
 */
export interface StateChangeEvent<T = any> {
    /** 键名 */
    key: string;
    /** 旧值 */
    oldValue: T;
    /** 新值 */
    newValue: T;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 状态变化监听器
 */
export type StateChangeListener<T = any> = (event: StateChangeEvent<T>) => void;

/**
 * 通信通道配置
 */
export interface CommunicationChannelConfig {
    /** 通道名称 */
    name: string;
    /** 是否启用持久化 */
    persistent?: boolean;
    /** 序列化函数 */
    serialize?: (data: any) => string;
    /** 反序列化函数 */
    deserialize?: (data: string) => any;
}

/**
 * 消息类型
 */
export interface Message<T = any> {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data: T;
    /** 发送者 */
    sender: string;
    /** 接收者 */
    receiver?: string;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 消息处理器
 */
export type MessageHandler<T = any> = (message: Message<T>) => void | Promise<void>;