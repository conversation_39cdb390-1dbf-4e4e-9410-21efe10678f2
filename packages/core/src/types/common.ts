/**
 * @fileoverview 通用类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type {
    AppStatusType,
    LogLevelType,
    SandboxTypeType
} from '../constants';

/**
 * 微前端核心配置
 */
export interface MicroCoreOptions {
    /** 是否为开发模式 */
    development?: boolean;
    /** 日志级别 */
    logLevel?: LogLevelType;
    /** 默认沙箱类型 */
    defaultSandbox?: SandboxTypeType;
    /** 错误处理函数 */
    errorHandler?: (error: Error) => void;
    /** 插件列表 */
    plugins?: Plugin[];
}

/**
 * 插件接口
 */
export interface Plugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件安装函数 */
    install?: (pluginSystem: any, options?: any) => void;
    /** 插件卸载函数 */
    uninstall?: (pluginSystem: any) => void;
}

/**
 * 钩子函数类型
 */
export type HookFn = (...args: any[]) => any | Promise<any>;

/**
 * 生命周期函数类型
 */
export type LifecycleFn = (props: any) => void | Promise<void>;

/**
 * 激活条件类型
 */
export type ActiveWhen =
    | string
    | string[]
    | ((location: Location) => boolean);

/**
 * 状态变化回调
 */
export type StateChangeCallback<T = any> = (change: {
    key: string;
    oldValue: T;
    newValue: T;
    timestamp: number;
}) => void;

/**
 * 加载选项
 */
export interface LoadOptions {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 缓存策略 */
    cache?: boolean;
}

/**
 * 微应用基本信息
 */
export interface MicroApp {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: AppStatusType;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
}

/**
 * 应用入口配置
 */
export interface AppEntry {
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML 模板 */
    html?: string;
}

/**
 * 事件总线接口
 */
export interface EventBus {
    on(event: string, listener: Function): void;
    off(event: string, listener: Function): void;
    emit(event: string, ...args: any[]): void;
    once(event: string, listener: Function): void;
    clear(): void;
}

/**
 * 全局状态接口
 */
export interface GlobalState {
    get<T = any>(key: string): T | undefined;
    set<T = any>(key: string, value: T): void;
    remove(key: string): void;
    clear(): void;
    onChange(callback: StateChangeCallback): () => void;
    onKeyChange<T = any>(key: string, callback: StateChangeCallback<T>): () => void;
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
    createSandbox(name: string, type?: SandboxTypeType): any;
    getSandbox(name: string): any;
    destroySandbox(name: string): void;
    destroyAll(): void;
}