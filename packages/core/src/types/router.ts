/**
 * @fileoverview 路由相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 路由信息
 */
export interface RouteInfo {
    /** 路径 */
    path: string;
    /** 查询参数 */
    query: Record<string, string>;
    /** 哈希值 */
    hash: string;
    /** 状态 */
    state: any;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 路由变化事件
 */
export interface RouteChangeEvent {
    /** 来源路径 */
    from: RouteInfo;
    /** 目标路径 */
    to: RouteInfo;
    /** 是否可以取消 */
    cancelable: boolean;
    /** 取消导航 */
    cancel?: () => void;
}

/**
 * 路由守卫函数
 */
export type RouteGuard = (
    to: RouteInfo,
    from: RouteInfo
) => boolean | Promise<boolean>;

/**
 * 路由模式
 */
export type RouterMode = 'hash' | 'history' | 'memory';

/**
 * 路由配置
 */
export interface RouterConfig {
    /** 路由模式 */
    mode?: RouterMode;
    /** 基础路径 */
    base?: string;
    /** 是否启用路由守卫 */
    enableGuards?: boolean;
    /** 全局前置守卫 */
    beforeEach?: RouteGuard;
    /** 全局后置守卫 */
    afterEach?: (to: RouteInfo, from: RouteInfo) => void;
}