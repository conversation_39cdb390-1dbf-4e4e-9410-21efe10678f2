/**
 * @fileoverview 应用相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { AppStatusType, SandboxTypeType } from '../constants';
import type { ActiveWhen, LifecycleFn } from './common';

/**
 * 应用配置
 */
export interface AppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器选择器或DOM元素 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
    /** 自定义属性 */
    props?: Record<string, any>;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 加载器配置 */
    loader?: LoaderConfig;
    /** 生命周期钩子 */
    lifecycle?: LifecycleHooks;
}

/**
 * 应用入口配置
 */
export interface AppEntry {
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML 模板 */
    html?: string;
}

/**
 * 应用信息
 */
export interface AppInfo {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
    /** 自定义属性 */
    props: Record<string, any>;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 加载器配置 */
    loader?: LoaderConfig;
    /** 生命周期钩子 */
    lifecycle?: LifecycleHooks;
    /** 应用状态 */
    status: AppStatusType;
    /** 加载时间 */
    loadTime: number;
    /** 挂载时间 */
    mountTime: number;
    /** 错误信息 */
    error: Error | null;
    /** 应用实例 */
    instance: any;
    /** 沙箱实例 */
    sandboxInstance: any;
    /** 资源列表 */
    resources: ResourceInfo[];
    /** 创建时间 */
    createdAt: number;
    /** 更新时间 */
    updatedAt: number;
}

/**
 * 沙箱配置
 */
export interface SandboxConfig {
    /** 沙箱类型 */
    type?: SandboxTypeType;
    /** 是否启用样式隔离 */
    styleIsolation?: boolean;
    /** 是否启用脚本隔离 */
    scriptIsolation?: boolean;
    /** 白名单 */
    allowList?: string[];
    /** 黑名单 */
    denyList?: string[];
    /** 自定义配置 */
    options?: Record<string, any>;
}

/**
 * 加载器配置
 */
export interface LoaderConfig {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 预加载策略 */
    prefetch?: boolean;
    /** 自定义加载器 */
    customLoader?: (entry: string | AppEntry) => Promise<any>;
}

/**
 * 生命周期钩子
 */
export interface LifecycleHooks {
    /** 启动前 */
    beforeBootstrap?: LifecycleFn;
    /** 启动 */
    bootstrap?: LifecycleFn;
    /** 启动后 */
    afterBootstrap?: LifecycleFn;
    /** 挂载前 */
    beforeMount?: LifecycleFn;
    /** 挂载 */
    mount?: LifecycleFn;
    /** 挂载后 */
    afterMount?: LifecycleFn;
    /** 卸载前 */
    beforeUnmount?: LifecycleFn;
    /** 卸载 */
    unmount?: LifecycleFn;
    /** 卸载后 */
    afterUnmount?: LifecycleFn;
    /** 更新前 */
    beforeUpdate?: LifecycleFn;
    /** 更新 */
    update?: LifecycleFn;
    /** 更新后 */
    afterUpdate?: LifecycleFn;
}

/**
 * 资源信息
 */
export interface ResourceInfo {
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: 'script' | 'style' | 'html' | 'image' | 'font' | 'other';
    /** 资源状态 */
    status: 'loading' | 'loaded' | 'error';
    /** 资源大小 */
    size?: number;
    /** 加载时间 */
    loadTime?: number;
    /** 错误信息 */
    error?: Error;
}

/**
 * 应用实例（向后兼容）
 */
export interface AppInstance extends AppInfo { }

/**
 * 应用属性（向后兼容）
 */
export interface AppProps extends Record<string, any> { }

/**
 * 应用生命周期（向后兼容）
 */
export interface AppLifecycles extends LifecycleHooks { }