/**
 * ProxySandbox - Proxy-based sandbox implementation for JavaScript isolation
 * @packageDocumentation
 */

import { ErrorCode, SandboxError } from '../errors';
import { Sandbox, SANDBOX_TYPES, SandboxOptions } from '../types';

/**
 * Proxy sandbox options
 */
export interface ProxySandboxOptions extends SandboxOptions {
    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Whether to enable snapshot mode
     */
    enableSnapshot?: boolean;

    /**
     * Whether to enable property tracking
     */
    enablePropertyTracking?: boolean;
}

/**
 * Property descriptor with additional metadata
 */
interface TrackedPropertyDescriptor extends PropertyDescriptor {
    originalDescriptor?: PropertyDescriptor;
    isModified?: boolean;
    modifiedAt?: Date;
}

/**
 * ProxySandbox - Proxy-based sandbox implementation for JavaScript isolation
 */
export class ProxySandbox implements Sandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = SANDBOX_TYPES.PROXY;

    /**
     * Sandbox options
     */
    private options: ProxySandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Proxy window object
     */
    private proxyWindow: Window | null = null;

    /**
     * Original window snapshot
     */
    private windowSnapshot: Record<string, any> = {};

    /**
     * Modified properties
     */
    private modifiedProps: Set<string> = new Set();

    /**
     * Added properties
     */
    private addedProps: Set<string> = new Set();

    /**
     * Property descriptors
     */
    private propertyDescriptors: Map<string, TrackedPropertyDescriptor> = new Map();

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Creates a new ProxySandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: ProxySandboxOptions) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            enableSnapshot: true,
            enablePropertyTracking: true,
            allowList: [],
            denyList: [],
            ...options
        };

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Create proxy window
        this.createProxyWindow();
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Create proxy window
     */
    private createProxyWindow(): void {
        const rawWindow = window;
        const fakeWindow = Object.create(null);

        // Create proxy handler
        const proxyHandler: ProxyHandler<Window> = {
            get: (target, prop, receiver) => {
                return this.getProperty(target, prop as string, receiver);
            },

            set: (target, prop, value, receiver) => {
                return this.setProperty(target, prop as string, value, receiver);
            },

            has: (target, prop) => {
                return this.hasProperty(target, prop as string);
            },

            deleteProperty: (target, prop) => {
                return this.deleteProperty(target, prop as string);
            },

            defineProperty: (target, prop, descriptor) => {
                return this.defineProperty(target, prop as string, descriptor);
            },

            getOwnPropertyDescriptor: (target, prop) => {
                return this.getOwnPropertyDescriptor(target, prop as string);
            },

            ownKeys: (target) => {
                return this.ownKeys(target);
            }
        };

        // Create proxy
        this.proxyWindow = new Proxy(fakeWindow, proxyHandler) as unknown as Window;
    }

    /**
     * Get property handler
     * @param target - Target object
     * @param prop - Property name
     * @param receiver - Receiver object
     * @returns Property value
     */
    private getProperty(target: any, prop: string, receiver: any): any {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new SandboxError(
                    this.name,
                    `Access to property "${prop}" is denied`,
                    ErrorCode.SANDBOX_EXECUTION_FAILED
                );
            }
            return undefined;
        }

        // Check if property is in allow list (if strict isolation is enabled)
        if (this.options.strictIsolation && this.allowListSet.size > 0 && !this.allowListSet.has(prop)) {
            return undefined;
        }

        // Check if property exists in sandbox
        if (prop in target) {
            return target[prop];
        }

        // Get from original window
        const value = (window as any)[prop];

        // Track property access if enabled
        if (this.options.enablePropertyTracking) {
            this.trackPropertyAccess(prop, 'get');
        }

        return value;
    }

    /**
     * Set property handler
     * @param target - Target object
     * @param prop - Property name
     * @param value - Property value
     * @param receiver - Receiver object
     * @returns Success status
     */
    private setProperty(target: any, prop: string, value: any, receiver: any): boolean {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new SandboxError(
                    this.name,
                    `Setting property "${prop}" is denied`,
                    ErrorCode.SANDBOX_EXECUTION_FAILED
                );
            }
            return false;
        }

        // Store original value if not already stored
        if (!(prop in target) && prop in window) {
            this.windowSnapshot[prop] = (window as any)[prop];
            this.modifiedProps.add(prop);
        } else if (!(prop in target) && !(prop in window)) {
            this.addedProps.add(prop);
        }

        // Set property in sandbox
        target[prop] = value;

        // Track property modification if enabled
        if (this.options.enablePropertyTracking) {
            this.trackPropertyAccess(prop, 'set', value);
        }

        return true;
    }

    /**
     * Has property handler
     * @param target - Target object
     * @param prop - Property name
     * @returns Whether property exists
     */
    private hasProperty(target: any, prop: string): boolean {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            return false;
        }

        // Check if property is in allow list (if strict isolation is enabled)
        if (this.options.strictIsolation && this.allowListSet.size > 0 && !this.allowListSet.has(prop)) {
            return false;
        }

        return prop in target || prop in window;
    }

    /**
     * Delete property handler
     * @param target - Target object
     * @param prop - Property name
     * @returns Success status
     */
    private deleteProperty(target: any, prop: string): boolean {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new SandboxError(
                    this.name,
                    `Deleting property "${prop}" is denied`,
                    ErrorCode.SANDBOX_EXECUTION_FAILED
                );
            }
            return false;
        }

        if (prop in target) {
            delete target[prop];

            // Track property deletion if enabled
            if (this.options.enablePropertyTracking) {
                this.trackPropertyAccess(prop, 'delete');
            }

            return true;
        }

        return false;
    }

    /**
     * Define property handler
     * @param target - Target object
     * @param prop - Property name
     * @param descriptor - Property descriptor
     * @returns Success status
     */
    private defineProperty(target: any, prop: string, descriptor: PropertyDescriptor): boolean {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            if (this.options.strictIsolation) {
                throw new SandboxError(
                    this.name,
                    `Defining property "${prop}" is denied`,
                    ErrorCode.SANDBOX_EXECUTION_FAILED
                );
            }
            return false;
        }

        // Store original descriptor if exists
        if (this.options.enablePropertyTracking) {
            const originalDescriptor = Object.getOwnPropertyDescriptor(window, prop);
            const trackedDescriptor: TrackedPropertyDescriptor = {
                ...descriptor,
                originalDescriptor,
                isModified: true,
                modifiedAt: new Date()
            };
            this.propertyDescriptors.set(prop, trackedDescriptor);
        }

        return Object.defineProperty(target, prop, descriptor);
    }

    /**
     * Get own property descriptor handler
     * @param target - Target object
     * @param prop - Property name
     * @returns Property descriptor
     */
    private getOwnPropertyDescriptor(target: any, prop: string): PropertyDescriptor | undefined {
        // Check if property is denied
        if (this.denyListSet.has(prop)) {
            return undefined;
        }

        // Check if property is in allow list (if strict isolation is enabled)
        if (this.options.strictIsolation && this.allowListSet.size > 0 && !this.allowListSet.has(prop)) {
            return undefined;
        }

        // Get from tracked descriptors first
        if (this.propertyDescriptors.has(prop)) {
            const tracked = this.propertyDescriptors.get(prop)!;
            return {
                value: tracked.value,
                writable: tracked.writable,
                enumerable: tracked.enumerable,
                configurable: tracked.configurable
            };
        }

        // Get from target or window
        return Object.getOwnPropertyDescriptor(target, prop) ||
            Object.getOwnPropertyDescriptor(window, prop);
    }

    /**
     * Own keys handler
     * @param target - Target object
     * @returns Array of property names
     */
    private ownKeys(target: any): (string | symbol)[] {
        const targetKeys = Object.getOwnPropertyNames(target);
        const windowKeys = Object.getOwnPropertyNames(window);

        // Filter keys based on allow/deny lists
        const allKeys = [...new Set([...targetKeys, ...windowKeys])];

        return allKeys.filter(key => {
            const keyStr = String(key);

            // Check deny list
            if (this.denyListSet.has(keyStr)) {
                return false;
            }

            // Check allow list (if strict isolation is enabled)
            if (this.options.strictIsolation && this.allowListSet.size > 0) {
                return this.allowListSet.has(keyStr);
            }

            return true;
        });
    }

    /**
     * Track property access
     * @param prop - Property name
     * @param operation - Operation type
     * @param value - Property value (for set operations)
     */
    private trackPropertyAccess(prop: string, operation: 'get' | 'set' | 'delete', value?: any): void {
        // This could be extended to provide detailed access logs
        // For now, we just track modified properties
        if (operation === 'set') {
            this.modifiedProps.add(prop);
        }
    }

    /**
     * Take a snapshot of the current window state
     */
    private takeSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        this.windowSnapshot = {};
        this.modifiedProps.clear();
        this.addedProps.clear();

        // Store current state of all enumerable properties
        for (const prop of Object.getOwnPropertyNames(window)) {
            try {
                this.windowSnapshot[prop] = (window as any)[prop];
            } catch (error) {
                // Some properties might not be accessible
                continue;
            }
        }
    }

    /**
     * Restore window state from snapshot
     */
    private restoreSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        // Restore modified properties
        for (const prop of this.modifiedProps) {
            try {
                if (prop in this.windowSnapshot) {
                    (window as any)[prop] = this.windowSnapshot[prop];
                } else {
                    delete (window as any)[prop];
                }
            } catch (error) {
                // Some properties might not be configurable
                continue;
            }
        }

        // Remove added properties
        for (const prop of this.addedProps) {
            try {
                delete (window as any)[prop];
            } catch (error) {
                // Some properties might not be configurable
                continue;
            }
        }

        // Clear tracking sets
        this.modifiedProps.clear();
        this.addedProps.clear();
    }

    /**
     * Activate the sandbox
     */
    active(): void {
        if (this.isActivated) {
            return;
        }

        // Take snapshot before activation
        this.takeSnapshot();

        this.isActivated = true;
    }

    /**
     * Deactivate the sandbox
     */
    inactive(): void {
        if (!this.isActivated) {
            return;
        }

        // Restore snapshot on deactivation
        this.restoreSnapshot();

        this.isActivated = false;
    }

    /**
     * Execute script in the sandbox
     * @param code - JavaScript code to execute
     * @returns Execution result
     */
    execScript(code: string): any {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        if (!this.proxyWindow) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Proxy window is not available',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            // Create a function with the proxy window as context
            const func = new Function('window', `
                with (window) {
                    return (function() {
                        ${code}
                    })();
                }
            `);

            return func(this.proxyWindow);
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Script execution failed: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Check if the sandbox is active
     * @returns True if active, false otherwise
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Get sandbox statistics
     * @returns Sandbox statistics
     */
    getStats(): {
        modifiedProps: string[];
        addedProps: string[];
        trackedDescriptors: number;
        snapshotSize: number;
    } {
        return {
            modifiedProps: Array.from(this.modifiedProps),
            addedProps: Array.from(this.addedProps),
            trackedDescriptors: this.propertyDescriptors.size,
            snapshotSize: Object.keys(this.windowSnapshot).length
        };
    }

    /**
     * Get proxy window
     * @returns Proxy window object
     */
    getProxyWindow(): Window | null {
        return this.proxyWindow;
    }

    /**
     * Reset sandbox state
     */
    reset(): void {
        this.inactive();
        this.modifiedProps.clear();
        this.addedProps.clear();
        this.propertyDescriptors.clear();
        this.windowSnapshot = {};
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        // Deactivate if active
        if (this.isActivated) {
            this.inactive();
        }

        // Clear all data
        this.reset();
        this.proxyWindow = null;
    }
}