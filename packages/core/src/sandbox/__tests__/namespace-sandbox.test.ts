/**
 * NamespaceSandbox tests
 */

import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { SANDBOX_TYPES } from '../../constants';
import { NamespaceSandbox } from '../namespace-sandbox';

describe('NamespaceSandbox', () => {
    let sandbox: NamespaceSandbox;

    beforeEach(() => {
        sandbox = new NamespaceSandbox('test-sandbox', {});
    });

    afterEach(() => {
        if (sandbox) {
            sandbox.destroy();
        }
    });

    describe('Basic functionality', () => {
        it('should create sandbox with correct name and type', () => {
            expect(sandbox.name).toBe('test-sandbox');
            expect(sandbox.type).toBe(SANDBOX_TYPES.NAMESPACE);
        });

        it('should start inactive', () => {
            expect(sandbox.isActive()).toBe(false);
        });

        it('should activate and deactivate correctly', () => {
            sandbox.active();
            expect(sandbox.isActive()).toBe(true);

            sandbox.inactive();
            expect(sandbox.isActive()).toBe(false);
        });

        it('should generate unique namespace prefix', () => {
            const prefix = sandbox.getNamespacePrefix();
            expect(prefix).toMatch(/^__test_sandbox_[a-z0-9]+__$/);
        });
    });

    describe('Variable management', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should set and get variables by name', () => {
            sandbox.setVariableByName('testVar', 'test value');

            const value = sandbox.getVariableByName('testVar');
            expect(value).toBe('test value');
        });

        it('should check if variable exists', () => {
            sandbox.setVariableByName('existingVar', 'value');

            expect(sandbox.hasVariable('existingVar')).toBe(true);
            expect(sandbox.hasVariable('nonExistentVar')).toBe(false);
        });

        it('should remove variables', () => {
            sandbox.setVariableByName('tempVar', 'temp value');
            expect(sandbox.hasVariable('tempVar')).toBe(true);

            sandbox.removeVariable('tempVar');
            expect(sandbox.hasVariable('tempVar')).toBe(false);
        });

        it('should track namespaced variables', () => {
            sandbox.setVariableByName('var1', 'value1');
            sandbox.setVariableByName('var2', 'value2');

            const namespacedVars = sandbox.getNamespacedVariables();
            expect(namespacedVars.size).toBe(2);
            expect(namespacedVars.has('var1')).toBe(true);
            expect(namespacedVars.has('var2')).toBe(true);
        });
    });

    describe('Script execution', () => {
        it('should throw error when executing script in inactive sandbox', () => {
            expect(() => {
                sandbox.execScript('var test = 1;');
            }).toThrow('Cannot execute script: Sandbox is not active');
        });

        it('should execute simple script when active', () => {
            sandbox.active();

            const result = sandbox.execScript('return 42;');
            expect(result).toBe(42);
        });

        it('should execute script with variable declarations', () => {
            sandbox.active();

            sandbox.execScript('var testVar = "hello world";');

            // Variable should be accessible through sandbox methods
            const namespacedVars = sandbox.getNamespacedVariables();
            expect(namespacedVars.size).toBeGreaterThan(0);
        });

        it('should handle script execution errors', () => {
            sandbox.active();

            expect(() => {
                sandbox.execScript('throw new Error("Test error");');
            }).toThrow('Script execution failed');
        });
    });

    describe('Namespace prefixing', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should create namespaced variable names', () => {
            sandbox.setVariableByName('myVar', 'test');

            const namespacedVars = sandbox.getNamespacedVariables();
            const varInfo = namespacedVars.get('myVar');

            expect(varInfo).toBeTruthy();
            expect(varInfo?.originalName).toBe('myVar');
            expect(varInfo?.namespacedName).toMatch(/^__test_sandbox_[a-z0-9]+__myVar$/);
        });

        it('should preserve original variable names when configured', () => {
            const preservingSandbox = new NamespaceSandbox('preserve-test', {
                preserveOriginalNames: true
            });

            preservingSandbox.active();
            preservingSandbox.setVariableByName('originalVar', 'value');

            const namespacedVars = preservingSandbox.getNamespacedVariables();
            const varInfo = namespacedVars.get('originalVar');

            expect(varInfo?.originalName).toBe('originalVar');

            preservingSandbox.destroy();
        });
    });

    describe('Conflict detection', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should detect conflicts when enabled', () => {
            const conflictSandbox = new NamespaceSandbox('conflict-test', {
                enableConflictDetection: true
            });

            conflictSandbox.active();

            // Set a global variable that might conflict
            const prefix = conflictSandbox.getNamespacePrefix();
            (window as any)[`${prefix}conflictVar`] = 'existing value';

            conflictSandbox.setVariableByName('conflictVar', 'new value');

            const conflicts = conflictSandbox.getConflicts();
            // Conflicts might be detected depending on the implementation

            conflictSandbox.destroy();
            delete (window as any)[`${prefix}conflictVar`];
        });

        it('should track conflicts', () => {
            const conflicts = sandbox.getConflicts();
            expect(conflicts).toBeInstanceOf(Map);
        });
    });

    describe('Options handling', () => {
        it('should respect custom namespace prefix', () => {
            const customSandbox = new NamespaceSandbox('custom-test', {
                namespacePrefix: '__custom_prefix__'
            });

            expect(customSandbox.getNamespacePrefix()).toBe('__custom_prefix__');

            customSandbox.destroy();
        });

        it('should handle auto prefixing option', () => {
            const autoPrefixSandbox = new NamespaceSandbox('auto-test', {
                enableAutoPrefixing: false
            });

            const stats = autoPrefixSandbox.getStats();
            expect(stats.isAutoPrefixingEnabled).toBe(false);

            autoPrefixSandbox.destroy();
        });

        it('should handle conflict detection option', () => {
            const conflictSandbox = new NamespaceSandbox('conflict-test', {
                enableConflictDetection: false
            });

            const stats = conflictSandbox.getStats();
            expect(stats.isConflictDetectionEnabled).toBe(false);

            conflictSandbox.destroy();
        });

        it('should handle exclude from prefixing list', () => {
            const excludeSandbox = new NamespaceSandbox('exclude-test', {
                excludeFromPrefixing: ['specialVar', 'anotherVar']
            });

            excludeSandbox.active();
            excludeSandbox.setVariableByName('specialVar', 'value');

            // Variable should not be in namespaced variables if excluded
            const namespacedVars = excludeSandbox.getNamespacedVariables();
            // The behavior depends on implementation details

            excludeSandbox.destroy();
        });
    });

    describe('Statistics and management', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should provide sandbox statistics', () => {
            sandbox.setVariableByName('var1', 'value1');
            sandbox.setVariableByName('var2', 'value2');

            const stats = sandbox.getStats();
            expect(stats).toHaveProperty('namespacePrefix');
            expect(stats).toHaveProperty('namespacedVariables');
            expect(stats).toHaveProperty('conflicts');
            expect(stats).toHaveProperty('originalGlobalsBackup');
            expect(stats).toHaveProperty('isAutoPrefixingEnabled');
            expect(stats).toHaveProperty('isConflictDetectionEnabled');

            expect(stats.namespacedVariables).toBe(2);
            expect(stats.isAutoPrefixingEnabled).toBe(true);
            expect(stats.isConflictDetectionEnabled).toBe(true);
        });

        it('should reset sandbox state', () => {
            sandbox.setVariableByName('tempVar', 'temp');

            sandbox.reset();

            expect(sandbox.isActive()).toBe(false);
            const stats = sandbox.getStats();
            expect(stats.namespacedVariables).toBe(0);
            expect(stats.conflicts).toBe(0);
        });

        it('should clean up variables on deactivation', () => {
            const prefix = sandbox.getNamespacePrefix();
            sandbox.setVariableByName('cleanupVar', 'cleanup value');

            const namespacedName = `${prefix}cleanupVar`;
            expect((window as any)[namespacedName]).toBe('cleanup value');

            sandbox.inactive();

            // Variable should be cleaned up
            expect((window as any)[namespacedName]).toBeUndefined();
        });
    });

    describe('Variable patterns', () => {
        it('should handle custom variable patterns', () => {
            const patternSandbox = new NamespaceSandbox('pattern-test', {
                variablePatterns: [/^test_[a-z]+$/]
            });

            patternSandbox.active();

            // Test that custom patterns are accepted
            expect(patternSandbox).toBeTruthy();

            patternSandbox.destroy();
        });
    });

    describe('Error handling', () => {
        it('should handle variable setting errors gracefully', () => {
            sandbox.active();

            // Test setting variables with problematic names
            expect(() => {
                sandbox.setVariableByName('', 'empty name');
            }).not.toThrow();
        });

        it('should handle variable removal errors gracefully', () => {
            sandbox.active();

            // Try to remove non-existent variable
            expect(() => {
                sandbox.removeVariable('nonExistentVar');
            }).not.toThrow();
        });

        it('should handle cleanup errors during deactivation', () => {
            sandbox.active();
            sandbox.setVariableByName('testVar', 'value');

            // Mock Object.defineProperty to make a property non-configurable
            const originalDefineProperty = Object.defineProperty;
            const prefix = sandbox.getNamespacePrefix();
            const namespacedName = `${prefix}testVar`;

            // Make the property non-configurable
            Object.defineProperty(window, namespacedName, {
                value: 'value',
                configurable: false,
                writable: false
            });

            // Should not throw during cleanup
            expect(() => {
                sandbox.inactive();
            }).not.toThrow();

            // Clean up
            try {
                delete (window as any)[namespacedName];
            } catch (error) {
                // Ignore cleanup errors
            }
        });
    });
});