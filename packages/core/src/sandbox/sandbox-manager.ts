/**
 * @fileoverview 沙箱管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { ErrorCodes } from './constants';
import { SandboxError } from './errors';
import type { SandboxConfig, SandboxInstance, SandboxType } from './types';
import { createLogger } from './utils';

/**
 * 沙箱管理器
 * 负责管理和协调不同的沙箱策略
 */
export class SandboxManager extends EventEmitter {
    private readonly logger = createLogger('SandboxManager');
    private readonly sandboxes = new Map<string, SandboxInstance>();
    private readonly factories = new Map<SandboxType, SandboxFactory>();
    private started = false;

    constructor() {
        super();
        this.registerBuiltinFactories();
    }

    /**
     * 启动沙箱管理器
     */
    async start(): Promise<void> {
        if (this.started) {
            this.logger.warn('沙箱管理器已经启动');
            return;
        }

        this.logger.info('启动沙箱管理器...');
        this.started = true;
        this.emit('started');
        this.logger.info('沙箱管理器启动成功');
    }

    /**
     * 停止沙箱管理器
     */
    async stop(): Promise<void> {
        if (!this.started) {
            this.logger.warn('沙箱管理器未启动');
            return;
        }

        this.logger.info('停止沙箱管理器...');

        // 停用所有沙箱
        const promises = Array.from(this.sandboxes.keys()).map(name =>
            this.destroySandbox(name).catch(error =>
                this.logger.error(`销毁沙箱失败: ${name}`, error)
            )
        );

        await Promise.all(promises);

        this.started = false;
        this.emit('stopped');
        this.logger.info('沙箱管理器停止成功');
    }

    /**
     * 销毁沙箱管理器
     */
    async destroy(): Promise<void> {
        if (this.started) {
            await this.stop();
        }

        this.sandboxes.clear();
        this.factories.clear();
        this.removeAllListeners();
        this.logger.info('沙箱管理器已销毁');
    }

    /**
     * 注册沙箱工厂
     * @param type 沙箱类型
     * @param factory 沙箱工厂
     */
    registerFactory(type: SandboxType, factory: SandboxFactory): void {
        this.factories.set(type, factory);
        this.logger.debug(`注册沙箱工厂: ${type}`);
    }

    /**
     * 创建沙箱实例
     * @param name 沙箱名称
     * @param type 沙箱类型
     * @param config 沙箱配置
     */
    async createSandbox(
        name: string,
        type: SandboxType = 'proxy',
        config: SandboxConfig = {}
    ): Promise<SandboxInstance> {
        if (this.sandboxes.has(name)) {
            throw new SandboxError(
                `沙箱 "${name}" 已存在`,
                type,
                ErrorCodes.SANDBOX_CREATE_FAILED
            );
        }

        const factory = this.factories.get(type);
        if (!factory) {
            throw new SandboxError(
                `不支持的沙箱类型: ${type}`,
                type,
                ErrorCodes.SANDBOX_CREATE_FAILED
            );
        }

        try {
            this.logger.debug(`创建沙箱: ${name} (${type})`);

            const sandbox = await factory.create(name, config);
            this.sandboxes.set(name, sandbox);

            this.emit('sandboxCreated', { name, type, sandbox });
            this.logger.info(`沙箱创建成功: ${name} (${type})`);

            return sandbox;
        } catch (error) {
            this.logger.error(`沙箱创建失败: ${name}`, error);
            throw new SandboxError(
                `沙箱创建失败: ${(error as Error).message}`,
                type,
                ErrorCodes.SANDBOX_CREATE_FAILED,
                { name, originalError: error }
            );
        }
    }

    /**
     * 获取沙箱实例
     * @param name 沙箱名称
     */
    getSandbox(name: string): SandboxInstance | null {
        return this.sandboxes.get(name) || null;
    }

    /**
     * 激活沙箱
     * @param name 沙箱名称
     */
    async activateSandbox(name: string): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            throw new SandboxError(
                `沙箱 "${name}" 不存在`,
                sandbox?.type || 'unknown',
                ErrorCodes.SANDBOX_ACTIVATE_FAILED
            );
        }

        try {
            this.logger.debug(`激活沙箱: ${name}`);
            await sandbox.activate();
            this.emit('sandboxActivated', { name, sandbox });
            this.logger.info(`沙箱激活成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱激活失败: ${name}`, error);
            throw new SandboxError(
                `沙箱激活失败: ${(error as Error).message}`,
                sandbox.type,
                ErrorCodes.SANDBOX_ACTIVATE_FAILED,
                { name, originalError: error }
            );
        }
    }

    /**
     * 停用沙箱
     * @param name 沙箱名称
     */
    async deactivateSandbox(name: string): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            throw new SandboxError(
                `沙箱 "${name}" 不存在`,
                sandbox?.type || 'unknown',
                ErrorCodes.SANDBOX_DEACTIVATE_FAILED
            );
        }

        try {
            this.logger.debug(`停用沙箱: ${name}`);
            await sandbox.deactivate();
            this.emit('sandboxDeactivated', { name, sandbox });
            this.logger.info(`沙箱停用成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱停用失败: ${name}`, error);
            throw new SandboxError(
                `沙箱停用失败: ${(error as Error).message}`,
                sandbox.type,
                ErrorCodes.SANDBOX_DEACTIVATE_FAILED,
                { name, originalError: error }
            );
        }
    }

    /**
     * 销毁沙箱
     * @param name 沙箱名称
     */
    async destroySandbox(name: string): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            this.logger.warn(`沙箱 "${name}" 不存在，跳过销毁`);
            return;
        }

        try {
            this.logger.debug(`销毁沙箱: ${name}`);
            await sandbox.destroy();
            this.sandboxes.delete(name);
            this.emit('sandboxDestroyed', { name, sandbox });
            this.logger.info(`沙箱销毁成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱销毁失败: ${name}`, error);
            // 即使销毁失败也要从管理器中移除
            this.sandboxes.delete(name);
            throw new SandboxError(
                `沙箱销毁失败: ${(error as Error).message}`,
                sandbox.type,
                ErrorCodes.SANDBOX_CREATE_FAILED,
                { name, originalError: error }
            );
        }
    }

    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): SandboxInstance[] {
        return Array.from(this.sandboxes.values());
    }

    /**
     * 获取沙箱统计信息
     */
    getStats(): {
        total: number;
        active: number;
        inactive: number;
        types: Record<SandboxType, number>;
    } {
        const sandboxes = Array.from(this.sandboxes.values());
        const types: Record<SandboxType, number> = {
            proxy: 0,
            defineProperty: 0,
            webComponent: 0,
            iframe: 0,
            namespace: 0,
            federation: 0
        };

        let active = 0;
        let inactive = 0;

        sandboxes.forEach(sandbox => {
            types[sandbox.type]++;

            // 假设沙箱有 isActive 方法或属性
            const context = sandbox.getContext?.();
            if (context?.active) {
                active++;
            } else {
                inactive++;
            }
        });

        return {
            total: sandboxes.length,
            active,
            inactive,
            types
        };
    }

    /**
     * 检查沙箱是否存在
     * @param name 沙箱名称
     */
    hasSandbox(name: string): boolean {
        return this.sandboxes.has(name);
    }

    /**
     * 获取支持的沙箱类型
     */
    getSupportedTypes(): SandboxType[] {
        return Array.from(this.factories.keys());
    }

    /**
     * 清理所有沙箱
     */
    async clearAll(): Promise<void> {
        const names = Array.from(this.sandboxes.keys());
        const promises = names.map(name => this.destroySandbox(name));
        await Promise.all(promises);
        this.logger.info('所有沙箱已清理');
    }

    /**
     * 注册内置沙箱工厂
     */
    private registerBuiltinFactories(): void {
        // 这里注册内置的沙箱工厂
        // 实际的沙箱实现会在对应的插件中提供
        this.logger.debug('注册内置沙箱工厂');
    }
}

/**
 * 沙箱工厂接口
 */
export interface SandboxFactory {
    /**
     * 创建沙箱实例
     * @param name 沙箱名称
     * @param config 沙箱配置
     */
    create(name: string, config: SandboxConfig): Promise<SandboxInstance>;

    /**
     * 检查是否支持当前环境
     */
    isSupported(): boolean;
}

export default SandboxManager;