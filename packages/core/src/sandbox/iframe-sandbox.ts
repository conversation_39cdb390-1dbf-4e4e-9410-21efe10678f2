/**
 * IframeSandbox - Iframe-based sandbox implementation
 * @packageDocumentation
 */

import { ErrorCode, SandboxError } from '../errors';
import { Sandbox, SANDBOX_TYPES, SandboxOptions } from '../types';

/**
 * Iframe sandbox options
 */
export interface IframeSandboxOptions extends Omit<SandboxOptions, 'type'> {
    /**
     * Type of sandbox (optional, defaults to iframe)
     */
    type?: 'iframe';

    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Whether to enable style isolation
     */
    enableStyleIsolation?: boolean;

    /**
     * Container element selector or element
     */
    container?: string | HTMLElement;

    /**
     * Iframe sandbox attributes
     */
    sandboxAttributes?: string[];

    /**
     * Whether to allow same origin
     */
    allowSameOrigin?: boolean;

    /**
     * Whether to allow scripts
     */
    allowScripts?: boolean;

    /**
     * Whether to allow forms
     */
    allowForms?: boolean;

    /**
     * Whether to allow popups
     */
    allowPopups?: boolean;

    /**
     * Custom iframe source document
     */
    srcDoc?: string;

    /**
     * Communication timeout in milliseconds
     */
    communicationTimeout?: number;
}

/**
 * Message types for iframe communication
 */
enum MessageType {
    EXEC_SCRIPT = 'exec_script',
    SCRIPT_RESULT = 'script_result',
    SCRIPT_ERROR = 'script_error',
    ADD_CSS = 'add_css',
    ADD_HTML = 'add_html',
    ADD_EVENT_LISTENER = 'add_event_listener',
    REMOVE_EVENT_LISTENER = 'remove_event_listener',
    READY = 'ready',
    PING = 'ping',
    PONG = 'pong'
}

/**
 * Communication message interface
 */
interface CommunicationMessage {
    type: MessageType;
    id: string;
    data?: any;
    error?: string;
}

/**
 * IframeSandbox - Iframe-based sandbox implementation
 * 
 * This sandbox uses iframes to provide the strongest isolation possible,
 * including JavaScript, CSS, and DOM isolation. Communication is handled
 * through postMessage API.
 */
export class IframeSandbox implements Sandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = SANDBOX_TYPES.IFRAME;

    /**
     * Sandbox options
     */
    private options: IframeSandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Iframe element
     */
    private iframe: HTMLIFrameElement | null = null;

    /**
     * Container element
     */
    private container: HTMLElement | null = null;

    /**
     * Message ID counter
     */
    private messageIdCounter: number = 0;

    /**
     * Pending messages
     */
    private pendingMessages: Map<string, {
        resolve: (value: any) => void;
        reject: (error: Error) => void;
        timeout: NodeJS.Timeout;
    }> = new Map();

    /**
     * Event listeners
     */
    private eventListeners: Map<string, EventListener[]> = new Map();

    /**
     * Whether iframe is ready
     */
    private isIframeReady: boolean = false;

    /**
     * Ready promise
     */
    private readyPromise: Promise<void> | null = null;

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Creates a new IframeSandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: IframeSandboxOptions) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            enableStyleIsolation: true,
            allowSameOrigin: false,
            allowScripts: true,
            allowForms: false,
            allowPopups: false,
            sandboxAttributes: [],
            communicationTimeout: 5000,
            allowList: [],
            denyList: [],
            ...options
        };

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Bind message handler
        this.handleMessage = this.handleMessage.bind(this);
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI',
            'URL',
            'URLSearchParams',
            'Blob',
            'File',
            'FileReader',
            'addEventListener',
            'removeEventListener',
            'dispatchEvent'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Generate unique message ID
     * @returns Message ID
     */
    private generateMessageId(): string {
        return `${this.name}_${++this.messageIdCounter}_${Date.now()}`;
    }

    /**
     * Get container element
     * @returns Container element
     */
    private getContainer(): HTMLElement {
        if (this.container) {
            return this.container;
        }

        if (typeof this.options.container === 'string') {
            const element = document.querySelector(this.options.container);
            if (!element) {
                throw new SandboxError(
                    this.name,
                    `Container element not found: ${this.options.container}`,
                    ErrorCode.SANDBOX_CREATION_FAILED
                );
            }
            this.container = element as HTMLElement;
        } else if (this.options.container instanceof HTMLElement) {
            this.container = this.options.container;
        } else {
            // Default to document.body
            this.container = document.body;
        }

        return this.container;
    }

    /**
     * Create iframe element
     * @returns Iframe element
     */
    private createIframe(): HTMLIFrameElement {
        const iframe = document.createElement('iframe');

        // Set basic attributes
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.style.display = 'block';

        // Build sandbox attributes
        const sandboxAttrs = [...this.options.sandboxAttributes!];

        if (this.options.allowScripts) {
            sandboxAttrs.push('allow-scripts');
        }

        if (this.options.allowSameOrigin) {
            sandboxAttrs.push('allow-same-origin');
        }

        if (this.options.allowForms) {
            sandboxAttrs.push('allow-forms');
        }

        if (this.options.allowPopups) {
            sandboxAttrs.push('allow-popups');
        }

        // Set sandbox attribute
        if (sandboxAttrs.length > 0) {
            iframe.setAttribute('sandbox', sandboxAttrs.join(' '));
        }

        // Set source document
        const srcDoc = this.options.srcDoc || this.createDefaultSrcDoc();
        iframe.srcdoc = srcDoc;

        return iframe;
    }

    /**
     * Create default source document
     * @returns HTML source document
     */
    private createDefaultSrcDoc(): string {
        const allowedGlobals = Array.from(this.allowListSet).join('", "');

        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Micro-Core Iframe Sandbox</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        ${this.options.enableStyleIsolation ? `
        * {
            box-sizing: border-box;
        }
        
        body {
            isolation: isolate;
        }
        ` : ''}
    </style>
</head>
<body>
    <div id="micro-core-content"></div>
    
    <script>
        (function() {
            const allowedGlobals = ["${allowedGlobals}"];
            const sandboxName = "${this.name}";
            const strictIsolation = ${this.options.strictIsolation};
            
            // Communication with parent
            function sendMessage(type, id, data, error) {
                parent.postMessage({
                    type: type,
                    id: id,
                    data: data,
                    error: error,
                    sandboxName: sandboxName
                }, '*');
            }
            
            // Handle script execution
            function execScript(code, messageId) {
                try {
                    const result = (function() {
                        return eval(code);
                    })();
                    sendMessage('${MessageType.SCRIPT_RESULT}', messageId, result);
                } catch (error) {
                    sendMessage('${MessageType.SCRIPT_ERROR}', messageId, null, error.message);
                }
            }
            
            // Handle CSS addition
            function addCSS(css) {
                const style = document.createElement('style');
                style.textContent = css;
                document.head.appendChild(style);
            }
            
            // Handle HTML addition
            function addHTML(html) {
                const container = document.getElementById('micro-core-content');
                if (container) {
                    container.innerHTML += html;
                }
            }
            
            // Message handler
            window.addEventListener('message', function(event) {
                const message = event.data;
                if (!message || !message.type) return;
                
                switch (message.type) {
                    case '${MessageType.EXEC_SCRIPT}':
                        execScript(message.data, message.id);
                        break;
                        
                    case '${MessageType.ADD_CSS}':
                        addCSS(message.data);
                        sendMessage('${MessageType.SCRIPT_RESULT}', message.id, true);
                        break;
                        
                    case '${MessageType.ADD_HTML}':
                        addHTML(message.data);
                        sendMessage('${MessageType.SCRIPT_RESULT}', message.id, true);
                        break;
                        
                    case '${MessageType.PING}':
                        sendMessage('${MessageType.PONG}', message.id, true);
                        break;
                }
            });
            
            // Signal ready
            sendMessage('${MessageType.READY}', 'ready', true);
        })();
    </script>
</body>
</html>
        `;
    }

    /**
     * Handle messages from iframe
     * @param event - Message event
     */
    private handleMessage(event: MessageEvent): void {
        const message = event.data as CommunicationMessage;

        if (!message || !message.type || !message.id) {
            return;
        }

        // Check if message is from our iframe
        if (event.source !== this.iframe?.contentWindow) {
            return;
        }

        switch (message.type) {
            case MessageType.READY:
                this.isIframeReady = true;
                break;

            case MessageType.SCRIPT_RESULT:
            case MessageType.SCRIPT_ERROR:
            case MessageType.PONG:
                this.handleResponse(message);
                break;
        }
    }

    /**
     * Handle response message
     * @param message - Response message
     */
    private handleResponse(message: CommunicationMessage): void {
        const pending = this.pendingMessages.get(message.id);
        if (!pending) {
            return;
        }

        // Clear timeout
        clearTimeout(pending.timeout);
        this.pendingMessages.delete(message.id);

        // Resolve or reject
        if (message.type === MessageType.SCRIPT_ERROR) {
            pending.reject(new Error(message.error || 'Script execution failed'));
        } else {
            pending.resolve(message.data);
        }
    }

    /**
     * Send message to iframe
     * @param type - Message type
     * @param data - Message data
     * @returns Promise that resolves with response
     */
    private sendMessage(type: MessageType, data?: any): Promise<any> {
        if (!this.iframe || !this.iframe.contentWindow) {
            return Promise.reject(new Error('Iframe not available'));
        }

        const messageId = this.generateMessageId();
        const message: CommunicationMessage = {
            type,
            id: messageId,
            data
        };

        return new Promise((resolve, reject) => {
            // Set up timeout
            const timeout = setTimeout(() => {
                this.pendingMessages.delete(messageId);
                reject(new Error(`Message timeout: ${type}`));
            }, this.options.communicationTimeout);

            // Store pending message
            this.pendingMessages.set(messageId, {
                resolve,
                reject,
                timeout
            });

            // Send message
            this.iframe!.contentWindow!.postMessage(message, '*');
        });
    }

    /**
     * Wait for iframe to be ready
     * @returns Promise that resolves when iframe is ready
     */
    private waitForReady(): Promise<void> {
        if (this.readyPromise) {
            return this.readyPromise;
        }

        this.readyPromise = new Promise((resolve, reject) => {
            if (this.isIframeReady) {
                resolve();
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('Iframe ready timeout'));
            }, this.options.communicationTimeout);

            const checkReady = () => {
                if (this.isIframeReady) {
                    clearTimeout(timeout);
                    resolve();
                } else {
                    setTimeout(checkReady, 10);
                }
            };

            checkReady();
        });

        return this.readyPromise;
    }

    /**
     * Activate the sandbox
     */
    active(): void {
        if (this.isActivated) {
            return;
        }

        try {
            // Create iframe
            this.iframe = this.createIframe();

            // Add message listener
            window.addEventListener('message', this.handleMessage);

            // Add to container
            const container = this.getContainer();
            container.appendChild(this.iframe);

            this.isActivated = true;
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Failed to activate Iframe sandbox: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Deactivate the sandbox
     */
    inactive(): void {
        if (!this.isActivated) {
            return;
        }

        try {
            // Remove message listener
            window.removeEventListener('message', this.handleMessage);

            // Clear pending messages
            for (const [id, pending] of this.pendingMessages.entries()) {
                clearTimeout(pending.timeout);
                pending.reject(new Error('Sandbox deactivated'));
            }
            this.pendingMessages.clear();

            // Remove iframe from DOM
            if (this.iframe && this.iframe.parentNode) {
                this.iframe.parentNode.removeChild(this.iframe);
            }

            // Clear event listeners
            this.eventListeners.clear();

            this.iframe = null;
            this.isIframeReady = false;
            this.readyPromise = null;
            this.isActivated = false;
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Failed to deactivate Iframe sandbox: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Execute script in the sandbox
     * @param code - JavaScript code to execute
     * @returns Execution result
     */
    async execScript(code: string): Promise<any> {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            // Wait for iframe to be ready
            await this.waitForReady();

            // Send script execution message
            const result = await this.sendMessage(MessageType.EXEC_SCRIPT, code);
            return result;
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Script execution failed: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Add CSS to the sandbox
     * @param css - CSS code
     */
    async addCSS(css: string): Promise<void> {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot add CSS: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            await this.waitForReady();
            await this.sendMessage(MessageType.ADD_CSS, css);
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Failed to add CSS: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Add HTML to the sandbox
     * @param html - HTML code
     */
    async addHTML(html: string): Promise<void> {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot add HTML: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            await this.waitForReady();
            await this.sendMessage(MessageType.ADD_HTML, html);
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Failed to add HTML: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Ping the iframe to check if it's responsive
     * @returns Promise that resolves if iframe responds
     */
    async ping(): Promise<boolean> {
        if (!this.isActivated) {
            return false;
        }

        try {
            await this.waitForReady();
            const result = await this.sendMessage(MessageType.PING);
            return result === true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Check if the sandbox is active
     * @returns True if active, false otherwise
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Get sandbox statistics
     * @returns Sandbox statistics
     */
    getStats(): {
        hasIframe: boolean;
        isReady: boolean;
        pendingMessages: number;
        eventListeners: number;
        sandboxAttributes: string[];
    } {
        return {
            hasIframe: !!this.iframe,
            isReady: this.isIframeReady,
            pendingMessages: this.pendingMessages.size,
            eventListeners: Array.from(this.eventListeners.values()).reduce((sum, listeners) => sum + listeners.length, 0),
            sandboxAttributes: this.options.sandboxAttributes || []
        };
    }

    /**
     * Get iframe element
     * @returns Iframe element
     */
    getIframe(): HTMLIFrameElement | null {
        return this.iframe;
    }

    /**
     * Reset sandbox state
     */
    reset(): void {
        this.inactive();
        this.messageIdCounter = 0;
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        // Deactivate if active
        if (this.isActivated) {
            this.inactive();
        }

        // Clear all data
        this.reset();
    }
}