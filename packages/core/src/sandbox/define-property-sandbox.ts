/**
 * DefinePropertySandbox - Object.defineProperty-based sandbox implementation
 * @packageDocumentation
 */

import { <PERSON><PERSON>rCode, SandboxError } from '../errors';
import { Sandbox, SANDBOX_TYPES, SandboxOptions } from '../types';

/**
 * DefineProperty sandbox options
 */
export interface DefinePropertySandboxOptions extends Omit<SandboxOptions, 'type'> {
    /**
     * Type of sandbox (optional, defaults to defineProperty)
     */
    type?: 'defineProperty';
    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Whether to enable snapshot mode
     */
    enableSnapshot?: boolean;

    /**
     * Whether to enable property tracking
     */
    enablePropertyTracking?: boolean;

    /**
     * Whether to intercept all window properties
     */
    interceptAllProperties?: boolean;
}

/**
 * Property descriptor with additional metadata
 */
interface TrackedPropertyDescriptor extends PropertyDescriptor {
    originalDescriptor?: PropertyDescriptor;
    isModified?: boolean;
    modifiedAt?: Date;
    isIntercepted?: boolean;
}

/**
 * DefinePropertySandbox - Object.defineProperty-based sandbox implementation
 * 
 * This sandbox uses Object.defineProperty to intercept property access,
 * making it compatible with older browsers that don't support Proxy.
 */
export class DefinePropertySandbox implements Sandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = SANDBOX_TYPES.DEFINE_PROPERTY;

    /**
     * Sandbox options
     */
    private options: DefinePropertySandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Fake window object
     */
    private fakeWindow: Record<string, any> = {};

    /**
     * Original window snapshot
     */
    private windowSnapshot: Record<string, any> = {};

    /**
     * Modified properties
     */
    private modifiedProps: Set<string> = new Set();

    /**
     * Added properties
     */
    private addedProps: Set<string> = new Set();

    /**
     * Property descriptors
     */
    private propertyDescriptors: Map<string, TrackedPropertyDescriptor> = new Map();

    /**
     * Intercepted properties
     */
    private interceptedProps: Set<string> = new Set();

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Original property descriptors
     */
    private originalDescriptors: Map<string, PropertyDescriptor> = new Map();

    /**
     * Creates a new DefinePropertySandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: DefinePropertySandboxOptions) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            enableSnapshot: true,
            enablePropertyTracking: true,
            interceptAllProperties: false,
            allowList: [],
            denyList: [],
            ...options
        };

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Initialize fake window
        this.initializeFakeWindow();
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI',
            'URL',
            'URLSearchParams',
            'Blob',
            'File',
            'FileReader'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Initialize fake window object
     */
    private initializeFakeWindow(): void {
        this.fakeWindow = Object.create(null);

        // Set up basic properties
        this.fakeWindow.window = this.fakeWindow;
        this.fakeWindow.self = this.fakeWindow;
        this.fakeWindow.globalThis = this.fakeWindow;
    }

    /**
     * Check if property access is allowed
     * @param prop - Property name
     * @returns True if allowed, false otherwise
     */
    private isPropertyAllowed(prop: string): boolean {
        // Check deny list first
        if (this.denyListSet.has(prop)) {
            return false;
        }

        // Check allow list (if strict isolation is enabled)
        if (this.options.strictIsolation && this.allowListSet.size > 0) {
            return this.allowListSet.has(prop);
        }

        return true;
    }

    /**
     * Create property getter
     * @param prop - Property name
     * @returns Getter function
     */
    private createGetter(prop: string): () => any {
        return () => {
            // Check if property access is allowed
            if (!this.isPropertyAllowed(prop)) {
                if (this.options.strictIsolation) {
                    throw new SandboxError(
                        this.name,
                        `Access to property "${prop}" is denied`,
                        ErrorCode.SANDBOX_EXECUTION_FAILED
                    );
                }
                return undefined;
            }

            // Check if property exists in fake window
            if (prop in this.fakeWindow) {
                return this.fakeWindow[prop];
            }

            // Get from original window
            const value = (window as any)[prop];

            // Track property access if enabled
            if (this.options.enablePropertyTracking) {
                this.trackPropertyAccess(prop, 'get');
            }

            return value;
        };
    }

    /**
     * Create property setter
     * @param prop - Property name
     * @returns Setter function
     */
    private createSetter(prop: string): (value: any) => void {
        return (value: any) => {
            // Check if property access is allowed
            if (!this.isPropertyAllowed(prop)) {
                if (this.options.strictIsolation) {
                    throw new SandboxError(
                        this.name,
                        `Setting property "${prop}" is denied`,
                        ErrorCode.SANDBOX_EXECUTION_FAILED
                    );
                }
                return;
            }

            // Store original value if not already stored
            if (!(prop in this.fakeWindow) && prop in window) {
                this.windowSnapshot[prop] = (window as any)[prop];
                this.modifiedProps.add(prop);
            } else if (!(prop in this.fakeWindow) && !(prop in window)) {
                this.addedProps.add(prop);
            }

            // Set property in fake window
            this.fakeWindow[prop] = value;

            // Track property modification if enabled
            if (this.options.enablePropertyTracking) {
                this.trackPropertyAccess(prop, 'set', value);
            }
        };
    }

    /**
     * Intercept property on window
     * @param prop - Property name
     */
    private interceptProperty(prop: string): void {
        if (this.interceptedProps.has(prop)) {
            return;
        }

        try {
            // Store original descriptor
            const originalDescriptor = Object.getOwnPropertyDescriptor(window, prop);
            if (originalDescriptor) {
                this.originalDescriptors.set(prop, originalDescriptor);
            }

            // Define intercepted property
            Object.defineProperty(window, prop, {
                get: this.createGetter(prop),
                set: this.createSetter(prop),
                enumerable: true,
                configurable: true
            });

            this.interceptedProps.add(prop);

            // Track descriptor if enabled
            if (this.options.enablePropertyTracking) {
                const trackedDescriptor: TrackedPropertyDescriptor = {
                    get: this.createGetter(prop),
                    set: this.createSetter(prop),
                    enumerable: true,
                    configurable: true,
                    originalDescriptor,
                    isModified: true,
                    modifiedAt: new Date(),
                    isIntercepted: true
                };
                this.propertyDescriptors.set(prop, trackedDescriptor);
            }
        } catch (error) {
            // Some properties might not be configurable
            console.warn(`Failed to intercept property "${prop}":`, error);
        }
    }

    /**
     * Restore intercepted property
     * @param prop - Property name
     */
    private restoreProperty(prop: string): void {
        if (!this.interceptedProps.has(prop)) {
            return;
        }

        try {
            const originalDescriptor = this.originalDescriptors.get(prop);

            if (originalDescriptor) {
                // Restore original descriptor
                Object.defineProperty(window, prop, originalDescriptor);
            } else {
                // Delete property if it didn't exist originally
                delete (window as any)[prop];
            }

            this.interceptedProps.delete(prop);
            this.originalDescriptors.delete(prop);
            this.propertyDescriptors.delete(prop);
        } catch (error) {
            // Some properties might not be configurable
            console.warn(`Failed to restore property "${prop}":`, error);
        }
    }

    /**
     * Intercept common global properties
     */
    private interceptCommonProperties(): void {
        const commonProps = [
            'document',
            'location',
            'history',
            'navigator',
            'screen',
            'localStorage',
            'sessionStorage',
            'indexedDB',
            'crypto',
            'performance',
            'URL',
            'URLSearchParams',
            'Blob',
            'File',
            'FileReader',
            'FormData',
            'Headers',
            'Request',
            'Response',
            'WebSocket',
            'EventSource',
            'Worker',
            'SharedWorker',
            'ServiceWorker',
            'Notification',
            'geolocation'
        ];

        commonProps.forEach(prop => {
            if (prop in window) {
                this.interceptProperty(prop);
            }
        });
    }

    /**
     * Intercept all window properties
     */
    private interceptAllProperties(): void {
        const windowProps = Object.getOwnPropertyNames(window);

        windowProps.forEach(prop => {
            // Skip non-configurable properties and functions
            try {
                const descriptor = Object.getOwnPropertyDescriptor(window, prop);
                if (descriptor && descriptor.configurable !== false) {
                    this.interceptProperty(prop);
                }
            } catch (error) {
                // Skip properties that can't be accessed
            }
        });
    }

    /**
     * Track property access
     * @param prop - Property name
     * @param operation - Operation type
     * @param value - Property value (for set operations)
     */
    private trackPropertyAccess(prop: string, operation: 'get' | 'set' | 'delete', value?: any): void {
        // This could be extended to provide detailed access logs
        // For now, we just track modified properties
        if (operation === 'set') {
            this.modifiedProps.add(prop);
        }
    }

    /**
     * Take a snapshot of the current window state
     */
    private takeSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        this.windowSnapshot = {};
        this.modifiedProps.clear();
        this.addedProps.clear();

        // Store current state of intercepted properties
        for (const prop of this.interceptedProps) {
            try {
                if (prop in window) {
                    this.windowSnapshot[prop] = (window as any)[prop];
                }
            } catch (error) {
                // Some properties might not be accessible
                continue;
            }
        }
    }

    /**
     * Restore window state from snapshot
     */
    private restoreSnapshot(): void {
        if (!this.options.enableSnapshot) {
            return;
        }

        // Restore modified properties
        for (const prop of this.modifiedProps) {
            try {
                if (prop in this.windowSnapshot) {
                    this.fakeWindow[prop] = this.windowSnapshot[prop];
                } else {
                    delete this.fakeWindow[prop];
                }
            } catch (error) {
                // Some properties might not be configurable
                continue;
            }
        }

        // Remove added properties
        for (const prop of this.addedProps) {
            try {
                delete this.fakeWindow[prop];
            } catch (error) {
                // Some properties might not be configurable
                continue;
            }
        }

        // Clear tracking sets
        this.modifiedProps.clear();
        this.addedProps.clear();
    }

    /**
     * Activate the sandbox
     */
    active(): void {
        if (this.isActivated) {
            return;
        }

        // Take snapshot before activation
        this.takeSnapshot();

        // Intercept properties
        if (this.options.interceptAllProperties) {
            this.interceptAllProperties();
        } else {
            this.interceptCommonProperties();
        }

        this.isActivated = true;
    }

    /**
     * Deactivate the sandbox
     */
    inactive(): void {
        if (!this.isActivated) {
            return;
        }

        // Restore snapshot on deactivation
        this.restoreSnapshot();

        // Restore all intercepted properties
        const propsToRestore = Array.from(this.interceptedProps);
        propsToRestore.forEach(prop => this.restoreProperty(prop));

        this.isActivated = false;
    }

    /**
     * Execute script in the sandbox
     * @param code - JavaScript code to execute
     * @returns Execution result
     */
    execScript(code: string): any {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            // Create a function with the fake window as context
            // We use 'with' statement to provide the sandbox context
            const func = new Function('window', `
                with (window) {
                    return (function() {
                        ${code}
                    })();
                }
            `);

            return func(this.fakeWindow);
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Script execution failed: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Check if the sandbox is active
     * @returns True if active, false otherwise
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Get sandbox statistics
     * @returns Sandbox statistics
     */
    getStats(): {
        modifiedProps: string[];
        addedProps: string[];
        interceptedProps: string[];
        trackedDescriptors: number;
        snapshotSize: number;
    } {
        return {
            modifiedProps: Array.from(this.modifiedProps),
            addedProps: Array.from(this.addedProps),
            interceptedProps: Array.from(this.interceptedProps),
            trackedDescriptors: this.propertyDescriptors.size,
            snapshotSize: Object.keys(this.windowSnapshot).length
        };
    }

    /**
     * Get fake window object
     * @returns Fake window object
     */
    getFakeWindow(): Record<string, any> {
        return this.fakeWindow;
    }

    /**
     * Add property to intercept
     * @param prop - Property name
     */
    addPropertyToIntercept(prop: string): void {
        if (this.isActivated && prop in window) {
            this.interceptProperty(prop);
        }
    }

    /**
     * Remove property from interception
     * @param prop - Property name
     */
    removePropertyFromIntercept(prop: string): void {
        if (this.isActivated) {
            this.restoreProperty(prop);
        }
    }

    /**
     * Reset sandbox state
     */
    reset(): void {
        this.inactive();
        this.modifiedProps.clear();
        this.addedProps.clear();
        this.propertyDescriptors.clear();
        this.interceptedProps.clear();
        this.originalDescriptors.clear();
        this.windowSnapshot = {};
        this.initializeFakeWindow();
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        // Deactivate if active
        if (this.isActivated) {
            this.inactive();
        }

        // Clear all data
        this.reset();
    }
}