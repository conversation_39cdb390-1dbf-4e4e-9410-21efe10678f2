/**
 * NamespaceSandbox - Namespace-based sandbox implementation
 * @packageDocumentation
 */

import { ErrorCode, SandboxError } from '../errors';
import { Sandbox, SANDBOX_TYPES, SandboxOptions } from '../types';

/**
 * Namespace sandbox options
 */
export interface NamespaceSandboxOptions extends Omit<SandboxOptions, 'type'> {
    /**
     * Type of sandbox (optional, defaults to namespace)
     */
    type?: 'namespace';

    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Namespace prefix for variables
     */
    namespacePrefix?: string;

    /**
     * Whether to enable automatic prefixing
     */
    enableAutoPrefixing?: boolean;

    /**
     * Whether to enable conflict detection
     */
    enableConflictDetection?: boolean;

    /**
     * Custom variable patterns to prefix
     */
    variablePatterns?: RegExp[];

    /**
     * Variables to exclude from prefixing
     */
    excludeFromPrefixing?: string[];

    /**
     * Whether to preserve original variable names
     */
    preserveOriginalNames?: boolean;
}

/**
 * Variable information
 */
interface VariableInfo {
    originalName: string;
    namespacedName: string;
    value: any;
    isGlobal: boolean;
    isConflicted: boolean;
    createdAt: Date;
}

/**
 * NamespaceSandbox - Namespace-based sandbox implementation
 * 
 * This sandbox uses namespace prefixing to isolate variables and prevent
 * conflicts between different micro-frontend applications.
 */
export class NamespaceSandbox implements Sandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = SANDBOX_TYPES.NAMESPACE;

    /**
     * Sandbox options
     */
    private options: NamespaceSandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Namespace prefix
     */
    private namespacePrefix: string;

    /**
     * Namespaced variables
     */
    private namespacedVariables: Map<string, VariableInfo> = new Map();

    /**
     * Original global variables backup
     */
    private originalGlobals: Map<string, any> = new Map();

    /**
     * Conflict registry
     */
    private conflicts: Map<string, string[]> = new Map();

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Exclude from prefixing set
     */
    private excludeFromPrefixingSet: Set<string>;

    /**
     * Variable patterns for prefixing
     */
    private variablePatterns: RegExp[];

    /**
     * Creates a new NamespaceSandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: NamespaceSandboxOptions) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            enableAutoPrefixing: true,
            enableConflictDetection: true,
            preserveOriginalNames: false,
            allowList: [],
            denyList: [],
            excludeFromPrefixing: [],
            variablePatterns: [],
            ...options
        };

        // Generate namespace prefix
        this.namespacePrefix = this.options.namespacePrefix || this.generateNamespacePrefix();

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);
        this.excludeFromPrefixingSet = new Set(this.options.excludeFromPrefixing || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Add default exclude patterns
        this.addDefaultExcludePatterns();

        // Set up variable patterns
        this.variablePatterns = [
            ...this.options.variablePatterns || [],
            ...this.getDefaultVariablePatterns()
        ];
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI',
            'URL',
            'URLSearchParams',
            'Blob',
            'File',
            'FileReader',
            'addEventListener',
            'removeEventListener',
            'dispatchEvent'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Add default exclude patterns
     */
    private addDefaultExcludePatterns(): void {
        const defaultExclude = [
            'window',
            'document',
            'location',
            'history',
            'navigator',
            'screen',
            'localStorage',
            'sessionStorage',
            'indexedDB',
            'crypto',
            'performance',
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame'
        ];

        defaultExclude.forEach(prop => this.excludeFromPrefixingSet.add(prop));
    }

    /**
     * Get default variable patterns
     * @returns Default variable patterns
     */
    private getDefaultVariablePatterns(): RegExp[] {
        return [
            /^[a-zA-Z_$][a-zA-Z0-9_$]*$/,  // Valid JavaScript identifiers
            /^[A-Z][A-Z0-9_]*$/,           // Constants
            /^[a-z][a-zA-Z0-9]*$/          // camelCase variables
        ];
    }

    /**
     * Generate namespace prefix
     * @returns Generated namespace prefix
     */
    private generateNamespacePrefix(): string {
        const sanitizedName = this.name.replace(/[^a-zA-Z0-9]/g, '_');
        const timestamp = Date.now().toString(36);
        return `__${sanitizedName}_${timestamp}__`;
    }

    /**
     * Check if variable should be prefixed
     * @param varName - Variable name
     * @returns True if should be prefixed
     */
    private shouldPrefixVariable(varName: string): boolean {
        // Skip if in exclude list
        if (this.excludeFromPrefixingSet.has(varName)) {
            return false;
        }

        // Skip if in allow list and strict isolation is disabled
        if (!this.options.strictIsolation && this.allowListSet.has(varName)) {
            return false;
        }

        // Skip if in deny list
        if (this.denyListSet.has(varName)) {
            return false;
        }

        // Check against variable patterns
        if (this.variablePatterns.length > 0) {
            return this.variablePatterns.some(pattern => pattern.test(varName));
        }

        return true;
    }

    /**
     * Create namespaced variable name
     * @param varName - Original variable name
     * @returns Namespaced variable name
     */
    private createNamespacedName(varName: string): string {
        return `${this.namespacePrefix}${varName}`;
    }

    /**
     * Extract original variable name from namespaced name
     * @param namespacedName - Namespaced variable name
     * @returns Original variable name
     */
    private extractOriginalName(namespacedName: string): string {
        if (namespacedName.startsWith(this.namespacePrefix)) {
            return namespacedName.substring(this.namespacePrefix.length);
        }
        return namespacedName;
    }

    /**
     * Check for variable conflicts
     * @param varName - Variable name
     * @returns True if conflict exists
     */
    private checkConflict(varName: string): boolean {
        if (!this.options.enableConflictDetection) {
            return false;
        }

        const namespacedName = this.createNamespacedName(varName);

        // Check if variable already exists in global scope
        if (namespacedName in window) {
            return true;
        }

        // Check if variable exists in other namespaces
        const existingVar = this.namespacedVariables.get(varName);
        if (existingVar && existingVar.namespacedName !== namespacedName) {
            return true;
        }

        return false;
    }

    /**
     * Register variable conflict
     * @param varName - Variable name
     * @param conflictSource - Source of conflict
     */
    private registerConflict(varName: string, conflictSource: string): void {
        if (!this.conflicts.has(varName)) {
            this.conflicts.set(varName, []);
        }
        this.conflicts.get(varName)!.push(conflictSource);
    }

    /**
     * Transform code to use namespaced variables
     * @param code - Original code
     * @returns Transformed code
     */
    private transformCode(code: string): string {
        if (!this.options.enableAutoPrefixing) {
            return code;
        }

        let transformedCode = code;

        // Find variable declarations and assignments
        const variablePatterns = [
            /\bvar\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
            /\blet\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
            /\bconst\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
            /\bfunction\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
            /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g
        ];

        variablePatterns.forEach(pattern => {
            transformedCode = transformedCode.replace(pattern, (match, varName) => {
                if (this.shouldPrefixVariable(varName)) {
                    const namespacedName = this.createNamespacedName(varName);

                    // Register variable
                    this.registerVariable(varName, namespacedName, undefined);

                    return match.replace(varName, namespacedName);
                }
                return match;
            });
        });

        // Transform variable references
        const referencePattern = /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g;
        transformedCode = transformedCode.replace(referencePattern, (match, varName) => {
            // Skip if it's a property access or method call
            const beforeMatch = transformedCode.substring(0, transformedCode.indexOf(match));
            const afterMatch = transformedCode.substring(transformedCode.indexOf(match) + match.length);

            if (beforeMatch.endsWith('.') || afterMatch.startsWith('(')) {
                return match;
            }

            // Check if we have a namespaced version of this variable
            const variableInfo = this.namespacedVariables.get(varName);
            if (variableInfo) {
                return variableInfo.namespacedName;
            }

            return match;
        });

        return transformedCode;
    }

    /**
     * Register a variable
     * @param originalName - Original variable name
     * @param namespacedName - Namespaced variable name
     * @param value - Variable value
     */
    private registerVariable(originalName: string, namespacedName: string, value: any): void {
        const isConflicted = this.checkConflict(originalName);

        if (isConflicted) {
            this.registerConflict(originalName, this.name);
        }

        const variableInfo: VariableInfo = {
            originalName,
            namespacedName,
            value,
            isGlobal: namespacedName in window,
            isConflicted,
            createdAt: new Date()
        };

        this.namespacedVariables.set(originalName, variableInfo);

        // Store original global value if it exists
        if (namespacedName in window && !this.originalGlobals.has(namespacedName)) {
            this.originalGlobals.set(namespacedName, (window as any)[namespacedName]);
        }
    }

    /**
     * Set variable value
     * @param varName - Variable name
     * @param value - Variable value
     */
    private setVariable(varName: string, value: any): void {
        if (this.shouldPrefixVariable(varName)) {
            const namespacedName = this.createNamespacedName(varName);
            this.registerVariable(varName, namespacedName, value);
            (window as any)[namespacedName] = value;
        } else {
            (window as any)[varName] = value;
        }
    }

    /**
     * Get variable value
     * @param varName - Variable name
     * @returns Variable value
     */
    private getVariable(varName: string): any {
        const variableInfo = this.namespacedVariables.get(varName);
        if (variableInfo) {
            return (window as any)[variableInfo.namespacedName];
        }
        return (window as any)[varName];
    }

    /**
     * Clean up namespaced variables
     */
    private cleanupVariables(): void {
        for (const [originalName, variableInfo] of this.namespacedVariables.entries()) {
            const namespacedName = variableInfo.namespacedName;

            // Restore original value if it existed
            if (this.originalGlobals.has(namespacedName)) {
                (window as any)[namespacedName] = this.originalGlobals.get(namespacedName);
            } else {
                // Delete the namespaced variable
                try {
                    delete (window as any)[namespacedName];
                } catch (error) {
                    // Some properties might not be configurable
                }
            }
        }

        // Clear registries
        this.namespacedVariables.clear();
        this.originalGlobals.clear();
        this.conflicts.clear();
    }

    /**
     * Activate the sandbox
     */
    active(): void {
        if (this.isActivated) {
            return;
        }

        this.isActivated = true;
    }

    /**
     * Deactivate the sandbox
     */
    inactive(): void {
        if (!this.isActivated) {
            return;
        }

        // Clean up namespaced variables
        this.cleanupVariables();

        this.isActivated = false;
    }

    /**
     * Execute script in the sandbox
     * @param code - JavaScript code to execute
     * @returns Execution result
     */
    execScript(code: string): any {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            // Transform code to use namespaced variables
            const transformedCode = this.transformCode(code);

            // Create execution context
            const func = new Function(`
                return (function() {
                    ${transformedCode}
                })();
            `);

            // Execute the transformed code
            const result = func();

            return result;
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Script execution failed: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Check if the sandbox is active
     * @returns True if active, false otherwise
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Get variable by original name
     * @param varName - Original variable name
     * @returns Variable value
     */
    getVariableByName(varName: string): any {
        return this.getVariable(varName);
    }

    /**
     * Set variable by original name
     * @param varName - Original variable name
     * @param value - Variable value
     */
    setVariableByName(varName: string, value: any): void {
        this.setVariable(varName, value);
    }

    /**
     * Get all namespaced variables
     * @returns Map of namespaced variables
     */
    getNamespacedVariables(): Map<string, VariableInfo> {
        return new Map(this.namespacedVariables);
    }

    /**
     * Get conflicts
     * @returns Map of conflicts
     */
    getConflicts(): Map<string, string[]> {
        return new Map(this.conflicts);
    }

    /**
     * Get namespace prefix
     * @returns Namespace prefix
     */
    getNamespacePrefix(): string {
        return this.namespacePrefix;
    }

    /**
     * Check if variable exists in namespace
     * @param varName - Variable name
     * @returns True if exists
     */
    hasVariable(varName: string): boolean {
        return this.namespacedVariables.has(varName);
    }

    /**
     * Remove variable from namespace
     * @param varName - Variable name
     */
    removeVariable(varName: string): void {
        const variableInfo = this.namespacedVariables.get(varName);
        if (variableInfo) {
            const namespacedName = variableInfo.namespacedName;

            // Restore original value if it existed
            if (this.originalGlobals.has(namespacedName)) {
                (window as any)[namespacedName] = this.originalGlobals.get(namespacedName);
                this.originalGlobals.delete(namespacedName);
            } else {
                // Delete the namespaced variable
                try {
                    delete (window as any)[namespacedName];
                } catch (error) {
                    // Some properties might not be configurable
                }
            }

            this.namespacedVariables.delete(varName);
        }
    }

    /**
     * Get sandbox statistics
     * @returns Sandbox statistics
     */
    getStats(): {
        namespacePrefix: string;
        namespacedVariables: number;
        conflicts: number;
        originalGlobalsBackup: number;
        isAutoPrefixingEnabled: boolean;
        isConflictDetectionEnabled: boolean;
    } {
        return {
            namespacePrefix: this.namespacePrefix,
            namespacedVariables: this.namespacedVariables.size,
            conflicts: this.conflicts.size,
            originalGlobalsBackup: this.originalGlobals.size,
            isAutoPrefixingEnabled: this.options.enableAutoPrefixing || false,
            isConflictDetectionEnabled: this.options.enableConflictDetection || false
        };
    }

    /**
     * Reset sandbox state
     */
    reset(): void {
        this.inactive();
        this.namespacedVariables.clear();
        this.originalGlobals.clear();
        this.conflicts.clear();
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        // Deactivate if active
        if (this.isActivated) {
            this.inactive();
        }

        // Clear all data
        this.reset();
    }
}