/**
 * FederationSandbox - Module Federation-based sandbox implementation
 * @packageDocumentation
 */

import { ErrorCode, SandboxError } from '../errors';
import { Sandbox, SANDBOX_TYPES, SandboxOptions } from '../types';

/**
 * Federation sandbox options
 */
export interface FederationSandboxOptions extends Omit<SandboxOptions, 'type'> {
    /**
     * Type of sandbox (optional, defaults to federation)
     */
    type?: 'federation';

    /**
     * Whether to use strict isolation
     */
    strictIsolation?: boolean;

    /**
     * List of global variables to allow
     */
    allowList?: string[];

    /**
     * List of global variables to deny
     */
    denyList?: string[];

    /**
     * Remote entries configuration
     */
    remotes?: Record<string, string>;

    /**
     * Shared modules configuration
     */
    shared?: Record<string, SharedModuleConfig>;

    /**
     * Module loading timeout in milliseconds
     */
    moduleTimeout?: number;

    /**
     * Whether to enable module caching
     */
    enableCaching?: boolean;

    /**
     * Whether to enable version checking
     */
    enableVersionCheck?: boolean;

    /**
     * Fallback modules for failed loads
     */
    fallbacks?: Record<string, any>;

    /**
     * Custom module resolver
     */
    moduleResolver?: (moduleName: string) => Promise<any>;
}

/**
 * Shared module configuration
 */
export interface SharedModuleConfig {
    /**
     * Module version
     */
    version?: string;

    /**
     * Whether module is singleton
     */
    singleton?: boolean;

    /**
     * Whether to use strict version matching
     */
    strictVersion?: boolean;

    /**
     * Required version range
     */
    requiredVersion?: string;

    /**
     * Module factory function
     */
    factory?: () => any;

    /**
     * Whether module is eager loaded
     */
    eager?: boolean;
}

/**
 * Remote entry information
 */
interface RemoteEntry {
    name: string;
    url: string;
    isLoaded: boolean;
    loadPromise?: Promise<any>;
    container?: any;
    modules?: Map<string, any>;
    error?: Error;
}

/**
 * Module cache entry
 */
interface ModuleCacheEntry {
    module: any;
    version: string;
    loadedAt: Date;
    usageCount: number;
}

/**
 * FederationSandbox - Module Federation-based sandbox implementation
 * 
 * This sandbox uses Module Federation concepts to load and manage
 * remote modules with proper isolation and sharing capabilities.
 */
export class FederationSandbox implements Sandbox {
    /**
     * Sandbox name
     */
    name: string;

    /**
     * Sandbox type
     */
    type: string = SANDBOX_TYPES.FEDERATION;

    /**
     * Sandbox options
     */
    private options: FederationSandboxOptions;

    /**
     * Whether the sandbox is active
     */
    private isActivated: boolean = false;

    /**
     * Remote entries registry
     */
    private remoteEntries: Map<string, RemoteEntry> = new Map();

    /**
     * Shared modules registry
     */
    private sharedModules: Map<string, SharedModuleConfig> = new Map();

    /**
     * Module cache
     */
    private moduleCache: Map<string, ModuleCacheEntry> = new Map();

    /**
     * Loading promises
     */
    private loadingPromises: Map<string, Promise<any>> = new Map();

    /**
     * Allow list set
     */
    private allowListSet: Set<string>;

    /**
     * Deny list set
     */
    private denyListSet: Set<string>;

    /**
     * Module scope
     */
    private moduleScope: Record<string, any> = {};

    /**
     * Creates a new FederationSandbox
     * @param name - Sandbox name
     * @param options - Sandbox options
     */
    constructor(name: string, options: FederationSandboxOptions) {
        this.name = name;
        this.options = {
            strictIsolation: true,
            moduleTimeout: 30000,
            enableCaching: true,
            enableVersionCheck: true,
            remotes: {},
            shared: {},
            fallbacks: {},
            allowList: [],
            denyList: [],
            ...options
        };

        // Convert arrays to sets for faster lookup
        this.allowListSet = new Set(this.options.allowList || []);
        this.denyListSet = new Set(this.options.denyList || []);

        // Add default allowed properties
        this.addDefaultAllowedProperties();

        // Initialize shared modules
        this.initializeSharedModules();

        // Initialize remote entries
        this.initializeRemoteEntries();
    }

    /**
     * Add default allowed properties
     */
    private addDefaultAllowedProperties(): void {
        const defaultAllowed = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'fetch',
            'XMLHttpRequest',
            'Promise',
            'Array',
            'Object',
            'String',
            'Number',
            'Boolean',
            'Date',
            'RegExp',
            'JSON',
            'Math',
            'parseInt',
            'parseFloat',
            'isNaN',
            'isFinite',
            'encodeURIComponent',
            'decodeURIComponent',
            'encodeURI',
            'decodeURI',
            'URL',
            'URLSearchParams',
            'Blob',
            'File',
            'FileReader',
            'addEventListener',
            'removeEventListener',
            'dispatchEvent',
            'import',
            'require'
        ];

        defaultAllowed.forEach(prop => this.allowListSet.add(prop));
    }

    /**
     * Initialize shared modules
     */
    private initializeSharedModules(): void {
        const shared = this.options.shared || {};

        for (const [moduleName, config] of Object.entries(shared)) {
            this.sharedModules.set(moduleName, {
                version: '1.0.0',
                singleton: false,
                strictVersion: false,
                eager: false,
                ...config
            });
        }
    }

    /**
     * Initialize remote entries
     */
    private initializeRemoteEntries(): void {
        const remotes = this.options.remotes || {};

        for (const [remoteName, remoteUrl] of Object.entries(remotes)) {
            this.remoteEntries.set(remoteName, {
                name: remoteName,
                url: remoteUrl,
                isLoaded: false,
                modules: new Map()
            });
        }
    }

    /**
     * Load remote entry
     * @param remoteName - Remote name
     * @returns Promise that resolves to remote container
     */
    private async loadRemoteEntry(remoteName: string): Promise<any> {
        const remoteEntry = this.remoteEntries.get(remoteName);
        if (!remoteEntry) {
            throw new SandboxError(
                this.name,
                `Remote entry not found: ${remoteName}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        // Return cached container if already loaded
        if (remoteEntry.isLoaded && remoteEntry.container) {
            return remoteEntry.container;
        }

        // Return existing loading promise if in progress
        if (remoteEntry.loadPromise) {
            return remoteEntry.loadPromise;
        }

        // Start loading
        remoteEntry.loadPromise = this.doLoadRemoteEntry(remoteEntry);

        try {
            const container = await remoteEntry.loadPromise;
            remoteEntry.container = container;
            remoteEntry.isLoaded = true;
            return container;
        } catch (error) {
            remoteEntry.error = error as Error;
            throw error;
        } finally {
            remoteEntry.loadPromise = undefined;
        }
    }

    /**
     * Actually load remote entry
     * @param remoteEntry - Remote entry info
     * @returns Promise that resolves to container
     */
    private async doLoadRemoteEntry(remoteEntry: RemoteEntry): Promise<any> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`Remote entry load timeout: ${remoteEntry.name}`));
            }, this.options.moduleTimeout);

            // Simulate loading remote entry
            // In a real implementation, this would load the actual remote module
            const script = document.createElement('script');
            script.src = remoteEntry.url;
            script.type = 'text/javascript';

            script.onload = () => {
                clearTimeout(timeout);

                // Simulate getting container from global scope
                const globalName = `__federation_${remoteEntry.name}__`;
                const container = (window as any)[globalName];

                if (container) {
                    resolve(container);
                } else {
                    // Create mock container for testing
                    const mockContainer = {
                        init: () => Promise.resolve(),
                        get: (moduleName: string) => Promise.resolve(() => ({ default: {} }))
                    };
                    resolve(mockContainer);
                }
            };

            script.onerror = () => {
                clearTimeout(timeout);
                reject(new Error(`Failed to load remote entry: ${remoteEntry.url}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Load module from remote or shared
     * @param moduleName - Module name
     * @param remoteName - Optional remote name
     * @returns Promise that resolves to module
     */
    private async loadModule(moduleName: string, remoteName?: string): Promise<any> {
        const cacheKey = remoteName ? `${remoteName}/${moduleName}` : moduleName;

        // Check cache first
        if (this.options.enableCaching && this.moduleCache.has(cacheKey)) {
            const cached = this.moduleCache.get(cacheKey)!;
            cached.usageCount++;
            return cached.module;
        }

        // Check if loading is already in progress
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }

        // Start loading
        const loadPromise = this.doLoadModule(moduleName, remoteName);
        this.loadingPromises.set(cacheKey, loadPromise);

        try {
            const module = await loadPromise;

            // Cache the module
            if (this.options.enableCaching) {
                this.moduleCache.set(cacheKey, {
                    module,
                    version: '1.0.0', // Would be determined from actual module
                    loadedAt: new Date(),
                    usageCount: 1
                });
            }

            return module;
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }

    /**
     * Actually load module
     * @param moduleName - Module name
     * @param remoteName - Optional remote name
     * @returns Promise that resolves to module
     */
    private async doLoadModule(moduleName: string, remoteName?: string): Promise<any> {
        // Check shared modules first
        if (this.sharedModules.has(moduleName)) {
            const sharedConfig = this.sharedModules.get(moduleName)!;

            if (sharedConfig.factory) {
                return sharedConfig.factory();
            }

            // Try to get from global scope or fallback
            if (moduleName in this.moduleScope) {
                return this.moduleScope[moduleName];
            }
        }

        // Load from remote if specified
        if (remoteName) {
            const container = await this.loadRemoteEntry(remoteName);

            if (container && container.get) {
                const moduleFactory = await container.get(moduleName);
                return moduleFactory();
            }
        }

        // Check fallbacks
        if (this.options.fallbacks && moduleName in this.options.fallbacks) {
            return this.options.fallbacks[moduleName];
        }

        // Use custom resolver if available
        if (this.options.moduleResolver) {
            return this.options.moduleResolver(moduleName);
        }

        throw new SandboxError(
            this.name,
            `Module not found: ${moduleName}`,
            ErrorCode.SANDBOX_EXECUTION_FAILED
        );
    }

    /**
     * Check version compatibility
     * @param moduleName - Module name
     * @param requiredVersion - Required version
     * @param actualVersion - Actual version
     * @returns True if compatible
     */
    private checkVersionCompatibility(
        moduleName: string,
        requiredVersion: string,
        actualVersion: string
    ): boolean {
        if (!this.options.enableVersionCheck) {
            return true;
        }

        const sharedConfig = this.sharedModules.get(moduleName);
        if (sharedConfig?.strictVersion) {
            return requiredVersion === actualVersion;
        }

        // Simple version check (in real implementation, use semver)
        return true;
    }

    /**
     * Create module import function
     * @returns Import function
     */
    private createImportFunction(): (moduleName: string, remoteName?: string) => Promise<any> {
        return async (moduleName: string, remoteName?: string) => {
            try {
                return await this.loadModule(moduleName, remoteName);
            } catch (error) {
                throw new SandboxError(
                    this.name,
                    `Failed to import module "${moduleName}": ${(error as Error).message}`,
                    ErrorCode.SANDBOX_EXECUTION_FAILED,
                    error
                );
            }
        };
    }

    /**
     * Create require function
     * @returns Require function
     */
    private createRequireFunction(): (moduleName: string) => any {
        return (moduleName: string) => {
            // Synchronous require - check cache and shared modules only
            const cached = this.moduleCache.get(moduleName);
            if (cached) {
                cached.usageCount++;
                return cached.module;
            }

            if (this.sharedModules.has(moduleName)) {
                const sharedConfig = this.sharedModules.get(moduleName)!;
                if (sharedConfig.factory) {
                    return sharedConfig.factory();
                }
            }

            if (moduleName in this.moduleScope) {
                return this.moduleScope[moduleName];
            }

            throw new SandboxError(
                this.name,
                `Module not found (synchronous): ${moduleName}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        };
    }

    /**
     * Activate the sandbox
     */
    active(): void {
        if (this.isActivated) {
            return;
        }

        // Set up module scope
        this.moduleScope = {
            import: this.createImportFunction(),
            require: this.createRequireFunction(),
            __webpack_require__: this.createRequireFunction(), // Webpack compatibility
            __federation_import__: this.createImportFunction()
        };

        this.isActivated = true;
    }

    /**
     * Deactivate the sandbox
     */
    inactive(): void {
        if (!this.isActivated) {
            return;
        }

        // Clear loading promises
        this.loadingPromises.clear();

        // Clear module cache if not persistent
        if (!this.options.enableCaching) {
            this.moduleCache.clear();
        }

        // Reset remote entries
        for (const remoteEntry of this.remoteEntries.values()) {
            remoteEntry.isLoaded = false;
            remoteEntry.container = undefined;
            remoteEntry.loadPromise = undefined;
            remoteEntry.error = undefined;
        }

        this.moduleScope = {};
        this.isActivated = false;
    }

    /**
     * Execute script in the sandbox
     * @param code - JavaScript code to execute
     * @returns Execution result
     */
    execScript(code: string): any {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot execute script: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        try {
            // Create execution context with module scope
            const func = new Function('moduleScope', `
                with (moduleScope) {
                    return (function() {
                        ${code}
                    })();
                }
            `);

            return func(this.moduleScope);
        } catch (error) {
            throw new SandboxError(
                this.name,
                `Script execution failed: ${(error as Error).message}`,
                ErrorCode.SANDBOX_EXECUTION_FAILED,
                error
            );
        }
    }

    /**
     * Import module
     * @param moduleName - Module name
     * @param remoteName - Optional remote name
     * @returns Promise that resolves to module
     */
    async importModule(moduleName: string, remoteName?: string): Promise<any> {
        if (!this.isActivated) {
            throw new SandboxError(
                this.name,
                'Cannot import module: Sandbox is not active',
                ErrorCode.SANDBOX_EXECUTION_FAILED
            );
        }

        return this.loadModule(moduleName, remoteName);
    }

    /**
     * Register shared module
     * @param moduleName - Module name
     * @param config - Module configuration
     */
    registerSharedModule(moduleName: string, config: SharedModuleConfig): void {
        this.sharedModules.set(moduleName, config);
    }

    /**
     * Register remote entry
     * @param remoteName - Remote name
     * @param remoteUrl - Remote URL
     */
    registerRemoteEntry(remoteName: string, remoteUrl: string): void {
        this.remoteEntries.set(remoteName, {
            name: remoteName,
            url: remoteUrl,
            isLoaded: false,
            modules: new Map()
        });
    }

    /**
     * Check if the sandbox is active
     * @returns True if active, false otherwise
     */
    isActive(): boolean {
        return this.isActivated;
    }

    /**
     * Get shared modules
     * @returns Map of shared modules
     */
    getSharedModules(): Map<string, SharedModuleConfig> {
        return new Map(this.sharedModules);
    }

    /**
     * Get remote entries
     * @returns Map of remote entries
     */
    getRemoteEntries(): Map<string, RemoteEntry> {
        return new Map(this.remoteEntries);
    }

    /**
     * Get module cache
     * @returns Map of cached modules
     */
    getModuleCache(): Map<string, ModuleCacheEntry> {
        return this.moduleCache;
    }

    /**
     * Clear module cache
     */
    clearModuleCache(): void {
        this.moduleCache.clear();
    }

    /**
     * Get sandbox statistics
     * @returns Sandbox statistics
     */
    getStats(): {
        remoteEntries: number;
        sharedModules: number;
        cachedModules: number;
        loadingPromises: number;
        isActivated: boolean;
        isCachingEnabled: boolean;
        isVersionCheckEnabled: boolean;
    } {
        return {
            remoteEntries: this.remoteEntries.size,
            sharedModules: this.sharedModules.size,
            cachedModules: this.moduleCache.size,
            loadingPromises: this.loadingPromises.size,
            isActivated: this.isActivated,
            isCachingEnabled: this.options.enableCaching || false,
            isVersionCheckEnabled: this.options.enableVersionCheck || false
        };
    }

    /**
     * Reset sandbox state
     */
    reset(): void {
        this.inactive();
        this.moduleCache.clear();
        this.loadingPromises.clear();

        // Reset remote entries
        for (const remoteEntry of this.remoteEntries.values()) {
            remoteEntry.isLoaded = false;
            remoteEntry.container = undefined;
            remoteEntry.loadPromise = undefined;
            remoteEntry.error = undefined;
        }
    }

    /**
     * Destroy the sandbox
     */
    destroy(): void {
        // Deactivate if active
        if (this.isActivated) {
            this.inactive();
        }

        // Clear all data
        this.reset();
        this.remoteEntries.clear();
        this.sharedModules.clear();
    }
}