/**
 * @fileoverview 生命周期管理器 - 统一的应用生命周期管理
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { APP_STATUS } from './constants';
import { ERROR_CODES, MicroCoreError } from './errors';
import { EventBus } from './event-bus';
import type { AppInstance, LifecycleHooks } from './types';
import { logger } from './utils';

/**
 * 生命周期管理器
 * 负责管理微前端应用的完整生命周期
 */
export class LifecycleManager {
    private eventBus: EventBus;
    private logger = logger.createLogger('[LifecycleManager]');
    private hooks: LifecycleHooks = {};

    constructor(eventBus: EventBus) {
        this.eventBus = eventBus;
    }

    /**
     * 注册生命周期钩子
     */
    registerHook(hookName: keyof LifecycleHooks, hook: Function): void {
        if (!this.hooks[hookName]) {
            this.hooks[hookName] = [];
        }
        this.hooks[hookName]!.push(hook);
    }

    /**
     * 执行生命周期钩子
     */
    private async executeHooks(hookName: keyof LifecycleHooks, app: AppInstance): Promise<void> {
        const hooks = this.hooks[hookName];
        if (!hooks || hooks.length === 0) {
            return;
        }

        for (const hook of hooks) {
            try {
                await hook(app);
            } catch (error) {
                this.logger.error(`执行 ${hookName} 钩子失败:`, error);
                throw new MicroCoreError(
                    ERROR_CODES.LIFECYCLE_ERROR,
                    `执行 ${hookName} 钩子失败: ${error instanceof Error ? error.message : String(error)}`,
                    { appName: app.name, hookName }
                );
            }
        }
    }

    /**
     * 引导应用
     */
    async bootstrap(app: AppInstance): Promise<void> {
        this.logger.debug(`引导应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeBootstrap', app);

            // 执行应用的 bootstrap 生命周期
            if (app.lifecycles?.bootstrap) {
                await app.lifecycles.bootstrap(app.config.props || {});
            }

            // 更新状态
            app.status = APP_STATUS.NOT_MOUNTED;

            // 执行后置钩子
            await this.executeHooks('afterBootstrap', app);

            // 发送事件
            this.eventBus.emit('app:bootstrapped', app);

            this.logger.info(`应用 ${app.name} 引导完成`);
        } catch (error) {
            app.status = APP_STATUS.SKIP_BECAUSE_BROKEN;
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 引导失败:`, error);
            this.eventBus.emit('app:bootstrap-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_BOOTSTRAP_FAILED,
                `应用 ${app.name} 引导失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 挂载应用
     */
    async mount(app: AppInstance): Promise<void> {
        this.logger.debug(`挂载应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeMount', app);

            // 更新状态
            app.status = APP_STATUS.MOUNTING;

            // 执行应用的 mount 生命周期
            if (app.lifecycles?.mount) {
                await app.lifecycles.mount({
                    ...app.config.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新状态和时间戳
            app.status = APP_STATUS.MOUNTED;
            app.mountTime = Date.now();

            // 执行后置钩子
            await this.executeHooks('afterMount', app);

            // 发送事件
            this.eventBus.emit('app:mounted', app);

            this.logger.info(`应用 ${app.name} 挂载完成`);
        } catch (error) {
            app.status = APP_STATUS.SKIP_BECAUSE_BROKEN;
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 挂载失败:`, error);
            this.eventBus.emit('app:mount-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_MOUNT_FAILED,
                `应用 ${app.name} 挂载失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 卸载应用
     */
    async unmount(app: AppInstance): Promise<void> {
        this.logger.debug(`卸载应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUnmount', app);

            // 更新状态
            app.status = APP_STATUS.UNMOUNTING;

            // 执行应用的 unmount 生命周期
            if (app.lifecycles?.unmount) {
                await app.lifecycles.unmount({
                    ...app.config.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新状态和时间戳
            app.status = APP_STATUS.NOT_MOUNTED;
            app.unmountTime = Date.now();

            // 执行后置钩子
            await this.executeHooks('afterUnmount', app);

            // 发送事件
            this.eventBus.emit('app:unmounted', app);

            this.logger.info(`应用 ${app.name} 卸载完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 卸载失败:`, error);
            this.eventBus.emit('app:unmount-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_UNMOUNT_FAILED,
                `应用 ${app.name} 卸载失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 更新应用
     */
    async update(app: AppInstance, props: Record<string, any>): Promise<void> {
        this.logger.debug(`更新应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUpdate', app);

            // 更新状态
            app.status = APP_STATUS.UPDATING;

            // 执行应用的 update 生命周期
            if (app.lifecycles?.update) {
                await app.lifecycles.update({
                    ...app.config.props,
                    ...props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新配置
            app.config.props = { ...app.config.props, ...props };

            // 恢复状态
            app.status = APP_STATUS.MOUNTED;

            // 执行后置钩子
            await this.executeHooks('afterUpdate', app);

            // 发送事件
            this.eventBus.emit('app:updated', app);

            this.logger.info(`应用 ${app.name} 更新完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 更新失败:`, error);
            this.eventBus.emit('app:update-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_UPDATE_FAILED,
                `应用 ${app.name} 更新失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 卸载应用资源
     */
    async unload(app: AppInstance): Promise<void> {
        this.logger.debug(`卸载应用资源: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUnload', app);

            // 更新状态
            app.status = APP_STATUS.UNLOADING;

            // 执行应用的 unload 生命周期
            if (app.lifecycles?.unload) {
                await app.lifecycles.unload({
                    ...app.config.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 清理应用实例
            app.container = null;
            app.sandbox = null;
            app.lifecycles = null;
            app.status = APP_STATUS.NOT_LOADED;

            // 执行后置钩子
            await this.executeHooks('afterUnload', app);

            // 发送事件
            this.eventBus.emit('app:unloaded', app);

            this.logger.info(`应用 ${app.name} 资源卸载完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 资源卸载失败:`, error);
            this.eventBus.emit('app:unload-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 资源卸载失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 获取应用当前生命周期状态
     */
    getLifecycleState(app: AppInstance): {
        status: string;
        canBootstrap: boolean;
        canMount: boolean;
        canUnmount: boolean;
        canUpdate: boolean;
        canUnload: boolean;
    } {
        const status = app.status;

        return {
            status,
            canBootstrap: status === APP_STATUS.NOT_BOOTSTRAPPED,
            canMount: status === APP_STATUS.NOT_MOUNTED,
            canUnmount: status === APP_STATUS.MOUNTED,
            canUpdate: status === APP_STATUS.MOUNTED,
            canUnload: [APP_STATUS.NOT_MOUNTED, APP_STATUS.SKIP_BECAUSE_BROKEN].includes(status)
        };
    }

    /**
     * 清理所有钩子
     */
    clearHooks(): void {
        this.hooks = {};
        this.logger.debug('已清理所有生命周期钩子');
    }
}