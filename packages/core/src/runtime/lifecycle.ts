import type { AppInstance, AppLifecycles } from './types/index';
import { AppStatus } from './types/index';
import { createLogger } from './utils';

const logger = createLogger('[Lifecycle]');

/**
 * 将值转换为Promise
 */
function toPromise<T>(value: T | Promise<T>): Promise<T> {
    return Promise.resolve(value);
}

/**
 * 生命周期管理器
 * 负责管理微前端应用的生命周期
 */
export class LifecycleManager {
    private readonly lifecycles = new Map<string, AppLifecycles>();

    /**
     * 注册应用生命周期
     */
    registerLifecycles(name: string, lifecycles: AppLifecycles): void {
        this.lifecycles.set(name, lifecycles);
        logger.debug(`注册应用生命周期: ${name}`);
    }

    /**
     * 获取应用生命周期
     */
    getLifecycles(name: string): AppLifecycles | undefined {
        return this.lifecycles.get(name);
    }

    /**
     * 移除应用生命周期
     */
    removeLifecycles(name: string): void {
        this.lifecycles.delete(name);
        logger.debug(`移除应用生命周期: ${name}`);
    }

    /**
     * 启动应用
     */
    async bootstrap(app: AppInstance): Promise<void> {
        if (app.status !== AppStatus.NOT_BOOTSTRAPPED) {
            logger.warn(`应用 ${app.name} 已经启动过，跳过启动`);
            return;
        }

        app.status = AppStatus.BOOTSTRAPPING;

        try {
            const lifecycles = this.getLifecycles(app.name);
            if (lifecycles?.bootstrap) {
                await toPromise(lifecycles.bootstrap(app.props));
            }
            app.status = AppStatus.NOT_MOUNTED;
            logger.info(`应用 ${app.name} 启动成功`);
        } catch (error) {
            app.status = AppStatus.LOAD_ERROR;
            app.error = error as Error;
            logger.error(`应用 ${app.name} 启动失败:`, error);
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    async mount(app: AppInstance): Promise<void> {
        if (app.status !== AppStatus.NOT_MOUNTED) {
            logger.warn(`应用 ${app.name} 状态不正确，无法挂载`);
            return;
        }

        app.status = AppStatus.MOUNTING;

        try {
            const lifecycles = this.getLifecycles(app.name);
            if (lifecycles?.mount) {
                await toPromise(lifecycles.mount(app.props));
            }
            app.status = AppStatus.MOUNTED;
            logger.info(`应用 ${app.name} 挂载成功`);
        } catch (error) {
            app.status = AppStatus.LOAD_ERROR;
            app.error = error as Error;
            logger.error(`应用 ${app.name} 挂载失败:`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmount(app: AppInstance): Promise<void> {
        if (app.status !== AppStatus.MOUNTED) {
            logger.warn(`应用 ${app.name} 状态不正确，无法卸载`);
            return;
        }

        app.status = AppStatus.UNMOUNTING;

        try {
            const lifecycles = this.getLifecycles(app.name);
            if (lifecycles?.unmount) {
                await toPromise(lifecycles.unmount(app.props));
            }
            app.status = AppStatus.NOT_MOUNTED;
            logger.info(`应用 ${app.name} 卸载成功`);
        } catch (error) {
            app.status = AppStatus.LOAD_ERROR;
            app.error = error as Error;
            logger.error(`应用 ${app.name} 卸载失败:`, error);
            throw error;
        }
    }

    /**
     * 更新应用
     */
    async update(app: AppInstance): Promise<void> {
        if (app.status !== AppStatus.MOUNTED) {
            logger.warn(`应用 ${app.name} 状态不正确，无法更新`);
            return;
        }

        app.status = AppStatus.UPDATING;

        try {
            const lifecycles = this.getLifecycles(app.name);
            if (lifecycles?.update) {
                await toPromise(lifecycles.update(app.props));
            }
            app.status = AppStatus.MOUNTED;
            logger.info(`应用 ${app.name} 更新成功`);
        } catch (error) {
            app.status = AppStatus.LOAD_ERROR;
            app.error = error as Error;
            logger.error(`应用 ${app.name} 更新失败:`, error);
            throw error;
        }
    }

    /**
     * 清理所有生命周期
     */
    clear(): void {
        this.lifecycles.clear();
        logger.debug('清理所有应用生命周期');
    }
}