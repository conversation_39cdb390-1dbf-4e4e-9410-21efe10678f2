/**
 * 全局状态管理器
 * 负责管理微前端系统的全局状态
 */
export class GlobalState {
    private _state = new Map<string, any>()
    private _listeners = new Map<string, Array<(value: any, oldValue: any) => void>>()

    /**
     * 设置状态
     */
    set(key: string, value: any): void {
        const oldValue = this._state.get(key)
        this._state.set(key, value)

        // 通知监听器
        const listeners = this._listeners.get(key) || []
        listeners.forEach(listener => {
            try {
                listener(value, oldValue)
            } catch (error) {
                console.error(`[GlobalState] 状态监听器执行失败:`, error)
            }
        })
    }

    /**
     * 获取状态
     */
    get(key: string): any {
        return this._state.get(key)
    }

    /**
     * 检查状态是否存在
     */
    has(key: string): boolean {
        return this._state.has(key)
    }

    /**
     * 删除状态
     */
    delete(key: string): boolean {
        const result = this._state.delete(key)
        this._listeners.delete(key)
        return result
    }

    /**
     * 监听状态变化
     */
    watch(key: string, listener: (value: any, oldValue: any) => void): () => void {
        if (!this._listeners.has(key)) {
            this._listeners.set(key, [])
        }

        this._listeners.get(key)!.push(listener)

        // 返回取消监听的函数
        return () => {
            const listeners = this._listeners.get(key)
            if (listeners) {
                const index = listeners.indexOf(listener)
                if (index > -1) {
                    listeners.splice(index, 1)
                }
            }
        }
    }

    /**
     * 取消监听
     */
    unwatch(key: string, listener?: (value: any, oldValue: any) => void): void {
        if (!listener) {
            this._listeners.delete(key)
            return
        }

        const listeners = this._listeners.get(key)
        if (listeners) {
            const index = listeners.indexOf(listener)
            if (index > -1) {
                listeners.splice(index, 1)
            }
        }
    }

    /**
     * 获取所有状态
     */
    getAll(): Record<string, any> {
        const result: Record<string, any> = {}
        for (const [key, value] of this._state) {
            result[key] = value
        }
        return result
    }

    /**
     * 清空所有状态
     */
    clear(): void {
        this._state.clear()
        this._listeners.clear()
    }

    /**
     * 销毁全局状态管理器
     */
    destroy(): void {
        this.clear()
        console.log('[GlobalState] 全局状态管理器已销毁')
    }
}