/**
 * @fileoverview 错误处理器
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCoreError } from './errors';

// const logger = createLogger('[ErrorHandler]'); // 暂时注释未使用的logger

// 定义错误类型枚举
enum ERROR_TYPES {
    LOAD_ERROR = 'LOAD_ERROR',
    MOUNT_ERROR = 'MOUNT_ERROR',
    UNMOUNT_ERROR = 'UNMOUNT_ERROR',
    BOOTSTRAP_ERROR = 'BOOTSTRAP_ERROR',
    UPDATE_ERROR = 'UPDATE_ERROR',
    NETWORK_ERROR = 'NETWORK_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    PLUGIN_ERROR = 'PLUGIN_ERROR',
    SANDBOX_ERROR = 'SANDBOX_ERROR',
    PARSE_ERROR = 'PARSE_ERROR'
}

type ErrorType = keyof typeof ERROR_TYPES;

// 扩展的错误接口
interface ExtendedMicroCoreError extends MicroCoreError {
    type?: string;
    appName?: string;
}

// 格式化错误消息的简单实现
function formatErrorMessage(error: ExtendedMicroCoreError): string {
    return `[${error.code}] ${error.message}`;
}

/**
 * 错误统计信息
 */
interface ErrorStats {
    total: number;
    byType: Record<string, number>;
    byApp: Record<string, number>;
    recent: ExtendedMicroCoreError[];
}

/**
 * 错误处理器
 * 负责统一处理和记录系统错误
 */
export class ErrorHandler {
    private readonly customHandler?: (error: ExtendedMicroCoreError) => void;
    private readonly errors: ExtendedMicroCoreError[] = [];
    private readonly maxErrorHistory = 100;
    private readonly stats: ErrorStats = {
        total: 0,
        byType: {} as Record<string, number>,
        byApp: {},
        recent: []
    };

    constructor(customHandler?: (error: ExtendedMicroCoreError) => void) {
        this.customHandler = customHandler;
        this.initializeStats();
    }

    /**
     * 处理错误
     */
    handleError(error: ExtendedMicroCoreError): void {
        try {
            // 记录错误
            this.recordError(error);

            // 更新统计信息
            this.updateStats(error);

            // 格式化错误消息
            const formattedMessage = formatErrorMessage(error);

            // 根据错误类型选择日志级别
            this.logError(error, formattedMessage);

            // 调用自定义错误处理器
            if (this.customHandler) {
                this.customHandler(error);
            }

            // 在开发模式下，可以考虑抛出错误以便调试
            if (process.env.NODE_ENV === 'development' && this.shouldThrowInDev(error)) {
                console.error('[ErrorHandler] 开发模式下重新抛出错误:', error);
                // 注意：这里不直接抛出，而是记录，避免影响应用运行
            }
        } catch (handlerError) {
            // 错误处理器本身出错，使用最基本的错误处理
            console.error('[ErrorHandler] 错误处理器内部错误:', handlerError);
            console.error('[ErrorHandler] 原始错误:', error);
        }
    }

    /**
     * 获取错误统计信息
     */
    getStats(): ErrorStats {
        return {
            ...this.stats,
            byType: { ...this.stats.byType },
            byApp: { ...this.stats.byApp },
            recent: [...this.stats.recent]
        };
    }

    /**
     * 获取所有错误历史
     */
    getErrorHistory(): ExtendedMicroCoreError[] {
        return [...this.errors];
    }

    /**
     * 获取特定类型的错误
     */
    getErrorsByType(type: string): ExtendedMicroCoreError[] {
        return this.errors.filter(error => error.type === type);
    }

    /**
     * 获取特定应用的错误
     */
    getErrorsByApp(appName: string): ExtendedMicroCoreError[] {
        return this.errors.filter(error => error.appName === appName);
    }

    /**
     * 获取最近的错误
     */
    getRecentErrors(count = 10): ExtendedMicroCoreError[] {
        return this.stats.recent.slice(-count);
    }

    /**
     * 清空错误历史
     */
    clearErrorHistory(): void {
        this.errors.length = 0;
        this.stats.recent.length = 0;
        this.stats.total = 0;
        this.initializeStats();
        console.log('[ErrorHandler] 错误历史已清空');
    }

    /**
     * 检查是否有特定类型的错误
     */
    hasErrorType(type: ErrorType): boolean {
        return this.stats.byType[type] > 0;
    }

    /**
     * 检查是否有特定应用的错误
     */
    hasAppErrors(appName: string): boolean {
        return this.stats.byApp[appName] > 0;
    }

    /**
     * 获取错误发生频率最高的应用
     */
    getMostErrorProneApp(): string | null {
        let maxErrors = 0;
        let mostErrorProneApp: string | null = null;

        for (const [appName, errorCount] of Object.entries(this.stats.byApp)) {
            if (errorCount > maxErrors) {
                maxErrors = errorCount;
                mostErrorProneApp = appName;
            }
        }

        return mostErrorProneApp;
    }

    /**
     * 获取最常见的错误类型
     */
    getMostCommonErrorType(): ErrorType | null {
        let maxErrors = 0;
        let mostCommonType: ErrorType | null = null;

        for (const [type, errorCount] of Object.entries(this.stats.byType)) {
            if (errorCount > maxErrors) {
                maxErrors = errorCount;
                mostCommonType = type as ErrorType;
            }
        }

        return mostCommonType;
    }

    /**
     * 记录错误
     */
    private recordError(error: ExtendedMicroCoreError): void {
        // 添加时间戳（如果没有的话）
        if (!(error as any).timestamp) {
            (error as any).timestamp = Date.now();
        }

        this.errors.push(error);

        // 限制错误历史大小
        if (this.errors.length > this.maxErrorHistory) {
            this.errors.shift();
        }
    }

    /**
     * 更新统计信息
     */
    private updateStats(error: ExtendedMicroCoreError): void {
        // 更新总数
        this.stats.total++;

        // 更新按类型统计
        if (error.type) {
            this.stats.byType[error.type] = (this.stats.byType[error.type] || 0) + 1;
        }

        // 更新按应用统计
        if (error.appName) {
            this.stats.byApp[error.appName] = (this.stats.byApp[error.appName] || 0) + 1;
        }

        // 更新最近错误
        this.stats.recent.push(error);
        if (this.stats.recent.length > 20) {
            this.stats.recent.shift();
        }
    }

    /**
     * 记录错误日志
     */
    private logError(error: ExtendedMicroCoreError, formattedMessage: string): void {
        const logData = {
            type: error.type,
            message: error.message,
            appName: error.appName,
            code: error.code,
            timestamp: (error as any).timestamp || Date.now(),
            stack: error.stack,
            context: error.context
        };

        const errorType = error.type || ERROR_TYPES.PARSE_ERROR;
        switch (errorType as ERROR_TYPES) {
            case ERROR_TYPES.LOAD_ERROR:
            case ERROR_TYPES.NETWORK_ERROR:
                console.error(`[ErrorHandler] ${formattedMessage}`, logData);
                break;

            case ERROR_TYPES.MOUNT_ERROR:
            case ERROR_TYPES.UNMOUNT_ERROR:
            case ERROR_TYPES.BOOTSTRAP_ERROR:
                console.error(`[ErrorHandler] ${formattedMessage}`, logData);
                break;

            case ERROR_TYPES.UPDATE_ERROR:
            case ERROR_TYPES.TIMEOUT_ERROR:
                console.warn(`[ErrorHandler] ${formattedMessage}`, logData);
                break;

            case ERROR_TYPES.PLUGIN_ERROR:
            case ERROR_TYPES.SANDBOX_ERROR:
                console.error(`[ErrorHandler] ${formattedMessage}`, logData);
                break;

            case ERROR_TYPES.PARSE_ERROR:
                console.warn(`[ErrorHandler] ${formattedMessage}`, logData);
                break;

            default:
                console.error(`[ErrorHandler] ${formattedMessage}`, logData);
                break;
        }
    }

    /**
     * 判断是否在开发模式下抛出错误
     */
    private shouldThrowInDev(error: ExtendedMicroCoreError): boolean {
        // 在开发模式下，某些严重错误可以考虑抛出
        const criticalErrors = [
            ERROR_TYPES.LOAD_ERROR,
            ERROR_TYPES.BOOTSTRAP_ERROR,
            ERROR_TYPES.PLUGIN_ERROR
        ];

        return criticalErrors.includes(error.type);
    }

    /**
     * 初始化统计信息
     */
    private initializeStats(): void {
        // 初始化所有错误类型的计数器
        Object.values(ERROR_TYPES).forEach(type => {
            this.stats.byType[type] = 0;
        });
    }
}