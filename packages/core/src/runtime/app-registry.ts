/**
 * @fileoverview 应用注册中心 - 管理所有已注册的微前端应用
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { APP_STATUS } from './constants';
import { ERROR_CODES, MicroCoreError } from './errors';
import { EventBus } from './event-bus';
import type { AppConfig, AppInstance, AppStatus } from './types';
import { logger } from './utils';

/**
 * 应用注册中心
 * 负责管理所有已注册的微前端应用的配置和状态
 */
export class AppRegistry {
    private apps = new Map<string, AppInstance>();
    private eventBus: EventBus;
    private logger = logger.createLogger('[AppRegistry]');

    constructor(eventBus: EventBus) {
        this.eventBus = eventBus;
    }

    /**
     * 注册应用
     */
    register(config: AppConfig): void {
        this.logger.debug('注册应用:', config.name);

        // 验证配置
        this.validateConfig(config);

        // 检查应用是否已存在
        if (this.apps.has(config.name)) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_ALREADY_EXISTS,
                `应用 ${config.name} 已存在`
            );
        }

        // 创建应用实例
        const appInstance: AppInstance = {
            name: config.name,
            config,
            status: APP_STATUS.NOT_LOADED,
            loadTime: null,
            mountTime: null,
            unmountTime: null,
            error: null,
            container: null,
            sandbox: null,
            lifecycles: null
        };

        // 注册应用
        this.apps.set(config.name, appInstance);

        // 发送事件
        this.eventBus.emit('app:registered', appInstance);

        this.logger.info(`应用 ${config.name} 注册成功`);
    }

    /**
     * 注销应用
     */
    unregister(name: string): void {
        this.logger.debug('注销应用:', name);

        const app = this.apps.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 不存在`
            );
        }

        // 检查应用状态
        if (app.status === APP_STATUS.MOUNTED) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_STILL_MOUNTED,
                `应用 ${name} 仍在挂载状态，无法注销`
            );
        }

        // 注销应用
        this.apps.delete(name);

        // 发送事件
        this.eventBus.emit('app:unregistered', app);

        this.logger.info(`应用 ${name} 注销成功`);
    }

    /**
     * 获取应用实例
     */
    get(name: string): AppInstance | null {
        return this.apps.get(name) || null;
    }

    /**
     * 检查应用是否存在
     */
    has(name: string): boolean {
        return this.apps.has(name);
    }

    /**
     * 获取所有应用
     */
    getAll(): AppInstance[] {
        return Array.from(this.apps.values());
    }

    /**
     * 根据状态获取应用
     */
    getByStatus(status: AppStatus): AppInstance[] {
        return this.getAll().filter(app => app.status === status);
    }

    /**
     * 根据路由获取匹配的应用
     */
    getByRoute(pathname: string): AppInstance[] {
        return this.getAll().filter(app => {
            const { activeWhen } = app.config;

            if (typeof activeWhen === 'string') {
                return pathname.startsWith(activeWhen);
            }

            if (Array.isArray(activeWhen)) {
                return activeWhen.some(route => pathname.startsWith(route));
            }

            if (typeof activeWhen === 'function') {
                return activeWhen(window.location);
            }

            return false;
        });
    }

    /**
     * 更新应用状态
     */
    updateStatus(name: string, status: AppStatus): void {
        const app = this.apps.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 不存在`
            );
        }

        const oldStatus = app.status;
        app.status = status;

        // 记录时间戳
        const now = Date.now();
        switch (status) {
            case APP_STATUS.LOADED:
                app.loadTime = now;
                break;
            case APP_STATUS.MOUNTED:
                app.mountTime = now;
                break;
            case APP_STATUS.UNMOUNTED:
                app.unmountTime = now;
                break;
        }

        // 发送状态变更事件
        this.eventBus.emit('app:status-changed', {
            app,
            oldStatus,
            newStatus: status
        });

        this.logger.debug(`应用 ${name} 状态变更: ${oldStatus} -> ${status}`);
    }

    /**
     * 设置应用错误
     */
    setError(name: string, error: Error): void {
        const app = this.apps.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 不存在`
            );
        }

        app.error = error;
        app.status = APP_STATUS.LOAD_ERROR;

        // 发送错误事件
        this.eventBus.emit('app:error', { app, error });

        this.logger.error(`应用 ${name} 发生错误:`, error);
    }

    /**
     * 清除应用错误
     */
    clearError(name: string): void {
        const app = this.apps.get(name);
        if (app) {
            app.error = null;
        }
    }

    /**
     * 获取应用统计信息
     */
    getStats(): {
        total: number;
        loaded: number;
        mounted: number;
        error: number;
    } {
        const apps = this.getAll();
        return {
            total: apps.length,
            loaded: apps.filter(app => app.status === APP_STATUS.LOADED).length,
            mounted: apps.filter(app => app.status === APP_STATUS.MOUNTED).length,
            error: apps.filter(app => app.status === APP_STATUS.LOAD_ERROR).length
        };
    }

    /**
     * 清空所有应用
     */
    clear(): void {
        this.logger.debug('清空所有应用');

        // 检查是否有挂载的应用
        const mountedApps = this.getByStatus(APP_STATUS.MOUNTED);
        if (mountedApps.length > 0) {
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                `存在 ${mountedApps.length} 个挂载中的应用，无法清空`
            );
        }

        this.apps.clear();
        this.eventBus.emit('app:registry-cleared');
        this.logger.info('应用注册中心已清空');
    }

    /**
     * 验证应用配置
     */
    private validateConfig(config: AppConfig): void {
        if (!config.name) {
            throw new MicroCoreError(
                ERROR_CODES.INVALID_ARGUMENT,
                '应用名称不能为空'
            );
        }

        if (!config.entry) {
            throw new MicroCoreError(
                ERROR_CODES.INVALID_ARGUMENT,
                '应用入口不能为空'
            );
        }

        if (!config.container) {
            throw new MicroCoreError(
                ERROR_CODES.INVALID_ARGUMENT,
                '应用容器不能为空'
            );
        }

        // 验证应用名称格式
        if (!/^[a-zA-Z][a-zA-Z0-9-_]*$/.test(config.name)) {
            throw new MicroCoreError(
                ERROR_CODES.INVALID_ARGUMENT,
                '应用名称格式不正确，只能包含字母、数字、连字符和下划线，且必须以字母开头'
            );
        }

        // 验证入口URL格式
        if (typeof config.entry === 'string') {
            try {
                new URL(config.entry);
            } catch {
                throw new MicroCoreError(
                    ERROR_CODES.INVALID_ARGUMENT,
                    `应用入口URL格式不正确: ${config.entry}`
                );
            }
        }
    }
}