/**
 * @fileoverview 微前端错误类定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { ErrorCodeType } from '../constants';

/**
 * 微前端核心错误类
 */
export class MicroCoreError extends Error {
    /** 错误代码 */
    public readonly code: ErrorCodeType;
    /** 错误上下文 */
    public readonly context: Record<string, any>;
    /** 原始错误 */
    public readonly cause?: Error;
    /** 错误时间戳 */
    public readonly timestamp: number;

    constructor(
        code: ErrorCodeType,
        message: string,
        context: Record<string, any> = {},
        cause?: Error
    ) {
        super(message);

        this.name = 'MicroCoreError';
        this.code = code;
        this.context = context;
        this.cause = cause;
        this.timestamp = Date.now();

        // 保持错误堆栈的正确性
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 转换为 JSON 格式
     */
    toJSON(): Record<string, any> {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            context: this.context,
            timestamp: this.timestamp,
            stack: this.stack,
            cause: this.cause ? {
                name: this.cause.name,
                message: this.cause.message,
                stack: this.cause.stack
            } : undefined
        };
    }

    /**
     * 转换为字符串
     */
    toString(): string {
        return `${this.name} [${this.code}]: ${this.message}`;
    }
}