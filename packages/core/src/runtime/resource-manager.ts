/**
 * @fileoverview 资源管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { ErrorCodes, RESOURCE_TYPES } from './constants';
import { ResourceError } from './errors';
import type { LoadOptions, ResourceInfo } from './types';
import { createLogger, isValidUrl, retry } from './utils';

/**
 * 扩展的加载选项
 */
interface ExtendedLoadOptions extends LoadOptions {
    /** 是否强制重新加载 */
    forceReload?: boolean;
    /** 是否为预加载 */
    preload?: boolean;
}

/**
 * 扩展的资源信息
 */
interface ExtendedResourceInfo extends ResourceInfo {
    /** 关联的应用名称 */
    app?: string;
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML内容 */
    html?: string;
}

/**
 * 资源管理器
 * 负责管理微前端应用的资源加载、缓存和预加载
 */
export class ResourceManager extends EventEmitter {
    private readonly logger = createLogger('ResourceManager');
    private readonly cache = new Map<string, ResourceInfo>();
    private readonly loadingPromises = new Map<string, Promise<ResourceInfo>>();
    private started = false;

    constructor() {
        super();
    }

    /**
     * 加载应用资源
     * @param entry 应用入口
     * @param options 加载选项
     */
    async loadAppResources(entry: string, options: ExtendedLoadOptions = {}): Promise<ExtendedResourceInfo> {
        const cacheKey = this.getCacheKey(entry, options);

        // 检查缓存
        if (this.resourceCache.has(cacheKey) && !options.forceReload) {
            this.logger.debug(`从缓存加载资源: ${entry}`);
            return this.resourceCache.get(cacheKey) as ExtendedResourceInfo;
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(cacheKey)) {
            this.logger.debug(`等待资源加载完成: ${entry}`);
            return this.loadingPromises.get(cacheKey) as Promise<ExtendedResourceInfo>;
        }

        // 开始加载资源
        const loadingPromise = this.doLoadAppResources(entry, options);
        this.loadingPromises.set(cacheKey, loadingPromise);

        try {
            const resourceInfo = await loadingPromise;

            // 缓存资源信息
            this.cacheResource(cacheKey, resourceInfo);

            this.logger.info(`资源加载成功: ${entry}`);
            return resourceInfo;
        } catch (error) {
            this.logger.error(`资源加载失败: ${entry}`, error);
            throw new ResourceError(
                `应用资源加载失败: ${(error as Error).message}`,
                entry,
                ErrorCodes.RESOURCE_LOAD_FAILED
            );
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }

    /**
     * 预加载应用资源
     * @param entry 应用入口
     * @param options 加载选项
     */
    async preloadAppResources(entry: string, options: ExtendedLoadOptions = {}): Promise<void> {
        try {
            await this.loadAppResources(entry, { ...options, preload: true });
            this.logger.info(`资源预加载成功: ${entry}`);
        } catch (error) {
            this.logger.warn(`资源预加载失败: ${entry}`, error);
            // 预加载失败不抛出错误
        }
    }

    /**
     * 获取应用资源信息
     * @param entry 应用入口
     */
    getAppResources(entry: string): ExtendedResourceInfo | null {
        const cacheKey = this.getCacheKey(entry);
        return (this.resourceCache.get(cacheKey) as ExtendedResourceInfo) || null;
    }

    /**
     * 清理应用资源
     * @param entry 应用入口
     */
    cleanupAppResources(entry: string): void {
        const cacheKey = this.getCacheKey(entry);
        const resourceInfo = this.resourceCache.get(cacheKey) as ExtendedResourceInfo;

        if (resourceInfo) {
            // 清理DOM中的资源
            this.cleanupDOMResources(resourceInfo);

            // 从缓存中移除
            this.resourceCache.delete(cacheKey);

            this.logger.info(`资源清理完成: ${entry}`);
        }
    }

    /**
     * 清理所有资源
     */
    cleanupAllResources(): void {
        for (const [cacheKey, resourceInfo] of this.resourceCache) {
            this.cleanupDOMResources(resourceInfo as ExtendedResourceInfo);
        }

        this.resourceCache.clear();
        this.loadingPromises.clear();

        this.logger.info('所有资源已清理');
    }

    /**
     * 实际加载应用资源
     * @param entry 应用入口
     * @param options 加载选项
     */
    private async doLoadAppResources(entry: string, options: ExtendedLoadOptions): Promise<ExtendedResourceInfo> {
        const resourceInfo: ExtendedResourceInfo = {
            url: entry,
            type: this.getResourceType(entry),
            loaded: false,
            loadTime: Date.now(),
            scripts: [],
            styles: [],
            html: ''
        };

        try {
            // 如果是HTML入口，解析HTML
            if (this.isHTMLEntry(entry)) {
                const htmlContent = await this.fetchHTML(entry);
                resourceInfo.html = htmlContent;
                resourceInfo.content = htmlContent;

                // 解析HTML中的资源
                const parsedResources = this.parseHTMLResources(htmlContent, entry);
                resourceInfo.scripts = parsedResources.scripts;
                resourceInfo.styles = parsedResources.styles;
            } else {
                // 直接加载JS入口
                resourceInfo.scripts = [entry];
                const content = await this.fetchResource(entry, options);
                resourceInfo.content = content;
            }

            // 预加载CSS资源
            if (!options.preload && resourceInfo.styles) {
                await this.loadStylesheets(resourceInfo.styles);
            }

            // 预加载JS资源（但不执行）
            if (!options.preload && resourceInfo.scripts) {
                await this.preloadScripts(resourceInfo.scripts);
            }

            resourceInfo.loaded = true;
            resourceInfo.loadTime = Date.now() - resourceInfo.loadTime;
            return resourceInfo;
        } catch (error) {
            resourceInfo.error = error as Error;
            resourceInfo.loadTime = Date.now() - resourceInfo.loadTime;
            throw new ResourceError(
                `解析
/**
 * @fileoverview 资源管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { ErrorCodes, RESOURCE_TYPES } from './constants';
import { ResourceError } from './errors';
import type { LoadOptions, ResourceInfo } from './types';
import { createLogger, isValidUrl, retry } from './utils';

/**
 * 扩展的加载选项
 */
interface ExtendedLoadOptions extends LoadOptions {
    /** 是否强制重新加载 */
    forceReload?: boolean;
    /** 是否为预加载 */
    preload?: boolean;
}

/**
 * 扩展的资源信息
 */
interface ExtendedResourceInfo extends ResourceInfo {
    /** 关联的应用名称 */
    app?: string;
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML内容 */
    html?: string;
}

/**
 * 资源管理器
 * 负责管理微前端应用的资源加载、缓存和预加载
 */
export class ResourceManager extends EventEmitter {
    private readonly logger = createLogger('ResourceManager');
    private readonly cache = new Map<string, ResourceInfo>();
    private readonly loadingPromises = new Map<string, Promise<ResourceInfo>>();
    private started = false;

    constructor() {
        super();
    }

    /**
     * 启动资源管理器
     */
    async start(): Promise<void> {
        if (this.started) {
            this.logger.warn('资源管理器已经启动');
            return;
        }

        this.logger.info('启动资源管理器...');
        this.started = true;
        this.emit('started');
        this.logger.info('资源管理器启动成功');
    }

    /**
     * 停止资源管理器
     */
    async stop(): Promise<void> {
        if (!this.started) {
            this.logger.warn('资源管理器未启动');
            return;
        }

        this.logger.info('停止资源管理器...');
        this.started = false;
        this.emit('stopped');
        this.logger.info('资源管理器停止成功');
    }

    /**
     * 销毁资源管理器
     */
    async destroy(): Promise<void> {
        if (this.started) {
            await this.stop();
        }

        // 清理缓存
        this.cache.clear();
        this.loadingPromises.clear();
        this.removeAllListeners();
        this.logger.info('资源管理器已销毁');
    }

    /**
     * 加载资源
     * @param url 资源URL
     * @param options 加载选项
     */
    async loadResource(url: string, options: LoadOptions = {}): Promise<ResourceInfo> {
        if (!isValidUrl(url)) {
            throw new ResourceError(`无效的资源URL: ${ url }`, url, ErrorCodes.INVALID_ARGUMENT);
        }

        // 检查缓存
        if (options.cache !== false) {
            const cached = this.cache.get(url);
            if (cached && cached.loaded) {
                this.logger.debug(`从缓存加载资源: ${ url }`);
                return cached;
            }
        }

        // 检查是否正在加载
        const loadingPromise = this.loadingPromises.get(url);
        if (loadingPromise) {
            this.logger.debug(`等待资源加载完成: ${ url }`);
            return loadingPromise;
        }

        // 开始加载
        const promise = this.doLoadResource(url, options);
        this.loadingPromises.set(url, promise);

        try {
            const result = await promise;
            this.loadingPromises.delete(url);
            return result;
        } catch (error) {
            this.loadingPromises.delete(url);
            throw error;
        }
    }

    /**
     * 预加载资源
     * @param urls 资源URL列表
     * @param options 加载选项
     */
    async preloadResources(urls: string[], options: LoadOptions = {}): Promise<ResourceInfo[]> {
        this.logger.info(`预加载 ${ urls.length } 个资源`);

        const promises = urls.map(url =>
            this.loadResource(url, { ...options, cache: true })
                .catch(error => {
                    this.logger.warn(`预加载资源失败: ${ url }`, error);
                    return null;
                })
        );

        const results = await Promise.all(promises);
        const successful = results.filter(Boolean) as ResourceInfo[];

        this.logger.info(`预加载完成: ${ successful.length } / ${ urls.length } 成功`);
        return successful;
    }

    /**
     * 获取资源信息
     * @param url 资源URL
     */
    getResource(url: string): ResourceInfo | null {
        return this.cache.get(url) || null;
    }

    /**
     * 获取所有资源信息
     */
    getAllResources(): ResourceInfo[] {
        return Array.from(this.cache.values());
    }

    /**
     * 清理缓存
     * @param url 可选的特定URL，不提供则清理所有缓存
     */
    clearCache(url?: string): void {
        if (url) {
            this.cache.delete(url);
            this.logger.debug(`清理缓存: ${ url }`);
        } else {
            this.cache.clear();
            this.logger.info('清理所有缓存');
        }
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats(): {
        total: number;
        loaded: number;
        failed: number;
        size: number;
    } {
        const resources = Array.from(this.cache.values());
        const loaded = resources.filter(r => r.loaded);
        const failed = resources.filter(r => r.error);
        const size = resources.reduce((total, r) => {
            return total + (r.content?.length || 0);
        }, 0);

        return {
            total: resources.length,
            loaded: loaded.length,
            failed: failed.length,
            size
        };
    }

    /**
     * 实际加载资源
     * @param url 资源URL
     * @param options 加载选项
     */
    private async doLoadResource(url: string, options: LoadOptions): Promise<ResourceInfo> {
        const startTime = Date.now();
        const resourceType = this.getResourceType(url);

        const resourceInfo: ResourceInfo = {
            url,
            type: resourceType,
            loaded: false,
            loadTime: undefined,
            content: undefined,
            error: undefined
        };

        // 缓存资源信息
        this.cache.set(url, resourceInfo);

        try {
            this.logger.debug(`开始加载资源: ${ url }`);
            this.emit('loadStart', resourceInfo);

            // 使用重试机制加载资源
            const content = await retry(
                () => this.fetchResource(url, options),
                options.retries || 3,
                1000
            );

            // 更新资源信息
            resourceInfo.content = content;
            resourceInfo.loaded = true;
            resourceInfo.loadTime = Date.now() - startTime;

            this.logger.debug(`资源加载成功: ${ url }(${ resourceInfo.loadTime }ms)`);
            this.emit('loadSuccess', resourceInfo);

            return resourceInfo;
        } catch (error) {
            const loadError = error instanceof Error ? error : new Error(String(error));
            resourceInfo.error = loadError;
            resourceInfo.loadTime = Date.now() - startTime;

            this.logger.error(`资源加载失败: ${ url }`, loadError);
            this.emit('loadError', resourceInfo);

            throw new ResourceError(
                `资源加载失败: ${ loadError.message }`,
                url,
                ErrorCodes.RESOURCE_LOAD_FAILED,
                { originalError: loadError }
            );
        }
    }

    /**
     * 获取资源类型
     * @param url 资源URL
     */
    private getResourceType(url: string): 'script' | 'style' | 'html' {
        const pathname = new URL(url).pathname.toLowerCase();

        if (pathname.endsWith('.js') || pathname.endsWith('.mjs')) {
            return RESOURCE_TYPES.SCRIPT;
        }

        if (pathname.endsWith('.css')) {
            return RESOURCE_TYPES.STYLE;
        }

        if (pathname.endsWith('.html') || pathname.endsWith('.htm')) {
            return RESOURCE_TYPES.HTML;
        }

        // 默认为脚本类型
        return RESOURCE_TYPES.SCRIPT;
    }

    /**
     * 获取资源内容
     * @param url 资源URL
     * @param options 加载选项
     */
    private async fetchResource(url: string, options: LoadOptions): Promise<string> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, options.timeout || 30000);

        try {
            const response = await fetch(url, {
                headers: options.headers,
                signal: controller.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP ${ response.status }: ${ response.statusText }`);
            }

            const content = await response.text();
            return content;
        } finally {
            clearTimeout(timeoutId);
        }
    }

    /**
     * 加载脚本资源
     * @param url 脚本URL
     * @param options 加载选项
     */
    async loadScript(url: string, options: LoadOptions = {}): Promise<void> {
        const resourceInfo = await this.loadResource(url, options);

        if (resourceInfo.type !== RESOURCE_TYPES.SCRIPT) {
            throw new ResourceError(
                `资源类型不匹配，期望脚本但得到 ${ resourceInfo.type }`,
                url,
                ErrorCodes.RESOURCE_LOAD_FAILED
            );
        }

        // 创建并插入脚本元素
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`脚本加载失败: ${ url }`));

            // 设置其他属性
            if (options.headers?.['crossorigin']) {
                script.crossOrigin = options.headers['crossorigin'];
            }

            document.head.appendChild(script);
        });
    }

    /**
     * 加载样式资源
     * @param url 样式URL
     * @param options 加载选项
     */
    async loadStyle(url: string, options: LoadOptions = {}): Promise<void> {
        const resourceInfo = await this.loadResource(url, options);

        if (resourceInfo.type !== RESOURCE_TYPES.STYLE) {
            throw new ResourceError(
                `资源类型不匹配，期望样式但得到 ${ resourceInfo.type }`,
                url,
                ErrorCodes.RESOURCE_LOAD_FAILED
            );
        }

        // 创建并插入样式元素
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = url;
            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`样式加载失败: ${ url }`));

            // 设置其他属性
            if (options.headers?.['crossorigin']) {
                link.crossOrigin = options.headers['crossorigin'];
            }

            document.head.appendChild(link);
        });
    }

    /**
     * 批量加载资源
     * @param resources 资源配置列表
     * @param options 加载选项
     */
    async loadResources(
        resources: Array<{ url: string; type?: 'script' | 'style' | 'html' }>,
        options: LoadOptions = {}
    ): Promise<ResourceInfo[]> {
        const promises = resources.map(async ({ url, type }) => {
            try {
                if (type === 'script') {
                    await this.loadScript(url, options);
                } else if (type === 'style') {
                    await this.loadStyle(url, options);
                }
                return await this.loadResource(url, options);
            } catch (error) {
                this.logger.warn(`资源加载失败: ${ url }`, error);
                throw error;
            }
        });

        return Promise.all(promises);
    }

    /**
     * 检查资源是否已加载
     * @param url 资源URL
     */
    isResourceLoaded(url: string): boolean {
        const resource = this.cache.get(url);
        return resource?.loaded === true;
    }

    /**
     * 检查资源是否正在加载
     * @param url 资源URL
     */
    isResourceLoading(url: string): boolean {
        return this.loadingPromises.has(url);
    }

    /**
     * 获取加载中的资源列表
     */
    getLoadingResources(): string[] {
        return Array.from(this.loadingPromises.keys());
    }

    /**
     * 获取已加载的资源列表
     */
    getLoadedResources(): ResourceInfo[] {
        return Array.from(this.cache.values()).filter(r => r.loaded);
    }

    /**
     * 获取加载失败的资源列表
     */
    getFailedResources(): ResourceInfo[] {
        return Array.from(this.cache.values()).filter(r => r.error);
    }
}

export default ResourceManager;