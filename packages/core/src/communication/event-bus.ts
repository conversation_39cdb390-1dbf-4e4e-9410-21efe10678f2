/**
 * @fileoverview 全局事件总线 - 提供发布订阅模式的事件通信
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'eventemitter3';
import type { EventMap } from './types';
import { logger } from './utils';

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件监听器选项
 */
export interface EventListenerOptions {
    once?: boolean;
    priority?: number;
}

/**
 * 全局事件总线
 * 基于 EventEmitter3 实现的高性能事件系统
 */
export class EventBus {
    private emitter: EventEmitter;
    private logger = logger.createLogger('[EventBus]');
    private listenerCount = 0;

    constructor() {
        this.emitter = new EventEmitter();
        this.setupErrorHandling();
    }

    /**
     * 监听事件
     */
    on<K extends keyof EventMap>(
        event: K,
        listener: EventListener<EventMap[K]>,
        options?: EventListenerOptions
    ): () => void;
    on(
        event: string,
        listener: EventListener,
        options?: EventListenerOptions
    ): () => void;
    on(
        event: string | symbol,
        listener: EventListener,
        options: EventListenerOptions = {}
    ): () => void {
        this.logger.debug(`监听事件: ${String(event)}`);

        if (options.once) {
            this.emitter.once(event, listener);
        } else {
            this.emitter.on(event, listener);
        }

        this.listenerCount++;

        // 返回取消监听的函数
        return () => {
            this.off(event, listener);
        };
    }

    /**
     * 监听事件（一次性）
     */
    once<K extends keyof EventMap>(
        event: K,
        listener: EventListener<EventMap[K]>
    ): () => void;
    once(event: string, listener: EventListener): () => void;
    once(event: string | symbol, listener: EventListener): () => void {
        return this.on(event, listener, { once: true });
    }

    /**
     * 取消监听事件
     */
    off<K extends keyof EventMap>(
        event: K,
        listener?: EventListener<EventMap[K]>
    ): void;
    off(event: string, listener?: EventListener): void;
    off(event: string | symbol, listener?: EventListener): void {
        this.logger.debug(`取消监听事件: ${String(event)}`);

        if (listener) {
            this.emitter.off(event, listener);
            this.listenerCount--;
        } else {
            const listenerCount = this.emitter.listenerCount(event);
            this.emitter.removeAllListeners(event);
            this.listenerCount -= listenerCount;
        }
    }

    /**
     * 发射事件
     */
    emit<K extends keyof EventMap>(event: K, data: EventMap[K]): boolean;
    emit(event: string, data?: any): boolean;
    emit(event: string | symbol, data?: any): boolean {
        this.logger.debug(`发射事件: ${String(event)}`, data);

        try {
            return this.emitter.emit(event, data);
        } catch (error) {
            this.logger.error(`事件处理器执行失败: ${String(event)}`, error);
            this.emitter.emit('error', { event, error, data });
            return false;
        }
    }

    /**
     * 异步发射事件
     */
    async emitAsync<K extends keyof EventMap>(
        event: K,
        data: EventMap[K]
    ): Promise<void>;
    async emitAsync(event: string, data?: any): Promise<void>;
    async emitAsync(event: string | symbol, data?: any): Promise<void> {
        this.logger.debug(`异步发射事件: ${String(event)}`, data);

        const listeners = this.emitter.listeners(event);
        if (listeners.length === 0) {
            return;
        }

        const promises = listeners.map(async (listener) => {
            try {
                await listener(data);
            } catch (error) {
                this.logger.error(`异步事件处理器执行失败: ${String(event)}`, error);
                this.emitter.emit('error', { event, error, data });
            }
        });

        await Promise.all(promises);
    }

    /**
     * 获取事件监听器数量
     */
    listenerCount(event?: string | symbol): number {
        if (event) {
            return this.emitter.listenerCount(event);
        }
        return this.listenerCount;
    }

    /**
     * 获取所有事件名称
     */
    eventNames(): (string | symbol)[] {
        return this.emitter.eventNames();
    }

    /**
     * 获取事件监听器
     */
    listeners(event: string | symbol): EventListener[] {
        return this.emitter.listeners(event) as EventListener[];
    }

    /**
     * 检查是否有监听器
     */
    hasListeners(event: string | symbol): boolean {
        return this.emitter.listenerCount(event) > 0;
    }

    /**
     * 清除所有监听器
     */
    clear(): void {
        this.logger.debug('清除所有事件监听器');
        this.emitter.removeAllListeners();
        this.listenerCount = 0;
    }

    /**
     * 创建命名空间事件总线
     */
    namespace(prefix: string): NamespacedEventBus {
        return new NamespacedEventBus(this, prefix);
    }

    /**
     * 设置错误处理
     */
    private setupErrorHandling(): void {
        this.emitter.on('error', (errorData) => {
            this.logger.error('事件总线错误:', errorData);
        });
    }

    /**
     * 获取统计信息
     */
    getStats(): {
        totalListeners: number;
        eventCount: number;
        events: Record<string, number>;
    } {
        const events: Record<string, number> = {};
        const eventNames = this.eventNames();

        eventNames.forEach(event => {
            events[String(event)] = this.emitter.listenerCount(event);
        });

        return {
            totalListeners: this.listenerCount,
            eventCount: eventNames.length,
            events
        };
    }
}

/**
 * 命名空间事件总线
 * 为特定模块提供隔离的事件空间
 */
export class NamespacedEventBus {
    private eventBus: EventBus;
    private prefix: string;
    private logger = logger.createLogger('[NamespacedEventBus]');

    constructor(eventBus: EventBus, prefix: string) {
        this.eventBus = eventBus;
        this.prefix = prefix;
    }

    /**
     * 获取带命名空间的事件名
     */
    private getNamespacedEvent(event: string): string {
        return `${this.prefix}:${event}`;
    }

    /**
     * 监听事件
     */
    on(event: string, listener: EventListener, options?: EventListenerOptions): () => void {
        const namespacedEvent = this.getNamespacedEvent(event);
        return this.eventBus.on(namespacedEvent, listener, options);
    }

    /**
     * 监听事件（一次性）
     */
    once(event: string, listener: EventListener): () => void {
        const namespacedEvent = this.getNamespacedEvent(event);
        return this.eventBus.once(namespacedEvent, listener);
    }

    /**
     * 取消监听事件
     */
    off(event: string, listener?: EventListener): void {
        const namespacedEvent = this.getNamespacedEvent(event);
        this.eventBus.off(namespacedEvent, listener);
    }

    /**
     * 发射事件
     */
    emit(event: string, data?: any): boolean {
        const namespacedEvent = this.getNamespacedEvent(event);
        return this.eventBus.emit(namespacedEvent, data);
    }

    /**
     * 异步发射事件
     */
    async emitAsync(event: string, data?: any): Promise<void> {
        const namespacedEvent = this.getNamespacedEvent(event);
        return this.eventBus.emitAsync(namespacedEvent, data);
    }

    /**
     * 获取事件监听器数量
     */
    listenerCount(event?: string): number {
        if (event) {
            const namespacedEvent = this.getNamespacedEvent(event);
            return this.eventBus.listenerCount(namespacedEvent);
        }

        // 统计所有命名空间下的监听器
        const allEvents = this.eventBus.eventNames();
        return allEvents
            .filter(eventName => String(eventName).startsWith(`${this.prefix}:`))
            .reduce((count, eventName) => count + this.eventBus.listenerCount(eventName), 0);
    }

    /**
     * 清除命名空间下的所有监听器
     */
    clear(): void {
        this.logger.debug(`清除命名空间 ${this.prefix} 下的所有监听器`);

        const allEvents = this.eventBus.eventNames();
        allEvents
            .filter(eventName => String(eventName).startsWith(`${this.prefix}:`))
            .forEach(eventName => this.eventBus.off(eventName));
    }
}

// 导出全局事件总线实例
export const globalEventBus = new EventBus();