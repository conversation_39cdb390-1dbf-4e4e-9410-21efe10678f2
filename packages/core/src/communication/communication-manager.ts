/**
 * @fileoverview 通信管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import type { CommunicationMessage } from '../types';
import { createLogger } from '../utils';

/**
 * 通信管理器类
 * 负责管理应用间的通信
 */
export class CommunicationManager extends EventEmitter {
    private channels = new Map<string, Set<string>>();
    private messageQueue = new Map<string, CommunicationMessage[]>();
    private logger = createLogger('CommunicationManager');

    constructor() {
        super();
    }

    /**
     * 发送消息
     * @param message 消息对象
     */
    sendMessage(message: CommunicationMessage): void {
        const { to, from, type, data } = message;

        this.logger.debug(`发送消息: ${from} -> ${to}`, { type, data });

        // 如果指定了接收者
        if (to) {
            this.sendDirectMessage(message);
        } else {
            // 广播消息
            this.broadcastMessage(message);
        }

        this.emit('message-sent', message);
    }

    /**
     * 订阅消息
     * @param appName 应用名称
     * @param messageType 消息类型
     * @param handler 处理函数
     */
    subscribe(
        appName: string,
        messageType: string,
        handler: (message: CommunicationMessage) => void
    ): () => void {
        const eventName = `${appName}:${messageType}`;
        this.on(eventName, handler);

        this.logger.debug(`应用 ${appName} 订阅消息类型: ${messageType}`);

        // 返回取消订阅函数
        return () => {
            this.off(eventName, handler);
            this.logger.debug(`应用 ${appName} 取消订阅消息类型: ${messageType}`);
        };
    }

    /**
     * 创建通信通道
     * @param channelName 通道名称
     * @param participants 参与者列表
     */
    createChannel(channelName: string, participants: string[]): void {
        this.channels.set(channelName, new Set(participants));
        this.logger.info(`创建通信通道: ${channelName}`, { participants });
    }

    /**
     * 加入通道
     * @param channelName 通道名称
     * @param appName 应用名称
     */
    joinChannel(channelName: string, appName: string): void {
        const channel = this.channels.get(channelName);
        if (channel) {
            channel.add(appName);
            this.logger.info(`应用 ${appName} 加入通道: ${channelName}`);
        }
    }

    /**
     * 离开通道
     * @param channelName 通道名称
     * @param appName 应用名称
     */
    leaveChannel(channelName: string, appName: string): void {
        const channel = this.channels.get(channelName);
        if (channel) {
            channel.delete(appName);
            this.logger.info(`应用 ${appName} 离开通道: ${channelName}`);
        }
    }

    /**
     * 向通道发送消息
     * @param channelName 通道名称
     * @param message 消息对象
     */
    sendToChannel(channelName: string, message: CommunicationMessage): void {
        const channel = this.channels.get(channelName);
        if (!channel) {
            this.logger.warn(`通道 ${channelName} 不存在`);
            return;
        }

        for (const appName of channel) {
            if (appName !== message.from) {
                this.sendDirectMessage({
                    ...message,
                    to: appName
                });
            }
        }
    }

    /**
     * 获取消息队列
     * @param appName 应用名称
     * @returns 消息数组
     */
    getMessageQueue(appName: string): CommunicationMessage[] {
        return this.messageQueue.get(appName) || [];
    }

    /**
     * 清空消息队列
     * @param appName 应用名称
     */
    clearMessageQueue(appName: string): void {
        this.messageQueue.delete(appName);
    }

    /**
     * 发送直接消息
     * @param message 消息对象
     */
    private sendDirectMessage(message: CommunicationMessage): void {
        const { to } = message;
        if (!to) return;

        const eventName = `${to}:${message.type}`;

        // 如果有监听器，直接发送
        if (this.listenerCount(eventName) > 0) {
            this.emit(eventName, message);
        } else {
            // 否则加入消息队列
            const queue = this.messageQueue.get(to) || [];
            queue.push(message);
            this.messageQueue.set(to, queue);

            this.logger.debug(`消息加入队列: ${to}`, message);
        }
    }

    /**
     * 广播消息
     * @param message 消息对象
     */
    private broadcastMessage(message: CommunicationMessage): void {
        this.emit('broadcast', message);
        this.logger.debug('广播消息', message);
    }

    /**
     * 销毁通信管理器
     */
    destroy(): void {
        this.channels.clear();
        this.messageQueue.clear();
        this.removeAllListeners();
        this.logger.info('通信管理器已销毁');
    }
}

export default CommunicationManager;