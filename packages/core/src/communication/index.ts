/**
 * @fileoverview 通信系统导出
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

export { CommunicationManager } from './communication-manager';
export { EventBus } from './event-bus';
export { GlobalState } from './global-state';
// MessageChannel 暂时注释，等待实现
// export { MessageChannel } from './message-channel';

export type {
    GlobalStateOptions, StateChangeCallback,
    StateWatcherOptions
} from './global-state';

// 定义CommunicationMessage类型
export interface CommunicationMessage {
    id: string;
    type: string;
    data: any;
    from: string;
    to?: string;
    timestamp: number;
}

// 暂时注释这些导出，等待实现
// export type {
//     ChannelConfig,
//     CommunicationAdapter, CommunicationConfig,
//     CommunicationEventType, CommunicationStats, CommunicationStatus,
//     ExtendedCommunicationMessage, MessageFilter,
//     MessageHandler, MessagePriority
// } from './communication-types';

// export * from './communication-utils';
