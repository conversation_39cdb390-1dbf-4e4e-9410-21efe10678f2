/**
 * @fileoverview 路由管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { createLogger } from '../utils';

/**
 * 路由配置接口
 */
export interface RouteConfig {
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    appName: string;
    /** 是否精确匹配 */
    exact?: boolean;
    /** 路由参数 */
    params?: Record<string, string>;
    /** 路由元数据 */
    meta?: Record<string, any>;
}

/**
 * 路由匹配结果
 */
export interface RouteMatch {
    /** 匹配的路由配置 */
    route: RouteConfig;
    /** 路由参数 */
    params: Record<string, string>;
    /** 查询参数 */
    query: Record<string, string>;
    /** 路径 */
    path: string;
}

/**
 * 路由管理器
 * 负责管理微前端应用的路由
 */
export class RouterManager extends EventEmitter {
    private logger = createLogger('[RouterManager]');
    private routes = new Map<string, RouteConfig>();
    private currentRoute: RouteMatch | null = null;
    private isListening = false;

    constructor() {
        super();
        this.setupRouteListener();
    }

    /**
     * 注册路由
     * @param route 路由配置
     */
    registerRoute(route: RouteConfig): void {
        this.routes.set(route.path, route);
        this.logger.debug(`注册路由: ${route.path} -> ${route.appName}`);
        this.emit('route-registered', route);
    }

    /**
     * 注销路由
     * @param path 路由路径
     */
    unregisterRoute(path: string): void {
        const route = this.routes.get(path);
        if (route) {
            this.routes.delete(path);
            this.logger.debug(`注销路由: ${path}`);
            this.emit('route-unregistered', route);
        }
    }

    /**
     * 获取路由配置
     * @param path 路由路径
     */
    getRoute(path: string): RouteConfig | undefined {
        return this.routes.get(path);
    }

    /**
     * 获取所有路由
     */
    getAllRoutes(): RouteConfig[] {
        return Array.from(this.routes.values());
    }

    /**
     * 匹配路由
     * @param path 当前路径
     */
    matchRoute(path: string): RouteMatch | null {
        for (const route of this.routes.values()) {
            const match = this.isRouteMatch(route, path);
            if (match) {
                return {
                    route,
                    params: match.params,
                    query: this.parseQuery(path),
                    path
                };
            }
        }
        return null;
    }

    /**
     * 导航到指定路径
     * @param path 目标路径
     * @param replace 是否替换当前历史记录
     */
    navigate(path: string, replace = false): void {
        if (replace) {
            window.history.replaceState(null, '', path);
        } else {
            window.history.pushState(null, '', path);
        }
        this.handleRouteChange();
    }

    /**
     * 返回上一页
     */
    goBack(): void {
        window.history.back();
    }

    /**
     * 前进到下一页
     */
    goForward(): void {
        window.history.forward();
    }

    /**
     * 获取当前路由
     */
    getCurrentRoute(): RouteMatch | null {
        return this.currentRoute;
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        return window.location.pathname + window.location.search + window.location.hash;
    }

    /**
     * 开始监听路由变化
     */
    startListening(): void {
        if (this.isListening) {
            return;
        }

        window.addEventListener('popstate', this.handlePopState);
        this.isListening = true;
        this.handleRouteChange();
        this.logger.debug('开始监听路由变化');
    }

    /**
     * 停止监听路由变化
     */
    stopListening(): void {
        if (!this.isListening) {
            return;
        }

        window.removeEventListener('popstate', this.handlePopState);
        this.isListening = false;
        this.logger.debug('停止监听路由变化');
    }

    /**
     * 销毁路由管理器
     */
    destroy(): void {
        this.stopListening();
        this.routes.clear();
        this.currentRoute = null;
        this.removeAllListeners();
        this.logger.debug('路由管理器已销毁');
    }

    /**
     * 设置路由监听器
     */
    private setupRouteListener(): void {
        this.handlePopState = this.handlePopState.bind(this);
    }

    /**
     * 处理 popstate 事件
     */
    private handlePopState = (): void => {
        this.handleRouteChange();
    };

    /**
     * 处理路由变化
     */
    private handleRouteChange(): void {
        const currentPath = this.getCurrentPath();
        const newRoute = this.matchRoute(currentPath);

        // 检查路由是否发生变化
        if (this.isRouteSame(this.currentRoute, newRoute)) {
            return;
        }

        const previousRoute = this.currentRoute;
        this.currentRoute = newRoute;

        this.logger.debug(`路由变化: ${previousRoute?.path || 'null'} -> ${newRoute?.path || 'null'}`);

        // 发送路由变化事件
        this.emit('route-change', {
            from: previousRoute,
            to: newRoute,
            path: currentPath
        });

        // 如果有匹配的路由，发送应用激活事件
        if (newRoute) {
            this.emit('app-activate', {
                appName: newRoute.route.appName,
                route: newRoute
            });
        }

        // 如果之前有路由，发送应用停用事件
        if (previousRoute && (!newRoute || previousRoute.route.appName !== newRoute.route.appName)) {
            this.emit('app-deactivate', {
                appName: previousRoute.route.appName,
                route: previousRoute
            });
        }
    }

    /**
     * 检查路由是否匹配
     * @param route 路由配置
     * @param path 当前路径
     */
    private isRouteMatch(route: RouteConfig, path: string): { params: Record<string, string> } | null {
        const routePath = route.path;
        const pathWithoutQuery = path.split('?')[0].split('#')[0];

        // 精确匹配
        if (route.exact) {
            if (routePath === pathWithoutQuery) {
                return { params: {} };
            }
            return null;
        }

        // 前缀匹配
        if (pathWithoutQuery.startsWith(routePath)) {
            // 如果路由路径以 / 结尾，或者当前路径的下一个字符是 / 或结束
            const nextChar = pathWithoutQuery[routePath.length];
            if (routePath.endsWith('/') || !nextChar || nextChar === '/') {
                return { params: this.extractParams(routePath, pathWithoutQuery) };
            }
        }

        return null;
    }

    /**
     * 提取路由参数
     * @param routePath 路由路径
     * @param actualPath 实际路径
     */
    private extractParams(routePath: string, actualPath: string): Record<string, string> {
        const params: Record<string, string> = {};

        // 简单的参数提取实现
        // 在实际项目中，可能需要更复杂的路径匹配逻辑
        const routeParts = routePath.split('/');
        const pathParts = actualPath.split('/');

        for (let i = 0; i < routeParts.length; i++) {
            const routePart = routeParts[i];
            const pathPart = pathParts[i];

            if (routePart.startsWith(':')) {
                const paramName = routePart.slice(1);
                params[paramName] = pathPart || '';
            }
        }

        return params;
    }

    /**
     * 解析查询参数
     * @param path 路径
     */
    private parseQuery(path: string): Record<string, string> {
        const query: Record<string, string> = {};
        const queryString = path.split('?')[1];

        if (queryString) {
            const pairs = queryString.split('&');
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                if (key) {
                    query[decodeURIComponent(key)] = decodeURIComponent(value || '');
                }
            }
        }

        return query;
    }

    /**
     * 检查两个路由是否相同
     * @param route1 路由1
     * @param route2 路由2
     */
    private isRouteSame(route1: RouteMatch | null, route2: RouteMatch | null): boolean {
        if (!route1 && !route2) {
            return true;
        }

        if (!route1 || !route2) {
            return false;
        }

        return route1.path === route2.path &&
            route1.route.appName === route2.route.appName;
    }
}

export default RouterManager;