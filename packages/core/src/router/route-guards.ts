import type { Route } from '../types';
import type { RouterGuard } from './router-manager';

/**
 * Authentication guard - checks if user is authenticated
 */
export function createAuthGuard(isAuthenticated: () => boolean | Promise<boolean>): RouterGuard {
    return async (to: Route, from: Route | null): Promise<boolean> => {
        const authenticated = await isAuthenticated();
        if (!authenticated) {
            console.warn(`Access denied to route: ${to.path}`);
            return false;
        }
        return true;
    };
}

/**
 * Permission guard - checks if user has required permissions
 */
export function createPermissionGuard(
    getPermissions: () => string[] | Promise<string[]>,
    requiredPermissions: Record<string, string[]>
): RouterGuard {
    return async (to: Route, from: Route | null): Promise<boolean> => {
        const permissions = await getPermissions();
        const required = requiredPermissions[to.path];

        if (!required || required.length === 0) {
            return true;
        }

        const hasPermission = required.every(permission => permissions.includes(permission));
        if (!hasPermission) {
            console.warn(`Insufficient permissions for route: ${to.path}`);
            return false;
        }

        return true;
    };
}

/**
 * Loading guard - shows loading state during navigation
 */
export function createLoadingGuard(
    showLoading: () => void,
    hideLoading: () => void
): RouterGuard {
    return async (to: Route, from: Route | null): Promise<boolean> => {
        showLoading();

        // Simulate async operation
        await new Promise(resolve => setTimeout(resolve, 100));

        hideLoading();
        return true;
    };
}

/**
 * Confirmation guard - asks for confirmation before navigation
 */
export function createConfirmationGuard(
    shouldConfirm: (to: Route, from: Route | null) => boolean,
    confirmMessage: string = 'Are you sure you want to leave this page?'
): RouterGuard {
    return async (to: Route, from: Route | null): Promise<boolean> => {
        if (!shouldConfirm(to, from)) {
            return true;
        }

        return window.confirm(confirmMessage);
    };
}

/**
 * Redirect guard - redirects to another route
 */
export function createRedirectGuard(
    redirectMap: Record<string, string>
): RouterGuard {
    return async (to: Route, from: Route | null): Promise<boolean> => {
        const redirectTo = redirectMap[to.path];
        if (redirectTo) {
            // Trigger navigation to redirect path
            setTimeout(() => {
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('micro-core:navigate', {
                        detail: { path: redirectTo }
                    }));
                }
            }, 0);
            return false;
        }
        return true;
    };
}

/**
 * Route guards collection
 */
export class RouteGuards {
    private guards: RouterGuard[] = [];

    /**
     * Add a guard
     */
    add(guard: RouterGuard): void {
        this.guards.push(guard);
    }

    /**
     * Remove a guard
     */
    remove(guard: RouterGuard): void {
        const index = this.guards.indexOf(guard);
        if (index > -1) {
            this.guards.splice(index, 1);
        }
    }

    /**
     * Run all guards
     */
    async run(to: Route, from: Route | null): Promise<boolean> {
        for (const guard of this.guards) {
            const result = await guard(to, from);
            if (result === false) {
                return false;
            }
        }
        return true;
    }

    /**
     * Clear all guards
     */
    clear(): void {
        this.guards = [];
    }

    /**
     * Get all guards
     */
    getAll(): RouterGuard[] {
        return [...this.guards];
    }
}