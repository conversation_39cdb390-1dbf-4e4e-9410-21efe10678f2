/**
 * @fileoverview 路由管理器 - 统一路由协调
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { createLogger } from '../utils';

/**
 * 路由配置接口
 */
export interface RouteConfig {
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    appName: string;
    /** 是否精确匹配 */
    exact?: boolean;
    /** 路由参数 */
    params?: Record<string, string>;
    /** 路由元数据 */
    meta?: Record<string, any>;
    /** 自定义激活条件 */
    activeWhen?: string | RegExp | ((location: Location) => boolean);
}

/**
 * 路由匹配结果
 */
export interface RouteMatch {
    /** 匹配的路由配置 */
    route: RouteConfig;
    /** 路由参数 */
    params: Record<string, string>;
    /** 查询参数 */
    query: Record<string, string>;
    /** 路径 */
    path: string;
}

/**
 * 路由变化事件
 */
export interface RouteChangeEvent {
    /** 事件类型 */
    type: 'route-change';
    /** 当前路径 */
    path: string;
    /** 之前的应用 */
    previousApp: string | null;
    /** 当前应用 */
    currentApp: string | null;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 历史记录适配器接口
 */
export interface HistoryAdapter {
    /** 获取当前路径 */
    getCurrentPath(): string;
    /** 导航到指定路径 */
    navigate(path: string, replace?: boolean): void;
    /** 监听路由变化 */
    listen(callback: () => void): () => void;
}

/**
 * 路由管理器
 * 负责管理微前端应用的路由，统一路由协调
 */
export class RouterManager extends EventEmitter {
    private logger = createLogger('[RouterManager]');
    private routes = new Map<string, RouteConfig>();
    private currentRoute: RouteMatch | null = null;
    private currentApp: string | null = null;
    private isStarted = false;
    private historyAdapter: HistoryAdapter;
    private originalPushState = window.history.pushState;
    private originalReplaceState = window.history.replaceState;

    constructor(historyAdapter?: HistoryAdapter) {
        super();
        this.historyAdapter = historyAdapter || this.createDefaultHistoryAdapter();
        this.setupRouteListener();
    }

  /**
   * 启动路由管理器
   */
  start(): void {
    if (this.isStarted) {
      return;
    }

    this.isStarted = true;
    this.setupRouteListeners();
    this.handleRouteChange();
    
    logger.info('路由管理器已启动');
  }

  /**
   * 停止路由管理器
   */
  stop(): void {
    if (!this.isStarted) {
      return;
    }

    this.isStarted = false;
    this.removeRouteListeners();
    
    logger.info('路由管理器已停止');
  }

  /**
   * 注册路由
   */
  registerRoute(config: RouteConfig): void {
    this.routes.set(config.appName, config);
    logger.debug(`注册路由: ${config.path} -> ${config.appName}`);

    // 如果路由管理器已启动，检查当前路由
    if (this.isStarted) {
      this.handleRouteChange();
    }
  }

  /**
   * 注销路由
   */
  unregisterRoute(appName: string): void {
    if (this.routes.has(appName)) {
      this.routes.delete(appName);
      logger.debug(`注销路由: ${appName}`);
    }
  }

  /**
   * 导航到指定路径
   */
  navigate(path: string, replace = false): void {
    if (replace) {
      window.history.replaceState(null, '', path);
    } else {
      window.history.pushState(null, '', path);
    }
    
    this.handleRouteChange();
  }

  /**
   * 获取当前激活的应用
   */
  getCurrentApp(): string | null {
    return this.currentApp;
  }

  /**
   * 获取当前路径
   */
  getCurrentPath(): string {
    return window.location.pathname + window.location.search + window.location.hash;
  }

  /**
   * 添加路由变化监听器
   */
  addListener(listener: (event: RouteChangeEvent) => void): void {
    this.listeners.add(listener);
  }

  /**
   * 移除路由变化监听器
   */
  removeListener(listener: (event: RouteChangeEvent) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * 检查路径是否匹配应用
   */
  isAppActive(appName: string, location = window.location): boolean {
    const route = this.routes.get(appName);
    if (!route) {
      return false;
    }

    return this.matchRoute(route, location);
  }

  /**
   * 获取匹配的应用名称
   */
  getMatchedApp(location = window.location): string | null {
    for (const [appName, route] of this.routes) {
      if (this.matchRoute(route, location)) {
        return appName;
      }
    }
    return null;
  }

  /**
   * 设置路由监听器
   */
  private setupRouteListeners(): void {
    // 监听 popstate 事件（浏览器前进后退）
    window.addEventListener('popstate', this.handleRouteChange);
    
    // 监听 pushState 和 replaceState
    this.patchHistoryMethods();
  }

  /**
   * 移除路由监听器
   */
  private removeRouteListeners(): void {
    window.removeEventListener('popstate', this.handleRouteChange);
    this.restoreHistoryMethods();
  }

  /**
   * 处理路由变化
   */
  private handleRouteChange = (): void => {
    const currentPath = this.getCurrentPath();
    const matchedApp = this.getMatchedApp();
    
    logger.debug(`路由变化: ${currentPath}, 匹配应用: ${matchedApp || 'none'}`);

    // 如果匹配的应用发生变化
    if (matchedApp !== this.currentApp) {
      const previousApp = this.currentApp;
      this.currentApp = matchedApp;

      // 触发路由变化事件
      const event: RouteChangeEvent = {
        type: 'route-change',
        path: currentPath,
        previousApp,
        currentApp: matchedApp,
        timestamp: Date.now()
      };

      this.notifyListeners(event);
    }
  };

  /**
   * 通知监听器
   */
  private notifyListeners(event: RouteChangeEvent): void {
    for (const listener of this.listeners) {
      try {
        listener(event);
      } catch (error) {
        logger.error('路由监听器执行失败', error);
      }
    }
  }

  /**
   * 匹配路由
   */
  private matchRoute(route: RouteConfig, location: Location): boolean {
    const { path, exact = false, activeWhen } = route;
    const currentPath = location.pathname;

    // 如果有自定义匹配函数
    if (activeWhen) {
      if (typeof activeWhen === 'function') {
        return activeWhen(location);
      } else if (activeWhen instanceof RegExp) {
        return activeWhen.test(currentPath);
      } else if (typeof activeWhen === 'string') {
        return exact ? currentPath === activeWhen : currentPath.startsWith(activeWhen);
      }
    }

    // 默认路径匹配
    if (exact) {
      return currentPath === path;
    } else {
      return currentPath.startsWith(path);
    }
  }

  /**
   * 修补 History API 方法
   */
  private originalPushState = window.history.pushState;
  private originalReplaceState = window.history.replaceState;

  private patchHistoryMethods(): void {
    const self = this;
    
    window.history.pushState = function(state, title, url) {
      self.originalPushState.call(this, state, title, url);
      self.handleRouteChange();
    };

    window.history.replaceState = function(state, title, url) {
      self.originalReplaceState.call(this, state, title, url);
      self.handleRouteChange();
    };
  }

  /**
   * 恢复 History API 方法
   */
  private restoreHistoryMethods(): void {
    window.history.pushState = this.originalPushState;
    window.history.replaceState = this.originalReplaceState;
  }

  /**
   * 获取路由统计信息
   */
  getStats(): {
    totalRoutes: number;
    currentApp: string | null;
    currentPath: string;
    routes: Array<{ appName: string; path: string; active: boolean }>;
  } {
    return {
      totalRoutes: this.routes.size,
      currentApp: this.currentApp,
      currentPath: this.getCurrentPath(),
      routes: Array.from(this.routes.entries()).map(([appName, route]) => ({
        appName,
        path: route.path,
        active: appName === this.currentApp
      }))
    };
  }

  /**
   * 清除所有路由
   */
  clearRoutes(): void {
    this.routes.clear();
    this.currentApp = null;
    logger.debug('清除所有路由');
  }
}