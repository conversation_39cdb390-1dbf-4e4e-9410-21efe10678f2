/**
 * @fileoverview 错误处理类和错误码定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 错误码枚举
 */
export const ERROR_CODES = {
    // 通用错误
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    OPERATION_FAILED: 'OPERATION_FAILED',

    // 应用相关错误
    APPLICATION_ERROR: 'APPLICATION_ERROR',
    APPLICATION_NOT_FOUND: 'APPLICATION_NOT_FOUND',
    APPLICATION_ALREADY_EXISTS: 'APPLICATION_ALREADY_EXISTS',
    APPLICATION_STILL_MOUNTED: 'APPLICATION_STILL_MOUNTED',
    APPLICATION_LOAD_FAILED: 'APPLICATION_LOAD_FAILED',
    APPLICATION_MOUNT_FAILED: 'APPLICATION_MOUNT_FAILED',
    APPLICATION_UNMOUNT_FAILED: 'APPLICATION_UNMOUNT_FAILED',

    // 生命周期错误
    LIFECYCLE_ERROR: 'LIFECYCLE_ERROR',
    LIFECYCLE_BOOTSTRAP_FAILED: 'LIFECYCLE_BOOTSTRAP_FAILED',
    LIFECYCLE_MOUNT_FAILED: 'LIFECYCLE_MOUNT_FAILED',
    LIFECYCLE_UNMOUNT_FAILED: 'LIFECYCLE_UNMOUNT_FAILED',
    LIFECYCLE_UPDATE_FAILED: 'LIFECYCLE_UPDATE_FAILED',
    LIFECYCLE_TIMEOUT: 'LIFECYCLE_TIMEOUT',

    // 插件相关错误
    PLUGIN_ERROR: 'PLUGIN_ERROR',
    PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
    PLUGIN_ALREADY_EXISTS: 'PLUGIN_ALREADY_EXISTS',
    PLUGIN_INSTALL_FAILED: 'PLUGIN_INSTALL_FAILED',
    PLUGIN_UNINSTALL_FAILED: 'PLUGIN_UNINSTALL_FAILED',

    // 沙箱相关错误
    SANDBOX_ERROR: 'SANDBOX_ERROR',
    SANDBOX_CREATE_FAILED: 'SANDBOX_CREATE_FAILED',
    SANDBOX_DESTROY_FAILED: 'SANDBOX_DESTROY_FAILED',
    SANDBOX_SCRIPT_EXECUTION_FAILED: 'SANDBOX_SCRIPT_EXECUTION_FAILED',

    // 路由相关错误
    ROUTER_ERROR: 'ROUTER_ERROR',
    ROUTER_NAVIGATION_FAILED: 'ROUTER_NAVIGATION_FAILED',
    ROUTER_GUARD_REJECTED: 'ROUTER_GUARD_REJECTED',

    // 通信相关错误
    COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
    EVENT_HANDLER_ERROR: 'EVENT_HANDLER_ERROR',
    STATE_UPDATE_FAILED: 'STATE_UPDATE_FAILED',

    // 资源相关错误
    RESOURCE_ERROR: 'RESOURCE_ERROR',
    RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
    RESOURCE_PARSE_FAILED: 'RESOURCE_PARSE_FAILED',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND'
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * Micro-Core 自定义错误类
 */
export class MicroCoreError extends Error {
    public readonly code: ErrorCode;
    public readonly timestamp: number;
    public readonly context?: Record<string, any>;

    constructor(
        code: ErrorCode,
        message: string,
        context?: Record<string, any>,
        cause?: Error
    ) {
        super(message);

        this.name = 'MicroCoreError';
        this.code = code;
        this.timestamp = Date.now();
        this.context = context;

        // 设置错误原因（如果支持）
        if (cause && 'cause' in Error.prototype) {
            (this as any).cause = cause;
        }

        // 确保堆栈跟踪正确
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 转换为JSON格式
     */
    toJSON(): Record<string, any> {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            timestamp: this.timestamp,
            context: this.context || {},
            stack: this.stack || ''
        };
    }

    /**
     * 创建应用错误
     */
    static createApplicationError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.APPLICATION_ERROR, message, context);
    }

    /**
     * 创建生命周期错误
     */
    static createLifecycleError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.LIFECYCLE_ERROR, message, context);
    }

    /**
     * 创建插件错误
     */
    static createPluginError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.PLUGIN_ERROR, message, context);
    }

    /**
     * 创建沙箱错误
     */
    static createSandboxError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.SANDBOX_ERROR, message, context);
    }

    /**
     * 创建路由错误
     */
    static createRouterError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.ROUTER_ERROR, message, context);
    }

    /**
     * 创建通信错误
     */
    static createCommunicationError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.COMMUNICATION_ERROR, message, context);
    }

    /**
     * 创建资源错误
     */
    static createResourceError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.RESOURCE_ERROR, message, context);
    }
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
    (error: MicroCoreError, context?: Record<string, any>): void;
}

/**
 * 默认错误处理器
 */
export const defaultErrorHandler: ErrorHandler = (error: MicroCoreError, context?: Record<string, any>) => {
    console.error('[MicroCore Error]', {
        code: error.code,
        message: error.message,
        timestamp: new Date(error.timestamp).toISOString(),
        context: { ...error.context, ...context },
        stack: error.stack || ''
    });
};

/**
 * 错误处理工具函数
 */
export class ErrorUtils {
    /**
     * 包装异步函数，自动捕获错误
     */
    static async wrapAsync<T>(
        fn: () => Promise<T>,
        errorCode: ErrorCode,
        errorMessage: string,
        context?: Record<string, any>
    ): Promise<T> {
        try {
            return await fn();
        } catch (error) {
            throw new MicroCoreError(
                errorCode,
                errorMessage,
                context,
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 包装同步函数，自动捕获错误
     */
    static wrapSync<T>(
        fn: () => T,
        errorCode: ErrorCode,
        errorMessage: string,
        context?: Record<string, any>
    ): T {
        try {
            return fn();
        } catch (error) {
            throw new MicroCoreError(
                errorCode,
                errorMessage,
                context,
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 检查是否为 MicroCore 错误
     */
    static isMicroCoreError(error: any): error is MicroCoreError {
        return error instanceof MicroCoreError;
    }

    /**
     * 格式化错误信息
     */
    static formatError(error: Error): string {
        if (ErrorUtils.isMicroCoreError(error)) {
            return `[${error.code}] ${error.message}`;
        }
        return error.message;
    }
}