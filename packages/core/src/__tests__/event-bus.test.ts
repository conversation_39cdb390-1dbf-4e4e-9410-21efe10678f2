import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../event-bus';

describe('EventBus 事件总线测试', () => {
    let eventBus: EventBus;

    beforeEach(() => {
        eventBus = new EventBus();
    });

    describe('基本功能', () => {
        it('应该能够注册和触发事件', () => {
            const mockCallback = vi.fn();

            eventBus.on('test-event', mockCallback);
            eventBus.emit('test-event', { data: 'test' });

            expect(mockCallback).toHaveBeenCalledTimes(1);
            expect(mockCallback).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该能够注册多个监听器', () => {
            const mockCallback1 = vi.fn();
            const mockCallback2 = vi.fn();

            eventBus.on('test-event', mockCallback1);
            eventBus.on('test-event', mockCallback2);
            eventBus.emit('test-event', 'data');

            expect(mockCallback1).toHaveBeenCalledWith('data');
            expect(mockCallback2).toHaveBeenCalledWith('data');
        });

        it('应该能够移除事件监听器', () => {
            const mockCallback = vi.fn();

            eventBus.on('test-event', mockCallback);
            eventBus.off('test-event', mockCallback);
            eventBus.emit('test-event', 'data');

            expect(mockCallback).not.toHaveBeenCalled();
        });

        it('应该能够移除所有监听器', () => {
            const mockCallback1 = vi.fn();
            const mockCallback2 = vi.fn();

            eventBus.on('test-event', mockCallback1);
            eventBus.on('test-event', mockCallback2);
            eventBus.off('test-event');
            eventBus.emit('test-event', 'data');

            expect(mockCallback1).not.toHaveBeenCalled();
            expect(mockCallback2).not.toHaveBeenCalled();
        });
    });

    describe('once 方法', () => {
        it('应该只触发一次', () => {
            const mockCallback = vi.fn();

            eventBus.once('test-event', mockCallback);
            eventBus.emit('test-event', 'data1');
            eventBus.emit('test-event', 'data2');

            expect(mockCallback).toHaveBeenCalledTimes(1);
            expect(mockCallback).toHaveBeenCalledWith('data1');
        });
    });

    describe('错误处理', () => {
        it('应该处理监听器中的错误', () => {
            const errorCallback = vi.fn(() => {
                throw new Error('Test error');
            });
            const normalCallback = vi.fn();

            eventBus.on('test-event', errorCallback);
            eventBus.on('test-event', normalCallback);

            // 不应该抛出错误
            expect(() => {
                eventBus.emit('test-event', 'data');
            }).not.toThrow();

            expect(errorCallback).toHaveBeenCalled();
            expect(normalCallback).toHaveBeenCalled();
        });
    });

    describe('通配符支持', () => {
        it('应该支持通配符事件', () => {
            const mockCallback = vi.fn();

            eventBus.on('*', mockCallback);
            eventBus.emit('any-event', 'data');

            expect(mockCallback).toHaveBeenCalledWith('data', 'any-event');
        });

        it('应该支持命名空间通配符', () => {
            const mockCallback = vi.fn();

            eventBus.on('app:*', mockCallback);
            eventBus.emit('app:mounted', 'data');
            eventBus.emit('app:unmounted', 'data2');
            eventBus.emit('other:event', 'data3');

            expect(mockCallback).toHaveBeenCalledTimes(2);
            expect(mockCallback).toHaveBeenNthCalledWith(1, 'data', 'app:mounted');
            expect(mockCallback).toHaveBeenNthCalledWith(2, 'data2', 'app:unmounted');
        });
    });

    describe('性能测试', () => {
        it('应该能够处理大量事件', () => {
            const mockCallback = vi.fn();

            eventBus.on('test-event', mockCallback);

            const startTime = Date.now();
            for (let i = 0; i < 10000; i++) {
                eventBus.emit('test-event', i);
            }
            const endTime = Date.now();

            expect(mockCallback).toHaveBeenCalledTimes(10000);
            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
        });
    });

    describe('内存泄漏防护', () => {
        it('应该能够清理所有监听器', () => {
            const mockCallback1 = vi.fn();
            const mockCallback2 = vi.fn();

            eventBus.on('event1', mockCallback1);
            eventBus.on('event2', mockCallback2);

            eventBus.clear();

            eventBus.emit('event1', 'data');
            eventBus.emit('event2', 'data');

            expect(mockCallback1).not.toHaveBeenCalled();
            expect(mockCallback2).not.toHaveBeenCalled();
        });
    });
});