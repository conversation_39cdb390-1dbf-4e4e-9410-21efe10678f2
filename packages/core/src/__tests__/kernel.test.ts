/**
 * 微内核测试用例
 */

import { MicroCoreKernel } from '../kernel';
import type { MicroAppConfig, Plugin } from '../types';

describe('MicroCoreKernel', () => {
    let kernel: MicroCoreKernel;

    beforeEach(() => {
        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        kernel.destroy();
    });

    describe('插件系统', () => {
        it('应该能够安装插件', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: jest.fn(),
                uninstall: jest.fn()
            };

            kernel.use(mockPlugin);
            expect(mockPlugin.install).toHaveBeenCalledWith(kernel);
        });

        it('应该能够卸载插件', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: jest.fn(),
                uninstall: jest.fn()
            };

            kernel.use(mockPlugin);
            kernel.unuse('test-plugin');
            expect(mockPlugin.uninstall).toHaveBeenCalledWith(kernel);
        });

        it('应该防止重复安装同名插件', () => {
            const mockPlugin1: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: jest.fn(),
                uninstall: jest.fn()
            };

            const mockPlugin2: Plugin = {
                name: 'test-plugin',
                version: '2.0.0',
                install: jest.fn(),
                uninstall: jest.fn()
            };

            kernel.use(mockPlugin1);
            kernel.use(mockPlugin2);

            expect(mockPlugin1.install).toHaveBeenCalledTimes(1);
            expect(mockPlugin2.install).not.toHaveBeenCalled();
        });
    });

    describe('应用管理', () => {
        it('应该能够注册应用', () => {
            const appConfig: MicroAppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#app',
                activeWhen: '/test'
            };

            kernel.registerApp(appConfig);
            const apps = kernel.getApps();
            expect(apps).toHaveLength(1);
            expect(apps[0].name).toBe('test-app');
        });

        it('应该能够卸载应用', () => {
            const appConfig: MicroAppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#app',
                activeWhen: '/test'
            };

            kernel.registerApp(appConfig);
            kernel.unregisterApp('test-app');
            const apps = kernel.getApps();
            expect(apps).toHaveLength(0);
        });

        it('应该防止重复注册同名应用', () => {
            const appConfig1: MicroAppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#app',
                activeWhen: '/test'
            };

            const appConfig2: MicroAppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3002',
                container: '#app2',
                activeWhen: '/test2'
            };

            kernel.registerApp(appConfig1);
            expect(() => kernel.registerApp(appConfig2)).toThrow();
        });
    });

    describe('生命周期管理', () => {
        it('应该能够启动内核', async () => {
            await kernel.start();
            expect(kernel.isStarted()).toBe(true);
        });

        it('应该能够停止内核', async () => {
            await kernel.start();
            await kernel.stop();
            expect(kernel.isStarted()).toBe(false);
        });

        it('应该能够销毁内核', () => {
            kernel.destroy();
            expect(kernel.isDestroyed()).toBe(true);
        });
    });

    describe('事件系统', () => {
        it('应该能够监听和触发事件', () => {
            const mockHandler = jest.fn();
            kernel.on('test-event', mockHandler);
            kernel.emit('test-event', { data: 'test' });
            expect(mockHandler).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该能够移除事件监听器', () => {
            const mockHandler = jest.fn();
            kernel.on('test-event', mockHandler);
            kernel.off('test-event', mockHandler);
            kernel.emit('test-event', { data: 'test' });
            expect(mockHandler).not.toHaveBeenCalled();
        });

        it('应该能够监听一次性事件', () => {
            const mockHandler = jest.fn();
            kernel.once('test-event', mockHandler);
            kernel.emit('test-event', { data: 'test1' });
            kernel.emit('test-event', { data: 'test2' });
            expect(mockHandler).toHaveBeenCalledTimes(1);
            expect(mockHandler).toHaveBeenCalledWith({ data: 'test1' });
        });
    });

    describe('沙箱管理', () => {
        it('应该能够注册沙箱创建器', () => {
            const mockCreator = jest.fn();
            kernel.registerSandboxCreator?.('test-sandbox', mockCreator);

            // 这里需要实际的沙箱管理器实现来测试
            // expect(kernel.getSandboxCreator('test-sandbox')).toBe(mockCreator);
        });
    });

    describe('适配器管理', () => {
        it('应该能够注册适配器', () => {
            const mockAdapter = {
                createLifecycles: jest.fn()
            };

            kernel.registerAdapter?.('test-framework', mockAdapter);

            // 这里需要实际的适配器管理器实现来测试
            // expect(kernel.getAdapter('test-framework')).toBe(mockAdapter);
        });
    });
});