import { describe, expect, it } from 'vitest';
import {
    deepClone,
    deepMerge,
    generateId,
    isFunction,
    isObject,
    isString
} from '../utils';

describe('Utils 工具函数测试', () => {
    describe('generateId', () => {
        it('应该生成唯一ID', () => {
            const id1 = generateId();
            const id2 = generateId();
            expect(id1).not.toBe(id2);
            expect(id1).toMatch(/^id_\d+_[a-z0-9]+$/);
        });

        it('应该支持自定义前缀', () => {
            const id = generateId('test');
            expect(id).toMatch(/^test_\d+_[a-z0-9]+$/);
        });
    });

    describe('类型检查函数', () => {
        it('isFunction 应该正确识别函数', () => {
            expect(isFunction(() => { })).toBe(true);
            expect(isFunction(function () { })).toBe(true);
            expect(isFunction('string')).toBe(false);
            expect(isFunction({})).toBe(false);
            expect(isFunction(null)).toBe(false);
        });

        it('isString 应该正确识别字符串', () => {
            expect(isString('hello')).toBe(true);
            expect(isString('')).toBe(true);
            expect(isString(123)).toBe(false);
            expect(isString({})).toBe(false);
            expect(isString(null)).toBe(false);
        });

        it('isObject 应该正确识别对象', () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject([])).toBe(false);
            expect(isObject(null)).toBe(false);
            expect(isObject('string')).toBe(false);
            expect(isObject(123)).toBe(false);
        });
    });

    describe('deepClone', () => {
        it('应该深度克隆对象', () => {
            const original = {
                a: 1,
                b: {
                    c: 2,
                    d: [3, 4, { e: 5 }]
                }
            };

            const cloned = deepClone(original);

            expect(cloned).toEqual(original);
            expect(cloned).not.toBe(original);
            expect(cloned.b).not.toBe(original.b);
            expect(cloned.b.d).not.toBe(original.b.d);
            expect(cloned.b.d[2]).not.toBe(original.b.d[2]);
        });

        it('应该处理基本类型', () => {
            expect(deepClone(123)).toBe(123);
            expect(deepClone('string')).toBe('string');
            expect(deepClone(true)).toBe(true);
            expect(deepClone(null)).toBe(null);
            expect(deepClone(undefined)).toBe(undefined);
        });

        it('应该处理日期对象', () => {
            const date = new Date();
            const cloned = deepClone(date);

            expect(cloned).toEqual(date);
            expect(cloned).not.toBe(date);
        });

        it('应该处理数组', () => {
            const arr = [1, 2, { a: 3 }];
            const cloned = deepClone(arr);

            expect(cloned).toEqual(arr);
            expect(cloned).not.toBe(arr);
            expect(cloned[2]).not.toBe(arr[2]);
        });
    });

    describe('deepMerge', () => {
        it('应该深度合并对象', () => {
            const target = {
                a: 1,
                b: {
                    c: 2,
                    d: 3
                }
            };

            const source = {
                b: {
                    d: 4,
                    e: 5
                },
                f: 6
            };

            const result = deepMerge(target, source);

            expect(result).toEqual({
                a: 1,
                b: {
                    c: 2,
                    d: 4,
                    e: 5
                },
                f: 6
            });
        });

        it('应该处理多个源对象', () => {
            const target = { a: 1 };
            const source1 = { b: 2 };
            const source2 = { c: 3 };

            const result = deepMerge(target, source1, source2);

            expect(result).toEqual({
                a: 1,
                b: 2,
                c: 3
            });
        });

        it('应该返回目标对象本身', () => {
            const target = { a: 1 };
            const source = { b: 2 };

            const result = deepMerge(target, source);

            expect(result).toBe(target);
        });
    });
});