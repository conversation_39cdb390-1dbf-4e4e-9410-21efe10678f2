{"name": "@micro-core/core", "version": "0.1.0", "description": "Micro-Core 核心运行时 - 微前端应用的核心引擎，提供应用注册、生命周期管理、沙箱隔离等核心功能", "keywords": ["micro-core", "micro-frontend", "microfrontend", "core", "runtime", "sandbox", "lifecycle"], "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/core"}, "license": "MIT", "author": {"name": "Echo", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsup", "build:watch": "tsup --watch", "clean": "rm -rf dist", "dev": "tsup --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "type-check": "tsc --noEmit"}, "dependencies": {"@micro-core/shared": "workspace:*", "eventemitter3": "^5.0.1"}, "devDependencies": {"@micro-core/eslint-config": "workspace:*", "@micro-core/ts-config": "workspace:*", "@types/node": "^20.10.6", "@vitest/coverage-v8": "^1.1.3", "@vitest/ui": "^1.1.3", "eslint": "^8.56.0", "jsdom": "^23.0.1", "tsup": "^8.0.1", "typescript": "^5.3.3", "vitest": "^1.1.3"}, "publishConfig": {"access": "public"}}