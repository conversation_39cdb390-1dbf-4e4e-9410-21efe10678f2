/**
 * @fileoverview 微前端内核测试
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MicroCoreKernel } from '../src/kernel';
import type { AppInfo, KernelConfig } from '../src/types';

describe('MicroCoreKernel', () => {
    let kernel: MicroCoreKernel;
    let mockAppInfo: AppInfo;

    beforeEach(() => {
        const config: KernelConfig = {
            sandbox: {
                enabled: true,
                strictStyleIsolation: true
            },
            prefetch: {
                enabled: true,
                strategy: 'idle'
            }
        };

        kernel = new MicroCoreKernel(config);

        mockAppInfo = {
            name: 'test-app',
            entry: 'http://localhost:3001',
            container: '#test-container',
            activeRule: '/test-app',
            props: {
                title: '测试应用'
            }
        };
    });

    afterEach(() => {
        if (kernel) {
            kernel.destroy();
        }
    });

    describe('初始化', () => {
        it('应该正确初始化内核', () => {
            expect(kernel).toBeDefined();
            expect(kernel.getStatus()).toBe('idle');
        });

        it('应该正确设置配置', () => {
            const config = kernel.getConfig();
            expect(config.sandbox?.enabled).toBe(true);
            expect(config.prefetch?.enabled).toBe(true);
        });
    });

    describe('应用注册', () => {
        it('应该成功注册应用', async () => {
            await kernel.registerApp(mockAppInfo);
            const apps = kernel.getApps();
            expect(apps).toHaveLength(1);
            expect(apps[0].name).toBe('test-app');
        });

        it('应该拒绝重复注册同名应用', async () => {
            await kernel.registerApp(mockAppInfo);
            await expect(kernel.registerApp(mockAppInfo)).rejects.toThrow('应用已存在');
        });

        it('应该验证应用配置', async () => {
            const invalidApp = { ...mockAppInfo, name: '' };
            await expect(kernel.registerApp(invalidApp)).rejects.toThrow('应用名称不能为空');
        });
    });

    describe('应用生命周期', () => {
        beforeEach(async () => {
            await kernel.registerApp(mockAppInfo);
            await kernel.start();
        });

        it('应该成功挂载应用', async () => {
            // 模拟DOM容器
            const container = document.createElement('div');
            container.id = 'test-container';
            document.body.appendChild(container);

            await kernel.mountApp('test-app');
            expect(kernel.getAppStatus('test-app')).toBe('mounted');

            document.body.removeChild(container);
        });

        it('应该成功卸载应用', async () => {
            const container = document.createElement('div');
            container.id = 'test-container';
            document.body.appendChild(container);

            await kernel.mountApp('test-app');
            await kernel.unmountApp('test-app');
            expect(kernel.getAppStatus('test-app')).toBe('unmounted');

            document.body.removeChild(container);
        });

        it('应该处理应用挂载失败', async () => {
            // 不创建容器，模拟挂载失败
            await expect(kernel.mountApp('test-app')).rejects.toThrow();
        });
    });

    describe('事件系统', () => {
        it('应该正确触发和监听事件', () => {
            const mockListener = vi.fn();
            kernel.on('test-event', mockListener);

            kernel.emit('test-event', { data: 'test' });
            expect(mockListener).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该支持移除事件监听器', () => {
            const mockListener = vi.fn();
            kernel.on('test-event', mockListener);
            kernel.off('test-event', mockListener);

            kernel.emit('test-event', { data: 'test' });
            expect(mockListener).not.toHaveBeenCalled();
        });

        it('应该触发应用生命周期事件', async () => {
            const mountListener = vi.fn();
            const unmountListener = vi.fn();

            kernel.on('app:mounted', mountListener);
            kernel.on('app:unmounted', unmountListener);

            await kernel.registerApp(mockAppInfo);
            await kernel.start();

            const container = document.createElement('div');
            container.id = 'test-container';
            document.body.appendChild(container);

            await kernel.mountApp('test-app');
            expect(mountListener).toHaveBeenCalled();

            await kernel.unmountApp('test-app');
            expect(unmountListener).toHaveBeenCalled();

            document.body.removeChild(container);
        });
    });

    describe('适配器管理', () => {
        it('应该成功注册适配器', () => {
            const mockAdapter = {
                name: 'test-adapter',
                version: '1.0.0',
                framework: 'test',
                bootstrap: vi.fn(),
                mount: vi.fn(),
                unmount: vi.fn()
            };

            kernel.registerAdapter('test', mockAdapter);
            const adapter = kernel.getAdapter('test');
            expect(adapter).toBe(mockAdapter);
        });

        it('应该拒绝重复注册同名适配器', () => {
            const mockAdapter = {
                name: 'test-adapter',
                version: '1.0.0',
                framework: 'test',
                bootstrap: vi.fn(),
                mount: vi.fn(),
                unmount: vi.fn()
            };

            kernel.registerAdapter('test', mockAdapter);
            expect(() => kernel.registerAdapter('test', mockAdapter)).toThrow('适配器已存在');
        });
    });

    describe('构建器管理', () => {
        it('应该成功注册构建器', () => {
            const mockBuilder = {
                name: 'test-builder',
                version: '1.0.0',
                type: 'test' as const,
                build: vi.fn(),
                serve: vi.fn(),
                stop: vi.fn()
            };

            kernel.registerBuilder('test', mockBuilder);
            const builder = kernel.getBuilder('test');
            expect(builder).toBe(mockBuilder);
        });
    });

    describe('内核状态管理', () => {
        it('应该正确管理内核状态', async () => {
            expect(kernel.getStatus()).toBe('idle');

            await kernel.start();
            expect(kernel.getStatus()).toBe('running');

            kernel.destroy();
            expect(kernel.getStatus()).toBe('destroyed');
        });

        it('应该防止重复启动', async () => {
            await kernel.start();
            await expect(kernel.start()).rejects.toThrow('内核已启动');
        });
    });

    describe('错误处理', () => {
        it('应该正确处理应用加载错误', async () => {
            const errorListener = vi.fn();
            kernel.on('app:error', errorListener);

            const invalidApp = {
                ...mockAppInfo,
                entry: 'invalid-url'
            };

            await kernel.registerApp(invalidApp);
            await kernel.start();

            await expect(kernel.mountApp('test-app')).rejects.toThrow();
            expect(errorListener).toHaveBeenCalled();
        });

        it('应该处理内核销毁', () => {
            kernel.destroy();
            expect(kernel.getStatus()).toBe('destroyed');
            expect(() => kernel.emit('test', {})).toThrow('内核已销毁');
        });
    });

    describe('配置更新', () => {
        it('应该支持运行时配置更新', () => {
            const newConfig = {
                sandbox: {
                    enabled: false,
                    strictStyleIsolation: false
                }
            };

            kernel.updateConfig(newConfig);
            const config = kernel.getConfig();
            expect(config.sandbox?.enabled).toBe(false);
        });
    });
});