/**
 * @fileoverview 生命周期管理器测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { LifecycleManager } from '../src/lifecycle-manager';
import type { AppInfo, LifecycleHook } from '../src/types';

describe('LifecycleManager', () => {
    let lifecycleManager: LifecycleManager;
    let mockAppInfo: AppInfo;

    beforeEach(() => {
        lifecycleManager = new LifecycleManager();
        mockAppInfo = {
            name: 'test-app',
            entry: 'http://localhost:3001',
            container: '#test-container',
            activeRule: '/test-app'
        };
    });

    describe('钩子注册', () => {
        it('应该成功注册生命周期钩子', () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'bootstrap', mockHook);

            const hooks = lifecycleManager.getHooks('test-app', 'bootstrap');
            expect(hooks).toHaveLength(1);
            expect(hooks[0]).toBe(mockHook);
        });

        it('应该支持注册多个钩子', () => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'bootstrap', hook2);

            const hooks = lifecycleManager.getHooks('test-app', 'bootstrap');
            expect(hooks).toHaveLength(2);
        });

        it('应该支持不同应用的同名钩子', () => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('app1', 'bootstrap', hook1);
            lifecycleManager.registerHook('app2', 'bootstrap', hook2);

            expect(lifecycleManager.getHooks('app1', 'bootstrap')).toHaveLength(1);
            expect(lifecycleManager.getHooks('app2', 'bootstrap')).toHaveLength(1);
        });
    });

    describe('钩子执行', () => {
        it('应该按顺序执行所有钩子', async () => {
            const executionOrder: number[] = [];
            const hook1: LifecycleHook = vi.fn(async () => {
                executionOrder.push(1);
            });
            const hook2: LifecycleHook = vi.fn(async () => {
                executionOrder.push(2);
            });

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'bootstrap', hook2);

            await lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo);

            expect(executionOrder).toEqual([1, 2]);
            expect(hook1).toHaveBeenCalledWith(mockAppInfo);
            expect(hook2).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该处理异步钩子', async () => {
            const asyncHook: LifecycleHook = vi.fn(async (appInfo) => {
                await new Promise(resolve => setTimeout(resolve, 10));
                return { success: true, appName: appInfo.name };
            });

            lifecycleManager.registerHook('test-app', 'bootstrap', asyncHook);

            await lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo);
            expect(asyncHook).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该在钩子执行失败时抛出错误', async () => {
            const errorHook: LifecycleHook = vi.fn(async () => {
                throw new Error('钩子执行失败');
            });

            lifecycleManager.registerHook('test-app', 'bootstrap', errorHook);

            await expect(
                lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo)
            ).rejects.toThrow('钩子执行失败');
        });

        it('应该在某个钩子失败时停止执行后续钩子', async () => {
            const hook1: LifecycleHook = vi.fn(async () => {
                throw new Error('第一个钩子失败');
            });
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'bootstrap', hook2);

            await expect(
                lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo)
            ).rejects.toThrow('第一个钩子失败');

            expect(hook1).toHaveBeenCalled();
            expect(hook2).not.toHaveBeenCalled();
        });
    });

    describe('钩子移除', () => {
        it('应该成功移除指定钩子', () => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'bootstrap', hook2);

            lifecycleManager.removeHook('test-app', 'bootstrap', hook1);

            const hooks = lifecycleManager.getHooks('test-app', 'bootstrap');
            expect(hooks).toHaveLength(1);
            expect(hooks[0]).toBe(hook2);
        });

        it('应该移除应用的所有钩子', () => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'mount', hook2);

            lifecycleManager.removeAppHooks('test-app');

            expect(lifecycleManager.getHooks('test-app', 'bootstrap')).toHaveLength(0);
            expect(lifecycleManager.getHooks('test-app', 'mount')).toHaveLength(0);
        });

        it('应该移除特定生命周期的所有钩子', () => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('app1', 'bootstrap', hook1);
            lifecycleManager.registerHook('app2', 'bootstrap', hook2);

            lifecycleManager.removeLifecycleHooks('bootstrap');

            expect(lifecycleManager.getHooks('app1', 'bootstrap')).toHaveLength(0);
            expect(lifecycleManager.getHooks('app2', 'bootstrap')).toHaveLength(0);
        });
    });

    describe('生命周期通知', () => {
        it('应该正确通知应用启动', async () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'bootstrap', mockHook);

            await lifecycleManager.notifyBootstrap(mockAppInfo);
            expect(mockHook).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该正确通知应用挂载', async () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'mount', mockHook);

            await lifecycleManager.notifyMounted(mockAppInfo);
            expect(mockHook).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该正确通知应用卸载', async () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'unmount', mockHook);

            await lifecycleManager.notifyUnmounted(mockAppInfo);
            expect(mockHook).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该正确通知应用更新', async () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'update', mockHook);

            const updateProps = { theme: 'dark' };
            await lifecycleManager.notifyUpdated(mockAppInfo, updateProps);
            expect(mockHook).toHaveBeenCalledWith(mockAppInfo, updateProps);
        });

        it('应该正确通知应用错误', async () => {
            const mockHook: LifecycleHook = vi.fn();
            lifecycleManager.registerHook('test-app', 'error', mockHook);

            const error = new Error('测试错误');
            await lifecycleManager.notifyError(mockAppInfo, error);
            expect(mockHook).toHaveBeenCalledWith(mockAppInfo, error);
        });
    });

    describe('钩子查询', () => {
        beforeEach(() => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'mount', hook2);
        });

        it('应该正确获取应用的所有钩子', () => {
            const allHooks = lifecycleManager.getAllHooks('test-app');
            expect(allHooks.bootstrap).toHaveLength(1);
            expect(allHooks.mount).toHaveLength(1);
            expect(allHooks.unmount).toHaveLength(0);
        });

        it('应该检查应用是否有特定生命周期钩子', () => {
            expect(lifecycleManager.hasHooks('test-app', 'bootstrap')).toBe(true);
            expect(lifecycleManager.hasHooks('test-app', 'unmount')).toBe(false);
        });

        it('应该获取钩子统计信息', () => {
            const stats = lifecycleManager.getHookStats();
            expect(stats.totalApps).toBe(1);
            expect(stats.totalHooks).toBe(2);
            expect(stats.hooksByLifecycle.bootstrap).toBe(1);
            expect(stats.hooksByLifecycle.mount).toBe(1);
        });
    });

    describe('错误处理', () => {
        it('应该处理钩子执行超时', async () => {
            const timeoutHook: LifecycleHook = vi.fn(async () => {
                await new Promise(resolve => setTimeout(resolve, 6000)); // 超过默认超时时间
            });

            lifecycleManager.registerHook('test-app', 'bootstrap', timeoutHook);

            await expect(
                lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo, { timeout: 1000 })
            ).rejects.toThrow('钩子执行超时');
        });

        it('应该收集钩子执行错误', async () => {
            const error1 = new Error('错误1');
            const error2 = new Error('错误2');

            const hook1: LifecycleHook = vi.fn(async () => { throw error1; });
            const hook2: LifecycleHook = vi.fn(async () => { throw error2; });

            lifecycleManager.registerHook('test-app', 'bootstrap', hook1);
            lifecycleManager.registerHook('test-app', 'bootstrap', hook2);

            try {
                await lifecycleManager.executeHooks('test-app', 'bootstrap', mockAppInfo, {
                    continueOnError: true
                });
            } catch (error) {
                // 应该收集所有错误
                expect(error).toBeInstanceOf(Error);
            }

            expect(hook1).toHaveBeenCalled();
            expect(hook2).toHaveBeenCalled();
        });
    });

    describe('清理操作', () => {
        beforeEach(() => {
            const hook1: LifecycleHook = vi.fn();
            const hook2: LifecycleHook = vi.fn();

            lifecycleManager.registerHook('app1', 'bootstrap', hook1);
            lifecycleManager.registerHook('app2', 'mount', hook2);
        });

        it('应该清理所有钩子', () => {
            lifecycleManager.clear();

            expect(lifecycleManager.getHooks('app1', 'bootstrap')).toHaveLength(0);
            expect(lifecycleManager.getHooks('app2', 'mount')).toHaveLength(0);
        });

        it('应该获取清理统计信息', () => {
            const statsBefore = lifecycleManager.getHookStats();
            expect(statsBefore.totalHooks).toBeGreaterThan(0);

            lifecycleManager.clear();

            const statsAfter = lifecycleManager.getHookStats();
            expect(statsAfter.totalHooks).toBe(0);
            expect(statsAfter.totalApps).toBe(0);
        });
    });
});
