/**
 * @fileoverview 插件系统测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PluginSystem } from '../src/plugin-system';
import type { Plugin, PluginContext } from '../src/types';

describe('PluginSystem', () => {
    let pluginSystem: PluginSystem;
    let mockPlugin: Plugin;
    let mockContext: PluginContext;

    beforeEach(() => {
        pluginSystem = new PluginSystem();

        mockContext = {
            kernel: {} as any,
            config: {},
            utils: {
                logger: {
                    info: vi.fn(),
                    warn: vi.fn(),
                    error: vi.fn()
                }
            }
        };

        mockPlugin = {
            name: 'test-plugin',
            version: '1.0.0',
            install: vi.fn(),
            uninstall: vi.fn()
        };
    });

    describe('插件注册', () => {
        it('应该成功注册插件', () => {
            pluginSystem.register(mockPlugin);
            expect(pluginSystem.has('test-plugin')).toBe(true);
            expect(pluginSystem.get('test-plugin')).toBe(mockPlugin);
        });

        it('应该拒绝重复注册同名插件', () => {
            pluginSystem.register(mockPlugin);
            expect(() => pluginSystem.register(mockPlugin)).toThrow('插件 test-plugin 已存在');
        });

        it('应该验证插件基本信息', () => {
            const invalidPlugin = { ...mockPlugin, name: '' };
            expect(() => pluginSystem.register(invalidPlugin)).toThrow('插件名称不能为空');
        });

        it('应该验证插件版本', () => {
            const invalidPlugin = { ...mockPlugin, version: '' };
            expect(() => pluginSystem.register(invalidPlugin)).toThrow('插件版本不能为空');
        });

        it('应该验证插件安装方法', () => {
            const invalidPlugin = { ...mockPlugin, install: undefined as any };
            expect(() => pluginSystem.register(invalidPlugin)).toThrow('插件必须提供install方法');
        });
    });

    describe('插件安装', () => {
        beforeEach(() => {
            pluginSystem.register(mockPlugin);
        });

        it('应该成功安装插件', async () => {
            await pluginSystem.install('test-plugin', mockContext);

            expect(mockPlugin.install).toHaveBeenCalledWith(mockContext);
            expect(pluginSystem.isInstalled('test-plugin')).toBe(true);
        });

        it('应该拒绝安装不存在的插件', async () => {
            await expect(pluginSystem.install('non-existent', mockContext)).rejects.toThrow('插件 non-existent 不存在');
        });

        it('应该拒绝重复安装插件', async () => {
            await pluginSystem.install('test-plugin', mockContext);
            await expect(pluginSystem.install('test-plugin', mockContext)).rejects.toThrow('插件 test-plugin 已安装');
        });

        it('应该处理插件安装失败', async () => {
            const errorPlugin = {
                ...mockPlugin,
                install: vi.fn(async () => {
                    throw new Error('安装失败');
                })
            };

            pluginSystem.register(errorPlugin);
            await expect(pluginSystem.install(errorPlugin.name, mockContext)).rejects.toThrow('安装失败');
            expect(pluginSystem.isInstalled(errorPlugin.name)).toBe(false);
        });
    });

    describe('插件卸载', () => {
        beforeEach(async () => {
            pluginSystem.register(mockPlugin);
            await pluginSystem.install('test-plugin', mockContext);
        });

        it('应该成功卸载插件', async () => {
            await pluginSystem.uninstall('test-plugin', mockContext);

            expect(mockPlugin.uninstall).toHaveBeenCalledWith(mockContext);
            expect(pluginSystem.isInstalled('test-plugin')).toBe(false);
        });

        it('应该拒绝卸载未安装的插件', async () => {
            const anotherPlugin = {
                ...mockPlugin,
                name: 'another-plugin'
            };
            pluginSystem.register(anotherPlugin);

            await expect(pluginSystem.uninstall('another-plugin', mockContext)).rejects.toThrow('插件 another-plugin 未安装');
        });

        it('应该处理插件卸载失败', async () => {
            const errorPlugin = {
                ...mockPlugin,
                name: 'error-plugin',
                uninstall: vi.fn(async () => {
                    throw new Error('卸载失败');
                })
            };

            pluginSystem.register(errorPlugin);
            await pluginSystem.install('error-plugin', mockContext);

            await expect(pluginSystem.uninstall('error-plugin', mockContext)).rejects.toThrow('卸载失败');
            // 即使卸载失败，也应该标记为未安装
            expect(pluginSystem.isInstalled('error-plugin')).toBe(false);
        });
    });

    describe('插件查询', () => {
        beforeEach(async () => {
            pluginSystem.register(mockPlugin);

            const anotherPlugin = {
                ...mockPlugin,
                name: 'another-plugin'
            };
            pluginSystem.register(anotherPlugin);
            await pluginSystem.install('test-plugin', mockContext);
        });

        it('应该正确查询插件是否存在', () => {
            expect(pluginSystem.has('test-plugin')).toBe(true);
            expect(pluginSystem.has('non-existent')).toBe(false);
        });

        it('应该正确查询插件是否已安装', () => {
            expect(pluginSystem.isInstalled('test-plugin')).toBe(true);
            expect(pluginSystem.isInstalled('another-plugin')).toBe(false);
        });

        it('应该正确获取插件信息', () => {
            const plugin = pluginSystem.get('test-plugin');
            expect(plugin).toBe(mockPlugin);
        });

        it('应该获取所有已注册插件', () => {
            const plugins = pluginSystem.getAll();
            expect(plugins).toHaveLength(2);
            expect(plugins.map(p => p.name)).toContain('test-plugin');
            expect(plugins.map(p => p.name)).toContain('another-plugin');
        });

        it('应该获取所有已安装插件', () => {
            const installedPlugins = pluginSystem.getInstalled();
            expect(installedPlugins).toHaveLength(1);
            expect(installedPlugins[0].name).toBe('test-plugin');
        });
    });

    describe('批量操作', () => {
        beforeEach(() => {
            const plugins = [
                { ...mockPlugin, name: 'plugin1' },
                { ...mockPlugin, name: 'plugin2' },
                { ...mockPlugin, name: 'plugin3' }
            ];

            plugins.forEach(plugin => pluginSystem.register(plugin));
        });

        it('应该批量安装插件', async () => {
            await pluginSystem.installAll(['plugin1', 'plugin2'], mockContext);

            expect(pluginSystem.isInstalled('plugin1')).toBe(true);
            expect(pluginSystem.isInstalled('plugin2')).toBe(true);
            expect(pluginSystem.isInstalled('plugin3')).toBe(false);
        });

        it('应该批量卸载插件', async () => {
            await pluginSystem.installAll(['plugin1', 'plugin2'], mockContext);
            await pluginSystem.uninstallAll(['plugin1', 'plugin2'], mockContext);

            expect(pluginSystem.isInstalled('plugin1')).toBe(false);
            expect(pluginSystem.isInstalled('plugin2')).toBe(false);
        });

        it('应该处理批量操作中的部分失败', async () => {
            const errorPlugin = {
                ...mockPlugin,
                name: 'error-plugin',
                install: vi.fn(async () => {
                    throw new Error('安装失败');
                })
            };
            pluginSystem.register(errorPlugin);

            const results = await pluginSystem.installAll(['plugin1', 'error-plugin', 'plugin2'], mockContext);

            expect(results.success).toHaveLength(2);
            expect(results.failed).toHaveLength(1);
            expect(results.failed[0].name).toBe('error-plugin');
        });
    });

    describe('插件依赖', () => {
        it('应该处理插件依赖关系', async () => {
            const dependentPlugin = {
                ...mockPlugin,
                name: 'dependent-plugin',
                dependencies: ['test-plugin']
            };

            pluginSystem.register(mockPlugin);
            pluginSystem.register(dependentPlugin);

            // 应该先安装依赖插件
            await pluginSystem.install('dependent-plugin', mockContext);

            expect(pluginSystem.isInstalled('test-plugin')).toBe(true);
            expect(pluginSystem.isInstalled('dependent-plugin')).toBe(true);
        });

        it('应该检测循环依赖', () => {
            const plugin1 = {
                ...mockPlugin,
                name: 'plugin1',
                dependencies: ['plugin2']
            };

            const plugin2 = {
                ...mockPlugin,
                name: 'plugin2',
                dependencies: ['plugin1']
            };

            pluginSystem.register(plugin1);
            pluginSystem.register(plugin2);

            expect(pluginSystem.install('plugin1', mockContext)).rejects.toThrow('检测到循环依赖');
        });

        it('应该拒绝卸载被依赖的插件', async () => {
            const dependentPlugin = {
                ...mockPlugin,
                name: 'dependent-plugin',
                dependencies: ['test-plugin']
            };

            pluginSystem.register(mockPlugin);
            pluginSystem.register(dependentPlugin);

            await pluginSystem.install('dependent-plugin', mockContext);

            await expect(pluginSystem.uninstall('test-plugin', mockContext)).rejects.toThrow('插件被其他插件依赖');
        });
    });

    describe('插件事件', () => {
        it('应该触发插件安装事件', async () => {
            const installListener = vi.fn();
            pluginSystem.on('plugin:installed', installListener);

            pluginSystem.register(mockPlugin);
            await pluginSystem.install('test-plugin', mockContext);

            expect(installListener).toHaveBeenCalledWith('test-plugin', mockPlugin);
        });

        it('应该触发插件卸载事件', async () => {
            const uninstallListener = vi.fn();
            pluginSystem.on('plugin:uninstalled', uninstallListener);

            pluginSystem.register(mockPlugin);
            await pluginSystem.install('test-plugin', mockContext);
            await pluginSystem.uninstall('test-plugin', mockContext);

            expect(uninstallListener).toHaveBeenCalledWith('test-plugin', mockPlugin);
        });

        it('应该触发插件错误事件', async () => {
            const errorListener = vi.fn();
            pluginSystem.on('plugin:error', errorListener);

            const errorPlugin = {
                ...mockPlugin,
                install: vi.fn(async () => {
                    throw new Error('安装失败');
                })
            };

            pluginSystem.register(errorPlugin);

            try {
                await pluginSystem.install(errorPlugin.name, mockContext);
            } catch (error) {
                // 忽略错误
            }

            expect(errorListener).toHaveBeenCalled();
        });
    });

    describe('插件统计', () => {
        beforeEach(async () => {
            const plugins = [
                { ...mockPlugin, name: 'plugin1' },
                { ...mockPlugin, name: 'plugin2' },
                { ...mockPlugin, name: 'plugin3' }
            ];

            plugins.forEach(plugin => pluginSystem.register(plugin));
            await pluginSystem.install('plugin1', mockContext);
            await pluginSystem.install('plugin2', mockContext);
        });

        it('应该获取插件统计信息', () => {
            const stats = pluginSystem.getStats();

            expect(stats.total).toBe(3);
            expect(stats.installed).toBe(2);
            expect(stats.uninstalled).toBe(1);
        });

        it('应该获取插件详细信息', () => {
            const info = pluginSystem.getPluginInfo('plugin1');

            expect(info.name).toBe('plugin1');
            expect(info.version).toBe('1.0.0');
            expect(info.installed).toBe(true);
        });
    });

    describe('清理操作', () => {
        beforeEach(async () => {
            pluginSystem.register(mockPlugin);
            await pluginSystem.install('test-plugin', mockContext);
        });

        it('应该清理所有插件', async () => {
            await pluginSystem.clear(mockContext);

            expect(pluginSystem.getAll()).toHaveLength(0);
            expect(pluginSystem.getInstalled()).toHaveLength(0);
        });

        it('应该在清理时卸载所有已安装插件', async () => {
            await pluginSystem.clear(mockContext);

            expect(mockPlugin.uninstall).toHaveBeenCalledWith(mockContext);
        });
    });
});