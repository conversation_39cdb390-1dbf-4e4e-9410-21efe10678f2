{"fileNames": ["../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./src/types.ts", "./src/types/index.ts", "./src/utils.ts", "./src/app-registry.ts", "./src/constants.ts", "./src/errors.ts", "./src/error-handler.ts", "./src/event-bus.ts", "./src/lifecycle.ts", "./src/plugin-system.ts", "./src/sandbox-manager.ts", "./src/kernel.ts", "./src/index.ts", "./src/lifecycle-manager.ts", "./src/resource-manager.ts", "./src/communication/event-bus.ts", "./src/communication/global-state.ts", "./src/communication/communication-manager.ts", "./src/communication/index.ts", "./src/lifecycle/lifecycle-manager.ts", "./src/plugin/plugin-system.ts", "./src/routing/router-manager.ts", "./src/routing/route-guards.ts", "./src/routing/index.ts", "./src/sandboxes/define-property-sandbox.ts", "./src/sandboxes/federation-sandbox.ts", "./src/sandboxes/iframe-sandbox.ts", "./src/sandboxes/proxy-sandbox.ts", "./src/sandboxes/index.ts", "./src/sandboxes/namespace-sandbox.ts", "./src/sandboxes/web-component-sandbox.ts", "./src/utils/index.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/header.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/readable.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/file.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/fetch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/formdata.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/connector.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-origin.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool-stats.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/handlers.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-handler.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/api.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/interceptors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/util.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cookies.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/patch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/websocket.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/eventsource.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/filereader.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/content-type.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cache.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/index.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/globals.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/assert.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/assert/strict.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/async_hooks.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/buffer.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/child_process.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/cluster.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/console.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/constants.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/crypto.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dgram.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dns.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dns/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/domain.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dom-events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/fs.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/fs/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/http.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/http2.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/https.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/inspector.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/module.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/net.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/os.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/path.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/process.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/punycode.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/querystring.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/readline.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/readline/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/repl.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/sea.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/web.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/string_decoder.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/test.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/timers.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/timers/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/tls.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/trace_events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/tty.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/url.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/util.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/v8.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/vm.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/wasi.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/worker_threads.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/zlib.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/index.d.ts"], "fileIdsList": [[85, 124, 127], [85, 126, 127], [127], [85, 127, 132, 161], [85, 127, 128, 133, 139, 140, 147, 158, 169], [85, 127, 128, 129, 139, 147], [85, 127], [80, 81, 82, 85, 127], [85, 127, 130, 170], [85, 127, 131, 132, 140, 148], [85, 127, 132, 158, 166], [85, 127, 133, 135, 139, 147], [85, 126, 127, 134], [85, 127, 135, 136], [85, 127, 137, 139], [85, 126, 127, 139], [85, 127, 139, 140, 141, 158, 169], [85, 127, 139, 140, 141, 154, 158, 161], [85, 122, 127], [85, 127, 135, 139, 142, 147, 158, 169], [85, 127, 139, 140, 142, 143, 147, 158, 166, 169], [85, 127, 142, 144, 158, 166, 169], [83, 84, 85, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175], [85, 127, 139, 145], [85, 127, 146, 169, 174], [85, 127, 135, 139, 147, 158], [85, 127, 148], [85, 127, 149], [85, 126, 127, 150], [85, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175], [85, 127, 152], [85, 127, 153], [85, 127, 139, 154, 155], [85, 127, 154, 156, 170, 172], [85, 127, 139, 158, 159, 161], [85, 127, 160, 161], [85, 127, 158, 159], [85, 127, 161], [85, 127, 162], [85, 124, 127, 158, 163], [85, 127, 139, 164, 165], [85, 127, 164, 165], [85, 127, 132, 147, 158, 166], [85, 127, 167], [85, 127, 147, 168], [85, 127, 142, 153, 169], [85, 127, 132, 170], [85, 127, 158, 171], [85, 127, 146, 172], [85, 127, 173], [85, 127, 139, 141, 150, 158, 161, 169, 172, 174], [85, 127, 158, 175], [85, 94, 98, 127, 169], [85, 94, 127, 158, 169], [85, 89, 127], [85, 91, 94, 127, 166, 169], [85, 127, 147, 166], [85, 127, 176], [85, 89, 127, 176], [85, 91, 94, 127, 147, 169], [85, 86, 87, 90, 93, 127, 139, 158, 169], [85, 94, 101, 127], [85, 86, 92, 127], [85, 94, 115, 116, 127], [85, 90, 94, 127, 161, 169, 176], [85, 115, 127, 176], [85, 88, 89, 127, 176], [85, 94, 127], [85, 88, 89, 90, 91, 92, 93, 94, 95, 96, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 118, 119, 120, 121, 127], [85, 94, 109, 127], [85, 94, 101, 102, 127], [85, 92, 94, 102, 103, 127], [85, 93, 127], [85, 86, 89, 94, 127], [85, 94, 98, 102, 103, 127], [85, 98, 127], [85, 92, 94, 97, 127, 169], [85, 86, 91, 94, 101, 127], [85, 127, 158], [85, 89, 94, 115, 127, 174, 176], [48, 49, 50, 85, 127], [50, 53, 63, 64, 85, 127], [50, 53, 85, 127], [50, 53, 63, 85, 127], [63, 64, 65, 85, 127], [52, 85, 127], [48, 85, 127], [48, 49, 50, 51, 55, 56, 57, 58, 59, 85, 127], [48, 49, 50, 51, 55, 56, 57, 58, 85, 127], [48, 50, 52, 53, 85, 127], [48, 52, 53, 85, 127], [48, 50, 85, 127], [48, 50, 53, 85, 127], [69, 70, 85, 127], [48, 69, 85, 127], [48, 53, 85, 127], [75, 85, 127]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, "fe21067644484b4968265eb1c7a07b812f6acd3187455a1827298003216b5f90", "db70ca6c64cce03c7d3325ec3ed2334649f62ce17acc4cffc7ea24470f461eb3", "1177f9d2e92939fc927e303ae5c6f43ec8f75be566230b2b1642b440f62e2f86", "b7357233ebe6ecb92e58592965b42a59a46908a8cead00e828f013198992b949", "08672377649f2a59a03888063a5e8f986dd1ed14c0b192c233513267a33c81e1", "31548c22523e79a47a46451d8bc7b242bbb804e110652dbfc497511b241061b8", "a78ff636acfc5bbef8d360f53460587ef39c419b956c2e580c745fd4ecfc76f9", "376b27e3b384b2a6393ec70cb5d09bac0de77f084ab6562db45bf0f0216ffc7c", "89825522e20ae1d50ec0a630286e53dd74eeeefd902a2b0ed917f5836e428dba", "425732787aa3b50a065dacf2c6a983c748322aef1aadf61072186389a4a360b8", "75fed741ed69441e6044a5c86f8049383880d4f201b42e5bf1a6493cc10f3550", "a92cf7c7dce735b9ccae1c15f07b3ae5f361824ecc12ec557663c562c2e20897", "9a94df4407ff032a2bb094b39d4592bb64f2aebd0f54aa09441782971f9ba50e", "8b62717b6feb1063c13f5d62f6da31b50ac3c9cc1782fbcf511417eb1d5ace63", "c5b036e096e1b55943d13dc89fbad3b150be0001f70066c0c9326fb3e38ad831", "8365f5923b5d6bb3630f39a22b1a38a835ce9e622f05ded1cb694bf698ca6539", "15b6ca2dcefc687a183bf0435bcf646c00c86cddd4494d03735dd54715b42912", "2d707335703d5d8f7090b524c3248ed2b38f335dd3e3d287dfaf4d010cfe5540", "28599c28b81e7e83cde8d16014b470bebb89eb40263787f50c4dcf4583142859", "742f5d6c33496a667488c9414798c34d49cb3bae435b3e38070d87da96fb69c6", "c147368d26cba43ab474d6c7186f2cbf4087ad0a91a5e311a87140a0e542f8f7", "ba689ce8129f84c23fb930a399400a88a54dd356f8b0efbca6846522b8e20f2c", "6c52de6db2689873b82719e64cff0dea8d593d7cf0472eab79804ea98473997f", "0214767cdc29059034434ddabf48b4cfd2651b6e68db1e4f9fee99f2da2528a2", "1b7fbb9a463ce6144dc9f5ccb5f686057ea4dbaaa6c172de6b4e03ad529c6f9c", "095fe1231da47d6508b100a99352d540fb341dd449f0b82608409f4bc353e4d9", "0864443102065c98e4c709c4b63c7ac014029c3ede19e41a4f110bc3fb34447d", "a8b8eb2e1987bf47aff835ef9e14746bed65f05c1e2caf71befa83cbd4a9d861", "69d00ed97fcef53a05d7f218d38d89daf0a7f8c95735b7c5e5c532c6ea7e59d0", "51755d40d50bdecd5e69973ea872066bc210214066c5ba8037d4194fb0778c16", "5fd01f041fae6c77c9744408cef3b5e8fca8fe40c054b42d541ea555f6b4f968", "5f4a530161b76329a643b52ebb01ce73be9557015d9b4007d5d8e537c27ab87c", {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6d586db0a09a9495ebb5dece28f54df9684bfbd6e1f568426ca153126dac4a40", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "567b7f607f400873151d7bc63a049514b53c3c00f5f56e9e95695d93b66a138e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "2bf469abae4cc9c0f340d4e05d9d26e37f936f9c8ca8f007a6534f109dcc77e4", "impliedFormat": 1}, {"version": "4aacb0dd020eeaef65426153686cc639a78ec2885dc72ad220be1d25f1a439df", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "54c4f21f578864961efc94e8f42bc893a53509e886370ec7dd602e0151b9266c", "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "461e54289e6287e8494a0178ba18182acce51a02bca8dea219149bf2cf96f105", "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}], "root": [[48, 79]], "options": {"allowImportingTsExtensions": true, "composite": true, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "strict": true, "target": 7}, "referencedMap": [[124, 1], [125, 1], [126, 2], [85, 3], [127, 4], [128, 5], [129, 6], [80, 7], [83, 8], [81, 7], [82, 7], [130, 9], [131, 10], [132, 11], [133, 12], [134, 13], [135, 14], [136, 14], [138, 7], [137, 15], [139, 16], [140, 17], [141, 18], [123, 19], [84, 7], [142, 20], [143, 21], [144, 22], [176, 23], [145, 24], [146, 25], [147, 26], [148, 27], [149, 28], [150, 29], [151, 30], [152, 31], [153, 32], [154, 33], [155, 33], [156, 34], [157, 7], [158, 35], [160, 36], [159, 37], [161, 38], [162, 39], [163, 40], [164, 41], [165, 42], [166, 43], [167, 44], [168, 45], [169, 46], [170, 47], [171, 48], [172, 49], [173, 50], [174, 51], [175, 52], [46, 7], [47, 7], [8, 7], [9, 7], [11, 7], [10, 7], [2, 7], [12, 7], [13, 7], [14, 7], [15, 7], [16, 7], [17, 7], [18, 7], [19, 7], [3, 7], [20, 7], [21, 7], [4, 7], [22, 7], [26, 7], [23, 7], [24, 7], [25, 7], [27, 7], [28, 7], [29, 7], [5, 7], [30, 7], [31, 7], [32, 7], [33, 7], [6, 7], [37, 7], [34, 7], [35, 7], [36, 7], [38, 7], [7, 7], [39, 7], [44, 7], [45, 7], [40, 7], [41, 7], [42, 7], [43, 7], [1, 7], [101, 53], [111, 54], [100, 53], [121, 55], [92, 56], [91, 57], [120, 58], [114, 59], [119, 60], [94, 61], [108, 62], [93, 63], [117, 64], [89, 65], [88, 58], [118, 66], [90, 67], [95, 68], [96, 7], [99, 68], [86, 7], [122, 69], [112, 70], [103, 71], [104, 72], [106, 73], [102, 74], [105, 75], [115, 58], [97, 76], [98, 77], [107, 78], [87, 79], [110, 70], [109, 68], [113, 7], [116, 80], [51, 81], [65, 82], [63, 83], [64, 84], [66, 85], [52, 7], [54, 83], [53, 86], [55, 87], [60, 88], [59, 89], [61, 90], [56, 81], [67, 91], [57, 92], [68, 91], [62, 93], [71, 94], [70, 95], [69, 96], [58, 92], [72, 96], [73, 96], [74, 96], [76, 97], [77, 96], [75, 96], [78, 96], [48, 7], [49, 7], [50, 7], [79, 7]], "semanticDiagnosticsPerFile": [[51, [{"start": 25, "length": 11, "messageText": "Module '\"./types\"' has no exported member 'AppInstance'.", "category": 1, "code": 2305}, {"start": 778, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'customProps' does not exist on type 'AppConfig'."}]], [55, [{"start": 14, "length": 8, "messageText": "Module '\"./types\"' has no exported member 'EventMap'.", "category": 1, "code": 2305}, {"start": 314, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 355, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 411, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 903, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 994, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 1212, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 1369, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 1460, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 1655, "length": 5, "messageText": "Implicit conversion of a 'symbol' to a 'string' will fail at runtime. Consider wrapping this expression in 'String(...)'.", "category": 1, "code": 2731}, {"start": 1837, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | number | symbol' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}]], [56, [{"start": 14, "length": 11, "messageText": "Module '\"./types\"' has no exported member 'AppInstance'.", "category": 1, "code": 2305}, {"start": 27, "length": 13, "messageText": "Module '\"./types\"' has no exported member 'AppLifecycles'.", "category": 1, "code": 2305}]], [57, [{"start": 14, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'MicroCoreKernel'.", "category": 1, "code": 2305}]], [58, [{"start": 14, "length": 13, "messageText": "Module '\"./types\"' has no exported member 'SandboxConfig'.", "category": 1, "code": 2305}, {"start": 29, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'SandboxInstance'.", "category": 1, "code": 2305}, {"start": 3209, "length": 6, "messageText": "'config' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [59, [{"start": 259, "length": 11, "messageText": "Module '\"./types\"' has no exported member 'AppInstance'.", "category": 1, "code": 2305}, {"start": 272, "length": 8, "messageText": "Module '\"./types\"' has no exported member 'EventMap'.", "category": 1, "code": 2305}, {"start": 282, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'MicroCoreKernel'.", "category": 1, "code": 2305}, {"start": 319, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'MicroCoreConfig'.", "category": 1, "code": 2305}, {"start": 1073, "length": 18, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/plugin-system.ts", "start": 262, "length": 23, "messageText": "An argument for 'kernel' was not provided.", "category": 3, "code": 6210}]}, {"start": 1390, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'has' does not exist on type 'AppRegistry'."}, {"start": 1750, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'customProps' does not exist on type 'AppConfig'."}, {"start": 1813, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'register' does not exist on type 'AppRegistry'."}, {"start": 2182, "length": 6, "messageText": "Parameter 'plugin' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2530, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'getAll' does not exist on type 'AppRegistry'."}, {"start": 2561, "length": 3, "messageText": "Parameter 'app' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2984, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'get' does not exist on type 'AppRegistry'."}, {"start": 3105, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'getAll' does not exist on type 'AppRegistry'."}, {"start": 3236, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'get' does not exist on type 'AppRegistry'."}, {"start": 4577, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'get' does not exist on type 'AppRegistry'."}, {"start": 5474, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'install' does not exist on type 'PluginSystem'."}, {"start": 5642, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'uninstall' does not exist on type 'PluginSystem'."}, {"start": 7165, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'getAll' does not exist on type 'AppRegistry'."}]], [60, [{"start": 370, "length": 11, "messageText": "Module '\"./types\"' has no exported member 'AppInstance'.", "category": 1, "code": 2305}, {"start": 387, "length": 13, "messageText": "Module '\"./types\"' has no exported member 'AppLifecycles'.", "category": 1, "code": 2305}, {"start": 406, "length": 8, "messageText": "Module '\"./types\"' has no exported member 'AppProps'.", "category": 1, "code": 2305}, {"start": 420, "length": 8, "messageText": "Module '\"./types\"' has no exported member 'EventMap'.", "category": 1, "code": 2305}, {"start": 434, "length": 12, "messageText": "Module '\"./types\"' has no exported member 'HookCallback'.", "category": 1, "code": 2305}, {"start": 452, "length": 12, "messageText": "Module '\"./types\"' has no exported member 'LoaderConfig'.", "category": 1, "code": 2305}, {"start": 470, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'MicroCoreConfig'.", "category": 1, "code": 2305}, {"start": 503, "length": 13, "messageText": "Module '\"./types\"' has no exported member 'SandboxConfig'.", "category": 1, "code": 2305}, {"start": 522, "length": 15, "messageText": "Module '\"./types\"' has no exported member 'SandboxInstance'.", "category": 1, "code": 2305}, {"start": 725, "length": 15, "messageText": "Cannot find name 'MicroCoreKernel'.", "category": 1, "code": 2304}, {"start": 813, "length": 15, "messageText": "Cannot find name 'MicroCoreKernel'.", "category": 1, "code": 2304}, {"start": 885, "length": 15, "messageText": "Cannot find name 'MicroCoreKernel'.", "category": 1, "code": 2304}, {"start": 1002, "length": 9, "messageText": "Cannot find name 'AppConfig'.", "category": 1, "code": 2304}, {"start": 1564, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1700, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1918, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}, {"start": 1964, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}, {"start": 2096, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}, {"start": 2142, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}, {"start": 2274, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}, {"start": 2300, "length": 8, "messageText": "Cannot find name 'EventMap'.", "category": 1, "code": 2304}]], [61, [{"start": 5888, "length": 5, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'never' has no call signatures.", "category": 1, "code": 2757}]}}]], [62, [{"start": 207, "length": 15, "messageText": "'\"./types\"' has no exported member named 'RESOURCE_STATUS'. Did you mean 'ResourceStatus'?", "category": 1, "code": 2724}, {"start": 224, "length": 14, "messageText": "Module '\"./types\"' has no exported member 'RESOURCE_TYPES'.", "category": 1, "code": 2305}]], [63, [{"start": 9613, "length": 5, "messageText": "Expected 1-4 arguments, but got 5.", "category": 1, "code": 2554}, {"start": 11328, "length": 5, "messageText": "Expected 1-4 arguments, but got 5.", "category": 1, "code": 2554}, {"start": 15951, "length": 5, "messageText": "'event' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 16108, "length": 8, "messageText": "'listener' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [68, [{"start": 102, "length": 11, "messageText": "Module '\"../types\"' has no exported member 'PluginHooks'.", "category": 1, "code": 2305}, {"start": 1759, "length": 5, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string | undefined'."}, {"start": 2766, "length": 5, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string | undefined'."}, {"start": 3869, "length": 1, "messageText": "Parameter 'h' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5057, "length": 5, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string | undefined'."}, {"start": 6207, "length": 5, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string | undefined'."}, {"start": 6512, "length": 1, "messageText": "Parameter 'h' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7118, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Plugin'."}, {"start": 7201, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Plugin'."}, {"start": 9683, "length": 4, "messageText": "Parameter 'hook' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [69, [{"start": 527, "length": 15, "messageText": "'mode' is specified more than once, so this usage will be overwritten.", "category": 1, "code": 2783, "relatedInformation": [{"start": 579, "length": 10, "messageText": "This spread always overwrites this property.", "category": 1, "code": 2785}]}]], [70, [{"start": 286, "length": 4, "messageText": "'from' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 806, "length": 4, "messageText": "'from' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1504, "length": 2, "messageText": "'to' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1515, "length": 4, "messageText": "'from' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2386, "length": 4, "messageText": "'from' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [72, [{"start": 183, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 6037, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 7156, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 12273, "length": 5, "messageText": "'value' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 15446, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 16091, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [73, [{"start": 175, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 3428, "length": 11, "messageText": "'denyListSet' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 7036, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 9170, "length": 10, "messageText": "'moduleName' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 12511, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 12785, "length": 25, "messageText": "'checkVersionCompatibility' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 13834, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 14903, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 16515, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17094, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17611, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [74, [{"start": 160, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 3478, "length": 11, "messageText": "'denyListSet' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6341, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_CREATION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_CREATE_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 2926, "length": 46, "messageText": "'SANDBOX_CREATE_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 15691, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 16091, "length": 2, "messageText": "'id' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 16912, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17363, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17852, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 18233, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 18580, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 18967, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 19317, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [75, [{"start": 183, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 3878, "length": 9, "messageText": "'rawWindow' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5341, "length": 8, "messageText": "'receiver' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5655, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 6657, "length": 8, "messageText": "'receiver' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6973, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 8773, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 9760, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 12721, "length": 5, "messageText": "'value' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 15469, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 15714, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 16291, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [77, [{"start": 166, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 7944, "length": 19, "messageText": "'extractOriginalName' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 13384, "length": 12, "messageText": "'originalName' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 14937, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 15612, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [78, [{"start": 170, "length": 13, "messageText": "'\"../types\"' has no exported member named 'SANDBOX_TYPES'. Did you mean 'SandboxType'?", "category": 1, "code": 2724}, {"start": 2237, "length": 11, "messageText": "'styleSheets' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5965, "length": 17, "messageText": "'isPropertyAllowed' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9078, "length": 4, "messageText": "'name' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9092, "length": 8, "messageText": "'oldValue' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 9110, "length": 8, "messageText": "'newValue' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 10093, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 11506, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_CREATION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_CREATE_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 2926, "length": 46, "messageText": "'SANDBOX_CREATE_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 13857, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 14675, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 15111, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 15354, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 16860, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17278, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 17689, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 18113, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 18637, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 19114, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}, {"start": 19657, "length": 24, "code": 2551, "category": 1, "messageText": "Property 'SANDBOX_EXECUTION_FAILED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 32 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'SANDBOX_SCRIPT_EXECUTION_FAILED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 3088, "length": 66, "messageText": "'SANDBOX_SCRIPT_EXECUTION_FAILED' is declared here.", "category": 3, "code": 2728}]}]], [79, [{"start": 1321, "length": 4, "messageText": "'this' implicitly has type 'any' because it does not have a type annotation.", "category": 1, "code": 2683, "relatedInformation": [{"start": 1057, "length": 8, "messageText": "An outer value of 'this' is shadowed by this container.", "category": 3, "code": 2738}]}, {"start": 1616, "length": 4, "messageText": "'this' implicitly has type 'any' because it does not have a type annotation.", "category": 1, "code": 2683, "relatedInformation": [{"start": 1383, "length": 8, "messageText": "An outer value of 'this' is shadowed by this container.", "category": 3, "code": 2738}]}, {"start": 4488, "length": 6, "messageText": "'target' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]]], "affectedFilesPendingEmit": [51, 65, 63, 64, 66, 52, 54, 53, 55, 60, 59, 61, 56, 67, 57, 68, 62, 71, 70, 69, 58, 72, 73, 74, 76, 77, 75, 78, 48, 49, 50, 79], "emitSignatures": [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "version": "5.8.3"}