/**
 * @fileoverview 微前端核心功能测试
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MicroCore } from '../src/micro-core';
import type { AppConfig, AppStatus } from '../src/types';

describe('MicroCore', () => {
    let microCore: MicroCore;

    beforeEach(() => {
        microCore = new MicroCore();
        vi.clearAllMocks();
    });

    afterEach(() => {
        microCore.destroy();
    });

    describe('应用注册', () => {
        it('应该能够注册应用', () => {
            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test'
            };

            microCore.registerApp(appConfig);

            const app = microCore.getApp('test-app');
            expect(app).toBeDefined();
            expect(app?.name).toBe('test-app');
            expect(app?.entry).toBe('http://localhost:3001');
        });

        it('应该拒绝重复注册同名应用', () => {
            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test'
            };

            microCore.registerApp(appConfig);

            expect(() => {
                microCore.registerApp(appConfig);
            }).toThrow('应用 test-app 已经注册');
        });

        it('应该验证应用配置', () => {
            expect(() => {
                microCore.registerApp({} as AppConfig);
            }).toThrow();

            expect(() => {
                microCore.registerApp({
                    name: '',
                    entry: 'http://localhost:3001',
                    container: '#container',
                    activeWhen: '/test'
                });
            }).toThrow();
        });
    });

    describe('应用生命周期', () => {
        let appConfig: AppConfig;

        beforeEach(() => {
            appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test',
                loader: vi.fn().mockResolvedValue({
                    mount: vi.fn().mockResolvedValue(undefined),
                    unmount: vi.fn().mockResolvedValue(undefined),
                    update: vi.fn().mockResolvedValue(undefined)
                })
            };
            microCore.registerApp(appConfig);
        });

        it('应该能够启动应用', async () => {
            await microCore.startApp('test-app');

            const app = microCore.getApp('test-app');
            expect(app?.status).toBe('MOUNTED' as AppStatus);
            expect(appConfig.loader).toHaveBeenCalled();
        });

        it('应该能够停止应用', async () => {
            await microCore.startApp('test-app');
            await microCore.stopApp('test-app');

            const app = microCore.getApp('test-app');
            expect(app?.status).toBe('UNMOUNTED' as AppStatus);
        });

        it('应该处理应用启动失败', async () => {
            const failingConfig: AppConfig = {
                name: 'failing-app',
                entry: 'http://localhost:3002',
                container: '#container',
                activeWhen: '/failing',
                loader: vi.fn().mockRejectedValue(new Error('加载失败'))
            };

            microCore.registerApp(failingConfig);

            await expect(microCore.startApp('failing-app')).rejects.toThrow('加载失败');

            const app = microCore.getApp('failing-app');
            expect(app?.status).toBe('LOAD_ERROR' as AppStatus);
        });
    });

    describe('应用管理', () => {
        beforeEach(() => {
            const apps = [
                {
                    name: 'app1',
                    entry: 'http://localhost:3001',
                    container: '#container1',
                    activeWhen: '/app1'
                },
                {
                    name: 'app2',
                    entry: 'http://localhost:3002',
                    container: '#container2',
                    activeWhen: '/app2'
                }
            ];

            apps.forEach(app => microCore.registerApp(app));
        });

        it('应该能够获取所有应用', () => {
            const apps = microCore.getApps();
            expect(apps).toHaveLength(2);
            expect(apps.map(app => app.name)).toEqual(['app1', 'app2']);
        });

        it('应该能够注销应用', () => {
            microCore.unregisterApp('app1');

            const apps = microCore.getApps();
            expect(apps).toHaveLength(1);
            expect(apps[0].name).toBe('app2');
        });

        it('应该能够获取指定状态的应用', () => {
            const notLoadedApps = microCore.getAppsByStatus('NOT_LOADED' as AppStatus);
            expect(notLoadedApps).toHaveLength(2);
        });
    });

    describe('路由匹配', () => {
        beforeEach(() => {
            const apps = [
                {
                    name: 'app1',
                    entry: 'http://localhost:3001',
                    container: '#container1',
                    activeWhen: '/app1'
                },
                {
                    name: 'app2',
                    entry: 'http://localhost:3002',
                    container: '#container2',
                    activeWhen: ['/app2', '/app2/*']
                },
                {
                    name: 'app3',
                    entry: 'http://localhost:3003',
                    container: '#container3',
                    activeWhen: (location) => location.pathname.startsWith('/app3')
                }
            ];

            apps.forEach(app => microCore.registerApp(app));
        });

        it('应该能够匹配字符串路由', () => {
            const matchedApps = microCore.getActiveApps('/app1');
            expect(matchedApps).toHaveLength(1);
            expect(matchedApps[0].name).toBe('app1');
        });

        it('应该能够匹配数组路由', () => {
            const matchedApps1 = microCore.getActiveApps('/app2');
            const matchedApps2 = microCore.getActiveApps('/app2/detail');

            expect(matchedApps1).toHaveLength(1);
            expect(matchedApps1[0].name).toBe('app2');
            expect(matchedApps2).toHaveLength(1);
            expect(matchedApps2[0].name).toBe('app2');
        });

        it('应该能够匹配函数路由', () => {
            const matchedApps = microCore.getActiveApps('/app3/page');
            expect(matchedApps).toHaveLength(1);
            expect(matchedApps[0].name).toBe('app3');
        });
    });

    describe('事件系统', () => {
        it('应该能够监听和触发事件', () => {
            const handler = vi.fn();

            microCore.on('test-event', handler);
            microCore.emit('test-event', { data: 'test' });

            expect(handler).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该能够取消事件监听', () => {
            const handler = vi.fn();

            microCore.on('test-event', handler);
            microCore.off('test-event', handler);
            microCore.emit('test-event', { data: 'test' });

            expect(handler).not.toHaveBeenCalled();
        });

        it('应该能够监听一次性事件', () => {
            const handler = vi.fn();

            microCore.once('test-event', handler);
            microCore.emit('test-event', { data: 'test1' });
            microCore.emit('test-event', { data: 'test2' });

            expect(handler).toHaveBeenCalledTimes(1);
            expect(handler).toHaveBeenCalledWith({ data: 'test1' });
        });
    });

    describe('全局状态管理', () => {
        it('应该能够设置和获取全局状态', () => {
            microCore.setState('test-key', { value: 'test' });

            const state = microCore.getState('test-key');
            expect(state).toEqual({ value: 'test' });
        });

        it('应该能够删除全局状态', () => {
            microCore.setState('test-key', { value: 'test' });
            microCore.removeState('test-key');

            const state = microCore.getState('test-key');
            expect(state).toBeUndefined();
        });

        it('应该能够获取所有状态', () => {
            microCore.setState('key1', 'value1');
            microCore.setState('key2', 'value2');

            const allStates = microCore.getAllStates();
            expect(allStates).toEqual({
                key1: 'value1',
                key2: 'value2'
            });
        });
    });

    describe('插件系统', () => {
        it('应该能够注册和使用插件', () => {
            const plugin = {
                name: 'test-plugin',
                install: vi.fn(),
                beforeLoad: vi.fn(),
                afterLoad: vi.fn(),
                beforeMount: vi.fn(),
                afterMount: vi.fn(),
                beforeUnmount: vi.fn(),
                afterUnmount: vi.fn()
            };

            microCore.use(plugin);

            expect(plugin.install).toHaveBeenCalledWith(microCore);

            const registeredPlugin = microCore.getPlugin('test-plugin');
            expect(registeredPlugin).toBe(plugin);
        });

        it('应该在应用生命周期中调用插件钩子', async () => {
            const plugin = {
                name: 'test-plugin',
                install: vi.fn(),
                beforeLoad: vi.fn(),
                afterLoad: vi.fn(),
                beforeMount: vi.fn(),
                afterMount: vi.fn()
            };

            microCore.use(plugin);

            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test',
                loader: vi.fn().mockResolvedValue({
                    mount: vi.fn().mockResolvedValue(undefined),
                    unmount: vi.fn().mockResolvedValue(undefined)
                })
            };

            microCore.registerApp(appConfig);
            await microCore.startApp('test-app');

            expect(plugin.beforeLoad).toHaveBeenCalled();
            expect(plugin.afterLoad).toHaveBeenCalled();
            expect(plugin.beforeMount).toHaveBeenCalled();
            expect(plugin.afterMount).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该处理应用加载错误', async () => {
            const errorHandler = vi.fn();
            microCore.on('error', errorHandler);

            const appConfig: AppConfig = {
                name: 'error-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test',
                loader: vi.fn().mockRejectedValue(new Error('加载失败'))
            };

            microCore.registerApp(appConfig);

            await expect(microCore.startApp('error-app')).rejects.toThrow('加载失败');
            expect(errorHandler).toHaveBeenCalled();
        });

        it('应该处理应用挂载错误', async () => {
            const errorHandler = vi.fn();
            microCore.on('error', errorHandler);

            const appConfig: AppConfig = {
                name: 'mount-error-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test',
                loader: vi.fn().mockResolvedValue({
                    mount: vi.fn().mockRejectedValue(new Error('挂载失败')),
                    unmount: vi.fn().mockResolvedValue(undefined)
                })
            };

            microCore.registerApp(appConfig);

            await expect(microCore.startApp('mount-error-app')).rejects.toThrow('挂载失败');
            expect(errorHandler).toHaveBeenCalled();
        });
    });

    describe('性能监控', () => {
        it('应该记录应用加载时间', async () => {
            const appConfig: AppConfig = {
                name: 'perf-app',
                entry: 'http://localhost:3001',
                container: '#container',
                activeWhen: '/test',
                loader: vi.fn().mockResolvedValue({
                    mount: vi.fn().mockResolvedValue(undefined),
                    unmount: vi.fn().mockResolvedValue(undefined)
                })
            };

            microCore.registerApp(appConfig);
            await microCore.startApp('perf-app');

            const app = microCore.getApp('perf-app');
            expect(app?.loadTime).toBeGreaterThan(0);
            expect(app?.mountTime).toBeGreaterThan(0);
        });
    });
});