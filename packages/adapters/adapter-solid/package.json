{"name": "@micro-core/adapter-solid", "version": "0.1.0", "description": "Solid.js 框架适配器，用于将 Solid.js 应用集成到微前端架构中", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "solid", "solidjs", "adapter", "微前端"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"solid-js": "^1.8.0", "typescript": "^5.3.0", "tsup": "^8.0.0", "vitest": "^1.0.0"}, "peerDependencies": {"solid-js": "^1.8.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-solid"}, "publishConfig": {"access": "public"}}