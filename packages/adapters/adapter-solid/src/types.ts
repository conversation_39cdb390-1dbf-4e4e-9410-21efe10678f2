/**
 * Solid.js 适配器类型定义
 */

import type { BaseAdapterOptions } from '../../shared/base-adapter';

export interface SolidAdapterOptions extends BaseAdapterOptions {
    /** 是否启用热模块替换 */
    enableHMR?: boolean;
    /** 是否启用开发工具 */
    enableDevtools?: boolean;
    /** 自定义渲染选项 */
    renderOptions?: any;
    /** Solid.js 组件 */
    component?: any;
    /** 挂载目标选择器 */
    target?: string | HTMLElement;
    /** 组件属性 */
    props?: Record<string, any>;
}

export interface SolidAppInstance {
    /** 应用名称 */
    name: string;
    /** 销毁函数 */
    dispose: () => void;
    /** 挂载容器 */
    container: Element;
    /** 应用属性 */
    props: any;
}

export interface SolidLifecycleProps {
    /** 应用名称 */
    name: string;
    /** 挂载容器 */
    container: Element;
    /** 自定义属性 */
    [key: string]: any;
}