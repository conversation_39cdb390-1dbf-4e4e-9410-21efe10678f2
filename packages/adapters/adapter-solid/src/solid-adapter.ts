import type { MicroCoreKernel } from '@micro-core/core';
import { render } from 'solid-js/web';
import { BaseAdapter, type LifecycleManager } from '../../shared/base-adapter';
import type { SolidAdapterOptions, SolidAppInstance } from './types';

/**
 * Solid.js 生命周期管理器
 */
class SolidLifecycleManager implements LifecycleManager {
    private app: any = null;
    private dispose: (() => void) | null = null;
    private options: SolidAdapterOptions;

    constructor(private appName: string, private appConfig: any, options: SolidAdapterOptions) {
        this.options = { ...options, ...appConfig };
    }

    async bootstrap(): Promise<void> {
        console.log(`[SolidAdapter] 应用 ${this.appName} 启动中...`);
        
        if (this.options.enableDevtools) {
            console.log('Solid.js 应用运行在开发模式');
        }
    }

    async mount(container?: HTMLElement): Promise<any> {
        console.log(`[SolidAdapter] 应用 ${this.appName} 挂载中...`);

        if (!this.options.component) {
            throw new Error('Solid.js 组件未定义');
        }

        try {
            // 确定挂载目标
            let target: HTMLElement;

            if (container) {
                target = container;
            } else if (typeof this.options.target === 'string') {
                const element = document.querySelector(this.options.target);
                if (!element) {
                    throw new Error(`找不到目标元素: ${this.options.target}`);
                }
                target = element as HTMLElement;
            } else if (this.options.target instanceof HTMLElement) {
                target = this.options.target;
            } else {
                throw new Error('未指定有效的挂载目标');
            }

            // 渲染 Solid.js 应用
            this.dispose = render(() => this.options.component(this.options.props || {}), target);
            this.app = { name: this.appName, dispose: this.dispose, container: target };

            console.log(`[SolidAdapter] 应用 ${this.appName} 挂载成功`);
            return this.app;
        } catch (error) {
            console.error(`[SolidAdapter] 应用 ${this.appName} 挂载失败:`, error);
            throw error;
        }
    }

    async unmount(): Promise<void> {
        console.log(`[SolidAdapter] 应用 ${this.appName} 卸载中...`);

        if (this.dispose) {
            try {
                this.dispose();
                
                // 清理 DOM
                if (this.app?.container) {
                    this.app.container.innerHTML = '';
                }
                
                this.dispose = null;
                this.app = null;
                console.log(`[SolidAdapter] 应用 ${this.appName} 卸载成功`);
            } catch (error) {
                console.error(`[SolidAdapter] 应用 ${this.appName} 卸载失败:`, error);
                throw error;
            }
        }
    }

    async update(props: any): Promise<void> {
        console.log(`[SolidAdapter] 应用 ${this.appName} 更新中...`, props);
        
        // Solid.js 的响应式系统会自动处理更新
        // 这里可以添加自定义的更新逻辑
        if (this.app) {
            console.log(`[SolidAdapter] 应用 ${this.appName} 更新成功`);
        }
    }

    destroy(): void {
        if (this.dispose) {
            this.dispose();
            this.dispose = null;
            this.app = null;
        }
    }

    getLifecycles() {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }
}

/**
 * Solid.js 适配器
 * 负责管理 Solid.js 应用的生命周期
 */
export class SolidAdapter extends BaseAdapter {
    public readonly name = 'adapter-solid';
    public readonly version = '0.1.0';

    constructor(options: SolidAdapterOptions = {}) {
        super({
            enableHMR: true,
            enableDevtools: process.env.NODE_ENV === 'development',
            ...options
        });
    }

    getAdapterType(): string {
        return 'solid';
    }

    createLifecycleManager(appName: string, appConfig: any): LifecycleManager {
        return new SolidLifecycleManager(appName, appConfig, this.options as SolidAdapterOptions);
    }

}