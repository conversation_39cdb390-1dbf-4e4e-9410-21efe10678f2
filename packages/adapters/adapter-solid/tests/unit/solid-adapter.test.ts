/**
 * @fileoverview Solid.js Adapter Unit Tests
 * 全面测试 Solid.js 适配器的核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SolidAdapter } from '../../src/index';
import type { MicroCoreKernel } from '@micro-core/core';

// Mock Solid.js
vi.mock('solid-js/web', () => ({
  render: vi.fn((component, container) => {
    const dispose = vi.fn();
    // Simulate rendering
    if (container && typeof component === 'function') {
      const result = component();
      container.innerHTML = '<div>Solid App</div>';
    }
    return dispose;
  }),
  hydrate: vi.fn()
}));

vi.mock('solid-js', () => ({
  createSignal: vi.fn(() => [() => 'test', vi.fn()]),
  createEffect: vi.fn(),
  createMemo: vi.fn(),
  onMount: vi.fn(),
  onCleanup: vi.fn(),
  createContext: vi.fn(),
  useContext: vi.fn()
}));

// Mock MicroCore Kernel
const mockKernel: Partial<MicroCoreKernel> = {
  registerAdapter: vi.fn(),
  unregisterAdapter: vi.fn(),
  getEventBus: vi.fn(() => ({
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    once: vi.fn()
  })),
  getGlobalState: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn()
  })),
  getLogger: vi.fn(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }))
};

describe('SolidAdapter Core Functionality', () => {
  let adapter: SolidAdapter;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    adapter = new SolidAdapter({
      enableSignalBridge: true,
      enableContextProvider: true,
      enableHMR: true
    });
    
    mockContainer = document.createElement('div');
    mockContainer.id = 'solid-app-container';
    document.body.appendChild(mockContainer);
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (mockContainer.parentNode) {
      mockContainer.parentNode.removeChild(mockContainer);
    }
  });

  describe('Adapter Installation and Configuration', () => {
    it('should install adapter with default options', () => {
      const defaultAdapter = new SolidAdapter();
      defaultAdapter.install(mockKernel as MicroCoreKernel);
      
      expect(mockKernel.registerAdapter).toHaveBeenCalledWith('solid', expect.any(Object));
      expect(defaultAdapter.getStatus().options).toEqual({
        enableSignalBridge: true,
        enableContextProvider: true,
        enableHMR: false
      });
    });

    it('should install adapter with custom options', () => {
      const customOptions = {
        enableSignalBridge: false,
        enableContextProvider: false,
        enableHMR: true
      };
      
      const customAdapter = new SolidAdapter(customOptions);
      customAdapter.install(mockKernel as MicroCoreKernel);
      
      expect(mockKernel.registerAdapter).toHaveBeenCalledWith('solid', expect.any(Object));
      expect(customAdapter.getStatus().options).toEqual(customOptions);
    });

    it('should handle installation errors gracefully', () => {
      const errorKernel = {
        ...mockKernel,
        registerAdapter: vi.fn(() => {
          throw new Error('Registration failed');
        })
      };

      expect(() => {
        adapter.install(errorKernel as MicroCoreKernel);
      }).not.toThrow();
    });

    it('should uninstall adapter and clean up resources', () => {
      adapter.install(mockKernel as MicroCoreKernel);
      
      // Create some lifecycles
      const appConfig = { name: 'test-app', entry: './src/App.tsx' };
      adapter.createLifecycles('test-app-1', appConfig);
      adapter.createLifecycles('test-app-2', appConfig);
      
      expect(adapter.getStatus().activeApps).toBe(2);
      
      adapter.uninstall(mockKernel as MicroCoreKernel);
      
      expect(adapter.getStatus().activeApps).toBe(0);
      expect(mockKernel.unregisterAdapter).toHaveBeenCalledWith('solid');
    });
  });

  describe('Lifecycle Management', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should create lifecycles for new app', () => {
      const appConfig = {
        name: 'solid-dashboard',
        entry: './src/Dashboard.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('dashboard-app', appConfig);

      expect(lifecycles).toBeDefined();
      expect(typeof lifecycles.bootstrap).toBe('function');
      expect(typeof lifecycles.mount).toBe('function');
      expect(typeof lifecycles.unmount).toBe('function');
      expect(typeof lifecycles.update).toBe('function');
    });

    it('should reuse existing lifecycles for same app', () => {
      const appConfig = {
        name: 'solid-app',
        entry: './src/App.tsx',
        container: mockContainer
      };

      const lifecycles1 = adapter.createLifecycles('test-app', appConfig);
      const lifecycles2 = adapter.createLifecycles('test-app', appConfig);

      expect(lifecycles1).toBe(lifecycles2);
    });

    it('should handle bootstrap lifecycle with signal bridge', async () => {
      const appConfig = {
        name: 'solid-app',
        entry: './src/App.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        userId: '123',
        theme: 'dark',
        signals: {
          user: 'John Doe',
          count: 42
        }
      };

      await expect(lifecycles.bootstrap(props)).resolves.toBeUndefined();
    });

    it('should handle mount lifecycle with context provider', async () => {
      const appConfig = {
        name: 'solid-app',
        entry: './src/App.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: mockContainer,
        context: {
          theme: 'light',
          user: { id: 1, name: 'Alice' }
        }
      };

      await expect(lifecycles.mount(props)).resolves.toBeUndefined();
      
      // Verify container has content
      expect(mockContainer.innerHTML).toContain('Solid App');
    });

    it('should handle update lifecycle with signal updates', async () => {
      const appConfig = {
        name: 'solid-app',
        entry: './src/App.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      
      // Mount first
      await lifecycles.mount({ container: mockContainer });
      
      // Then update
      const updateProps = {
        container: mockContainer,
        signals: {
          count: 100,
          message: 'Updated!'
        }
      };

      await expect(lifecycles.update(updateProps)).resolves.toBeUndefined();
    });

    it('should handle unmount lifecycle with proper cleanup', async () => {
      const { render } = await import('solid-js/web');
      const mockDispose = vi.fn();
      (render as any).mockReturnValue(mockDispose);

      const appConfig = {
        name: 'solid-app',
        entry: './src/App.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      
      // Mount first
      await lifecycles.mount({ container: mockContainer });
      
      // Then unmount
      await expect(lifecycles.unmount({ container: mockContainer }))
        .resolves.toBeUndefined();
      
      expect(mockDispose).toHaveBeenCalled();
    });
  });

  describe('Signal Bridge Integration', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should bridge signals with global state', async () => {
      const appConfig = {
        name: 'signal-app',
        entry: './src/SignalApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('signal-app', appConfig);
      const globalState = mockKernel.getGlobalState!();
      
      const props = {
        container: mockContainer,
        signals: {
          user: 'John',
          count: 10
        }
      };

      await lifecycles.mount(props);

      // Verify global state integration
      expect(globalState.set).toHaveBeenCalledWith('user', 'John');
      expect(globalState.set).toHaveBeenCalledWith('count', 10);
    });

    it('should handle signal updates from global state', async () => {
      const appConfig = {
        name: 'signal-app',
        entry: './src/SignalApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('signal-app', appConfig);
      const globalState = mockKernel.getGlobalState!();
      
      await lifecycles.mount({ container: mockContainer });

      // Simulate global state change
      const mockCallback = vi.fn();
      globalState.subscribe('user', mockCallback);
      
      expect(globalState.subscribe).toHaveBeenCalledWith('user', expect.any(Function));
    });
  });

  describe('Context Provider Integration', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should provide micro-frontend context to Solid components', async () => {
      const appConfig = {
        name: 'context-app',
        entry: './src/ContextApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('context-app', appConfig);
      const eventBus = mockKernel.getEventBus!();
      
      const props = {
        container: mockContainer,
        context: {
          appName: 'context-app',
          eventBus,
          globalState: mockKernel.getGlobalState!()
        }
      };

      await lifecycles.mount(props);

      // Verify context is provided
      const { createContext } = await import('solid-js');
      expect(createContext).toHaveBeenCalled();
    });

    it('should handle context updates', async () => {
      const appConfig = {
        name: 'context-app',
        entry: './src/ContextApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('context-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });
      
      const updatedContext = {
        container: mockContainer,
        context: {
          theme: 'dark',
          user: { id: 2, name: 'Bob' }
        }
      };

      await expect(lifecycles.update(updatedContext)).resolves.toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should handle mount errors gracefully', async () => {
      const { render } = await import('solid-js/web');
      (render as any).mockImplementation(() => {
        throw new Error('Render failed');
      });

      const appConfig = {
        name: 'error-app',
        entry: './src/ErrorApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('error-app', appConfig);
      
      await expect(lifecycles.mount({ container: mockContainer }))
        .rejects.toThrow('Render failed');
    });

    it('should handle unmount errors gracefully', async () => {
      const mockDispose = vi.fn(() => {
        throw new Error('Dispose failed');
      });
      
      const { render } = await import('solid-js/web');
      (render as any).mockReturnValue(mockDispose);

      const appConfig = {
        name: 'error-app',
        entry: './src/ErrorApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('error-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });
      
      // Should handle dispose error gracefully
      await expect(lifecycles.unmount({ container: mockContainer }))
        .resolves.toBeUndefined();
    });

    it('should handle missing container', async () => {
      const appConfig = {
        name: 'no-container-app',
        entry: './src/App.tsx',
        container: null
      };

      const lifecycles = adapter.createLifecycles('no-container-app', appConfig);
      
      await expect(lifecycles.mount({ container: null }))
        .rejects.toThrow();
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should handle rapid lifecycle operations', async () => {
      const appConfig = {
        name: 'perf-app',
        entry: './src/PerfApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('perf-app', appConfig);
      
      const startTime = performance.now();

      // Perform 50 mount/unmount cycles
      for (let i = 0; i < 50; i++) {
        await lifecycles.mount({ container: mockContainer });
        await lifecycles.unmount({ container: mockContainer });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(1000);
    });

    it('should handle large signal datasets efficiently', async () => {
      const appConfig = {
        name: 'large-data-app',
        entry: './src/LargeDataApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('large-data-app', appConfig);
      
      // Create large signal dataset
      const largeSignals = {};
      for (let i = 0; i < 1000; i++) {
        largeSignals[`signal_${i}`] = `value_${i}`;
      }

      const startTime = performance.now();

      await lifecycles.mount({
        container: mockContainer,
        signals: largeSignals
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should handle large datasets efficiently
      expect(duration).toBeLessThan(500);
    });
  });

  describe('Memory Management', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should prevent memory leaks with proper cleanup', async () => {
      const appConfig = {
        name: 'memory-app',
        entry: './src/MemoryApp.tsx',
        container: mockContainer
      };

      // Create multiple instances
      for (let i = 0; i < 10; i++) {
        const lifecycles = adapter.createLifecycles(`memory-app-${i}`, appConfig);
        await lifecycles.mount({ container: mockContainer });
        await lifecycles.unmount({ container: mockContainer });
      }

      // Uninstall should clean up everything
      adapter.uninstall(mockKernel as MicroCoreKernel);
      expect(adapter.getStatus().activeApps).toBe(0);
    });

    it('should handle circular references in signals', async () => {
      const appConfig = {
        name: 'circular-app',
        entry: './src/CircularApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('circular-app', appConfig);
      
      // Create circular reference
      const objA: any = { name: 'A' };
      const objB: any = { name: 'B' };
      objA.ref = objB;
      objB.ref = objA;

      const props = {
        container: mockContainer,
        signals: {
          circular: objA
        }
      };

      // Should handle circular references without crashing
      await expect(lifecycles.mount(props)).resolves.toBeUndefined();
    });
  });

  describe('Adapter Status and Monitoring', () => {
    it('should provide accurate status information', () => {
      const status = adapter.getStatus();

      expect(status).toHaveProperty('activeApps');
      expect(status).toHaveProperty('options');
      expect(status).toHaveProperty('version');
      expect(typeof status.activeApps).toBe('number');
      expect(typeof status.options).toBe('object');
      expect(typeof status.version).toBe('string');
    });

    it('should track active apps correctly', () => {
      adapter.install(mockKernel as MicroCoreKernel);

      const appConfig = {
        name: 'tracking-app',
        entry: './src/TrackingApp.tsx',
        container: mockContainer
      };

      expect(adapter.getStatus().activeApps).toBe(0);

      adapter.createLifecycles('app-1', appConfig);
      expect(adapter.getStatus().activeApps).toBe(1);

      adapter.createLifecycles('app-2', appConfig);
      expect(adapter.getStatus().activeApps).toBe(2);

      adapter.createLifecycles('app-1', appConfig); // Should reuse
      expect(adapter.getStatus().activeApps).toBe(2);
    });
  });
});
