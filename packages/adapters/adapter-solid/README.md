# @micro-core/adapter-solid

> Solid.js 框架适配器，将微前端生命周期映射到 Solid.js 生命周期

## 📋 目录

- [概述](#概述)
- [安装](#安装)
- [快速开始](#快速开始)
- [API 参考](#api-参考)
- [配置选项](#配置选项)
- [信号桥接](#信号桥接)
- [上下文提供器](#上下文提供器)
- [生命周期](#生命周期)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [示例](#示例)

## 概述

`@micro-core/adapter-solid` 是 Micro-Core 微前端框架的 Solid.js 适配器，专门为 Solid.js 的响应式系统和细粒度更新机制设计。该适配器提供了信号桥接、上下文提供器等高级功能，确保 Solid.js 应用能够充分利用其性能优势。

### 主要特性

- ⚡ **信号桥接** - 将 Solid.js 信号与微前端全局状态双向同步
- 🎯 **上下文提供器** - 为子应用提供微前端上下文信息
- 🔄 **细粒度更新** - 充分利用 Solid.js 的细粒度响应式更新
- 🚀 **零配置启动** - 开箱即用的 Solid.js 应用集成
- 🛡️ **错误边界** - 内置错误处理和恢复机制
- 📊 **性能监控** - 专为 Solid.js 优化的性能监控
- 🔧 **HMR 支持** - 开发时热模块替换支持
- 🌐 **跨应用通信** - 基于信号的高效通信机制

## 安装

```bash
# 使用 npm
npm install @micro-core/adapter-solid

# 使用 yarn
yarn add @micro-core/adapter-solid

# 使用 pnpm
pnpm add @micro-core/adapter-solid
```

### 依赖要求

- Node.js >= 16.0.0
- solid-js >= 1.0.0
- @micro-core/core >= 0.1.0

## 快速开始

### 1. 创建 Solid.js 适配器

```typescript
import { SolidAdapter } from '@micro-core/adapter-solid';
import { MicroCoreKernel } from '@micro-core/core';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 创建 Solid.js 适配器
const solidAdapter = new SolidAdapter({
  enableSignalBridge: true,
  enableContextProvider: true,
  enableHMR: true
});

// 安装适配器
kernel.use(solidAdapter);
```

### 2. 配置子应用入口

```typescript
// 子应用入口文件 (sub-app-solid/src/index.tsx)
import type { Component } from 'solid-js';
import { render } from 'solid-js/web';
import App from './App';

let dispose: (() => void) | undefined;

export async function bootstrap(props: any) {
  console.log('Solid app bootstraped', props);
}

export async function mount(props: any) {
  console.log('Solid app mount', props);
  const container = props.container.querySelector('#sub-app-solid-root');
  dispose = render(() => <App {...props} />, container);
}

export async function unmount(props: any) {
  console.log('Solid app unmount', props);
  if (dispose) {
    dispose();
    dispose = undefined;
  }
}

export async function update(props: any) {
  console.log('Solid app update', props);
  // Solid.js 通过信号自动更新，无需手动处理
}
```

### 3. 注册和启动应用

```typescript
// 注册 Solid.js 应用
kernel.registerApp({
  name: 'solid-app',
  entry: '//localhost:8080',
  container: '#solid-container',
  activeWhen: '/solid',
  adapter: 'solid'
});

// 启动应用
kernel.start();
```

## API 参考

### SolidAdapter

#### 构造函数

```typescript
constructor(options?: SolidAdapterOptions)
```

#### 配置选项

```typescript
interface SolidAdapterOptions {
  // 是否启用信号桥接
  enableSignalBridge?: boolean;
  
  // 是否启用上下文提供器
  enableContextProvider?: boolean;
  
  // 是否启用 HMR
  enableHMR?: boolean;
  
  // 错误处理配置
  errorHandler?: (error: Error, info: string) => void;
  
  // 性能监控配置
  performanceMonitoring?: boolean;
  
  // 信号同步配置
  signalSyncOptions?: {
    debounceMs?: number;
    batchUpdates?: boolean;
  };
}
```

## 信号桥接

信号桥接是 Solid.js 适配器的核心功能，它将 Solid.js 的信号系统与微前端的全局状态管理连接起来。

### 基础用法

```typescript
// App.tsx
import { createSignal, createEffect } from 'solid-js';
import { useMicroContext } from '@micro-core/adapter-solid';

function App(props: any) {
  const microContext = useMicroContext();
  
  // 创建本地信号
  const [count, setCount] = createSignal(0);
  const [user, setUser] = createSignal(null);
  
  // 与全局状态同步
  createEffect(() => {
    microContext.globalState.set('count', count());
  });
  
  createEffect(() => {
    const globalUser = microContext.globalState.get('user');
    if (globalUser !== user()) {
      setUser(globalUser);
    }
  });
  
  return (
    <div>
      <h1>Solid Micro App</h1>
      <p>Count: {count()}</p>
      <p>User: {user()?.name || 'Anonymous'}</p>
      <button onClick={() => setCount(c => c + 1)}>
        Increment
      </button>
    </div>
  );
}
```

### 高级信号桥接

```typescript
// 使用信号桥接工具
import { createSignalBridge } from '@micro-core/adapter-solid';

function App(props: any) {
  // 创建双向同步的信号桥接
  const [globalCount, setGlobalCount] = createSignalBridge(
    'count', // 全局状态键
    0,       // 初始值
    {
      debounce: 100,    // 防抖延迟
      transform: {      // 数据转换
        toGlobal: (value) => ({ count: value, timestamp: Date.now() }),
        fromGlobal: (data) => data?.count || 0
      }
    }
  );
  
  return (
    <div>
      <p>Global Count: {globalCount()}</p>
      <button onClick={() => setGlobalCount(c => c + 1)}>
        Increment Global
      </button>
    </div>
  );
}
```

## 上下文提供器

上下文提供器为 Solid.js 应用提供微前端的上下文信息，包括事件总线、全局状态、路由器等。

### 使用上下文

```typescript
// MicroContext.tsx
import { createContext, useContext } from 'solid-js';

export const MicroContext = createContext();

export function useMicroContext() {
  const context = useContext(MicroContext);
  if (!context) {
    throw new Error('useMicroContext must be used within MicroContextProvider');
  }
  return context;
}

// App.tsx
import { MicroContextProvider } from '@micro-core/adapter-solid';

function App(props: any) {
  return (
    <MicroContextProvider value={props.microContext}>
      <MainComponent />
    </MicroContextProvider>
  );
}

function MainComponent() {
  const { eventBus, globalState, router } = useMicroContext();
  
  // 使用事件总线
  const handleClick = () => {
    eventBus.emit('solid:button-clicked', { timestamp: Date.now() });
  };
  
  // 使用路由器
  const navigate = (path: string) => {
    router.push(`/solid${path}`);
  };
  
  return (
    <div>
      <button onClick={handleClick}>Send Event</button>
      <button onClick={() => navigate('/dashboard')}>
        Go to Dashboard
      </button>
    </div>
  );
}
```

## 生命周期

### Bootstrap 阶段

```typescript
export async function bootstrap(props: any) {
  // 初始化全局配置
  window.__SOLID_APP_CONFIG__ = props.config;
  
  // 预加载资源
  await preloadAssets(props.assets);
  
  // 初始化信号桥接
  initializeSignalBridge(props.globalState);
}
```

### Mount 阶段

```typescript
export async function mount(props: any) {
  const container = props.container.querySelector('#sub-app-solid-root');
  
  // 创建微前端上下文
  const microContext = {
    eventBus: props.eventBus,
    globalState: props.globalState,
    router: props.router,
    logger: props.logger
  };
  
  // 渲染应用
  dispose = render(() => (
    <MicroContextProvider value={microContext}>
      <App {...props} />
    </MicroContextProvider>
  ), container);
  
  // 监听全局事件
  props.eventBus.on('theme-change', handleThemeChange);
}
```

### Update 阶段

```typescript
export async function update(props: any) {
  // Solid.js 通过信号自动处理更新
  // 只需要更新上下文中的数据
  if (props.microContext) {
    updateMicroContext(props.microContext);
  }
}
```

### Unmount 阶段

```typescript
export async function unmount(props: any) {
  // 清理事件监听器
  props.eventBus.off('theme-change', handleThemeChange);
  
  // 清理信号桥接
  cleanupSignalBridge();
  
  // 销毁应用
  if (dispose) {
    dispose();
    dispose = undefined;
  }
}
```

## 最佳实践

### 1. 信号组织

```typescript
// signals/index.ts
import { createSignal } from 'solid-js';
import { createSignalBridge } from '@micro-core/adapter-solid';

// 本地信号
export const [loading, setLoading] = createSignal(false);
export const [error, setError] = createSignal(null);

// 全局同步信号
export const [user, setUser] = createSignalBridge('user', null);
export const [theme, setTheme] = createSignalBridge('theme', 'light');
export const [notifications, setNotifications] = createSignalBridge('notifications', []);
```

### 2. 组件设计

```typescript
// components/UserProfile.tsx
import { Show, createEffect, onCleanup } from 'solid-js';
import { user, setUser } from '../signals';
import { useMicroContext } from '@micro-core/adapter-solid';

function UserProfile() {
  const { eventBus } = useMicroContext();
  
  // 监听用户登录事件
  const handleUserLogin = (userData) => {
    setUser(userData);
  };
  
  createEffect(() => {
    eventBus.on('user:login', handleUserLogin);
    
    onCleanup(() => {
      eventBus.off('user:login', handleUserLogin);
    });
  });
  
  return (
    <div class="user-profile">
      <Show when={user()} fallback={<div>Please login</div>}>
        <h2>Welcome, {user().name}!</h2>
        <p>Email: {user().email}</p>
      </Show>
    </div>
  );
}
```

### 3. 性能优化

```typescript
// 使用 createMemo 优化计算
import { createMemo, createSignal } from 'solid-js';

function ExpensiveComponent() {
  const [items, setItems] = createSignal([]);
  
  // 缓存昂贵的计算
  const expensiveComputation = createMemo(() => {
    return items().reduce((acc, item) => {
      // 复杂计算逻辑
      return acc + item.value * item.multiplier;
    }, 0);
  });
  
  return (
    <div>
      <p>Total: {expensiveComputation()}</p>
    </div>
  );
}
```

### 4. 错误处理

```typescript
// ErrorBoundary.tsx
import { createSignal, children, ErrorBoundary } from 'solid-js';

function AppErrorBoundary(props) {
  const [error, setError] = createSignal(null);
  const c = children(() => props.children);
  
  return (
    <ErrorBoundary
      fallback={(err, reset) => (
        <div class="error-boundary">
          <h2>Something went wrong</h2>
          <p>{err.message}</p>
          <button onClick={reset}>Try again</button>
        </div>
      )}
    >
      {c()}
    </ErrorBoundary>
  );
}
```

## 故障排除

### 常见问题

#### 1. 信号同步问题

**问题**: 信号与全局状态不同步

**解决方案**:
```typescript
// 确保信号桥接正确配置
const adapter = new SolidAdapter({
  enableSignalBridge: true,
  signalSyncOptions: {
    debounceMs: 50,
    batchUpdates: true
  }
});

// 检查信号桥接状态
console.log('Signal bridge status:', adapter.getSignalBridgeStatus());
```

#### 2. 上下文不可用

**问题**: 无法访问微前端上下文

**解决方案**:
```typescript
// 确保组件在 MicroContextProvider 内部
function App(props) {
  return (
    <MicroContextProvider value={props.microContext}>
      <YourComponent />
    </MicroContextProvider>
  );
}

// 添加上下文检查
function YourComponent() {
  const context = useMicroContext();
  
  if (!context) {
    console.error('MicroContext not available');
    return <div>Context Error</div>;
  }
  
  return <div>Component Content</div>;
}
```

#### 3. 内存泄漏

**问题**: 信号和效果没有正确清理

**解决方案**:
```typescript
import { createEffect, onCleanup } from 'solid-js';

function Component() {
  createEffect(() => {
    const subscription = someObservable.subscribe(handleData);
    
    // 重要：清理订阅
    onCleanup(() => {
      subscription.unsubscribe();
    });
  });
}
```

### 调试技巧

#### 1. 信号调试

```typescript
// 启用信号调试
import { createSignal } from 'solid-js';

const [count, setCount] = createSignal(0, {
  name: 'count', // 为信号命名便于调试
  equals: false  // 强制更新（调试时使用）
});

// 监控信号变化
createEffect(() => {
  console.log('Count changed:', count());
});
```

#### 2. 性能分析

```typescript
// 使用 Solid 开发工具
import { createEffect } from 'solid-js';

createEffect(() => {
  const start = performance.now();
  
  // 你的逻辑
  someExpensiveOperation();
  
  const end = performance.now();
  console.log(`Operation took ${end - start} milliseconds`);
});
```

## 示例

### 完整示例项目

查看 [examples/solid-micro-app](../../examples/solid-micro-app) 目录获取完整的示例项目。

### 在线演示

- [基础集成示例](https://micro-core-demo.netlify.app/solid)
- [信号桥接演示](https://micro-core-demo.netlify.app/solid-signals)
- [高级功能演示](https://micro-core-demo.netlify.app/solid-advanced)

## 贡献

我们欢迎所有形式的贡献！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详细信息。

## 许可证

MIT © [Echo](https://github.com/echo008)

---

## 更新日志

### v0.1.0 (2025-07-26)

- ✨ 初始版本发布
- ⚡ 信号桥接功能
- 🎯 上下文提供器
- 🔄 细粒度更新支持
- 🛡️ 错误边界和恢复
- 📊 性能监控和优化
- 🔧 HMR 支持
- 🌐 跨应用通信能力

---

**需要帮助？** 

- 📖 [文档](https://micro-core.dev/docs/adapters/solid)
- 💬 [讨论区](https://github.com/echo008/micro-core/discussions)
- 🐛 [问题反馈](https://github.com/echo008/micro-core/issues)
- 📧 [邮件支持](mailto:<EMAIL>)
