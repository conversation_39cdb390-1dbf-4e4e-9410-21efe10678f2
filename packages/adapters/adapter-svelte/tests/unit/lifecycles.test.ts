/**
 * @fileoverview Svelte Adapter Lifecycles Unit Tests
 * 全面测试 Svelte 适配器的生命周期管理功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SvelteAdapter } from '../../src/index';
import type { MicroCoreKernel } from '@micro-core/core';

// Mock Svelte
vi.mock('svelte', () => ({
  SvelteComponent: class MockSvelteComponent {
    constructor(options: any) {
      this.options = options;
    }
    $destroy() {
      this.destroyed = true;
    }
    destroyed = false;
    options: any;
  }
}));

// Mock MicroCore Kernel
const mockKernel: Partial<MicroCoreKernel> = {
  registerAdapter: vi.fn(),
  unregisterAdapter: vi.fn(),
  getEventBus: vi.fn(() => ({
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn()
  })),
  getGlobalState: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    subscribe: vi.fn()
  }))
};

describe('SvelteAdapter Lifecycles', () => {
  let adapter: SvelteAdapter;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    adapter = new SvelteAdapter();
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-container';
    document.body.appendChild(mockContainer);
    
    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (mockContainer.parentNode) {
      mockContainer.parentNode.removeChild(mockContainer);
    }
  });

  describe('Adapter Installation', () => {
    it('should install adapter successfully', () => {
      adapter.install(mockKernel as MicroCoreKernel);
      
      expect(mockKernel.registerAdapter).toHaveBeenCalledWith('svelte', expect.any(Object));
    });

    it('should handle installation errors gracefully', () => {
      const errorKernel = {
        ...mockKernel,
        registerAdapter: vi.fn(() => {
          throw new Error('Registration failed');
        })
      };

      expect(() => {
        adapter.install(errorKernel as MicroCoreKernel);
      }).not.toThrow();
    });
  });

  describe('Lifecycle Management', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should create lifecycles for new app', () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);

      expect(lifecycles).toBeDefined();
      expect(typeof lifecycles.bootstrap).toBe('function');
      expect(typeof lifecycles.mount).toBe('function');
      expect(typeof lifecycles.unmount).toBe('function');
    });

    it('should reuse existing lifecycles for same app', () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles1 = adapter.createLifecycles('test-app', appConfig);
      const lifecycles2 = adapter.createLifecycles('test-app', appConfig);

      expect(lifecycles1).toBe(lifecycles2);
    });

    it('should handle bootstrap lifecycle', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { data: 'test-data' };

      await expect(lifecycles.bootstrap(props)).resolves.toBeUndefined();
    });

    it('should handle mount lifecycle', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: mockContainer,
        data: 'test-data' 
      };

      await expect(lifecycles.mount(props)).resolves.toBeUndefined();
    });

    it('should handle unmount lifecycle', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: mockContainer,
        data: 'test-data' 
      };

      // Mount first
      await lifecycles.mount(props);
      
      // Then unmount
      await expect(lifecycles.unmount(props)).resolves.toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should handle mount errors gracefully', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: null // Invalid container
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: null,
        data: 'test-data' 
      };

      await expect(lifecycles.mount(props)).rejects.toThrow();
    });

    it('should handle unmount errors gracefully', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: mockContainer,
        data: 'test-data' 
      };

      // Try to unmount without mounting first
      await expect(lifecycles.unmount(props)).resolves.toBeUndefined();
    });
  });

  describe('Performance Tests', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should handle rapid mount/unmount cycles', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      const props = { 
        container: mockContainer,
        data: 'test-data' 
      };

      const startTime = performance.now();

      // Perform 100 mount/unmount cycles
      for (let i = 0; i < 100; i++) {
        await lifecycles.mount(props);
        await lifecycles.unmount(props);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
    });

    it('should handle multiple concurrent apps', async () => {
      const apps = Array.from({ length: 10 }, (_, i) => ({
        name: `test-app-${i}`,
        config: {
          name: `test-svelte-app-${i}`,
          entry: './src/App.svelte',
          container: mockContainer
        }
      }));

      const startTime = performance.now();

      // Create lifecycles for all apps concurrently
      const lifecyclesPromises = apps.map(app => 
        Promise.resolve(adapter.createLifecycles(app.name, app.config))
      );

      const allLifecycles = await Promise.all(lifecyclesPromises);

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(allLifecycles).toHaveLength(10);
      expect(duration).toBeLessThan(500); // Should be fast
    });
  });

  describe('Memory Management', () => {
    beforeEach(() => {
      adapter.install(mockKernel as MicroCoreKernel);
    });

    it('should clean up resources on uninstall', () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      // Create some lifecycles
      adapter.createLifecycles('test-app-1', appConfig);
      adapter.createLifecycles('test-app-2', appConfig);

      expect(adapter.getStatus().activeApps).toBe(2);

      // Uninstall adapter
      adapter.uninstall(mockKernel as MicroCoreKernel);

      expect(adapter.getStatus().activeApps).toBe(0);
      expect(mockKernel.unregisterAdapter).toHaveBeenCalledWith('svelte');
    });

    it('should prevent memory leaks with large datasets', async () => {
      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('test-app', appConfig);
      
      // Create large dataset
      const largeData = new Array(10000).fill(0).map((_, i) => ({
        id: i,
        data: `item-${i}`,
        nested: { value: i * 2 }
      }));

      const props = { 
        container: mockContainer,
        data: largeData 
      };

      await lifecycles.mount(props);
      await lifecycles.unmount(props);

      // Memory should be cleaned up
      expect(true).toBe(true); // Placeholder for memory assertion
    });
  });

  describe('Adapter Status', () => {
    it('should return correct status information', () => {
      const status = adapter.getStatus();

      expect(status).toHaveProperty('activeApps');
      expect(status).toHaveProperty('options');
      expect(typeof status.activeApps).toBe('number');
      expect(typeof status.options).toBe('object');
    });

    it('should track active apps correctly', () => {
      adapter.install(mockKernel as MicroCoreKernel);

      const appConfig = {
        name: 'test-svelte-app',
        entry: './src/App.svelte',
        container: mockContainer
      };

      expect(adapter.getStatus().activeApps).toBe(0);

      adapter.createLifecycles('test-app-1', appConfig);
      expect(adapter.getStatus().activeApps).toBe(1);

      adapter.createLifecycles('test-app-2', appConfig);
      expect(adapter.getStatus().activeApps).toBe(2);
    });
  });
});
