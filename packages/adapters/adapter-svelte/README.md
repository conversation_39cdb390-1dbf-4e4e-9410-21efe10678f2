# @micro-core/adapter-svelte

> Svelte 框架适配器，将微前端生命周期映射到 Svelte 生命周期

## 📋 目录

- [概述](#概述)
- [安装](#安装)
- [快速开始](#快速开始)
- [API 参考](#api-参考)
- [配置选项](#配置选项)
- [生命周期](#生命周期)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [示例](#示例)
- [贡献](#贡献)

## 概述

`@micro-core/adapter-svelte` 是 Micro-Core 微前端框架的 Svelte 适配器，它提供了将 Svelte 应用无缝集成到微前端架构中的能力。该适配器负责管理 Svelte 应用的生命周期，处理组件的挂载、卸载和更新操作。

### 主要特性

- 🚀 **零配置启动** - 开箱即用的 Svelte 应用集成
- 🔄 **完整生命周期管理** - 支持 bootstrap、mount、unmount、update 生命周期
- 🎯 **组件包装器** - 提供 Svelte 组件包装和增强功能
- 🛡️ **错误边界** - 内置错误处理和恢复机制
- 📊 **性能监控** - 内置性能监控和优化建议
- 🔧 **开发工具支持** - 支持 HMR 和开发者工具
- 🎨 **样式隔离** - 支持样式隔离和主题切换
- 🌐 **跨应用通信** - 与其他微前端应用的通信能力

## 安装

```bash
# 使用 npm
npm install @micro-core/adapter-svelte

# 使用 yarn
yarn add @micro-core/adapter-svelte

# 使用 pnpm
pnpm add @micro-core/adapter-svelte
```

### 依赖要求

- Node.js >= 16.0.0
- Svelte >= 3.0.0
- @micro-core/core >= 0.1.0

## 快速开始

### 1. 创建 Svelte 适配器

```typescript
import { SvelteAdapter } from '@micro-core/adapter-svelte';
import { MicroCoreKernel } from '@micro-core/core';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 创建 Svelte 适配器
const svelteAdapter = new SvelteAdapter({
  enableHMR: true,
  enableDevtools: true
});

// 安装适配器
kernel.use(svelteAdapter);
```

### 2. 配置子应用入口

```javascript
// 子应用入口文件 (sub-app-svelte/src/main.js)
import App from './App.svelte';

let app;

export async function bootstrap(props) {
  console.log('Svelte app bootstraped', props);
}

export async function mount(props) {
  console.log('Svelte app mount', props);
  const container = props.container.querySelector('#sub-app-svelte-root');
  app = new App({
    target: container,
    props: {
      // 传递 props
      ...props
    }
  });
}

export async function unmount(props) {
  console.log('Svelte app unmount', props);
  if (app) {
    app.$destroy();
    app = null;
  }
}

export async function update(props) {
  console.log('Svelte app update', props);
  if (app) {
    app.$set(props);
  }
}
```

### 3. 注册和启动应用

```typescript
// 注册 Svelte 应用
kernel.registerApp({
  name: 'svelte-app',
  entry: '//localhost:8080',
  container: '#svelte-container',
  activeWhen: '/svelte',
  adapter: 'svelte'
});

// 启动应用
kernel.start();
```

## API 参考

### SvelteAdapter

#### 构造函数

```typescript
constructor(options?: SvelteAdapterOptions)
```

#### 配置选项

```typescript
interface SvelteAdapterOptions {
  // 是否启用 HMR
  enableHMR?: boolean;
  
  // 是否启用开发者工具
  enableDevtools?: boolean;
  
  // 错误处理配置
  errorHandler?: (error: Error, info: string) => void;
  
  // 性能监控配置
  performanceMonitoring?: boolean;
  
  // 样式隔离配置
  styleIsolation?: boolean;
}
```

#### 方法

##### install(kernel: MicroCoreKernel): void

安装适配器到内核。

```typescript
const adapter = new SvelteAdapter();
adapter.install(kernel);
```

##### uninstall(kernel: MicroCoreKernel): void

从内核卸载适配器。

```typescript
adapter.uninstall(kernel);
```

##### createLifecycles(appName: string, appConfig: any): AppLifecycles

为指定应用创建生命周期管理器。

```typescript
const lifecycles = adapter.createLifecycles('my-app', {
  name: 'my-svelte-app',
  entry: './src/App.svelte',
  container: document.getElementById('app-container')
});
```

##### getStatus(): AdapterStatus

获取适配器当前状态。

```typescript
const status = adapter.getStatus();
console.log(`Active apps: ${status.activeApps}`);
```

### 生命周期接口

```typescript
interface AppLifecycles {
  bootstrap(props: any): Promise<void>;
  mount(props: any): Promise<void>;
  unmount(props: any): Promise<void>;
  update?(props: any): Promise<void>;
}
```

## 配置选项

### 基础配置

```typescript
const adapter = new SvelteAdapter({
  // 启用热模块替换
  enableHMR: process.env.NODE_ENV === 'development',
  
  // 启用开发者工具
  enableDevtools: true,
  
  // 性能监控
  performanceMonitoring: true,
  
  // 样式隔离
  styleIsolation: true
});
```

### 错误处理配置

```typescript
const adapter = new SvelteAdapter({
  errorHandler: (error, info) => {
    console.error('Svelte app error:', error);
    console.error('Error info:', info);
    
    // 发送错误到监控系统
    errorReporting.captureException(error, { extra: { info } });
  }
});
```

### 性能优化配置

```typescript
const adapter = new SvelteAdapter({
  performanceMonitoring: true,
  
  // 自定义性能阈值
  performanceThresholds: {
    mountTime: 100,    // 挂载时间阈值 (ms)
    renderTime: 16,    // 渲染时间阈值 (ms)
    memoryUsage: 50    // 内存使用阈值 (MB)
  }
});
```

## 生命周期

### Bootstrap 阶段

应用初始化阶段，用于准备应用资源和配置。

```javascript
export async function bootstrap(props) {
  // 初始化全局配置
  window.__SVELTE_APP_CONFIG__ = props.config;
  
  // 预加载资源
  await preloadAssets(props.assets);
  
  // 初始化状态管理
  initializeStore(props.initialState);
}
```

### Mount 阶段

应用挂载阶段，创建 Svelte 实例并渲染到容器。

```javascript
export async function mount(props) {
  const container = props.container.querySelector('#sub-app-svelte-root');
  
  app = new App({
    target: container,
    props: {
      // 应用属性
      ...props,
      
      // 微前端上下文
      microContext: {
        eventBus: props.eventBus,
        globalState: props.globalState,
        router: props.router
      }
    }
  });
  
  // 监听全局事件
  props.eventBus.on('theme-change', handleThemeChange);
}
```

### Update 阶段

应用更新阶段，更新 Svelte 实例的属性。

```javascript
export async function update(props) {
  if (app) {
    // 更新应用属性
    app.$set({
      ...props,
      microContext: {
        eventBus: props.eventBus,
        globalState: props.globalState,
        router: props.router
      }
    });
  }
}
```

### Unmount 阶段

应用卸载阶段，清理 Svelte 实例和相关资源。

```javascript
export async function unmount(props) {
  if (app) {
    // 清理事件监听器
    props.eventBus.off('theme-change', handleThemeChange);
    
    // 销毁 Svelte 实例
    app.$destroy();
    app = null;
  }
  
  // 清理全局状态
  cleanupGlobalState();
}
```

## 最佳实践

### 1. 组件设计

```svelte
<!-- App.svelte -->
<script>
  import { onMount, onDestroy } from 'svelte';
  import { createEventDispatcher } from 'svelte';
  
  export let microContext;
  export let initialData = {};
  
  const dispatch = createEventDispatcher();
  
  let data = initialData;
  
  onMount(() => {
    // 订阅全局状态
    microContext.globalState.subscribe('user', handleUserChange);
    
    // 发送应用就绪事件
    dispatch('app-ready', { name: 'svelte-app' });
  });
  
  onDestroy(() => {
    // 清理订阅
    microContext.globalState.unsubscribe('user', handleUserChange);
  });
  
  function handleUserChange(user) {
    data = { ...data, user };
  }
</script>

<div class="svelte-app">
  <h1>Svelte Micro App</h1>
  <p>User: {data.user?.name || 'Anonymous'}</p>
</div>

<style>
  .svelte-app {
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
</style>
```

### 2. 状态管理

```javascript
// store.js
import { writable } from 'svelte/store';

export const appState = writable({
  user: null,
  theme: 'light',
  loading: false
});

// 与全局状态同步
export function syncWithGlobalState(globalState) {
  // 订阅全局状态变化
  globalState.subscribe('user', (user) => {
    appState.update(state => ({ ...state, user }));
  });
  
  // 将本地状态变化同步到全局
  appState.subscribe((state) => {
    globalState.set('svelte-app-state', state);
  });
}
```

### 3. 路由集成

```javascript
// router.js
import { push } from 'svelte-spa-router';

export function integrateWithMicroRouter(microRouter) {
  // 监听微前端路由变化
  microRouter.on('route-change', (route) => {
    if (route.path.startsWith('/svelte')) {
      const subPath = route.path.replace('/svelte', '') || '/';
      push(subPath);
    }
  });
  
  // 子应用路由变化时通知主路由
  return function navigateToSubRoute(subPath) {
    microRouter.push(`/svelte${subPath}`);
  };
}
```

### 4. 性能优化

```svelte
<!-- 使用懒加载 -->
<script>
  import { onMount } from 'svelte';
  
  let HeavyComponent;
  
  onMount(async () => {
    // 懒加载重型组件
    const module = await import('./HeavyComponent.svelte');
    HeavyComponent = module.default;
  });
</script>

{#if HeavyComponent}
  <svelte:component this={HeavyComponent} />
{:else}
  <div>Loading...</div>
{/if}
```

## 故障排除

### 常见问题

#### 1. 应用无法挂载

**问题**: Svelte 应用无法正确挂载到容器

**解决方案**:
```javascript
export async function mount(props) {
  // 确保容器存在
  const container = props.container?.querySelector('#sub-app-svelte-root');
  if (!container) {
    throw new Error('Container not found: #sub-app-svelte-root');
  }
  
  // 确保容器为空
  container.innerHTML = '';
  
  app = new App({
    target: container,
    props
  });
}
```

#### 2. 样式冲突

**问题**: 样式与主应用或其他子应用冲突

**解决方案**:
```svelte
<style>
  /* 使用 :global() 修饰符避免样式隔离 */
  :global(.svelte-app) {
    /* 全局样式 */
  }
  
  /* 或使用 CSS Modules */
  .component :global(.external-class) {
    /* 外部样式覆盖 */
  }
</style>
```

#### 3. 事件通信问题

**问题**: 与其他微前端应用的事件通信不正常

**解决方案**:
```javascript
export async function mount(props) {
  // 确保事件总线可用
  if (!props.eventBus) {
    console.warn('Event bus not available');
    return;
  }
  
  // 使用命名空间避免事件冲突
  props.eventBus.on('svelte:user-login', handleUserLogin);
  props.eventBus.emit('svelte:app-mounted', { name: 'svelte-app' });
}
```

### 调试技巧

#### 1. 启用详细日志

```typescript
const adapter = new SvelteAdapter({
  enableDevtools: true,
  logLevel: 'debug'
});
```

#### 2. 性能分析

```javascript
// 在组件中添加性能监控
import { onMount, beforeUpdate, afterUpdate } from 'svelte';

let renderStart;

beforeUpdate(() => {
  renderStart = performance.now();
});

afterUpdate(() => {
  const renderTime = performance.now() - renderStart;
  if (renderTime > 16) {
    console.warn(`Slow render: ${renderTime.toFixed(2)}ms`);
  }
});
```

## 示例

### 完整示例项目

查看 [examples/svelte-micro-app](../../examples/svelte-micro-app) 目录获取完整的示例项目。

### 在线演示

- [基础集成示例](https://micro-core-demo.netlify.app/svelte)
- [高级功能演示](https://micro-core-demo.netlify.app/svelte-advanced)

## 贡献

我们欢迎所有形式的贡献！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详细信息。

### 开发设置

```bash
# 克隆仓库
git clone https://github.com/echo008/micro-core.git

# 安装依赖
cd micro-core
pnpm install

# 开发模式
pnpm dev

# 运行测试
pnpm test

# 构建
pnpm build
```

## 许可证

MIT © [Echo](https://github.com/echo008)

---

## 更新日志

### v0.1.0 (2025-07-26)

- ✨ 初始版本发布
- 🚀 支持 Svelte 3.x+ 集成
- 🔄 完整生命周期管理
- 🛡️ 错误边界和恢复
- 📊 性能监控和优化
- 🎨 样式隔离支持
- 🌐 跨应用通信能力

---

**需要帮助？** 

- 📖 [文档](https://micro-core.dev/docs/adapters/svelte)
- 💬 [讨论区](https://github.com/echo008/micro-core/discussions)
- 🐛 [问题反馈](https://github.com/echo008/micro-core/issues)
- 📧 [邮件支持](mailto:<EMAIL>)
