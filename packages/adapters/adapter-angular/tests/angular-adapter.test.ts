/**
 * Angular Adapter Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AngularAdapter } from '../src/angular-adapter';
import type { AngularAdapterConfig, AngularAppInstance } from '../src/types';

// Mock Angular dependencies
const mockPlatformBrowserDynamic = {
  bootstrapModule: vi.fn().mockResolvedValue({
    destroy: vi.fn(),
    injector: {
      get: vi.fn()
    }
  })
};

const mockNgZone = {
  run: vi.fn((fn) => fn()),
  runOutsideAngular: vi.fn((fn) => fn())
};

// Mock global Angular
Object.defineProperty(global, 'ng', {
  value: {
    core: {
      NgZone: vi.fn(() => mockNgZone),
      ApplicationRef: vi.fn(),
      Injector: vi.fn()
    },
    platformBrowserDynamic: {
      platformBrowserDynamic: vi.fn(() => mockPlatformBrowserDynamic)
    }
  },
  writable: true
});

describe('AngularAdapter', () => {
  let adapter: AngularAdapter;
  let mockContainer: HTMLElement;
  let mockConfig: AngularAdapterConfig;

  beforeEach(() => {
    adapter = new AngularAdapter();
    
    // Setup DOM
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-app';
    document.body.appendChild(mockContainer);

    mockConfig = {
      id: 'test-angular-app',
      name: 'Test Angular App',
      module: {
        bootstrap: ['AppComponent'],
        declarations: ['AppComponent'],
        imports: [],
        providers: []
      },
      component: {
        selector: 'app-root',
        template: '<div>Test Angular App</div>'
      }
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Cleanup DOM
    if (mockContainer.parentNode) {
      document.body.removeChild(mockContainer);
    }
    vi.restoreAllMocks();
  });

  describe('初始化', () => {
    it('应该正确初始化适配器', () => {
      expect(adapter.name).toBe('angular-adapter');
      expect(adapter.version).toBe('1.0.0');
      expect(adapter.supportedVersions).toEqual(['^15.0.0', '^16.0.0', '^17.0.0']);
    });
  });

  describe('canHandle', () => {
    it('应该能够处理有效的 Angular 配置', async () => {
      const result = await adapter.canHandle(mockConfig);
      expect(result).toBe(true);
    });

    it('应该拒绝无效配置', async () => {
      const invalidConfig = {
        id: 'test',
        name: 'test'
        // 缺少必要的模块配置
      };

      const result = await adapter.canHandle(invalidConfig as AngularAdapterConfig);
      expect(result).toBe(false);
    });

    it('应该检查 Angular 可用性', async () => {
      // 临时移除 ng
      const originalNg = global.ng;
      delete (global as any).ng;

      const result = await adapter.canHandle(mockConfig);
      expect(result).toBe(false);

      // 恢复 ng
      (global as any).ng = originalNg;
    });
  });

  describe('load', () => {
    it('应该成功加载 Angular 应用', async () => {
      const appInstance = await adapter.load(mockConfig);

      expect(appInstance).toBeDefined();
      expect(appInstance.id).toBe(mockConfig.id);
      expect(appInstance.name).toBe(mockConfig.name);
      expect(appInstance.status).toBe('loaded');
    });

    it('应该处理加载错误', async () => {
      mockPlatformBrowserDynamic.bootstrapModule.mockRejectedValueOnce(
        new Error('Bootstrap failed')
      );

      await expect(adapter.load(mockConfig)).rejects.toThrow('Bootstrap failed');
    });
  });

  describe('mount', () => {
    let appInstance: AngularAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
    });

    it('应该成功挂载应用', async () => {
      await adapter.mount(appInstance.id, mockContainer);

      expect(appInstance.status).toBe('mounted');
      expect(appInstance.container).toBe(mockContainer);
      expect(mockPlatformBrowserDynamic.bootstrapModule).toHaveBeenCalled();
    });

    it('应该处理挂载错误', async () => {
      mockPlatformBrowserDynamic.bootstrapModule.mockRejectedValueOnce(
        new Error('Mount failed')
      );

      await expect(adapter.mount(appInstance.id, mockContainer)).rejects.toThrow('Mount failed');
    });
  });

  describe('unmount', () => {
    let appInstance: AngularAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
      await adapter.mount(appInstance.id, mockContainer);
    });

    it('应该成功卸载应用', async () => {
      await adapter.unmount(appInstance.id);

      expect(appInstance.status).toBe('unmounted');
      expect(appInstance.moduleRef?.destroy).toHaveBeenCalled();
    });
  });

  describe('destroy', () => {
    let appInstance: AngularAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
    });

    it('应该成功销毁应用', async () => {
      await adapter.destroy(appInstance.id);

      expect(adapter.getAppInstance(appInstance.id)).toBeUndefined();
    });
  });

  describe('错误处理', () => {
    it('应该处理模块引导错误', async () => {
      const errorConfig = {
        ...mockConfig,
        module: null as any
      };

      await expect(adapter.load(errorConfig)).rejects.toThrow();
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成加载', async () => {
      const startTime = Date.now();
      await adapter.load(mockConfig);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(2000); // 2秒内完成
    });
  });
});
