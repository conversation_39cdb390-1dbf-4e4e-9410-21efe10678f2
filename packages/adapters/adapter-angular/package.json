{"name": "@micro-core/adapter-angular", "version": "0.1.0", "description": "Micro-Core Angular 适配器 - 支持 Angular 框架的微前端集成", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "angular", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-angular"}, "peerDependencies": {"@micro-core/core": "workspace:*", "@angular/core": ">=12.0.0", "@angular/platform-browser-dynamic": ">=12.0.0"}, "devDependencies": {"@micro-core/core": "workspace:*", "@types/node": "^20.0.0", "typescript": "^5.3.0", "vite": "^7.0.4", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}