/**
 * Angular 适配器
 * 提供 Angular 应用的微前端集成支持
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { BaseAdapter, BaseAdapterOptions, LifecycleManager } from '../../shared/base-adapter';

/**
 * Angular 适配器配置选项
 */
export interface AngularAdapterOptions {
    /** Angular 版本 */
    version?: string;
    /** 是否启用 Zone.js */
    enableZoneJs?: boolean;
    /** 是否启用路由集成 */
    enableRouterIntegration?: boolean;
    /** 是否启用服务集成 */
    enableServiceIntegration?: boolean;
    /** 自定义模块加载器 */
    moduleLoader?: (appName: string) => Promise<any>;
    /** 是否启用日志 */
    enableLogging?: boolean;
}

/**
 * Angular 应用配置
 */
export interface AngularAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用模块 */
    module: any;
    /** 挂载容器 */
    container: string | HTMLElement;
    /** 应用属性 */
    props?: Record<string, any>;
    /** 路由配置 */
    routing?: {
        /** 基础路径 */
        basePath?: string;
        /** 路由策略 */
        strategy?: 'hash' | 'path';
    };
}

/**
 * Angular 生命周期函数
 */
export interface AngularLifecycles {
    bootstrap: () => Promise<any>;
    mount: (props: any) => Promise<any>;
    unmount: (props: any) => Promise<any>;
    update?: (props: any) => Promise<any>;
}

/**
 * Angular 应用实例
 */
export class AngularAppInstance {
    private name: string;
    private config: AngularAppConfig;
    private moduleRef: any;
    private componentRef: any;
    private isBootstrapped = false;
    private isMounted = false;

    constructor(name: string, config: AngularAppConfig) {
        this.name = name;
        this.config = config;
    }

    /**
     * 启动应用
     */
    async bootstrap(): Promise<void> {
        if (this.isBootstrapped) {
            return;
        }

        try {
            // 动态导入 Angular 依赖（避免编译时错误）
            const { platformBrowserDynamic } = await import('@angular/platform-browser-dynamic');
            const { NgZone } = await import('@angular/core');

            // 创建平台
            const platform = platformBrowserDynamic();

            // 启动模块
            this.moduleRef = await platform.bootstrapModule(this.config.module);

            this.isBootstrapped = true;

            console.log(`[AngularAdapter] 应用已启动: ${this.name}`);
        } catch (error) {
            console.error(`[AngularAdapter] 应用启动失败: ${this.name}`, error);
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    async mount(props: any = {}): Promise<void> {
        if (!this.isBootstrapped) {
            await this.bootstrap();
        }

        if (this.isMounted) {
            return;
        }

        try {
            // 获取挂载容器
            const container = typeof this.config.container === 'string'
                ? document.querySelector(this.config.container)
                : this.config.container;

            if (!container) {
                throw new Error(`挂载容器未找到: ${this.config.container}`);
            }

            // 创建应用组件
            const appComponent = this.moduleRef.injector.get('APP_COMPONENT', null);
            if (appComponent) {
                // 这里需要根据实际的 Angular 应用结构来实现
                // 由于每个 Angular 应用的结构可能不同，这里提供一个通用的实现思路
            }

            this.isMounted = true;

            console.log(`[AngularAdapter] 应用已挂载: ${this.name}`);
        } catch (error) {
            console.error(`[AngularAdapter] 应用挂载失败: ${this.name}`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmount(): Promise<void> {
        if (!this.isMounted) {
            return;
        }

        try {
            // 销毁组件引用
            if (this.componentRef) {
                this.componentRef.destroy();
                this.componentRef = null;
            }

            this.isMounted = false;

            console.log(`[AngularAdapter] 应用已卸载: ${this.name}`);
        } catch (error) {
            console.error(`[AngularAdapter] 应用卸载失败: ${this.name}`, error);
            throw error;
        }
    }

    /**
     * 更新应用属性
     */
    async update(props: any): Promise<void> {
        if (!this.isMounted) {
            return;
        }

        try {
            // 更新组件属性
            if (this.componentRef && this.componentRef.instance) {
                Object.assign(this.componentRef.instance, props);
                this.componentRef.changeDetectorRef?.detectChanges();
            }

            console.log(`[AngularAdapter] 应用属性已更新: ${this.name}`);
        } catch (error) {
            console.error(`[AngularAdapter] 应用属性更新失败: ${this.name}`, error);
            throw error;
        }
    }

    /**
     * 销毁应用
     */
    destroy(): void {
        try {
            this.unmount();

            // 销毁模块引用
            if (this.moduleRef) {
                this.moduleRef.destroy();
                this.moduleRef = null;
            }

            this.isBootstrapped = false;

            console.log(`[AngularAdapter] 应用已销毁: ${this.name}`);
        } catch (error) {
            console.error(`[AngularAdapter] 应用销毁失败: ${this.name}`, error);
        }
    }

    /**
     * 获取应用状态
     */
    getStatus() {
        return {
            name: this.name,
            isBootstrapped: this.isBootstrapped,
            isMounted: this.isMounted,
            hasModuleRef: !!this.moduleRef,
            hasComponentRef: !!this.componentRef
        };
    }
}

/**
 * Angular 适配器类
 */
export class AngularAdapter extends BaseAdapter {
    public readonly name = 'adapter-angular';
    public readonly version = '1.0.0';

    private appInstances: Map<string, AngularAppInstance> = new Map();

    constructor(options: AngularAdapterOptions = {}) {
        super({
            version: '16.0.0',
            enableZoneJs: true,
            enableRouterIntegration: true,
            enableServiceIntegration: true,
            enableLogging: false,
            ...options
        });
    }

    /**
     * 获取适配器类型
     */
    protected getAdapterType(): string {
        return 'angular';
    }

    /**
     * 创建生命周期管理器
     */
    protected createLifecycleManager(
        appName: string,
        appConfig: any,
        options: BaseAdapterOptions
    ): LifecycleManager {
        this.validateAppConfig(appConfig);
        return new AngularLifecycleManager(appName, appConfig, options as AngularAdapterOptions, this.appInstances);
    }

    /**
     * Angular 特定的安装逻辑
     */
    protected override onInstall(kernel: MicroCoreKernel): void {
        const options = this.options as AngularAdapterOptions;
        
        // 初始化 Zone.js（如果启用）
        if (options.enableZoneJs && typeof window !== 'undefined') {
            this.initializeZoneJs();
        }

        // 设置 Angular 开发工具
        if (options.enableDevtools && typeof window !== 'undefined') {
            (window as any).ng = (window as any).ng || {};
        }

        if (options.enableLogging) {
            console.log('[AngularAdapter] Angular 适配器已安装');
        }
    }

    /**
     * Angular 特定的卸载逻辑
     */
    protected override onUninstall(kernel: MicroCoreKernel): void {
        const options = this.options as AngularAdapterOptions;
        
        // 销毁所有应用实例
        this.appInstances.forEach(instance => {
            instance.destroy();
        });
        this.appInstances.clear();

        if (options.enableLogging) {
            console.log('[AngularAdapter] Angular 适配器已卸载');
        }
    }

    /**
     * 创建生命周期函数
     */
    createLifecycles(config: AngularAppConfig): AngularLifecycles {
        const instance = new AngularAppInstance(config.name, config);
        this.appInstances.set(config.name, instance);

        return {
            bootstrap: async () => {
                return instance.bootstrap();
            },
            mount: async (props: any) => {
                return instance.mount(props);
            },
            unmount: async (props: any) => {
                return instance.unmount();
            },
            update: async (props: any) => {
                return instance.update(props);
            }
        };
    }

    /**
     * 获取应用实例
     */
    getAppInstance(name: string): AngularAppInstance | undefined {
        return this.appInstances.get(name);
    }

    /**
     * 获取所有应用实例
     */
    getAllAppInstances(): Map<string, AngularAppInstance> {
        return new Map(this.appInstances);
    }

    /**
     * 获取适配器状态
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            options: this.options,
            appCount: this.appInstances.size,
            apps: Array.from(this.appInstances.keys())
        };
    }
}

/**
 * 创建 Angular 适配器实例
 */
export function createAngularAdapter(options?: AngularAdapterOptions): AngularAdapter {
    return new AngularAdapter(options);
}

/**
 * 默认导出
 */
export default AngularAdapter;
