import type { ApplicationRef, NgModuleRef, PlatformRef } from '@angular/core';
import { createApplication, platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import type { AppLifecycles, AppProps } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * Angular 适配器配置
 */
export interface AngularAdapterConfig {
    /** 应用名称 */
    name: string;
    /** Angular 模块或组件 */
    module?: any;
    /** 根组件 */
    component?: any;
    /** 是否使用独立组件 */
    standalone?: boolean;
    /** 平台配置 */
    platformOptions?: any;
    /** 模块配置 */
    moduleOptions?: any;
    /** 应用配置 */
    appOptions?: any;
}

/**
 * Angular 适配器
 * 负责管理 Angular 应用的生命周期
 */
export class AngularAdapter {
    private config: AngularAdapterConfig;
    private platformRef?: PlatformRef;
    private moduleRef?: NgModuleRef<any>;
    private appRef?: ApplicationRef;
    private isBootstrapped = false;
    private isMounted = false;

    constructor(config: AngularAdapterConfig) {
        this.config = config;
    }

    /**
     * 创建生命周期函数
     */
    createLifecycles(): AppLifecycles {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }

    /**
     * 启动应用
     */
    private async bootstrap(props: AppProps): Promise<void> {
        if (this.isBootstrapped) {
            logger.warn(`Angular 应用 ${this.config.name} 已经启动`);
            return;
        }

        try {
            // 创建平台
            this.platformRef = platformBrowserDynamic(this.config.platformOptions);

            if (this.config.standalone && this.config.component) {
                // 独立组件模式
                this.appRef = await createApplication({
                    providers: this.config.appOptions?.providers || []
                });
            } else if (this.config.module) {
                // 模块模式
                this.moduleRef = await this.platformRef.bootstrapModule(
                    this.config.module,
                    this.config.moduleOptions
                );
                this.appRef = this.moduleRef.injector.get(ApplicationRef);
            } else {
                throw new Error('必须提供 Angular 模块或独立组件');
            }

            this.isBootstrapped = true;
            logger.info(`Angular 应用 ${this.config.name} 启动成功`);
        } catch (error) {
            logger.error(`Angular 应用 ${this.config.name} 启动失败:`, error);
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    private async mount(props: AppProps): Promise<void> {
        if (!this.isBootstrapped) {
            throw new Error(`Angular 应用 ${this.config.name} 未启动，无法挂载`);
        }

        if (this.isMounted) {
            logger.warn(`Angular 应用 ${this.config.name} 已经挂载`);
            return;
        }

        try {
            if (!this.appRef) {
                throw new Error('ApplicationRef 不存在');
            }

            // 创建根组件
            if (this.config.standalone && this.config.component) {
                // 独立组件模式
                const componentRef = this.appRef.bootstrap(this.config.component);

                // 将组件插入到容器中
                if (props.container && componentRef.location?.nativeElement) {
                    props.container.appendChild(componentRef.location.nativeElement);
                }
            } else if (this.config.component) {
                // 模块模式下的组件
                const componentRef = this.appRef.bootstrap(this.config.component);

                // 将组件插入到容器中
                if (props.container && componentRef.location?.nativeElement) {
                    props.container.appendChild(componentRef.location.nativeElement);
                }
            }

            this.isMounted = true;
            logger.info(`Angular 应用 ${this.config.name} 挂载成功`);
        } catch (error) {
            logger.error(`Angular 应用 ${this.config.name} 挂载失败:`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    private async unmount(props: AppProps): Promise<void> {
        if (!this.isMounted) {
            logger.warn(`Angular 应用 ${this.config.name} 未挂载，无需卸载`);
            return;
        }

        try {
            // 销毁应用
            if (this.appRef) {
                this.appRef.destroy();
                this.appRef = undefined;
            }

            // 销毁模块
            if (this.moduleRef) {
                this.moduleRef.destroy();
                this.moduleRef = undefined;
            }

            // 销毁平台
            if (this.platformRef) {
                this.platformRef.destroy();
                this.platformRef = undefined;
            }

            // 清理 DOM
            if (props.container) {
                props.container.innerHTML = '';
            }

            this.isMounted = false;
            this.isBootstrapped = false;

            logger.info(`Angular 应用 ${this.config.name} 卸载成功`);
        } catch (error) {
            logger.error(`Angular 应用 ${this.config.name} 卸载失败:`, error);
            throw error;
        }
    }

    /**
     * 更新应用
     */
    private async update(props: AppProps): Promise<void> {
        if (!this.isMounted) {
            logger.warn(`Angular 应用 ${this.config.name} 未挂载，无法更新`);
            return;
        }

        try {
            // Angular 应用的更新通常通过服务或状态管理来处理
            // 这里可以触发变更检测
            if (this.appRef) {
                this.appRef.tick();
            }

            logger.info(`Angular 应用 ${this.config.name} 更新成功`);
        } catch (error) {
            logger.error(`Angular 应用 ${this.config.name} 更新失败:`, error);
            throw error;
        }
    }

    /**
     * 获取应用引用
     */
    getApplicationRef(): ApplicationRef | undefined {
        return this.appRef;
    }

    /**
     * 获取模块引用
     */
    getModuleRef(): NgModuleRef<any> | undefined {
        return this.moduleRef;
    }

    /**
     * 获取平台引用
     */
    getPlatformRef(): PlatformRef | undefined {
        return this.platformRef;
    }

    /**
     * 检查是否已启动
     */
    isAppBootstrapped(): boolean {
        return this.isBootstrapped;
    }

    /**
     * 检查是否已挂载
     */
    isAppMounted(): boolean {
        return this.isMounted;
    }
}

/**
 * 创建 Angular 适配器
 */
export function createAngularAdapter(config: AngularAdapterConfig): AngularAdapter {
    return new AngularAdapter(config);
}

/**
 * 创建 Angular 生命周期函数
 */
export function createAngularLifecycles(config: AngularAdapterConfig): AppLifecycles {
    const adapter = new AngularAdapter(config);
    return adapter.createLifecycles();
}