import { resolve } from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'MicroCoreAdapterAngular',
            formats: ['es', 'cjs'],
            fileName: (format) => `index.${format === 'es' ? 'esm' : format}.js`
        },
        rollupOptions: {
            external: [
                '@micro-core/core',
                '@angular/core',
                '@angular/platform-browser',
                '@angular/platform-browser-dynamic'
            ],
            output: {
                globals: {
                    '@micro-core/core': 'MicroCore',
                    '@angular/core': 'ng.core',
                    '@angular/platform-browser': 'ng.platformBrowser',
                    '@angular/platform-browser-dynamic': 'ng.platformBrowserDynamic'
                }
            }
        },
        sourcemap: true,
        minify: false
    },
    test: {
        globals: true,
        environment: 'jsdom'
    }
});