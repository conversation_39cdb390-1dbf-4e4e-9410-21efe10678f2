/**
 * Vue 3 适配器
 * 提供 Vue 3 应用的微前端集成支持
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { BaseAdapter, BaseAdapterOptions, LifecycleManager } from '../../shared/base-adapter';
import { Vue3Lifecycles } from './lifecycles';

/**
 * Vue 3 适配器选项接口
 */
export interface Vue3AdapterOptions extends BaseAdapterOptions {
  /** 是否启用开发者工具 */
  enableDevtools?: boolean;
  /** 是否启用生产提示 */
  enableProductionTip?: boolean;
  /** Vue 3 全局配置 */
  globalProperties?: Record<string, any>;
  /** 是否启用组合式 API */
  enableCompositionAPI?: boolean;
}

/**
 * Vue 3 适配器插件
 * 负责将 Vue 3 应用集成到微前端框架中
 */
export class Vue3Adapter extends BaseAdapter {
  public readonly name = 'adapter-vue3';
  public readonly version = '0.1.0';

  constructor(options: Vue3AdapterOptions = {}) {
    super({
      enableDevtools: process.env.NODE_ENV === 'development',
      enableProductionTip: false,
      enableCompositionAPI: true,
      globalProperties: {},
      ...options
    });
  }

  /**
   * 获取适配器类型
   */
  protected getAdapterType(): string {
    return 'vue3';
  }

  /**
   * 创建生命周期管理器
   */
  protected createLifecycleManager(
    appName: string,
    appConfig: any,
    options: BaseAdapterOptions
  ): LifecycleManager {
    this.validateAppConfig(appConfig);
    return new Vue3Lifecycles(appName, appConfig, options as Vue3AdapterOptions);
  }

  /**
   * Vue 3 特定的安装逻辑
   */
  protected override onInstall(kernel: MicroCoreKernel): void {
    // Vue 3 特定的初始化逻辑
    if (this.options.enableDevtools && typeof window !== 'undefined') {
      // 启用 Vue DevTools 支持
      (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ = 
        (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ || {};
    }

    // 设置 Vue 3 全局配置
    if (typeof window !== 'undefined' && (window as any).Vue) {
      const Vue = (window as any).Vue;
      if (Vue.config && this.options.enableProductionTip !== undefined) {
        Vue.config.productionTip = this.options.enableProductionTip;
      }
    }
  }

  /**
   * Vue 3 特定的卸载逻辑
   */
  protected override onUninstall(kernel: MicroCoreKernel): void {
    // Vue 3 特定的清理逻辑
    // 清理全局属性、指令、组件等
  }

  /**
   * 获取 Vue 3 特定的适配器状态
   */
  public getVue3Status(): {
    activeApps: number;
    options: Vue3AdapterOptions;
    vueVersion?: string;
    compositionAPIEnabled: boolean;
  } {
    const baseStatus = this.getStatus();
    return {
      activeApps: baseStatus.activeApps,
      options: this.options as Vue3AdapterOptions,
      vueVersion: this.getVueVersion(),
      compositionAPIEnabled: (this.options as Vue3AdapterOptions).enableCompositionAPI ?? true
    };
  }

  /**
   * 获取 Vue 版本
   */
  private getVueVersion(): string | undefined {
    try {
      // 尝试获取 Vue 版本
      if (typeof window !== 'undefined' && (window as any).Vue) {
        return (window as any).Vue.version;
      }
      return undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * 添加全局属性
   */
  public addGlobalProperty(key: string, value: any): void {
    const options = this.options as Vue3AdapterOptions;
    if (!options.globalProperties) {
      options.globalProperties = {};
    }
    options.globalProperties[key] = value;
  }

  /**
   * 移除全局属性
   */
  public removeGlobalProperty(key: string): void {
    const options = this.options as Vue3AdapterOptions;
    if (options.globalProperties) {
      delete options.globalProperties[key];
    }
  }
}

// 导出工厂函数
export function createVue3Adapter(options?: Vue3AdapterOptions): Vue3Adapter {
  return new Vue3Adapter(options);
}

// 默认导出
export default Vue3Adapter;

// 导出相关类型和工具
export * from './lifecycles';
