/**
 * @fileoverview Vue 3 Utility Functions
 * 提供 Vue 3 适配器专用的工具函数
 */

import { 
  createApp, 
  defineComponent, 
  ref, 
  reactive, 
  computed, 
  watch, 
  nextTick,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  type App,
  type Component,
  type ComponentOptions,
  type Ref
} from 'vue';

/**
 * Vue 3 版本检测工具
 */
export class Vue3VersionDetector {
  private static _version: string | null = null;

  /**
   * 获取 Vue 3 版本
   */
  static getVersion(): string {
    if (this._version) {
      return this._version;
    }

    try {
      // 尝试从 Vue 实例获取版本信息
      const app = createApp({});
      this._version = app.version || '3.0.0';
      return this._version;
    } catch (error) {
      console.warn('[Vue3Utils] Failed to detect Vue version:', error);
      return '3.0.0'; // 默认版本
    }
  }

  /**
   * 检查是否支持 Composition API
   */
  static supportsCompositionAPI(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 3;
  }

  /**
   * 检查是否支持 Teleport
   */
  static supportsTeleport(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 3;
  }

  /**
   * 检查是否支持 Suspense
   */
  static supportsSuspense(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 3;
  }

  /**
   * 检查是否支持多根节点
   */
  static supportsMultipleRoots(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 3;
  }
}

/**
 * Vue 3 应用工具
 */
export class Vue3AppUtils {
  /**
   * 创建安全的 Vue 应用实例
   */
  static createSafeApp(
    rootComponent: Component,
    rootProps?: Record<string, any>
  ): App {
    try {
      const app = createApp(rootComponent, rootProps);
      
      // 添加全局错误处理
      app.config.errorHandler = (error, instance, info) => {
        console.error('[Vue3Utils] Global error handler:', error, info);
      };

      // 添加全局警告处理
      app.config.warnHandler = (msg, instance, trace) => {
        console.warn('[Vue3Utils] Global warning handler:', msg, trace);
      };

      return app;
    } catch (error) {
      console.error('[Vue3Utils] Failed to create Vue app:', error);
      throw error;
    }
  }

  /**
   * 安全地挂载应用
   */
  static async safeMountApp(
    app: App,
    container: string | Element
  ): Promise<any> {
    try {
      await nextTick();
      return app.mount(container);
    } catch (error) {
      console.error('[Vue3Utils] Failed to mount Vue app:', error);
      throw error;
    }
  }

  /**
   * 安全地卸载应用
   */
  static async safeUnmountApp(app: App): Promise<void> {
    try {
      await nextTick();
      app.unmount();
    } catch (error) {
      console.error('[Vue3Utils] Failed to unmount Vue app:', error);
      throw error;
    }
  }

  /**
   * 配置应用插件
   */
  static configurePlugins(
    app: App,
    plugins: Array<{ plugin: any; options?: any }>
  ): App {
    plugins.forEach(({ plugin, options }) => {
      try {
        app.use(plugin, options);
      } catch (error) {
        console.error('[Vue3Utils] Failed to install plugin:', error);
      }
    });

    return app;
  }

  /**
   * 配置全局属性
   */
  static configureGlobalProperties(
    app: App,
    properties: Record<string, any>
  ): App {
    Object.entries(properties).forEach(([key, value]) => {
      app.config.globalProperties[key] = value;
    });

    return app;
  }
}

/**
 * Vue 3 组件工具
 */
export class Vue3ComponentUtils {
  /**
   * 创建动态组件
   */
  static createDynamicComponent(
    componentOptions: ComponentOptions,
    props?: Record<string, any>
  ): Component {
    return defineComponent({
      ...componentOptions,
      props: {
        ...componentOptions.props,
        ...props
      }
    });
  }

  /**
   * 包装组件以支持错误边界
   */
  static wrapWithErrorBoundary(
    component: Component,
    errorHandler?: (error: Error, instance: any, info: string) => void
  ): Component {
    return defineComponent({
      name: `ErrorBoundary(${component.name || 'Component'})`,
      setup(props, { slots }) {
        const hasError = ref(false);
        const error = ref<Error | null>(null);

        const handleError = (err: Error, instance: any, info: string) => {
          hasError.value = true;
          error.value = err;
          
          console.error('[Vue3Utils] Component error boundary:', err, info);
          
          if (errorHandler) {
            errorHandler(err, instance, info);
          }
        };

        return () => {
          if (hasError.value) {
            return slots.error?.({ error: error.value }) || 
              h('div', {
                style: {
                  padding: '20px',
                  border: '1px solid #ff6b6b',
                  borderRadius: '4px',
                  backgroundColor: '#ffe0e0',
                  color: '#d63031'
                }
              }, `组件渲染错误: ${error.value?.message || '未知错误'}`);
          }

          try {
            return h(component, props, slots);
          } catch (err) {
            handleError(err as Error, getCurrentInstance(), 'render');
            return null;
          }
        };
      }
    });
  }

  /**
   * 创建高阶组件
   */
  static createHOC(
    name: string,
    enhancer: (WrappedComponent: Component) => Component
  ) {
    return function wrapComponent(WrappedComponent: Component): Component {
      const enhanced = enhancer(WrappedComponent);
      enhanced.name = `${name}(${WrappedComponent.name || 'Component'})`;
      return enhanced;
    };
  }

  /**
   * 创建可组合函数
   */
  static createComposable<T extends (...args: any[]) => any>(
    composableFn: T
  ): T {
    return ((...args: Parameters<T>) => {
      try {
        return composableFn(...args);
      } catch (error) {
        console.error('[Vue3Utils] Composable error:', error);
        throw error;
      }
    }) as T;
  }
}

/**
 * Vue 3 响应式工具
 */
export class Vue3ReactivityUtils {
  /**
   * 创建深度响应式对象
   */
  static createDeepReactive<T extends object>(obj: T): T {
    return reactive(obj);
  }

  /**
   * 创建浅层响应式对象
   */
  static createShallowReactive<T extends object>(obj: T): T {
    // Vue 3.2+ 支持 shallowReactive
    try {
      const { shallowReactive } = require('vue');
      return shallowReactive(obj);
    } catch {
      return reactive(obj);
    }
  }

  /**
   * 创建只读响应式对象
   */
  static createReadonly<T extends object>(obj: T): T {
    try {
      const { readonly } = require('vue');
      return readonly(obj);
    } catch {
      return obj;
    }
  }

  /**
   * 批量创建 ref
   */
  static createRefs<T extends Record<string, any>>(
    initialValues: T
  ): { [K in keyof T]: Ref<T[K]> } {
    const refs = {} as any;
    
    Object.entries(initialValues).forEach(([key, value]) => {
      refs[key] = ref(value);
    });

    return refs;
  }

  /**
   * 创建计算属性
   */
  static createComputed<T>(
    getter: () => T,
    setter?: (value: T) => void
  ): Ref<T> {
    if (setter) {
      return computed({
        get: getter,
        set: setter
      });
    }
    return computed(getter);
  }

  /**
   * 创建监听器
   */
  static createWatcher<T>(
    source: () => T,
    callback: (newVal: T, oldVal: T) => void,
    options?: {
      immediate?: boolean;
      deep?: boolean;
      flush?: 'pre' | 'post' | 'sync';
    }
  ): () => void {
    return watch(source, callback, options);
  }

  /**
   * 批量监听
   */
  static createBatchWatcher(
    watchers: Array<{
      source: () => any;
      callback: (newVal: any, oldVal: any) => void;
      options?: any;
    }>
  ): () => void {
    const stopFunctions = watchers.map(({ source, callback, options }) =>
      watch(source, callback, options)
    );

    return () => {
      stopFunctions.forEach(stop => stop());
    };
  }
}

/**
 * Vue 3 生命周期工具
 */
export class Vue3LifecycleUtils {
  /**
   * 创建生命周期钩子管理器
   */
  static createLifecycleManager() {
    const hooks = {
      mounted: new Set<() => void>(),
      unmounted: new Set<() => void>(),
      updated: new Set<() => void>(),
      activated: new Set<() => void>(),
      deactivated: new Set<() => void>()
    };

    return {
      addMountedHook: (callback: () => void) => {
        hooks.mounted.add(callback);
        onMounted(callback);
      },
      addUnmountedHook: (callback: () => void) => {
        hooks.unmounted.add(callback);
        onUnmounted(callback);
      },
      addUpdatedHook: (callback: () => void) => {
        hooks.updated.add(callback);
        // Vue 3 中使用 watchEffect 或 watch 来监听更新
      },
      cleanup: () => {
        Object.values(hooks).forEach(hookSet => hookSet.clear());
      }
    };
  }

  /**
   * 安全地执行生命周期钩子
   */
  static safeExecuteHook(hookFn: () => void, hookName: string): void {
    try {
      hookFn();
    } catch (error) {
      console.error(`[Vue3Utils] Lifecycle hook error (${hookName}):`, error);
    }
  }
}

/**
 * Vue 3 性能工具
 */
export class Vue3PerformanceUtils {
  private static performanceData = new Map<string, {
    renderCount: number;
    totalRenderTime: number;
    averageRenderTime: number;
  }>();

  /**
   * 监控组件性能
   */
  static monitorComponent(component: Component): Component {
    return defineComponent({
      name: `PerformanceMonitor(${component.name || 'Component'})`,
      setup(props, ctx) {
        const componentName = component.name || 'AnonymousComponent';
        let renderStartTime: number;

        onMounted(() => {
          console.debug(`[Vue3Performance] ${componentName} mounted`);
        });

        onUnmounted(() => {
          console.debug(`[Vue3Performance] ${componentName} unmounted`);
        });

        return () => {
          renderStartTime = performance.now();
          
          try {
            const result = h(component, props, ctx.slots);
            
            nextTick(() => {
              const renderTime = performance.now() - renderStartTime;
              this.recordRenderTime(componentName, renderTime);
            });
            
            return result;
          } catch (error) {
            console.error(`[Vue3Performance] Render error in ${componentName}:`, error);
            throw error;
          }
        };
      }
    });
  }

  /**
   * 记录渲染时间
   */
  private static recordRenderTime(componentName: string, renderTime: number): void {
    let data = this.performanceData.get(componentName);
    if (!data) {
      data = { renderCount: 0, totalRenderTime: 0, averageRenderTime: 0 };
      this.performanceData.set(componentName, data);
    }

    data.renderCount++;
    data.totalRenderTime += renderTime;
    data.averageRenderTime = data.totalRenderTime / data.renderCount;

    if (renderTime > 16) {
      console.warn(`[Vue3Performance] Slow render for ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取性能报告
   */
  static getPerformanceReport(): Record<string, any> {
    const report: Record<string, any> = {};
    
    this.performanceData.forEach((data, componentName) => {
      report[componentName] = {
        ...data,
        averageRenderTime: parseFloat(data.averageRenderTime.toFixed(2))
      };
    });

    return report;
  }

  /**
   * 清除性能数据
   */
  static clearPerformanceData(): void {
    this.performanceData.clear();
  }
}

/**
 * Vue 3 调试工具
 */
export class Vue3DebugUtils {
  /**
   * 获取组件实例信息
   */
  static getComponentInfo(instance: any): {
    name: string;
    props: any;
    data: any;
    computed: string[];
    methods: string[];
    refs: string[];
  } {
    if (!instance) {
      return {
        name: 'Unknown',
        props: {},
        data: {},
        computed: [],
        methods: [],
        refs: []
      };
    }

    return {
      name: instance.type?.name || 'Anonymous',
      props: instance.props || {},
      data: instance.data || {},
      computed: Object.keys(instance.computed || {}),
      methods: Object.keys(instance.methods || {}),
      refs: Object.keys(instance.refs || {})
    };
  }

  /**
   * 打印组件树
   */
  static printComponentTree(instance: any, depth: number = 0): void {
    if (!instance) return;

    const indent = '  '.repeat(depth);
    const info = this.getComponentInfo(instance);
    
    console.log(`${indent}${info.name}`, {
      props: info.props,
      data: info.data,
      computed: info.computed,
      methods: info.methods
    });

    // 递归打印子组件
    if (instance.subTree && instance.subTree.children) {
      instance.subTree.children.forEach((child: any) => {
        if (child.component) {
          this.printComponentTree(child.component, depth + 1);
        }
      });
    }
  }

  /**
   * 检查组件健康状态
   */
  static checkComponentHealth(instance: any): {
    isActive: boolean;
    hasErrors: boolean;
    memoryUsage: string;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    
    if (!instance) {
      recommendations.push('Component instance is null or undefined');
    }

    return {
      isActive: !!instance && !instance.isUnmounted,
      hasErrors: false, // 简化实现
      memoryUsage: 'N/A', // 简化实现
      recommendations
    };
  }
}

/**
 * 导出所有工具类
 */
export {
  Vue3VersionDetector,
  Vue3AppUtils,
  Vue3ComponentUtils,
  Vue3ReactivityUtils,
  Vue3LifecycleUtils,
  Vue3PerformanceUtils,
  Vue3DebugUtils
};

/**
 * 默认导出工具集合
 */
export default {
  Vue3VersionDetector,
  Vue3AppUtils,
  Vue3ComponentUtils,
  Vue3ReactivityUtils,
  Vue3LifecycleUtils,
  Vue3PerformanceUtils,
  Vue3DebugUtils
};
