/**
 * @fileoverview Vue 3 Adapter Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { createApp, App, Component, ComponentPublicInstance } from 'vue';
import type {
  IVue3Adapter,
  Vue3AdapterConfig,
  Vue3AppInstance,
  Vue3LifecycleHooks,
  Vue3ErrorInfo,
  Vue3Context
} from './types';
import { Vue3LifecycleAdapter } from './lifecycle-adapter';
import { VUE3_ADAPTER_NAME, VUE3_DEFAULT_CONFIG } from './constants';
import {
  validateVue3Config,
  formatVue3Error,
  extractVue3Component,
  createVue3Container,
  cleanupVue3Container
} from './utils';

/**
 * Vue 3 适配器实现
 */
export class Vue3Adapter implements IVue3Adapter {
  /** 适配器名称 */
  public readonly name = VUE3_ADAPTER_NAME;

  /** 适配器版本 */
  public readonly version = '1.0.0';

  /** 支持的框架版本 */
  public readonly supportedVersions = ['^3.0.0'];

  /** 应用实例映射 */
  private readonly appInstances = new Map<string, Vue3AppInstance>();

  /** 生命周期适配器 */
  private readonly lifecycleAdapter: Vue3LifecycleAdapter;

  constructor() {
    this.lifecycleAdapter = new Vue3LifecycleAdapter();
  }

  /**
   * 检查是否可以处理指定的应用
   */
  async canHandle(config: Vue3AdapterConfig): Promise<boolean> {
    try {
      // 检查配置有效性
      if (!validateVue3Config(config)) {
        return false;
      }

      // 检查是否存在 Vue 3
      if (typeof createApp !== 'function') {
        return false;
      }

      // 检查组件配置
      if (config.component?.rootComponent) {
        const component = config.component.rootComponent;
        if (typeof component !== 'object' && typeof component !== 'function') {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.warn('[Vue3Adapter] canHandle error:', error);
      return false;
    }
  }

  /**
   * 加载应用
   */
  async load(config: Vue3AdapterConfig): Promise<Vue3AppInstance> {
    try {
      // 验证配置
      if (!validateVue3Config(config)) {
        throw new Error('Invalid Vue 3 adapter configuration');
      }

      // 合并默认配置
      const finalConfig: Vue3AdapterConfig = {
        ...VUE3_DEFAULT_CONFIG,
        ...config
      };

      // 执行加载前钩子
      await this.lifecycleAdapter.executeHook('beforeLoad', finalConfig);

      // 创建 Vue 3 应用
      const app = await this.createApp(finalConfig);

      // 配置应用
      await this.configureApp(app, finalConfig);

      // 创建应用实例
      const appInstance: Vue3AppInstance = {
        id: finalConfig.id!,
        name: finalConfig.name!,
        app,
        config: finalConfig,
        lifecycleHooks: finalConfig.lifecycleHooks || {},
        status: 'loaded',
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      // 注册应用实例
      this.appInstances.set(appInstance.id, appInstance);
      this.lifecycleAdapter.registerApp(appInstance.id, appInstance);

      // 执行加载后钩子
      await this.lifecycleAdapter.executeHook('afterLoad', appInstance);

      return appInstance;
    } catch (error) {
      const errorInfo = formatVue3Error(error, { adapter: this, config });
      await this.lifecycleAdapter.executeHook('onError', errorInfo);
      throw error;
    }
  }

  /**
   * 挂载应用
   */
  async mount(appId: string, container: Element): Promise<void> {
    try {
      const appInstance = this.appInstances.get(appId);
      if (!appInstance) {
        throw new Error(`Vue 3 app instance not found: ${appId}`);
      }

      // 执行挂载前钩子
      await this.lifecycleAdapter.executeHook('beforeMount', appInstance, container);

      // 挂载应用
      const instance = await this.mountApp(appInstance.app, container);
      
      // 更新应用实例
      appInstance.instance = instance;
      appInstance.container = container;
      appInstance.status = 'mounted';
      appInstance.updatedAt = Date.now();

      // 执行挂载后钩子
      await this.lifecycleAdapter.executeHook('afterMount', appInstance);
    } catch (error) {
      const appInstance = this.appInstances.get(appId);
      const context: Vue3Context = {
        app: appInstance?.app!,
        instance: appInstance?.instance,
        adapter: this,
        config: appInstance?.config!,
        container
      };
      const errorInfo = formatVue3Error(error, context);
      await this.lifecycleAdapter.executeHook('onError', errorInfo);
      throw error;
    }
  }

  /**
   * 卸载应用
   */
  async unmount(appId: string): Promise<void> {
    try {
      const appInstance = this.appInstances.get(appId);
      if (!appInstance) {
        throw new Error(`Vue 3 app instance not found: ${appId}`);
      }

      // 执行卸载前钩子
      await this.lifecycleAdapter.executeHook('beforeUnmount', appInstance);

      // 卸载应用
      await this.unmountApp(appInstance.app);

      // 清理容器
      if (appInstance.container) {
        cleanupVue3Container(appInstance.container);
      }

      // 更新应用实例
      appInstance.instance = undefined;
      appInstance.container = undefined;
      appInstance.status = 'unmounted';
      appInstance.updatedAt = Date.now();

      // 执行卸载后钩子
      await this.lifecycleAdapter.executeHook('afterUnmount', appInstance);
    } catch (error) {
      const appInstance = this.appInstances.get(appId);
      const context: Vue3Context = {
        app: appInstance?.app!,
        instance: appInstance?.instance,
        adapter: this,
        config: appInstance?.config!
      };
      const errorInfo = formatVue3Error(error, context);
      await this.lifecycleAdapter.executeHook('onError', errorInfo);
      throw error;
    }
  }

  /**
   * 更新应用
   */
  async update(appId: string, config: Partial<Vue3AdapterConfig>): Promise<void> {
    try {
      const appInstance = this.appInstances.get(appId);
      if (!appInstance) {
        throw new Error(`Vue 3 app instance not found: ${appId}`);
      }

      const oldConfig = { ...appInstance.config };
      const newConfig = { ...appInstance.config, ...config };

      // 验证新配置
      if (!validateVue3Config(newConfig)) {
        throw new Error('Invalid Vue 3 adapter configuration');
      }

      // 执行更新前钩子
      await this.lifecycleAdapter.executeHook('beforeUpdate', appInstance, newConfig);

      // 更新应用配置
      await this.updateAppConfig(appId, config);

      // 更新应用实例
      appInstance.config = newConfig;
      appInstance.updatedAt = Date.now();

      // 执行更新后钩子
      await this.lifecycleAdapter.executeHook('afterUpdate', appInstance, oldConfig);
    } catch (error) {
      const appInstance = this.appInstances.get(appId);
      const context: Vue3Context = {
        app: appInstance?.app!,
        instance: appInstance?.instance,
        adapter: this,
        config: appInstance?.config!
      };
      const errorInfo = formatVue3Error(error, context);
      await this.lifecycleAdapter.executeHook('onError', errorInfo);
      throw error;
    }
  }

  /**
   * 销毁应用
   */
  async destroy(appId: string): Promise<void> {
    try {
      const appInstance = this.appInstances.get(appId);
      if (!appInstance) {
        return;
      }

      // 执行销毁前钩子
      await this.lifecycleAdapter.executeHook('beforeDestroy', appInstance);

      // 如果应用已挂载，先卸载
      if (appInstance.status === 'mounted') {
        await this.unmount(appId);
      }

      // 销毁应用
      if (appInstance.app) {
        appInstance.app.unmount();
      }

      // 清理资源
      this.appInstances.delete(appId);
      this.lifecycleAdapter.unregisterApp(appId);

      // 执行销毁后钩子
      await this.lifecycleAdapter.executeHook('afterDestroy', appInstance);
    } catch (error) {
      const appInstance = this.appInstances.get(appId);
      const context: Vue3Context = {
        app: appInstance?.app!,
        instance: appInstance?.instance,
        adapter: this,
        config: appInstance?.config!
      };
      const errorInfo = formatVue3Error(error, context);
      await this.lifecycleAdapter.executeHook('onError', errorInfo);
      throw error;
    }
  }

  /**
   * 获取应用实例
   */
  getAppInstance(appId: string): Vue3AppInstance | undefined {
    return this.appInstances.get(appId);
  }

  /**
   * 获取所有应用实例
   */
  getAllAppInstances(): Vue3AppInstance[] {
    return Array.from(this.appInstances.values());
  }

  /**
   * 创建 Vue 3 应用
   */
  async createApp(config: Vue3AdapterConfig): Promise<App> {
    // 执行应用创建前钩子
    if (config.lifecycleHooks?.beforeAppCreate) {
      await config.lifecycleHooks.beforeAppCreate(config);
    }

    // 获取根组件
    const rootComponent = config.component?.rootComponent || {};

    // 创建 Vue 3 应用
    const app = createApp(rootComponent, config.component?.props);

    // 执行应用创建后钩子
    if (config.lifecycleHooks?.afterAppCreate) {
      await config.lifecycleHooks.afterAppCreate(app, config);
    }

    return app;
  }

  /**
   * 配置 Vue 3 应用
   */
  async configureApp(app: App, config: Vue3AdapterConfig): Promise<void> {
    const appConfig = config.app;
    if (!appConfig) return;

    // 配置全局属性
    if (appConfig.globalProperties) {
      Object.assign(app.config.globalProperties, appConfig.globalProperties);
    }

    // 注册全局组件
    if (appConfig.components) {
      Object.entries(appConfig.components).forEach(([name, component]) => {
        app.component(name, component);
      });
    }

    // 注册全局指令
    if (appConfig.directives) {
      Object.entries(appConfig.directives).forEach(([name, directive]) => {
        app.directive(name, directive);
      });
    }

    // 安装插件
    if (appConfig.plugins) {
      appConfig.plugins.forEach(({ plugin, options }) => {
        app.use(plugin, options);
      });
    }

    // 配置混入
    if (appConfig.mixins) {
      appConfig.mixins.forEach(mixin => {
        app.mixin(mixin);
      });
    }

    // 配置提供者
    if (appConfig.provide) {
      Object.entries(appConfig.provide).forEach(([key, value]) => {
        app.provide(key, value);
      });
    }

    // 配置错误处理
    app.config.errorHandler = (error, instance, info) => {
      const errorInfo: Vue3ErrorInfo = {
        error,
        message: error.message,
        stack: error.stack,
        app,
        instance: instance || undefined,
        type: 'handler',
        timestamp: Date.now()
      };
      this.lifecycleAdapter.executeHook('onError', errorInfo);
    };
  }

  /**
   * 挂载 Vue 3 应用
   */
  async mountApp(app: App, container: Element): Promise<ComponentPublicInstance> {
    // 执行应用挂载前钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('beforeAppMount', app, container);
    }

    // 挂载应用
    const instance = app.mount(container);

    // 执行应用挂载后钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('afterAppMount', app, instance);
    }

    return instance;
  }

  /**
   * 卸载 Vue 3 应用
   */
  async unmountApp(app: App): Promise<void> {
    // 执行应用卸载前钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('beforeAppUnmount', app, undefined);
    }

    // 卸载应用
    app.unmount();

    // 执行应用卸载后钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('afterAppUnmount', app);
    }
  }

  /**
   * 更新应用配置
   */
  async updateAppConfig(appId: string, config: Partial<Vue3AdapterConfig>): Promise<void> {
    const appInstance = this.appInstances.get(appId);
    if (!appInstance) {
      throw new Error(`Vue 3 app instance not found: ${appId}`);
    }

    const newConfig = { ...appInstance.config, ...config };

    // 执行应用更新前钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('beforeAppUpdate', appInstance.app, newConfig);
    }

    // 重新配置应用
    await this.configureApp(appInstance.app, newConfig);

    // 执行应用更新后钩子
    if (this.lifecycleAdapter) {
      await this.lifecycleAdapter.executeHook('afterAppUpdate', appInstance.app, appInstance.config);
    }
  }
}
