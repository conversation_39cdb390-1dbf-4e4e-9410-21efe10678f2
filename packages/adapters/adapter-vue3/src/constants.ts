/**
 * @fileoverview Vue 3 Adapter Constants
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { Vue3AdapterConfig } from './types';

/**
 * Vue 3 适配器名称
 */
export const VUE3_ADAPTER_NAME = '@micro-core/adapter-vue3';

/**
 * Vue 3 适配器版本
 */
export const VUE3_ADAPTER_VERSION = '1.0.0';

/**
 * Vue 3 支持的版本范围
 */
export const VUE3_SUPPORTED_VERSIONS = ['^3.0.0', '^3.1.0', '^3.2.0', '^3.3.0', '^3.4.0'];

/**
 * Vue 3 默认配置
 */
export const VUE3_DEFAULT_CONFIG: Partial<Vue3AdapterConfig> = {
  sandbox: {
    type: 'proxy',
    isolation: {
      js: true,
      css: true,
      dom: false
    }
  },
  app: {
    globalProperties: {},
    components: {},
    directives: {},
    plugins: [],
    mixins: [],
    provide: {}
  },
  component: {
    props: {},
    emits: [],
    slots: {}
  },
  render: {
    ssr: false,
    hydration: {
      enabled: false
    }
  },
  devtools: {
    enabled: process.env.NODE_ENV === 'development'
  }
};

/**
 * Vue 3 事件常量
 */
export const VUE3_EVENTS = {
  APP_CREATED: 'app:created',
  APP_MOUNTED: 'app:mounted',
  APP_UNMOUNTED: 'app:unmounted',
  APP_UPDATED: 'app:updated',
  APP_ERROR: 'app:error',
  COMPONENT_CREATED: 'component:created',
  COMPONENT_MOUNTED: 'component:mounted',
  COMPONENT_UNMOUNTED: 'component:unmounted',
  COMPONENT_UPDATED: 'component:updated',
  COMPONENT_ERROR: 'component:error'
} as const;

/**
 * Vue 3 错误类型常量
 */
export const VUE3_ERROR_TYPES = {
  SETUP: 'setup',
  RENDER: 'render',
  WATCH: 'watch',
  LIFECYCLE: 'lifecycle',
  HANDLER: 'handler',
  SCHEDULER: 'scheduler'
} as const;

/**
 * Vue 3 生命周期钩子常量
 */
export const VUE3_LIFECYCLE_HOOKS = {
  BEFORE_CREATE: 'beforeCreate',
  CREATED: 'created',
  BEFORE_MOUNT: 'beforeMount',
  MOUNTED: 'mounted',
  BEFORE_UPDATE: 'beforeUpdate',
  UPDATED: 'updated',
  BEFORE_UNMOUNT: 'beforeUnmount',
  UNMOUNTED: 'unmounted',
  ERROR_CAPTURED: 'errorCaptured',
  RENDER_TRACKED: 'renderTracked',
  RENDER_TRIGGERED: 'renderTriggered',
  ACTIVATED: 'activated',
  DEACTIVATED: 'deactivated'
} as const;
