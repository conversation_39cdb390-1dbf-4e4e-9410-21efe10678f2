/**
 * @fileoverview Vue 3 Adapter Utilities
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { App, Component } from 'vue';
import type {
  IVue3Adapter,
  Vue3AdapterConfig,
  Vue3ErrorInfo,
  Vue3Context,
  Vue3Utils
} from './types';
import { Vue3Adapter } from './vue3-adapter';
import { VUE3_DEFAULT_CONFIG } from './constants';

/**
 * 创建 Vue 3 适配器实例
 */
export function createVue3Adapter(config?: Partial<Vue3AdapterConfig>): IVue3Adapter {
  return new Vue3Adapter();
}

/**
 * 创建 Vue 3 应用
 */
export function createVue3App(component: Component, props?: Record<string, any>): App {
  const { createApp } = require('vue');
  return createApp(component, props);
}

/**
 * 检测是否为 Vue 3 应用
 */
export function isVue3App(app: any): app is App {
  return app && typeof app === 'object' && 
         typeof app.mount === 'function' &&
         typeof app.unmount === 'function' &&
         typeof app.use === 'function' &&
         typeof app.component === 'function';
}

/**
 * 验证 Vue 3 配置
 */
export function validateVue3Config(config: Vue3AdapterConfig): boolean {
  try {
    // 检查基本配置
    if (!config || typeof config !== 'object') {
      return false;
    }

    // 检查必需字段
    if (!config.id || typeof config.id !== 'string') {
      return false;
    }

    if (!config.name || typeof config.name !== 'string') {
      return false;
    }

    // 检查组件配置
    if (config.component) {
      if (config.component.rootComponent) {
        const component = config.component.rootComponent;
        if (typeof component !== 'object' && typeof component !== 'function') {
          return false;
        }
      }

      if (config.component.props && typeof config.component.props !== 'object') {
        return false;
      }
    }

    // 检查应用配置
    if (config.app) {
      if (config.app.globalProperties && typeof config.app.globalProperties !== 'object') {
        return false;
      }

      if (config.app.components && typeof config.app.components !== 'object') {
        return false;
      }

      if (config.app.directives && typeof config.app.directives !== 'object') {
        return false;
      }

      if (config.app.plugins && !Array.isArray(config.app.plugins)) {
        return false;
      }
    }

    // 检查渲染配置
    if (config.render) {
      if (config.render.ssr !== undefined && typeof config.render.ssr !== 'boolean') {
        return false;
      }

      if (config.render.hydration) {
        if (config.render.hydration.enabled !== undefined && 
            typeof config.render.hydration.enabled !== 'boolean') {
          return false;
        }
      }
    }

    return true;
  } catch (error) {
    console.warn('[Vue3Adapter] validateVue3Config error:', error);
    return false;
  }
}

/**
 * 格式化 Vue 3 错误信息
 */
export function formatVue3Error(error: any, context?: Vue3Context): Vue3ErrorInfo {
  const errorInfo: Vue3ErrorInfo = {
    error,
    message: error?.message || 'Unknown Vue 3 error',
    stack: error?.stack,
    timestamp: Date.now()
  };

  if (context) {
    errorInfo.app = context.app;
    errorInfo.instance = context.instance;
    
    // 尝试确定错误类型
    if (error?.message) {
      const message = error.message.toLowerCase();
      if (message.includes('setup')) {
        errorInfo.type = 'setup';
      } else if (message.includes('render')) {
        errorInfo.type = 'render';
      } else if (message.includes('watch')) {
        errorInfo.type = 'watch';
      } else if (message.includes('lifecycle')) {
        errorInfo.type = 'lifecycle';
      } else if (message.includes('handler')) {
        errorInfo.type = 'handler';
      } else {
        errorInfo.type = 'scheduler';
      }
    }
  }

  return errorInfo;
}

/**
 * 提取 Vue 3 组件
 */
export function extractVue3Component(app: App): Component | null {
  try {
    // 尝试从应用实例中提取根组件
    if (app && (app as any)._component) {
      return (app as any)._component;
    }

    // 尝试从应用配置中提取
    if (app && (app as any).config && (app as any).config.app) {
      return (app as any).config.app;
    }

    return null;
  } catch (error) {
    console.warn('[Vue3Adapter] extractVue3Component error:', error);
    return null;
  }
}

/**
 * 创建 Vue 3 容器
 */
export function createVue3Container(selector?: string): Element {
  const containerId = selector || `vue3-app-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  let container: Element;
  
  if (selector && selector.startsWith('#')) {
    // 通过 ID 查找
    const element = document.getElementById(selector.slice(1));
    if (element) {
      container = element;
    } else {
      container = document.createElement('div');
      container.id = selector.slice(1);
      document.body.appendChild(container);
    }
  } else if (selector && selector.startsWith('.')) {
    // 通过类名查找
    const element = document.querySelector(selector);
    if (element) {
      container = element;
    } else {
      container = document.createElement('div');
      container.className = selector.slice(1);
      document.body.appendChild(container);
    }
  } else if (selector) {
    // 通过选择器查找
    const element = document.querySelector(selector);
    if (element) {
      container = element;
    } else {
      container = document.createElement('div');
      container.setAttribute('data-selector', selector);
      document.body.appendChild(container);
    }
  } else {
    // 创建新容器
    container = document.createElement('div');
    container.id = containerId;
    document.body.appendChild(container);
  }

  // 添加 Vue 3 特定属性
  container.setAttribute('data-vue3-app', 'true');
  container.setAttribute('data-created-at', Date.now().toString());

  return container;
}

/**
 * 清理 Vue 3 容器
 */
export function cleanupVue3Container(container: Element): void {
  try {
    // 清理 Vue 3 特定属性
    container.removeAttribute('data-vue3-app');
    container.removeAttribute('data-created-at');

    // 清理内容
    container.innerHTML = '';

    // 移除样式类
    container.className = container.className
      .split(' ')
      .filter(cls => !cls.startsWith('vue3-'))
      .join(' ');

    // 如果是动态创建的容器，从 DOM 中移除
    if (container.hasAttribute('data-dynamic-container')) {
      container.parentNode?.removeChild(container);
    }
  } catch (error) {
    console.warn('[Vue3Adapter] cleanupVue3Container error:', error);
  }
}

/**
 * 获取 Vue 3 版本信息
 */
export function getVue3Version(): string | null {
  try {
    const Vue = require('vue');
    return Vue.version || null;
  } catch (error) {
    return null;
  }
}

/**
 * 检查 Vue 3 版本兼容性
 */
export function checkVue3Compatibility(requiredVersion?: string): boolean {
  try {
    const currentVersion = getVue3Version();
    if (!currentVersion) {
      return false;
    }

    if (!requiredVersion) {
      return true;
    }

    // 简单的版本比较（实际项目中应使用 semver 库）
    const current = currentVersion.split('.').map(Number);
    const required = requiredVersion.replace(/[^\d.]/g, '').split('.').map(Number);

    for (let i = 0; i < Math.max(current.length, required.length); i++) {
      const currentPart = current[i] || 0;
      const requiredPart = required[i] || 0;

      if (currentPart > requiredPart) {
        return true;
      } else if (currentPart < requiredPart) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.warn('[Vue3Adapter] checkVue3Compatibility error:', error);
    return false;
  }
}

/**
 * 创建 Vue 3 应用配置
 */
export function createVue3Config(options: Partial<Vue3AdapterConfig> = {}): Vue3AdapterConfig {
  return {
    ...VUE3_DEFAULT_CONFIG,
    ...options,
    id: options.id || `vue3-app-${Date.now()}`,
    name: options.name || 'Vue3 App'
  };
}

/**
 * 合并 Vue 3 配置
 */
export function mergeVue3Config(
  base: Vue3AdapterConfig,
  override: Partial<Vue3AdapterConfig>
): Vue3AdapterConfig {
  const merged = { ...base, ...override };

  // 深度合并应用配置
  if (base.app && override.app) {
    merged.app = {
      ...base.app,
      ...override.app,
      globalProperties: {
        ...base.app.globalProperties,
        ...override.app.globalProperties
      },
      components: {
        ...base.app.components,
        ...override.app.components
      },
      directives: {
        ...base.app.directives,
        ...override.app.directives
      },
      plugins: [
        ...(base.app.plugins || []),
        ...(override.app.plugins || [])
      ],
      provide: {
        ...base.app.provide,
        ...override.app.provide
      }
    };
  }

  // 深度合并组件配置
  if (base.component && override.component) {
    merged.component = {
      ...base.component,
      ...override.component,
      props: {
        ...base.component.props,
        ...override.component.props
      },
      slots: {
        ...base.component.slots,
        ...override.component.slots
      }
    };
  }

  return merged;
}

/**
 * 导出工具函数集合
 */
export const vue3Utils: Vue3Utils = {
  createVue3Adapter,
  isVue3App,
  validateVue3Config,
  formatVue3Error,
  extractVue3Component,
  createVue3Container,
  cleanupVue3Container
};
