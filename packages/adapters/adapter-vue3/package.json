{"name": "@micro-core/adapter-vue3", "version": "0.1.0", "description": "Micro-Core Vue 3 适配器", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "vue", "vue3", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-vue3"}, "peerDependencies": {"@micro-core/core": "workspace:*", "vue": ">=3.0.0"}, "devDependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "vue": "^3.3.0", "typescript": "^5.3.0", "vite": "^7.0.4", "vitest": "^3.2.4"}}