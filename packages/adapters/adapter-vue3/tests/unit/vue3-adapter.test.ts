/**
 * @fileoverview Vue 3 Adapter Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createApp, App, Component } from 'vue';
import { Vue3Adapter } from '../src/vue3-adapter';
import type { Vue3AdapterConfig } from '../src/types';

// Mock Vue 3
vi.mock('vue', () => ({
  createApp: vi.fn(),
  defineComponent: vi.fn(),
  ref: vi.fn(),
  reactive: vi.fn(),
  computed: vi.fn(),
  watch: vi.fn(),
  onMounted: vi.fn(),
  onUnmounted: vi.fn()
}));

describe('Vue3Adapter', () => {
  let adapter: Vue3Adapter;
  let mockApp: Partial<App>;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    adapter = new Vue3Adapter();
    
    // Mock Vue 3 app instance
    mockApp = {
      mount: vi.fn().mockReturnValue({}),
      unmount: vi.fn(),
      use: vi.fn(),
      component: vi.fn(),
      directive: vi.fn(),
      mixin: vi.fn(),
      provide: vi.fn(),
      config: {
        globalProperties: {},
        errorHandler: null
      }
    };

    // Mock createApp
    (createApp as any).mockReturnValue(mockApp);

    // Mock container
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-container';
    document.body.appendChild(mockContainer);
  });

  afterEach(() => {
    vi.clearAllMocks();
    if (mockContainer.parentNode) {
      mockContainer.parentNode.removeChild(mockContainer);
    }
  });

  describe('canHandle', () => {
    it('should return true for valid Vue 3 config', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          rootComponent: { template: '<div>Test</div>' }
        }
      };

      const result = await adapter.canHandle(config);
      expect(result).toBe(true);
    });

    it('should return false for invalid config', async () => {
      const config = {} as Vue3AdapterConfig;
      const result = await adapter.canHandle(config);
      expect(result).toBe(false);
    });

    it('should return false when Vue 3 is not available', async () => {
      (createApp as any).mockImplementation(() => {
        throw new Error('Vue 3 not available');
      });

      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const result = await adapter.canHandle(config);
      expect(result).toBe(false);
    });
  });

  describe('load', () => {
    it('should load Vue 3 app successfully', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          rootComponent: { template: '<div>Test</div>' }
        }
      };

      const appInstance = await adapter.load(config);

      expect(appInstance).toBeDefined();
      expect(appInstance.id).toBe('test-app');
      expect(appInstance.name).toBe('Test App');
      expect(appInstance.status).toBe('loaded');
      expect(createApp).toHaveBeenCalled();
    });

    it('should throw error for invalid config', async () => {
      const config = {} as Vue3AdapterConfig;

      await expect(adapter.load(config)).rejects.toThrow('Invalid Vue 3 adapter configuration');
    });

    it('should configure app with global properties', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        app: {
          globalProperties: {
            $test: 'test-value'
          }
        }
      };

      await adapter.load(config);

      expect(mockApp.config?.globalProperties).toHaveProperty('$test', 'test-value');
    });

    it('should register global components', async () => {
      const testComponent = { template: '<div>Test Component</div>' };
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        app: {
          components: {
            TestComponent: testComponent
          }
        }
      };

      await adapter.load(config);

      expect(mockApp.component).toHaveBeenCalledWith('TestComponent', testComponent);
    });

    it('should install plugins', async () => {
      const testPlugin = { install: vi.fn() };
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        app: {
          plugins: [
            { plugin: testPlugin, options: { test: true } }
          ]
        }
      };

      await adapter.load(config);

      expect(mockApp.use).toHaveBeenCalledWith(testPlugin, { test: true });
    });
  });

  describe('mount', () => {
    it('should mount Vue 3 app successfully', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockApp.mount).toHaveBeenCalledWith(mockContainer);
      
      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.status).toBe('mounted');
      expect(updatedInstance?.container).toBe(mockContainer);
    });

    it('should throw error for non-existent app', async () => {
      await expect(adapter.mount('non-existent', mockContainer))
        .rejects.toThrow('Vue 3 app instance not found: non-existent');
    });
  });

  describe('unmount', () => {
    it('should unmount Vue 3 app successfully', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);
      await adapter.unmount(appInstance.id);

      expect(mockApp.unmount).toHaveBeenCalled();
      
      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.status).toBe('unmounted');
      expect(updatedInstance?.container).toBeUndefined();
    });

    it('should throw error for non-existent app', async () => {
      await expect(adapter.unmount('non-existent'))
        .rejects.toThrow('Vue 3 app instance not found: non-existent');
    });
  });

  describe('update', () => {
    it('should update Vue 3 app configuration', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const appInstance = await adapter.load(config);
      
      const updateConfig = {
        app: {
          globalProperties: {
            $updated: 'updated-value'
          }
        }
      };

      await adapter.update(appInstance.id, updateConfig);

      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.config.app?.globalProperties).toHaveProperty('$updated', 'updated-value');
    });

    it('should throw error for invalid update config', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const appInstance = await adapter.load(config);
      
      const invalidUpdateConfig = {
        id: '', // Invalid ID
        name: ''  // Invalid name
      };

      await expect(adapter.update(appInstance.id, invalidUpdateConfig))
        .rejects.toThrow('Invalid Vue 3 adapter configuration');
    });
  });

  describe('destroy', () => {
    it('should destroy Vue 3 app successfully', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);
      await adapter.destroy(appInstance.id);

      expect(mockApp.unmount).toHaveBeenCalled();
      expect(adapter.getAppInstance(appInstance.id)).toBeUndefined();
    });

    it('should handle destroying non-existent app gracefully', async () => {
      await expect(adapter.destroy('non-existent')).resolves.not.toThrow();
    });
  });

  describe('getAppInstance', () => {
    it('should return app instance for existing app', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      await adapter.load(config);
      const instance = adapter.getAppInstance('test-app');

      expect(instance).toBeDefined();
      expect(instance?.id).toBe('test-app');
    });

    it('should return undefined for non-existent app', () => {
      const instance = adapter.getAppInstance('non-existent');
      expect(instance).toBeUndefined();
    });
  });

  describe('getAllAppInstances', () => {
    it('should return all app instances', async () => {
      const config1: Vue3AdapterConfig = {
        id: 'test-app-1',
        name: 'Test App 1'
      };

      const config2: Vue3AdapterConfig = {
        id: 'test-app-2',
        name: 'Test App 2'
      };

      await adapter.load(config1);
      await adapter.load(config2);

      const instances = adapter.getAllAppInstances();
      expect(instances).toHaveLength(2);
      expect(instances.map(i => i.id)).toContain('test-app-1');
      expect(instances.map(i => i.id)).toContain('test-app-2');
    });

    it('should return empty array when no apps loaded', () => {
      const instances = adapter.getAllAppInstances();
      expect(instances).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    it('should handle app creation errors', async () => {
      (createApp as any).mockImplementation(() => {
        throw new Error('App creation failed');
      });

      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      await expect(adapter.load(config)).rejects.toThrow('App creation failed');
    });

    it('should handle mount errors', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      mockApp.mount = vi.fn().mockImplementation(() => {
        throw new Error('Mount failed');
      });

      const appInstance = await adapter.load(config);
      
      await expect(adapter.mount(appInstance.id, mockContainer))
        .rejects.toThrow('Mount failed');
    });

    it('should configure error handler', async () => {
      const config: Vue3AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      await adapter.load(config);

      expect(mockApp.config?.errorHandler).toBeDefined();
      expect(typeof mockApp.config?.errorHandler).toBe('function');
    });
  });
});
