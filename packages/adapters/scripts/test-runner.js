#!/usr/bin/env node

/**
 * @fileoverview Comprehensive Test Runner for Adapters
 * 为所有适配器提供统一的测试执行脚本
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration
const config = {
  rootDir: process.cwd(),
  adapters: [
    'adapter-react',
    'adapter-vue2', 
    'adapter-vue3',
    'adapter-angular',
    'adapter-svelte',
    'adapter-solid',
    'adapter-html',
    'shared'
  ],
  testTypes: ['unit', 'integration'],
  coverageThreshold: 80,
  maxConcurrency: 4
};

/**
 * Logger utility
 */
class Logger {
  static info(message) {
    console.log(`${colors.blue}ℹ${colors.reset} ${message}`);
  }

  static success(message) {
    console.log(`${colors.green}✓${colors.reset} ${message}`);
  }

  static warning(message) {
    console.log(`${colors.yellow}⚠${colors.reset} ${message}`);
  }

  static error(message) {
    console.log(`${colors.red}✗${colors.reset} ${message}`);
  }

  static header(message) {
    console.log(`\n${colors.bright}${colors.cyan}${message}${colors.reset}\n`);
  }

  static separator() {
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`);
  }
}

/**
 * Test runner class
 */
class AdapterTestRunner {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      coverage: 0,
      adapters: new Map()
    };
    this.startTime = Date.now();
  }

  /**
   * Run all tests
   */
  async runAll() {
    Logger.header('🚀 Starting Adapter Test Suite');
    Logger.separator();

    try {
      // Pre-test setup
      await this.preTestSetup();

      // Run tests for each adapter
      for (const adapter of config.adapters) {
        await this.runAdapterTests(adapter);
      }

      // Generate reports
      await this.generateReports();

      // Post-test cleanup
      await this.postTestCleanup();

      // Display summary
      this.displaySummary();

    } catch (error) {
      Logger.error(`Test suite failed: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * Pre-test setup
   */
  async preTestSetup() {
    Logger.info('Setting up test environment...');

    // Create test results directory
    const resultsDir = path.join(config.rootDir, 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // Create coverage directory
    const coverageDir = path.join(config.rootDir, 'coverage');
    if (!fs.existsSync(coverageDir)) {
      fs.mkdirSync(coverageDir, { recursive: true });
    }

    // Verify dependencies
    await this.verifyDependencies();

    Logger.success('Test environment setup complete');
  }

  /**
   * Verify dependencies
   */
  async verifyDependencies() {
    const requiredDeps = ['vitest', '@vitest/ui', 'jsdom'];
    
    for (const dep of requiredDeps) {
      try {
        require.resolve(dep);
      } catch (error) {
        Logger.warning(`Missing dependency: ${dep}`);
        Logger.info(`Installing ${dep}...`);
        execSync(`npm install ${dep} --save-dev`, { stdio: 'inherit' });
      }
    }
  }

  /**
   * Run tests for a specific adapter
   */
  async runAdapterTests(adapterName) {
    Logger.header(`Testing ${adapterName}`);

    const adapterPath = path.join(config.rootDir, adapterName);
    
    // Check if adapter exists
    if (!fs.existsSync(adapterPath)) {
      Logger.warning(`Adapter ${adapterName} not found, skipping...`);
      return;
    }

    const adapterResult = {
      name: adapterName,
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      coverage: 0,
      duration: 0,
      errors: []
    };

    const startTime = Date.now();

    try {
      // Run unit tests
      if (await this.hasTests(adapterPath, 'unit')) {
        Logger.info(`Running unit tests for ${adapterName}...`);
        const unitResult = await this.runTestType(adapterPath, 'unit');
        this.mergeResults(adapterResult, unitResult);
      }

      // Run integration tests
      if (await this.hasTests(adapterPath, 'integration')) {
        Logger.info(`Running integration tests for ${adapterName}...`);
        const integrationResult = await this.runTestType(adapterPath, 'integration');
        this.mergeResults(adapterResult, integrationResult);
      }

      // Calculate coverage
      adapterResult.coverage = await this.calculateCoverage(adapterPath);

      adapterResult.duration = Date.now() - startTime;
      
      Logger.success(`${adapterName} tests completed: ${adapterResult.passed}/${adapterResult.tests} passed`);

    } catch (error) {
      Logger.error(`Tests failed for ${adapterName}: ${error.message}`);
      adapterResult.errors.push(error.message);
    }

    this.results.adapters.set(adapterName, adapterResult);
    this.updateGlobalResults(adapterResult);
  }

  /**
   * Check if adapter has tests of specific type
   */
  async hasTests(adapterPath, testType) {
    const testPath = path.join(adapterPath, 'tests', testType);
    return fs.existsSync(testPath) && fs.readdirSync(testPath).some(file => 
      file.endsWith('.test.ts') || file.endsWith('.test.js')
    );
  }

  /**
   * Run specific test type
   */
  async runTestType(adapterPath, testType) {
    return new Promise((resolve, reject) => {
      const testPattern = path.join(adapterPath, 'tests', testType, '**/*.test.{ts,js}');
      
      const vitestProcess = spawn('npx', [
        'vitest',
        'run',
        '--config', path.join(config.rootDir, 'vitest.config.ts'),
        '--reporter=json',
        testPattern
      ], {
        cwd: config.rootDir,
        stdio: ['inherit', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      vitestProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      vitestProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      vitestProcess.on('close', (code) => {
        try {
          const result = this.parseTestOutput(stdout, stderr);
          resolve(result);
        } catch (error) {
          reject(new Error(`Failed to parse test output: ${error.message}`));
        }
      });

      vitestProcess.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Parse test output
   */
  parseTestOutput(stdout, stderr) {
    const result = {
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };

    try {
      // Try to parse JSON output
      const lines = stdout.split('\n');
      const jsonLine = lines.find(line => line.trim().startsWith('{'));
      
      if (jsonLine) {
        const testResult = JSON.parse(jsonLine);
        
        result.tests = testResult.numTotalTests || 0;
        result.passed = testResult.numPassedTests || 0;
        result.failed = testResult.numFailedTests || 0;
        result.skipped = testResult.numPendingTests || 0;
      }
    } catch (error) {
      // Fallback to parsing text output
      Logger.warning('Failed to parse JSON output, using text parsing');
      
      const testMatch = stdout.match(/(\d+) passing/);
      if (testMatch) {
        result.passed = parseInt(testMatch[1], 10);
        result.tests = result.passed;
      }

      const failMatch = stdout.match(/(\d+) failing/);
      if (failMatch) {
        result.failed = parseInt(failMatch[1], 10);
        result.tests += result.failed;
      }
    }

    if (stderr) {
      result.errors.push(stderr);
    }

    return result;
  }

  /**
   * Calculate coverage for adapter
   */
  async calculateCoverage(adapterPath) {
    try {
      const coverageFile = path.join(adapterPath, 'coverage', 'coverage-summary.json');
      
      if (fs.existsSync(coverageFile)) {
        const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
        return coverage.total?.lines?.pct || 0;
      }
    } catch (error) {
      Logger.warning(`Failed to calculate coverage for ${path.basename(adapterPath)}`);
    }
    
    return 0;
  }

  /**
   * Merge test results
   */
  mergeResults(target, source) {
    target.tests += source.tests;
    target.passed += source.passed;
    target.failed += source.failed;
    target.skipped += source.skipped;
    target.errors.push(...source.errors);
  }

  /**
   * Update global results
   */
  updateGlobalResults(adapterResult) {
    this.results.total += adapterResult.tests;
    this.results.passed += adapterResult.passed;
    this.results.failed += adapterResult.failed;
    this.results.skipped += adapterResult.skipped;
  }

  /**
   * Generate reports
   */
  async generateReports() {
    Logger.info('Generating test reports...');

    // Generate JSON report
    const jsonReport = {
      summary: this.results,
      adapters: Object.fromEntries(this.results.adapters),
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime
    };

    const reportPath = path.join(config.rootDir, 'test-results', 'adapters-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(jsonReport, null, 2));

    // Generate HTML report
    await this.generateHTMLReport(jsonReport);

    Logger.success('Test reports generated');
  }

  /**
   * Generate HTML report
   */
  async generateHTMLReport(jsonReport) {
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adapter Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #fff; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .adapter { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Adapter Test Report</h1>
        <p>Generated: ${jsonReport.timestamp}</p>
        <p>Duration: ${(jsonReport.duration / 1000).toFixed(2)}s</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 2em; font-weight: bold;">${jsonReport.summary.total}</div>
        </div>
        <div class="metric">
            <h3>Passed</h3>
            <div style="font-size: 2em; font-weight: bold;" class="passed">${jsonReport.summary.passed}</div>
        </div>
        <div class="metric">
            <h3>Failed</h3>
            <div style="font-size: 2em; font-weight: bold;" class="failed">${jsonReport.summary.failed}</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div style="font-size: 2em; font-weight: bold;">${((jsonReport.summary.passed / jsonReport.summary.total) * 100).toFixed(1)}%</div>
        </div>
    </div>
    
    <h2>Adapter Results</h2>
    ${Object.values(jsonReport.adapters).map(adapter => `
        <div class="adapter">
            <h3>${adapter.name}</h3>
            <div class="progress">
                <div class="progress-bar" style="width: ${(adapter.passed / adapter.tests) * 100}%"></div>
            </div>
            <p>
                <span class="passed">${adapter.passed} passed</span> | 
                <span class="failed">${adapter.failed} failed</span> | 
                <span class="skipped">${adapter.skipped} skipped</span> |
                Coverage: ${adapter.coverage.toFixed(1)}%
            </p>
            <p>Duration: ${(adapter.duration / 1000).toFixed(2)}s</p>
            ${adapter.errors.length > 0 ? `
                <details>
                    <summary>Errors (${adapter.errors.length})</summary>
                    <pre>${adapter.errors.join('\n\n')}</pre>
                </details>
            ` : ''}
        </div>
    `).join('')}
</body>
</html>`;

    const htmlPath = path.join(config.rootDir, 'test-results', 'adapters-report.html');
    fs.writeFileSync(htmlPath, htmlTemplate);
  }

  /**
   * Post-test cleanup
   */
  async postTestCleanup() {
    Logger.info('Cleaning up test environment...');
    // Add any cleanup logic here
    Logger.success('Cleanup complete');
  }

  /**
   * Display summary
   */
  displaySummary() {
    Logger.separator();
    Logger.header('📊 Test Summary');

    const duration = (Date.now() - this.startTime) / 1000;
    const successRate = this.results.total > 0 ? (this.results.passed / this.results.total) * 100 : 0;

    console.log(`Total Tests: ${this.results.total}`);
    console.log(`${colors.green}Passed: ${this.results.passed}${colors.reset}`);
    console.log(`${colors.red}Failed: ${this.results.failed}${colors.reset}`);
    console.log(`${colors.yellow}Skipped: ${this.results.skipped}${colors.reset}`);
    console.log(`Success Rate: ${successRate.toFixed(1)}%`);
    console.log(`Duration: ${duration.toFixed(2)}s`);

    Logger.separator();

    // Display adapter-specific results
    this.results.adapters.forEach((result, name) => {
      const adapterSuccessRate = result.tests > 0 ? (result.passed / result.tests) * 100 : 0;
      const status = result.failed === 0 ? colors.green : colors.red;
      
      console.log(`${status}${name}${colors.reset}: ${result.passed}/${result.tests} (${adapterSuccessRate.toFixed(1)}%) - Coverage: ${result.coverage.toFixed(1)}%`);
    });

    Logger.separator();

    if (this.results.failed > 0) {
      Logger.error(`Test suite completed with ${this.results.failed} failures`);
      process.exit(1);
    } else {
      Logger.success('All tests passed! 🎉');
      process.exit(0);
    }
  }
}

// CLI handling
async function main() {
  const args = process.argv.slice(2);
  const runner = new AdapterTestRunner();

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Adapter Test Runner

Usage: node test-runner.js [options]

Options:
  --help, -h     Show this help message
  --watch, -w    Run tests in watch mode
  --coverage     Generate coverage report
  --adapter=name Run tests for specific adapter only

Examples:
  node test-runner.js                    # Run all tests
  node test-runner.js --adapter=react    # Run React adapter tests only
  node test-runner.js --coverage         # Run with coverage
`);
    process.exit(0);
  }

  // Handle specific adapter
  const adapterArg = args.find(arg => arg.startsWith('--adapter='));
  if (adapterArg) {
    const adapterName = adapterArg.split('=')[1];
    config.adapters = [adapterName];
  }

  await runner.runAll();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    Logger.error(`Test runner failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = AdapterTestRunner;
