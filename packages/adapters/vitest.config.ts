/**
 * @fileoverview Comprehensive Vitest Configuration for Adapters
 * 为所有适配器提供统一的测试配置
 */

import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Global setup
    setupFiles: ['./tests/setup.ts'],
    
    // Test patterns
    include: [
      '**/tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      '**/src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/coverage/**',
      '**/.{idea,git,cache,output,temp}/**'
    ],
    
    // Test execution
    globals: true,
    clearMocks: true,
    restoreMocks: true,
    mockReset: true,
    
    // Timeouts
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    
    // Concurrency
    threads: true,
    maxThreads: 4,
    minThreads: 1,
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'coverage/**',
        'dist/**',
        '**/node_modules/**',
        '**/tests/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/vite.config.*',
        '**/vitest.config.*'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      all: true,
      clean: true
    },
    
    // Reporters
    reporter: [
      'verbose',
      'json',
      'html'
    ],
    
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    },
    
    // Watch mode
    watch: false,
    
    // Retry configuration
    retry: 2,
    
    // Isolation
    isolate: true,
    
    // Pool options
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true
      }
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@tests': resolve(__dirname, './tests'),
      '@shared': resolve(__dirname, './shared/src'),
      '@adapter-react': resolve(__dirname, './adapter-react/src'),
      '@adapter-vue2': resolve(__dirname, './adapter-vue2/src'),
      '@adapter-vue3': resolve(__dirname, './adapter-vue3/src'),
      '@adapter-angular': resolve(__dirname, './adapter-angular/src'),
      '@adapter-svelte': resolve(__dirname, './adapter-svelte/src'),
      '@adapter-solid': resolve(__dirname, './adapter-solid/src'),
      '@adapter-html': resolve(__dirname, './adapter-html/src')
    }
  },
  
  // Define configuration
  define: {
    __TEST__: true,
    __DEV__: true,
    __VERSION__: JSON.stringify('0.1.0')
  },
  
  // Esbuild configuration
  esbuild: {
    target: 'node14'
  }
});
