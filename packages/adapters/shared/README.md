# @micro-core/adapter-shared

Micro-Core 适配器系统的共享工具库，为所有适配器提供通用的类型定义、工具函数和基础实现。

## 功能特性

- 🏗️ **基础适配器类** - 提供统一的适配器抽象基类
- 📝 **类型定义** - 完整的 TypeScript 类型支持
- 🛠️ **工具函数** - 通用的工具函数和辅助方法
- 📊 **常量定义** - 标准化的常量和枚举值
- 🔄 **生命周期管理** - 统一的生命周期钩子系统
- ⚡ **性能优化** - 内置性能监控和优化机制

## 安装

```bash
npm install @micro-core/adapter-shared
```

## 使用方式

### 创建自定义适配器

```typescript
import { AbstractBaseAdapter, BaseAppConfig, BaseAppInstance } from '@micro-core/adapter-shared';

export class MyCustomAdapter extends AbstractBaseAdapter {
  readonly name = 'my-custom-adapter';
  readonly version = '1.0.0';
  readonly supportedVersions = ['*'];

  async canHandle(config: BaseAppConfig): Promise<boolean> {
    return config.framework === 'my-framework';
  }

  protected async performLoad(config: BaseAppConfig): Promise<BaseAppInstance> {
    // 实现加载逻辑
    return {
      id: config.id!,
      name: config.name,
      config,
      status: 'loaded',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  protected async performMount(appInstance: BaseAppInstance, container: Element): Promise<void> {
    // 实现挂载逻辑
  }

  protected async performUnmount(appInstance: BaseAppInstance): Promise<void> {
    // 实现卸载逻辑
  }

  protected async performUpdate(appInstance: BaseAppInstance, config: Partial<BaseAppConfig>): Promise<void> {
    // 实现更新逻辑
  }

  protected async performDestroy(appInstance: BaseAppInstance): Promise<void> {
    // 实现销毁逻辑
  }
}
```

### 使用工具函数

```typescript
import {
  generateId,
  validateAppConfig,
  mergeConfig,
  getContainer,
  formatError,
  retry
} from '@micro-core/adapter-shared';

// 生成唯一ID
const appId = generateId('my-app');

// 验证应用配置
const isValid = validateAppConfig(config);

// 合并配置
const finalConfig = mergeConfig(defaultConfig, userConfig);

// 获取容器元素
const container = getContainer('#app-container');

// 格式化错误
const errorInfo = formatError(error, { type: 'mount', appId });

// 重试机制
const result = await retry(async () => {
  return await loadResource();
}, 3, 1000);
```

### 使用类型定义

```typescript
import type {
  BaseAdapter,
  BaseAdapterConfig,
  BaseAppConfig,
  BaseAppInstance,
  BaseLifecycleHooks,
  BaseErrorInfo,
  AdapterStatus
} from '@micro-core/adapter-shared';

// 定义适配器配置
const config: BaseAdapterConfig = {
  enableDevTools: true,
  strictMode: false,
  errorBoundary: true
};

// 定义应用配置
const appConfig: BaseAppConfig = {
  id: 'my-app',
  name: 'My Application',
  entry: 'http://localhost:3000',
  container: '#app'
};

// 定义生命周期钩子
const hooks: BaseLifecycleHooks = {
  beforeLoad: async (config) => {
    console.log('Before load:', config);
  },
  afterMount: async (appInstance) => {
    console.log('After mount:', appInstance);
  },
  onError: async (error) => {
    console.error('Error occurred:', error);
  }
};
```

## API 参考

### 基础适配器类

#### AbstractBaseAdapter

抽象基类，提供适配器的通用实现。

**方法：**

- `load(config: BaseAppConfig): Promise<BaseAppInstance>` - 加载应用
- `mount(appId: string, container: Element): Promise<void>` - 挂载应用
- `unmount(appId: string): Promise<void>` - 卸载应用
- `update(appId: string, config: Partial<BaseAppConfig>): Promise<void>` - 更新应用
- `destroy(appId: string): Promise<void>` - 销毁应用
- `getAppInstance(appId: string): BaseAppInstance | undefined` - 获取应用实例
- `getAllAppInstances(): BaseAppInstance[]` - 获取所有应用实例
- `getStatus(): AdapterStatus` - 获取适配器状态
- `enable(): void` - 启用适配器
- `disable(): void` - 禁用适配器
- `on(event: AdapterEventType, listener: Function): void` - 添加事件监听器
- `off(event: AdapterEventType, listener: Function): void` - 移除事件监听器

### 工具函数

#### 基础工具

- `generateId(prefix?: string): string` - 生成唯一ID
- `validateAppConfig(config: BaseAppConfig): boolean` - 验证应用配置
- `mergeConfig<T>(defaultConfig: T, userConfig: Partial<T>): T` - 合并配置对象

#### DOM 操作

- `getContainer(container: string | HTMLElement, fallbackId?: string): HTMLElement` - 获取容器元素
- `cleanupContainer(container: Element): void` - 清理容器内容
- `createStyleElement(css: string, id?: string, attributes?: Record<string, string>): HTMLStyleElement` - 创建样式元素
- `injectStyles(css: string, id?: string, attributes?: Record<string, string>): HTMLStyleElement` - 注入样式
- `removeStyles(selector: string): void` - 移除样式

#### 错误处理

- `formatError(error: Error | string, context?: Record<string, any>): BaseErrorInfo` - 格式化错误信息

#### 异步工具

- `retry<T>(fn: () => Promise<T>, maxAttempts?: number, delay?: number): Promise<T>` - 异步重试
- `debounce<T>(func: T, wait: number): T` - 防抖函数
- `createCancelToken(): CancelToken` - 创建取消令牌

#### 数据处理

- `deepClone<T>(obj: T): T` - 深度克隆对象
- `safeJsonParse<T>(json: string, defaultValue: T): T` - 安全的 JSON 解析
- `isDevelopment(): boolean` - 检查是否为开发环境

### 常量

#### 适配器状态

- `ADAPTER_STATUS` - 适配器状态常量
- `ADAPTER_EVENTS` - 适配器事件常量
- `ERROR_TYPES` - 错误类型常量

#### 配置

- `DEFAULT_ADAPTER_CONFIG` - 默认适配器配置
- `DEFAULT_APP_CONFIG` - 默认应用配置
- `PERFORMANCE_THRESHOLDS` - 性能阈值常量

#### 框架支持

- `ADAPTER_NAMES` - 适配器名称常量
- `FRAMEWORK_VERSIONS` - 框架版本支持

## 开发指南

### 扩展基础适配器

继承 `AbstractBaseAdapter` 类并实现抽象方法：

```typescript
export class MyAdapter extends AbstractBaseAdapter {
  // 实现抽象方法
  protected async performLoad(config: BaseAppConfig): Promise<BaseAppInstance> {
    // 加载逻辑
  }
  
  protected async performMount(appInstance: BaseAppInstance, container: Element): Promise<void> {
    // 挂载逻辑
  }
  
  // ... 其他抽象方法
}
```

### 自定义生命周期钩子

```typescript
const adapter = new MyAdapter({
  beforeLoad: async (config) => {
    console.log('准备加载应用:', config.name);
  },
  afterMount: async (appInstance) => {
    console.log('应用挂载完成:', appInstance.name);
  },
  onError: async (error) => {
    console.error('应用错误:', error.message);
    // 自定义错误处理逻辑
  }
});
```

### 性能监控

基础适配器自动监控性能指标：

```typescript
// 获取适配器状态
const status = adapter.getStatus();
console.log('活跃应用数量:', status.activeApps);
console.log('最后活动时间:', new Date(status.lastActivity));

// 监听性能事件
adapter.on('app:mounted', (event) => {
  const mountTime = event.timestamp - event.data.startTime;
  if (mountTime > PERFORMANCE_THRESHOLDS.MOUNT_TIME) {
    console.warn('挂载时间超过阈值:', mountTime);
  }
});
```

## 许可证

MIT License
