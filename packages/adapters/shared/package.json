{"name": "@micro-core/adapter-shared", "version": "1.0.0", "description": "Shared utilities and types for micro-core adapters", "keywords": ["microfrontend", "adapter", "shared", "utilities", "types"], "author": "Echo <<EMAIL>>", "license": "MIT", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./types": {"import": "./dist/types.js", "require": "./dist/types.cjs", "types": "./dist/types.d.ts"}, "./utils": {"import": "./dist/utils.js", "require": "./dist/utils.cjs", "types": "./dist/utils.d.ts"}}, "files": ["dist", "src", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@micro-core/ts-config": "workspace:*", "@micro-core/eslint-config": "workspace:*", "@micro-core/vitest-config": "workspace:*", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^1.0.0", "rimraf": "^5.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/micro-core/micro-core.git", "directory": "packages/adapters/shared"}, "engines": {"node": ">=16.0.0"}}