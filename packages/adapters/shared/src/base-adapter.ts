/**
 * 基础适配器抽象类
 * 为所有适配器提供通用的基础实现
 */

import type {
  BaseAdapter,
  BaseAdapterConfig,
  BaseAppConfig,
  BaseAppInstance,
  BaseLifecycleHooks,
  AdapterStatus,
  AdapterEventType,
  AdapterEventData
} from './types';
import {
  generateId,
  validateAppConfig,
  mergeConfig,
  formatError,
  createEventData,
  retry
} from './utils';
import {
  DEFAULT_ADAPTER_CONFIG,
  ADAPTER_STATUS,
  ADAPTER_EVENTS,
  PERFORMANCE_THRESHOLDS
} from './constants';

/**
 * 基础适配器抽象类
 */
export abstract class AbstractBaseAdapter implements BaseAdapter {
  /** 适配器名称 */
  public abstract readonly name: string;
  
  /** 适配器版本 */
  public abstract readonly version: string;
  
  /** 支持的框架版本 */
  public abstract readonly supportedVersions: string[];

  /** 适配器配置 */
  protected config: BaseAdapterConfig;
  
  /** 应用实例映射 */
  protected readonly appInstances = new Map<string, BaseAppInstance>();
  
  /** 事件监听器 */
  protected readonly eventListeners = new Map<AdapterEventType, Set<Function>>();
  
  /** 是否已启用 */
  protected enabled = false;
  
  /** 最后活动时间 */
  protected lastActivity = Date.now();

  constructor(config: BaseAdapterConfig = {}) {
    this.config = mergeConfig(DEFAULT_ADAPTER_CONFIG, config);
  }

  /**
   * 检查是否可以处理指定的应用配置
   */
  abstract canHandle(config: BaseAppConfig): Promise<boolean> | boolean;

  /**
   * 加载应用
   */
  async load(config: BaseAppConfig): Promise<BaseAppInstance> {
    const startTime = Date.now();
    
    try {
      // 验证配置
      if (!validateAppConfig(config)) {
        throw new Error('Invalid app configuration');
      }

      // 生成应用ID
      const appId = config.id || generateId(this.name);
      const finalConfig = { ...config, id: appId };

      // 检查是否已存在
      if (this.appInstances.has(appId)) {
        return this.appInstances.get(appId)!;
      }

      // 触发加载前事件
      this.emitEvent('app:loading', appId, { config: finalConfig });

      // 执行加载前钩子
      await this.executeLifecycleHook('beforeLoad', finalConfig);

      // 执行具体的加载逻辑
      const appInstance = await this.performLoad(finalConfig);

      // 注册应用实例
      this.appInstances.set(appId, appInstance);
      this.updateLastActivity();

      // 执行加载后钩子
      await this.executeLifecycleHook('afterLoad', appInstance);

      // 触发加载完成事件
      this.emitEvent('app:loaded', appId, { appInstance });

      // 性能检查
      const loadTime = Date.now() - startTime;
      if (loadTime > PERFORMANCE_THRESHOLDS.LOAD_TIME) {
        console.warn(`[${this.name}] Load time exceeded threshold: ${loadTime}ms`);
      }

      return appInstance;
    } catch (error) {
      const errorInfo = formatError(error as Error, {
        type: 'load',
        adapter: this.name,
        config
      });
      
      this.emitEvent('app:error', config.id, { error: errorInfo });
      throw error;
    }
  }

  /**
   * 挂载应用
   */
  async mount(appId: string, container: Element): Promise<void> {
    const startTime = Date.now();
    
    try {
      const appInstance = this.getAppInstance(appId);
      if (!appInstance) {
        throw new Error(`App instance not found: ${appId}`);
      }

      if (appInstance.status === 'mounted') {
        return; // 已挂载，无需重复操作
      }

      // 触发挂载前事件
      this.emitEvent('app:mounting', appId, { container });

      // 执行挂载前钩子
      await this.executeLifecycleHook('beforeMount', appInstance, container);

      // 执行具体的挂载逻辑
      await this.performMount(appInstance, container);

      // 更新应用实例状态
      appInstance.status = 'mounted';
      appInstance.container = container;
      appInstance.updatedAt = Date.now();
      this.updateLastActivity();

      // 执行挂载后钩子
      await this.executeLifecycleHook('afterMount', appInstance);

      // 触发挂载完成事件
      this.emitEvent('app:mounted', appId, { appInstance });

      // 性能检查
      const mountTime = Date.now() - startTime;
      if (mountTime > PERFORMANCE_THRESHOLDS.MOUNT_TIME) {
        console.warn(`[${this.name}] Mount time exceeded threshold: ${mountTime}ms`);
      }
    } catch (error) {
      const errorInfo = formatError(error as Error, {
        type: 'mount',
        adapter: this.name,
        appId
      });
      
      this.emitEvent('app:error', appId, { error: errorInfo });
      throw error;
    }
  }

  /**
   * 卸载应用
   */
  async unmount(appId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      const appInstance = this.getAppInstance(appId);
      if (!appInstance) {
        return; // 应用不存在，无需卸载
      }

      if (appInstance.status === 'unmounted') {
        return; // 已卸载，无需重复操作
      }

      // 触发卸载前事件
      this.emitEvent('app:unmounting', appId);

      // 执行卸载前钩子
      await this.executeLifecycleHook('beforeUnmount', appInstance);

      // 执行具体的卸载逻辑
      await this.performUnmount(appInstance);

      // 更新应用实例状态
      appInstance.status = 'unmounted';
      appInstance.container = undefined;
      appInstance.instance = undefined;
      appInstance.updatedAt = Date.now();
      this.updateLastActivity();

      // 执行卸载后钩子
      await this.executeLifecycleHook('afterUnmount', appInstance);

      // 触发卸载完成事件
      this.emitEvent('app:unmounted', appId, { appInstance });

      // 性能检查
      const unmountTime = Date.now() - startTime;
      if (unmountTime > PERFORMANCE_THRESHOLDS.UNMOUNT_TIME) {
        console.warn(`[${this.name}] Unmount time exceeded threshold: ${unmountTime}ms`);
      }
    } catch (error) {
      const errorInfo = formatError(error as Error, {
        type: 'unmount',
        adapter: this.name,
        appId
      });
      
      this.emitEvent('app:error', appId, { error: errorInfo });
      throw error;
    }
  }

  /**
   * 更新应用
   */
  async update(appId: string, config: Partial<BaseAppConfig>): Promise<void> {
    const startTime = Date.now();
    
    try {
      const appInstance = this.getAppInstance(appId);
      if (!appInstance) {
        throw new Error(`App instance not found: ${appId}`);
      }

      const oldConfig = { ...appInstance.config };
      const newConfig = { ...appInstance.config, ...config };

      // 触发更新前事件
      this.emitEvent('app:updating', appId, { oldConfig, newConfig });

      // 执行更新前钩子
      await this.executeLifecycleHook('beforeUpdate', appInstance, newConfig);

      // 执行具体的更新逻辑
      await this.performUpdate(appInstance, config);

      // 更新应用实例配置
      appInstance.config = newConfig;
      appInstance.updatedAt = Date.now();
      this.updateLastActivity();

      // 执行更新后钩子
      await this.executeLifecycleHook('afterUpdate', appInstance, oldConfig);

      // 触发更新完成事件
      this.emitEvent('app:updated', appId, { appInstance, oldConfig });

      // 性能检查
      const updateTime = Date.now() - startTime;
      if (updateTime > PERFORMANCE_THRESHOLDS.UPDATE_TIME) {
        console.warn(`[${this.name}] Update time exceeded threshold: ${updateTime}ms`);
      }
    } catch (error) {
      const errorInfo = formatError(error as Error, {
        type: 'update',
        adapter: this.name,
        appId
      });
      
      this.emitEvent('app:error', appId, { error: errorInfo });
      throw error;
    }
  }

  /**
   * 销毁应用
   */
  async destroy(appId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      const appInstance = this.getAppInstance(appId);
      if (!appInstance) {
        return; // 应用不存在，无需销毁
      }

      // 触发销毁前事件
      this.emitEvent('app:destroying', appId);

      // 执行销毁前钩子
      await this.executeLifecycleHook('beforeDestroy', appInstance);

      // 如果应用已挂载，先卸载
      if (appInstance.status === 'mounted') {
        await this.unmount(appId);
      }

      // 执行具体的销毁逻辑
      await this.performDestroy(appInstance);

      // 从映射中移除
      this.appInstances.delete(appId);
      this.updateLastActivity();

      // 执行销毁后钩子
      await this.executeLifecycleHook('afterDestroy', appInstance);

      // 触发销毁完成事件
      this.emitEvent('app:destroyed', appId, { appInstance });

      // 性能检查
      const destroyTime = Date.now() - startTime;
      if (destroyTime > PERFORMANCE_THRESHOLDS.DESTROY_TIME) {
        console.warn(`[${this.name}] Destroy time exceeded threshold: ${destroyTime}ms`);
      }
    } catch (error) {
      const errorInfo = formatError(error as Error, {
        type: 'destroy',
        adapter: this.name,
        appId
      });
      
      this.emitEvent('app:error', appId, { error: errorInfo });
      throw error;
    }
  }

  /**
   * 获取应用实例
   */
  getAppInstance(appId: string): BaseAppInstance | undefined {
    return this.appInstances.get(appId);
  }

  /**
   * 获取所有应用实例
   */
  getAllAppInstances(): BaseAppInstance[] {
    return Array.from(this.appInstances.values());
  }

  /**
   * 获取适配器状态
   */
  getStatus(): AdapterStatus {
    return {
      name: this.name,
      version: this.version,
      enabled: this.enabled,
      activeApps: this.appInstances.size,
      totalApps: this.appInstances.size,
      lastActivity: this.lastActivity,
      options: this.config
    };
  }

  /**
   * 启用适配器
   */
  enable(): void {
    if (!this.enabled) {
      this.enabled = true;
      this.emitEvent('adapter:enabled', undefined, { adapter: this.name });
    }
  }

  /**
   * 禁用适配器
   */
  disable(): void {
    if (this.enabled) {
      this.enabled = false;
      this.emitEvent('adapter:disabled', undefined, { adapter: this.name });
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: AdapterEventType, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  /**
   * 移除事件监听器
   */
  off(event: AdapterEventType, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * 触发事件
   */
  protected emitEvent(
    type: AdapterEventType,
    appId?: string,
    data?: any
  ): void {
    const eventData = createEventData(type, this.name, appId, data);
    const listeners = this.eventListeners.get(type);
    
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(eventData);
        } catch (error) {
          console.error(`[${this.name}] Event listener error:`, error);
        }
      });
    }
  }

  /**
   * 执行生命周期钩子
   */
  protected async executeLifecycleHook(
    hookName: keyof BaseLifecycleHooks,
    ...args: any[]
  ): Promise<void> {
    try {
      const hook = this.config[hookName] as Function;
      if (typeof hook === 'function') {
        await hook(...args);
      }
    } catch (error) {
      console.error(`[${this.name}] Lifecycle hook error (${hookName}):`, error);
      throw error;
    }
  }

  /**
   * 更新最后活动时间
   */
  protected updateLastActivity(): void {
    this.lastActivity = Date.now();
  }

  // 抽象方法，由具体适配器实现
  protected abstract performLoad(config: BaseAppConfig): Promise<BaseAppInstance>;
  protected abstract performMount(appInstance: BaseAppInstance, container: Element): Promise<void>;
  protected abstract performUnmount(appInstance: BaseAppInstance): Promise<void>;
  protected abstract performUpdate(appInstance: BaseAppInstance, config: Partial<BaseAppConfig>): Promise<void>;
  protected abstract performDestroy(appInstance: BaseAppInstance): Promise<void>;
}
