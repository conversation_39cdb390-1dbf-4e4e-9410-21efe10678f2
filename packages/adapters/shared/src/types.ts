/**
 * 适配器共享类型定义
 * 为所有适配器提供通用的基础类型和接口
 */

import type { 
  AppConfig, 
  LifecycleHooks, 
  ErrorInfo, 
  SandboxConfig 
} from '@micro-core/core';

/**
 * 适配器基础配置接口
 */
export interface BaseAdapterConfig {
  /** 适配器名称 */
  name?: string;
  /** 适配器版本 */
  version?: string;
  /** 是否启用开发工具 */
  enableDevTools?: boolean;
  /** 是否启用严格模式 */
  strictMode?: boolean;
  /** 是否启用错误边界 */
  errorBoundary?: boolean;
  /** 自定义配置 */
  [key: string]: any;
}

/**
 * 适配器应用配置基础接口
 */
export interface BaseAppConfig extends AppConfig {
  /** 应用唯一标识 */
  id?: string;
  /** 应用名称 */
  name: string;
  /** 应用入口 */
  entry?: string;
  /** 挂载容器 */
  container?: string | HTMLElement;
  /** 应用状态 */
  status?: 'loading' | 'loaded' | 'mounting' | 'mounted' | 'unmounting' | 'unmounted' | 'error';
}

/**
 * 适配器生命周期钩子基础接口
 */
export interface BaseLifecycleHooks extends LifecycleHooks {
  /** 应用创建前 */
  beforeAppCreate?: (config: any) => Promise<void> | void;
  /** 应用创建后 */
  afterAppCreate?: (app: any, config: any) => Promise<void> | void;
  /** 应用挂载前 */
  beforeAppMount?: (app: any, container: Element) => Promise<void> | void;
  /** 应用挂载后 */
  afterAppMount?: (app: any, instance: any) => Promise<void> | void;
  /** 应用卸载前 */
  beforeAppUnmount?: (app: any, instance: any) => Promise<void> | void;
  /** 应用卸载后 */
  afterAppUnmount?: (app: any) => Promise<void> | void;
  /** 应用更新前 */
  beforeAppUpdate?: (app: any, newConfig: any) => Promise<void> | void;
  /** 应用更新后 */
  afterAppUpdate?: (app: any, oldConfig: any) => Promise<void> | void;
}

/**
 * 适配器错误信息基础接口
 */
export interface BaseErrorInfo extends ErrorInfo {
  /** 错误对象 */
  error: Error;
  /** 错误消息 */
  message: string;
  /** 错误堆栈 */
  stack?: string;
  /** 错误类型 */
  type: 'load' | 'mount' | 'unmount' | 'update' | 'destroy' | 'handler' | 'unknown';
  /** 错误时间戳 */
  timestamp: number;
  /** 应用实例 */
  app?: any;
  /** 组件实例 */
  instance?: any;
}

/**
 * 适配器沙箱配置基础接口
 */
export interface BaseSandboxConfig extends SandboxConfig {
  /** 是否启用沙箱 */
  enabled?: boolean;
  /** 沙箱类型 */
  type?: 'proxy' | 'snapshot' | 'iframe' | 'webcomponent';
  /** 沙箱选项 */
  options?: Record<string, any>;
}

/**
 * 适配器钩子上下文基础接口
 */
export interface BaseHookContext {
  /** 适配器实例 */
  adapter: any;
  /** 应用配置 */
  config: BaseAppConfig;
  /** 应用实例 */
  app?: any;
  /** 组件实例 */
  instance?: any;
  /** 挂载容器 */
  container?: Element;
  /** 额外数据 */
  data?: Record<string, any>;
}

/**
 * 适配器应用实例基础类型
 */
export type BaseAppInstance = {
  /** 应用唯一标识 */
  id: string;
  /** 应用名称 */
  name: string;
  /** 应用配置 */
  config: BaseAppConfig;
  /** 应用状态 */
  status: 'loaded' | 'mounted' | 'unmounted' | 'destroyed';
  /** 应用实例 */
  app?: any;
  /** 组件实例 */
  instance?: any;
  /** 挂载容器 */
  container?: Element;
  /** 生命周期钩子 */
  lifecycleHooks?: BaseLifecycleHooks;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
};

/**
 * 适配器接口基础定义
 */
export interface BaseAdapter {
  /** 适配器名称 */
  readonly name: string;
  /** 适配器版本 */
  readonly version: string;
  /** 支持的框架版本 */
  readonly supportedVersions: string[];
  
  /**
   * 检查是否可以处理指定的应用配置
   */
  canHandle(config: BaseAppConfig): Promise<boolean> | boolean;
  
  /**
   * 加载应用
   */
  load(config: BaseAppConfig): Promise<BaseAppInstance>;
  
  /**
   * 挂载应用
   */
  mount(appId: string, container: Element): Promise<void>;
  
  /**
   * 卸载应用
   */
  unmount(appId: string): Promise<void>;
  
  /**
   * 更新应用
   */
  update(appId: string, config: Partial<BaseAppConfig>): Promise<void>;
  
  /**
   * 销毁应用
   */
  destroy(appId: string): Promise<void>;
  
  /**
   * 获取应用实例
   */
  getAppInstance(appId: string): BaseAppInstance | undefined;
  
  /**
   * 获取所有应用实例
   */
  getAllAppInstances(): BaseAppInstance[];
}

/**
 * 适配器工厂函数类型
 */
export type AdapterFactory<T extends BaseAdapter = BaseAdapter> = (
  config?: BaseAdapterConfig
) => T;

/**
 * 适配器注册信息
 */
export interface AdapterRegistration {
  /** 适配器名称 */
  name: string;
  /** 适配器工厂函数 */
  factory: AdapterFactory;
  /** 适配器配置 */
  config?: BaseAdapterConfig;
  /** 是否默认启用 */
  enabled?: boolean;
}

/**
 * 适配器管理器接口
 */
export interface AdapterManager {
  /**
   * 注册适配器
   */
  register(registration: AdapterRegistration): void;
  
  /**
   * 注销适配器
   */
  unregister(name: string): void;
  
  /**
   * 获取适配器
   */
  getAdapter(name: string): BaseAdapter | undefined;
  
  /**
   * 获取所有适配器
   */
  getAllAdapters(): BaseAdapter[];
  
  /**
   * 查找能处理指定配置的适配器
   */
  findAdapter(config: BaseAppConfig): Promise<BaseAdapter | undefined>;
}

/**
 * 适配器状态信息
 */
export interface AdapterStatus {
  /** 适配器名称 */
  name: string;
  /** 适配器版本 */
  version: string;
  /** 是否已启用 */
  enabled: boolean;
  /** 活跃应用数量 */
  activeApps: number;
  /** 总应用数量 */
  totalApps: number;
  /** 最后活动时间 */
  lastActivity: number;
  /** 配置选项 */
  options: BaseAdapterConfig;
}

/**
 * 适配器事件类型
 */
export type AdapterEventType = 
  | 'adapter:registered'
  | 'adapter:unregistered'
  | 'adapter:enabled'
  | 'adapter:disabled'
  | 'app:loading'
  | 'app:loaded'
  | 'app:mounting'
  | 'app:mounted'
  | 'app:unmounting'
  | 'app:unmounted'
  | 'app:updating'
  | 'app:updated'
  | 'app:destroying'
  | 'app:destroyed'
  | 'app:error';

/**
 * 适配器事件数据
 */
export interface AdapterEventData {
  /** 事件类型 */
  type: AdapterEventType;
  /** 适配器名称 */
  adapterName: string;
  /** 应用ID（如果适用） */
  appId?: string;
  /** 事件数据 */
  data?: any;
  /** 事件时间戳 */
  timestamp: number;
}
