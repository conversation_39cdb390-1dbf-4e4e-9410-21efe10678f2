/**
 * 适配器共享工具函数
 * 为所有适配器提供通用的工具函数和辅助方法
 */

import type { 
  BaseAdapterConfig, 
  BaseAppConfig, 
  BaseAppInstance, 
  BaseErrorInfo,
  AdapterEventType,
  AdapterEventData
} from './types';

/**
 * 生成唯一ID
 */
export function generateId(prefix = 'app'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 验证应用配置
 */
export function validateAppConfig(config: BaseAppConfig): boolean {
  if (!config || typeof config !== 'object') {
    return false;
  }

  if (!config.name || typeof config.name !== 'string') {
    return false;
  }

  return true;
}

/**
 * 合并配置对象
 */
export function mergeConfig<T extends Record<string, any>>(
  defaultConfig: T,
  userConfig: Partial<T>
): T {
  const merged = { ...defaultConfig };
  
  for (const key in userConfig) {
    const userValue = userConfig[key];
    const defaultValue = defaultConfig[key];
    
    if (userValue !== undefined) {
      if (
        typeof userValue === 'object' &&
        userValue !== null &&
        !Array.isArray(userValue) &&
        typeof defaultValue === 'object' &&
        defaultValue !== null &&
        !Array.isArray(defaultValue)
      ) {
        merged[key] = mergeConfig(defaultValue, userValue);
      } else {
        merged[key] = userValue;
      }
    }
  }
  
  return merged;
}

/**
 * 格式化错误信息
 */
export function formatError(
  error: Error | string,
  context?: Record<string, any>
): BaseErrorInfo {
  const errorObj = typeof error === 'string' ? new Error(error) : error;
  
  return {
    error: errorObj,
    message: errorObj.message,
    stack: errorObj.stack,
    type: 'unknown',
    timestamp: Date.now(),
    ...context
  };
}

/**
 * 获取容器元素
 */
export function getContainer(
  container: string | HTMLElement | undefined,
  fallbackId?: string
): HTMLElement {
  if (!container) {
    if (fallbackId) {
      const fallbackElement = document.getElementById(fallbackId);
      if (fallbackElement) {
        return fallbackElement;
      }
    }
    throw new Error('Container not specified and no fallback found');
  }

  if (typeof container === 'string') {
    const element = container.startsWith('#') 
      ? document.getElementById(container.slice(1))
      : document.querySelector(container);
    
    if (!element) {
      throw new Error(`Container not found: ${container}`);
    }
    
    return element as HTMLElement;
  }

  return container;
}

/**
 * 清理容器内容
 */
export function cleanupContainer(container: Element): void {
  if (container) {
    container.innerHTML = '';
    
    // 移除所有事件监听器
    const newContainer = container.cloneNode(false);
    container.parentNode?.replaceChild(newContainer, container);
  }
}

/**
 * 创建样式元素
 */
export function createStyleElement(
  css: string,
  id?: string,
  attributes?: Record<string, string>
): HTMLStyleElement {
  const style = document.createElement('style');
  style.textContent = css;
  
  if (id) {
    style.id = id;
  }
  
  if (attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      style.setAttribute(key, value);
    });
  }
  
  return style;
}

/**
 * 注入样式到文档
 */
export function injectStyles(
  css: string,
  id?: string,
  attributes?: Record<string, string>
): HTMLStyleElement {
  const style = createStyleElement(css, id, attributes);
  document.head.appendChild(style);
  return style;
}

/**
 * 移除样式元素
 */
export function removeStyles(selector: string): void {
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => element.remove());
}

/**
 * 执行脚本代码
 */
export function executeScript(
  code: string,
  context?: Record<string, any>
): any {
  try {
    // 创建函数上下文
    const contextKeys = context ? Object.keys(context) : [];
    const contextValues = context ? Object.values(context) : [];
    
    // 使用 Function 构造函数执行代码
    const func = new Function(...contextKeys, code);
    return func(...contextValues);
  } catch (error) {
    console.error('Script execution error:', error);
    throw error;
  }
}

/**
 * 创建事件数据
 */
export function createEventData(
  type: AdapterEventType,
  adapterName: string,
  appId?: string,
  data?: any
): AdapterEventData {
  return {
    type,
    adapterName,
    appId,
    data,
    timestamp: Date.now()
  };
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastTime >= wait) {
      lastTime = now;
      func(...args);
    }
  };
}

/**
 * 异步重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development' || 
         typeof __DEV__ !== 'undefined' && __DEV__;
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T = any>(
  json: string,
  defaultValue: T
): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

/**
 * 安全的 JSON 字符串化
 */
export function safeJsonStringify(
  obj: any,
  space?: number
): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch {
    return '{}';
  }
}

/**
 * 创建取消令牌
 */
export interface CancelToken {
  isCancelled: boolean;
  cancel(): void;
  onCancel(callback: () => void): void;
}

export function createCancelToken(): CancelToken {
  let isCancelled = false;
  const callbacks: (() => void)[] = [];
  
  return {
    get isCancelled() {
      return isCancelled;
    },
    cancel() {
      if (!isCancelled) {
        isCancelled = true;
        callbacks.forEach(callback => callback());
        callbacks.length = 0;
      }
    },
    onCancel(callback: () => void) {
      if (isCancelled) {
        callback();
      } else {
        callbacks.push(callback);
      }
    }
  };
}
