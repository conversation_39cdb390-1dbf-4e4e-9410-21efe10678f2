/**
 * 适配器共享常量
 */

/**
 * 适配器状态常量
 */
export const ADAPTER_STATUS = {
  LOADING: 'loading',
  LOADED: 'loaded',
  MOUNTING: 'mounting',
  MOUNTED: 'mounted',
  UNMOUNTING: 'unmounting',
  UNMOUNTED: 'unmounted',
  UPDATING: 'updating',
  DESTROYING: 'destroying',
  DESTROYED: 'destroyed',
  ERROR: 'error'
} as const;

/**
 * 适配器事件常量
 */
export const ADAPTER_EVENTS = {
  REGISTERED: 'adapter:registered',
  UNREGISTERED: 'adapter:unregistered',
  ENABLED: 'adapter:enabled',
  DISABLED: 'adapter:disabled',
  APP_LOADING: 'app:loading',
  APP_LOADED: 'app:loaded',
  APP_MOUNTING: 'app:mounting',
  APP_MOUNTED: 'app:mounted',
  APP_UNMOUNTING: 'app:unmounting',
  APP_UNMOUNTED: 'app:unmounted',
  APP_UPDATING: 'app:updating',
  APP_UPDATED: 'app:updated',
  APP_DESTROYING: 'app:destroying',
  APP_DESTROYED: 'app:destroyed',
  APP_ERROR: 'app:error'
} as const;

/**
 * 错误类型常量
 */
export const ERROR_TYPES = {
  LOAD: 'load',
  MOUNT: 'mount',
  UNMOUNT: 'unmount',
  UPDATE: 'update',
  DESTROY: 'destroy',
  HANDLER: 'handler',
  UNKNOWN: 'unknown'
} as const;

/**
 * 沙箱类型常量
 */
export const SANDBOX_TYPES = {
  PROXY: 'proxy',
  SNAPSHOT: 'snapshot',
  IFRAME: 'iframe',
  WEBCOMPONENT: 'webcomponent'
} as const;

/**
 * 默认配置
 */
export const DEFAULT_ADAPTER_CONFIG = {
  enableDevTools: false,
  strictMode: false,
  errorBoundary: true,
  retryAttempts: 3,
  retryDelay: 1000,
  timeout: 30000
} as const;

/**
 * 默认应用配置
 */
export const DEFAULT_APP_CONFIG = {
  status: ADAPTER_STATUS.LOADING,
  retryAttempts: 3,
  retryDelay: 1000,
  timeout: 30000
} as const;

/**
 * 生命周期钩子名称
 */
export const LIFECYCLE_HOOKS = {
  BEFORE_LOAD: 'beforeLoad',
  AFTER_LOAD: 'afterLoad',
  BEFORE_MOUNT: 'beforeMount',
  AFTER_MOUNT: 'afterMount',
  BEFORE_UNMOUNT: 'beforeUnmount',
  AFTER_UNMOUNT: 'afterUnmount',
  BEFORE_UPDATE: 'beforeUpdate',
  AFTER_UPDATE: 'afterUpdate',
  BEFORE_DESTROY: 'beforeDestroy',
  AFTER_DESTROY: 'afterDestroy',
  BEFORE_APP_CREATE: 'beforeAppCreate',
  AFTER_APP_CREATE: 'afterAppCreate',
  BEFORE_APP_MOUNT: 'beforeAppMount',
  AFTER_APP_MOUNT: 'afterAppMount',
  BEFORE_APP_UNMOUNT: 'beforeAppUnmount',
  AFTER_APP_UNMOUNT: 'afterAppUnmount',
  BEFORE_APP_UPDATE: 'beforeAppUpdate',
  AFTER_APP_UPDATE: 'afterAppUpdate',
  ON_ERROR: 'onError'
} as const;

/**
 * 适配器名称常量
 */
export const ADAPTER_NAMES = {
  REACT: 'react-adapter',
  VUE2: 'vue2-adapter',
  VUE3: 'vue3-adapter',
  ANGULAR: 'angular-adapter',
  SVELTE: 'svelte-adapter',
  SOLID: 'solid-adapter',
  HTML: 'html-adapter'
} as const;

/**
 * 框架版本支持
 */
export const FRAMEWORK_VERSIONS = {
  REACT: ['^16.8.0', '^17.0.0', '^18.0.0'],
  VUE2: ['^2.6.0', '^2.7.0'],
  VUE3: ['^3.0.0', '^3.1.0', '^3.2.0', '^3.3.0', '^3.4.0'],
  ANGULAR: ['^12.0.0', '^13.0.0', '^14.0.0', '^15.0.0', '^16.0.0', '^17.0.0'],
  SVELTE: ['^3.0.0', '^4.0.0'],
  SOLID: ['^1.0.0'],
  HTML: ['*']
} as const;

/**
 * 性能阈值常量
 */
export const PERFORMANCE_THRESHOLDS = {
  LOAD_TIME: 5000,      // 5秒
  MOUNT_TIME: 2000,     // 2秒
  UNMOUNT_TIME: 1000,   // 1秒
  UPDATE_TIME: 1000,    // 1秒
  DESTROY_TIME: 1000    // 1秒
} as const;

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  MAX_SIZE: 100,
  TTL: 300000,  // 5分钟
  CHECK_INTERVAL: 60000  // 1分钟
} as const;
