/**
 * 共享适配器工具函数
 * 提供通用的工具方法，减少代码重复
 */

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 记录操作性能
   */
  recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    this.metrics.get(operation)!.push(duration);
  }

  /**
   * 获取操作的平均性能
   */
  getAverageMetric(operation: string): number {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) return 0;
    return metrics.reduce((sum, metric) => sum + metric, 0) / metrics.length;
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    for (const [operation, metrics] of this.metrics) {
      result[operation] = {
        average: this.getAverageMetric(operation),
        count: metrics.length,
        latest: metrics[metrics.length - 1] || 0
      };
    }
    return result;
  }

  /**
   * 清理性能指标
   */
  clearMetrics(): void {
    this.metrics.clear();
  }
}

/**
 * 错误处理工具
 */
export class ErrorHandler {
  private static errorCounts: Map<string, number> = new Map();

  /**
   * 统一错误处理
   */
  static handle(error: Error, context: string, adapterName: string): void {
    const errorKey = `${adapterName}:${context}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);

    console.error(`[${adapterName}] Error in ${context}:`, error);
    
    // 在开发环境下提供更详细的错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('Stack trace:', error.stack);
      console.error('Error count for this context:', count + 1);
    }
  }

  /**
   * 获取错误统计
   */
  static getErrorStats(): Record<string, number> {
    return Object.fromEntries(this.errorCounts);
  }

  /**
   * 清理错误统计
   */
  static clearErrorStats(): void {
    this.errorCounts.clear();
  }
}

/**
 * DOM 工具函数
 */
export class DOMUtils {
  /**
   * 安全地查找 DOM 元素
   */
  static safeQuerySelector(selector: string, container?: Element | Document): Element | null {
    try {
      const root = container || document;
      return root.querySelector(selector);
    } catch (error) {
      console.warn(`Invalid selector: ${selector}`, error);
      return null;
    }
  }

  /**
   * 创建安全的容器元素
   */
  static createSafeContainer(id: string, className?: string): HTMLElement {
    const container = document.createElement('div');
    container.id = id;
    if (className) {
      container.className = className;
    }
    
    // 添加基础样式以防止样式冲突
    container.style.cssText = `
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    `;
    
    return container;
  }

  /**
   * 清理 DOM 元素
   */
  static cleanupElement(element: Element): void {
    try {
      // 移除所有事件监听器
      const clone = element.cloneNode(true);
      element.parentNode?.replaceChild(clone, element);
      
      // 清空内容
      (clone as HTMLElement).innerHTML = '';
    } catch (error) {
      console.warn('Failed to cleanup element:', error);
    }
  }

  /**
   * 检查元素是否在视口中
   */
  static isElementInViewport(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }
}

/**
 * 内存管理工具
 */
export class MemoryManager {
  private static observers: WeakMap<object, FinalizationRegistry<string>> = new WeakMap();

  /**
   * 监控对象内存泄漏
   */
  static watchForLeaks(obj: object, name: string): void {
    if (typeof FinalizationRegistry !== 'undefined') {
      const registry = new FinalizationRegistry((heldValue: string) => {
        console.debug(`Object ${heldValue} has been garbage collected`);
      });
      
      registry.register(obj, name);
      this.observers.set(obj, registry);
    }
  }

  /**
   * 强制垃圾回收（仅在支持的环境中）
   */
  static forceGC(): void {
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  }

  /**
   * 获取内存使用情况（仅在支持的环境中）
   */
  static getMemoryUsage(): any {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory;
    }
    return null;
  }
}

/**
 * 事件工具
 */
export class EventUtils {
  private static eventMap: WeakMap<object, Map<string, Function[]>> = new WeakMap();

  /**
   * 安全地添加事件监听器
   */
  static addEventListener(
    target: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): () => void {
    target.addEventListener(event, handler, options);
    
    // 记录事件监听器以便清理
    if (!this.eventMap.has(target)) {
      this.eventMap.set(target, new Map());
    }
    const events = this.eventMap.get(target)!;
    if (!events.has(event)) {
      events.set(event, []);
    }
    events.get(event)!.push(handler);

    // 返回清理函数
    return () => {
      target.removeEventListener(event, handler, options);
      const handlers = events.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * 清理对象的所有事件监听器
   */
  static cleanupEventListeners(target: EventTarget): void {
    const events = this.eventMap.get(target);
    if (events) {
      for (const [event, handlers] of events) {
        for (const handler of handlers) {
          target.removeEventListener(event, handler as EventListener);
        }
      }
      events.clear();
    }
  }

  /**
   * 创建防抖函数
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * 创建节流函数
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

/**
 * 配置验证工具
 */
export class ConfigValidator {
  /**
   * 验证应用配置
   */
  static validateAppConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config) {
      errors.push('Config is required');
      return { valid: false, errors };
    }

    if (!config.name || typeof config.name !== 'string') {
      errors.push('Config must have a valid name (string)');
    }

    if (config.entry && typeof config.entry !== 'string') {
      errors.push('Config entry must be a string');
    }

    if (config.container && typeof config.container !== 'string') {
      errors.push('Config container must be a string selector');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 验证适配器选项
   */
  static validateAdapterOptions(options: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (options && typeof options !== 'object') {
      errors.push('Options must be an object');
      return { valid: false, errors };
    }

    if (options?.errorHandler && typeof options.errorHandler !== 'function') {
      errors.push('errorHandler must be a function');
    }

    if (options?.performanceMonitoring !== undefined && typeof options.performanceMonitoring !== 'boolean') {
      errors.push('performanceMonitoring must be a boolean');
    }

    return { valid: errors.length === 0, errors };
  }
}

/**
 * 异步工具
 */
export class AsyncUtils {
  /**
   * 创建可取消的 Promise
   */
  static createCancellablePromise<T>(
    promise: Promise<T>
  ): { promise: Promise<T>; cancel: () => void } {
    let cancelled = false;
    
    const cancellablePromise = new Promise<T>((resolve, reject) => {
      promise
        .then(value => {
          if (!cancelled) {
            resolve(value);
          }
        })
        .catch(error => {
          if (!cancelled) {
            reject(error);
          }
        });
    });

    return {
      promise: cancellablePromise,
      cancel: () => {
        cancelled = true;
      }
    };
  }

  /**
   * 带超时的 Promise
   */
  static withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
      })
    ]);
  }

  /**
   * 重试机制
   */
  static async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
      }
    }
    
    throw lastError!;
  }
}

/**
 * 日志工具
 */
export class Logger {
  private static logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';

  static setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    this.logLevel = level;
  }

  static debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }

  static info(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(`[INFO] ${message}`, ...args);
    }
  }

  static warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }

  static error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  }

  private static shouldLog(level: string): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.logLevel);
  }
}

/**
 * 类型工具
 */
export class TypeUtils {
  /**
   * 检查是否为 Promise
   */
  static isPromise(value: any): value is Promise<any> {
    return value && typeof value.then === 'function';
  }

  /**
   * 检查是否为函数
   */
  static isFunction(value: any): value is Function {
    return typeof value === 'function';
  }

  /**
   * 深度克隆对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as any;
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item)) as any;
    }

    if (typeof obj === 'object') {
      const cloned = {} as any;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key]);
        }
      }
      return cloned;
    }

    return obj;
  }

  /**
   * 安全地获取嵌套属性
   */
  static safeGet(obj: any, path: string, defaultValue?: any): any {
    try {
      return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
    } catch {
      return defaultValue;
    }
  }
}
