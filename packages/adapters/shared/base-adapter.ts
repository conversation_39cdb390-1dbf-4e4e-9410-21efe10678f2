import type { MicroCoreKernel } from '@micro-core/core';

/**
 * Plugin interface definition
 */
export interface Plugin {
  name: string;
  version: string;
  install(kernel: MicroCoreKernel): void;
  uninstall(kernel: MicroCoreKernel): void;
}

/**
 * 基础适配器选项接口
 */
export interface BaseAdapterOptions {
  /** 是否启用开发者工具 */
  enableDevtools?: boolean;
  /** 是否启用性能监控 */
  performanceMonitoring?: boolean;
  /** 错误处理函数 */
  errorHandler?: (error: Error, info: string) => void;
  /** 自定义配置 */
  [key: string]: any;
}

/**
 * 适配器状态接口
 */
export interface AdapterStatus {
  /** 适配器名称 */
  name: string;
  /** 适配器版本 */
  version: string;
  /** 活跃应用数量 */
  activeApps: number;
  /** 适配器选项 */
  options: BaseAdapterOptions;
  /** 应用列表 */
  apps?: string[];
}

/**
 * 生命周期管理器接口
 */
export interface LifecycleManager {
  /** 获取生命周期方法 */
  getLifecycles(): any;
  /** 销毁生命周期管理器 */
  destroy(): void;
}

/**
 * 基础适配器抽象类
 * 提供通用的适配器功能实现，减少代码重复
 */
export abstract class BaseAdapter implements Plugin {
  /** 适配器名称 */
  public abstract readonly name: string;
  /** 适配器版本 */
  public abstract readonly version: string;

  /** 适配器选项 */
  protected options: BaseAdapterOptions;
  /** 生命周期管理器映射 */
  protected lifecycles = new Map<string, LifecycleManager>();
  /** 内核实例引用 */
  protected kernel?: MicroCoreKernel;

  /**
   * 构造函数
   * @param options 适配器选项
   */
  constructor(options: BaseAdapterOptions = {}) {
    this.options = {
      enableDevtools: process.env.NODE_ENV === 'development',
      performanceMonitoring: true,
      errorHandler: this.defaultErrorHandler.bind(this),
      ...options
    };
  }

  /**
   * 安装适配器到内核
   * @param kernel 微前端内核实例
   */
  public install(kernel: MicroCoreKernel): void {
    this.kernel = kernel;
    
    // 注册适配器到内核（如果内核支持）
    if (typeof (kernel as any).registerAdapter === 'function') {
      (kernel as any).registerAdapter(this.getAdapterType(), {
        createLifecycles: (appName: string, appConfig: any) => {
          return this.createLifecycles(appName, appConfig);
        }
      });
    }

    // 执行子类特定的安装逻辑
    this.onInstall(kernel);
  }

  /**
   * 从内核卸载适配器
   * @param kernel 微前端内核实例
   */
  public uninstall(kernel: MicroCoreKernel): void {
    // 清理所有生命周期管理器
    for (const [appName, lifecycle] of this.lifecycles) {
      try {
        lifecycle.destroy();
      } catch (error) {
        this.handleError(error as Error, `Failed to destroy lifecycle for app: ${appName}`);
      }
    }
    this.lifecycles.clear();

    // 从内核注销适配器（如果内核支持）
    if (typeof (kernel as any).unregisterAdapter === 'function') {
      (kernel as any).unregisterAdapter(this.getAdapterType());
    }

    // 执行子类特定的卸载逻辑
    this.onUninstall(kernel);

    this.kernel = undefined;
  }

  /**
   * 创建应用生命周期管理器
   * @param appName 应用名称
   * @param appConfig 应用配置
   * @returns 生命周期方法
   */
  public createLifecycles(appName: string, appConfig: any): any {
    // 如果已存在，直接返回
    if (this.lifecycles.has(appName)) {
      return this.lifecycles.get(appName)!.getLifecycles();
    }

    try {
      // 创建新的生命周期管理器
      const lifecycle = this.createLifecycleManager(appName, appConfig, this.options);
      this.lifecycles.set(appName, lifecycle);
      return lifecycle.getLifecycles();
    } catch (error) {
      this.handleError(error as Error, `Failed to create lifecycle for app: ${appName}`);
      throw error;
    }
  }

  /**
   * 获取适配器状态
   * @returns 适配器状态信息
   */
  public getStatus(): AdapterStatus {
    return {
      name: this.name,
      version: this.version,
      activeApps: this.lifecycles.size,
      options: this.options,
      apps: Array.from(this.lifecycles.keys())
    };
  }

  /**
   * 销毁指定应用的生命周期管理器
   * @param appName 应用名称
   */
  public destroyLifecycle(appName: string): void {
    const lifecycle = this.lifecycles.get(appName);
    if (lifecycle) {
      try {
        lifecycle.destroy();
        this.lifecycles.delete(appName);
      } catch (error) {
        this.handleError(error as Error, `Failed to destroy lifecycle for app: ${appName}`);
      }
    }
  }

  /**
   * 获取指定应用的生命周期管理器
   * @param appName 应用名称
   * @returns 生命周期管理器或undefined
   */
  public getLifecycleManager(appName: string): LifecycleManager | undefined {
    return this.lifecycles.get(appName);
  }

  /**
   * 更新适配器选项
   * @param newOptions 新的选项
   */
  public updateOptions(newOptions: Partial<BaseAdapterOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * 获取适配器类型（用于内核注册）
   * 子类需要实现此方法
   */
  protected abstract getAdapterType(): string;

  /**
   * 创建生命周期管理器
   * 子类需要实现此方法
   * @param appName 应用名称
   * @param appConfig 应用配置
   * @param options 适配器选项
   * @returns 生命周期管理器实例
   */
  protected abstract createLifecycleManager(
    appName: string,
    appConfig: any,
    options: BaseAdapterOptions
  ): LifecycleManager;

  /**
   * 子类特定的安装逻辑
   * 子类可以重写此方法
   * @param kernel 微前端内核实例
   */
  protected onInstall(kernel: MicroCoreKernel): void {
    // 默认为空实现，子类可以重写
  }

  /**
   * 子类特定的卸载逻辑
   * 子类可以重写此方法
   * @param kernel 微前端内核实例
   */
  protected onUninstall(kernel: MicroCoreKernel): void {
    // 默认为空实现，子类可以重写
  }

  /**
   * 默认错误处理函数
   * @param error 错误对象
   * @param info 错误信息
   */
  private defaultErrorHandler(error: Error, info: string): void {
    console.error(`[${this.name}] ${info}:`, error);
    
    // 在开发环境下抛出错误，生产环境下记录错误
    if (process.env.NODE_ENV === 'development') {
      throw error;
    }
  }

  /**
   * 统一错误处理方法
   * @param error 错误对象
   * @param info 错误信息
   */
  protected handleError(error: Error, info: string): void {
    if (this.options.errorHandler) {
      this.options.errorHandler(error, info);
    } else {
      this.defaultErrorHandler(error, info);
    }
  }

  /**
   * 性能监控装饰器
   * @param operation 操作名称
   * @param fn 要监控的函数
   * @returns 监控后的函数结果
   */
  protected async withPerformanceMonitoring<T>(
    operation: string,
    fn: () => Promise<T> | T
  ): Promise<T> {
    if (!this.options.performanceMonitoring) {
      return await fn();
    }

    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      // 记录性能数据
      console.debug(`[${this.name}] ${operation} completed in ${duration.toFixed(2)}ms`);
      
      // 如果执行时间过长，发出警告
      if (duration > 100) {
        console.warn(`[${this.name}] ${operation} took ${duration.toFixed(2)}ms, consider optimization`);
      }

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`[${this.name}] ${operation} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }

  /**
   * 验证应用配置
   * @param appConfig 应用配置
   * @returns 是否有效
   */
  protected validateAppConfig(appConfig: any): boolean {
    if (!appConfig) {
      throw new Error('App config is required');
    }

    if (!appConfig.name || typeof appConfig.name !== 'string') {
      throw new Error('App config must have a valid name');
    }

    return true;
  }

  /**
   * 获取应用数量
   * @returns 当前活跃应用数量
   */
  public getActiveAppCount(): number {
    return this.lifecycles.size;
  }

  /**
   * 获取所有应用名称
   * @returns 应用名称数组
   */
  public getActiveAppNames(): string[] {
    return Array.from(this.lifecycles.keys());
  }

  /**
   * 检查应用是否存在
   * @param appName 应用名称
   * @returns 是否存在
   */
  public hasApp(appName: string): boolean {
    return this.lifecycles.has(appName);
  }
}

/**
 * 基础生命周期管理器抽象类
 */
export abstract class BaseLifecycleManager implements LifecycleManager {
  /** 应用名称 */
  protected appName: string;
  /** 应用配置 */
  protected appConfig: any;
  /** 适配器选项 */
  protected options: BaseAdapterOptions;
  /** 是否已销毁 */
  protected destroyed = false;

  constructor(appName: string, appConfig: any, options: BaseAdapterOptions) {
    this.appName = appName;
    this.appConfig = appConfig;
    this.options = options;
  }

  /**
   * 获取生命周期方法
   * 子类需要实现此方法
   */
  public abstract getLifecycles(): any;

  /**
   * 销毁生命周期管理器
   */
  public destroy(): void {
    if (this.destroyed) {
      return;
    }

    this.onDestroy();
    this.destroyed = true;
  }

  /**
   * 子类特定的销毁逻辑
   * 子类可以重写此方法
   */
  protected onDestroy(): void {
    // 默认为空实现，子类可以重写
  }

  /**
   * 检查是否已销毁
   * @returns 是否已销毁
   */
  public isDestroyed(): boolean {
    return this.destroyed;
  }

  /**
   * 确保未销毁
   * @throws 如果已销毁则抛出错误
   */
  protected ensureNotDestroyed(): void {
    if (this.destroyed) {
      throw new Error(`Lifecycle manager for app "${this.appName}" has been destroyed`);
    }
  }
}
