/**
 * 适配器集成测试
 * 测试适配器之间的协作和整体功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BaseAdapter, BaseAdapterOptions, LifecycleManager } from '../../shared/base-adapter';
import { 
  PerformanceMonitor, 
  ErrorHandler, 
  DOMUtils, 
  MemoryManager,
  EventUtils,
  ConfigValidator,
  AsyncUtils,
  Logger
} from '../../shared/adapter-utils';

// Mock 内核
const mockKernel = {
  registerAdapter: vi.fn(),
  unregisterAdapter: vi.fn(),
  getEventBus: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn()
  })),
  getGlobalState: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn()
  }))
};

// 测试适配器实现
class TestAdapter extends BaseAdapter {
  public readonly name = 'test-adapter';
  public readonly version = '1.0.0';

  protected getAdapterType(): string {
    return 'test';
  }

  protected createLifecycleManager(
    appName: string,
    appConfig: any,
    options: BaseAdapterOptions
  ): LifecycleManager {
    return new TestLifecycleManager(appName, appConfig, options);
  }
}

class TestLifecycleManager implements LifecycleManager {
  private destroyed = false;

  constructor(
    private appName: string,
    private appConfig: any,
    private options: BaseAdapterOptions
  ) {}

  getLifecycles() {
    return {
      bootstrap: vi.fn().mockResolvedValue(undefined),
      mount: vi.fn().mockResolvedValue(undefined),
      unmount: vi.fn().mockResolvedValue(undefined),
      update: vi.fn().mockResolvedValue(undefined)
    };
  }

  destroy(): void {
    this.destroyed = true;
  }

  isDestroyed(): boolean {
    return this.destroyed;
  }
}

describe('Adapter Integration Tests', () => {
  let adapter: TestAdapter;
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    adapter = new TestAdapter();
    performanceMonitor = PerformanceMonitor.getInstance();
    performanceMonitor.clearMetrics();
    ErrorHandler.clearErrorStats();
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (adapter) {
      adapter.uninstall(mockKernel as any);
    }
  });

  describe('Base Adapter Functionality', () => {
    it('should install and register adapter correctly', () => {
      adapter.install(mockKernel as any);
      
      expect(mockKernel.registerAdapter).toHaveBeenCalledWith('test', expect.any(Object));
      expect(adapter.getStatus().name).toBe('test-adapter');
      expect(adapter.getStatus().version).toBe('1.0.0');
    });

    it('should create and manage lifecycle instances', () => {
      adapter.install(mockKernel as any);
      
      const appConfig = { name: 'test-app', entry: './src/main.js' };
      const lifecycles1 = adapter.createLifecycles('test-app', appConfig);
      const lifecycles2 = adapter.createLifecycles('test-app', appConfig);
      
      // 应该返回相同的实例
      expect(lifecycles1).toBe(lifecycles2);
      expect(adapter.getStatus().activeApps).toBe(1);
      expect(adapter.hasApp('test-app')).toBe(true);
    });

    it('should handle multiple apps correctly', () => {
      adapter.install(mockKernel as any);
      
      adapter.createLifecycles('app1', { name: 'app1' });
      adapter.createLifecycles('app2', { name: 'app2' });
      
      expect(adapter.getStatus().activeApps).toBe(2);
      expect(adapter.getActiveAppNames()).toEqual(['app1', 'app2']);
    });

    it('should cleanup properly on uninstall', () => {
      adapter.install(mockKernel as any);
      adapter.createLifecycles('test-app', { name: 'test-app' });
      
      adapter.uninstall(mockKernel as any);
      
      expect(mockKernel.unregisterAdapter).toHaveBeenCalledWith('test');
      expect(adapter.getStatus().activeApps).toBe(0);
    });

    it('should validate app config', () => {
      adapter.install(mockKernel as any);
      
      expect(() => {
        adapter.createLifecycles('invalid-app', null);
      }).toThrow('App config is required');
      
      expect(() => {
        adapter.createLifecycles('invalid-app', {});
      }).toThrow('App config must have a valid name');
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should record and retrieve performance metrics', async () => {
      const operation = 'test-operation';
      
      performanceMonitor.recordMetric(operation, 100);
      performanceMonitor.recordMetric(operation, 200);
      performanceMonitor.recordMetric(operation, 150);
      
      const average = performanceMonitor.getAverageMetric(operation);
      expect(average).toBe(150);
      
      const allMetrics = performanceMonitor.getAllMetrics();
      expect(allMetrics[operation]).toEqual({
        average: 150,
        count: 3,
        latest: 150
      });
    });

    it('should handle performance monitoring in adapter operations', async () => {
      const testAdapter = new (class extends TestAdapter {
        async testPerformanceOperation() {
          return this.withPerformanceMonitoring('test-op', async () => {
            await new Promise(resolve => setTimeout(resolve, 10));
            return 'success';
          });
        }
      })();
      
      const result = await testAdapter.testPerformanceOperation();
      expect(result).toBe('success');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle and track errors correctly', () => {
      const error = new Error('Test error');
      const context = 'test-context';
      const adapterName = 'test-adapter';
      
      ErrorHandler.handle(error, context, adapterName);
      
      const stats = ErrorHandler.getErrorStats();
      expect(stats[`${adapterName}:${context}`]).toBe(1);
    });

    it('should handle errors in adapter lifecycle operations', () => {
      const errorAdapter = new (class extends TestAdapter {
        protected createLifecycleManager(): LifecycleManager {
          throw new Error('Lifecycle creation failed');
        }
      })();
      
      errorAdapter.install(mockKernel as any);
      
      expect(() => {
        errorAdapter.createLifecycles('error-app', { name: 'error-app' });
      }).toThrow('Lifecycle creation failed');
    });
  });

  describe('DOM Utils Integration', () => {
    beforeEach(() => {
      // 设置 DOM 环境
      document.body.innerHTML = '<div id="test-container" class="container"></div>';
    });

    it('should safely query DOM elements', () => {
      const element = DOMUtils.safeQuerySelector('#test-container');
      expect(element).not.toBeNull();
      expect(element?.id).toBe('test-container');
      
      const invalidElement = DOMUtils.safeQuerySelector('invalid[selector');
      expect(invalidElement).toBeNull();
    });

    it('should create safe containers', () => {
      const container = DOMUtils.createSafeContainer('new-container', 'test-class');
      
      expect(container.id).toBe('new-container');
      expect(container.className).toBe('test-class');
      expect(container.style.position).toBe('relative');
    });

    it('should check element viewport visibility', () => {
      const element = document.getElementById('test-container')!;
      
      // Mock getBoundingClientRect
      element.getBoundingClientRect = vi.fn().mockReturnValue({
        top: 0,
        left: 0,
        bottom: 100,
        right: 100
      });
      
      // Mock window dimensions
      Object.defineProperty(window, 'innerHeight', { value: 800, writable: true });
      Object.defineProperty(window, 'innerWidth', { value: 1200, writable: true });
      
      const isVisible = DOMUtils.isElementInViewport(element);
      expect(isVisible).toBe(true);
    });
  });

  describe('Event Utils Integration', () => {
    it('should manage event listeners safely', () => {
      const target = document.createElement('div');
      const handler = vi.fn();
      
      const cleanup = EventUtils.addEventListener(target, 'click', handler);
      
      // 触发事件
      target.dispatchEvent(new Event('click'));
      expect(handler).toHaveBeenCalledTimes(1);
      
      // 清理事件监听器
      cleanup();
      target.dispatchEvent(new Event('click'));
      expect(handler).toHaveBeenCalledTimes(1); // 不应该再次调用
    });

    it('should create debounced functions', async () => {
      const fn = vi.fn();
      const debouncedFn = EventUtils.debounce(fn, 100);
      
      debouncedFn();
      debouncedFn();
      debouncedFn();
      
      expect(fn).not.toHaveBeenCalled();
      
      await new Promise(resolve => setTimeout(resolve, 150));
      expect(fn).toHaveBeenCalledTimes(1);
    });

    it('should create throttled functions', async () => {
      const fn = vi.fn();
      const throttledFn = EventUtils.throttle(fn, 100);
      
      throttledFn();
      throttledFn();
      throttledFn();
      
      expect(fn).toHaveBeenCalledTimes(1);
      
      await new Promise(resolve => setTimeout(resolve, 150));
      throttledFn();
      expect(fn).toHaveBeenCalledTimes(2);
    });
  });

  describe('Config Validation Integration', () => {
    it('should validate app configurations', () => {
      const validConfig = { name: 'test-app', entry: './src/main.js' };
      const result = ConfigValidator.validateAppConfig(validConfig);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid app configurations', () => {
      const invalidConfig = { entry: './src/main.js' }; // 缺少 name
      const result = ConfigValidator.validateAppConfig(invalidConfig);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Config must have a valid name (string)');
    });

    it('should validate adapter options', () => {
      const validOptions = {
        enableDevtools: true,
        performanceMonitoring: false,
        errorHandler: () => {}
      };
      
      const result = ConfigValidator.validateAdapterOptions(validOptions);
      expect(result.valid).toBe(true);
    });
  });

  describe('Async Utils Integration', () => {
    it('should create cancellable promises', async () => {
      const originalPromise = new Promise(resolve => {
        setTimeout(() => resolve('success'), 100);
      });
      
      const { promise, cancel } = AsyncUtils.createCancellablePromise(originalPromise);
      
      cancel();
      
      // 取消后的 promise 不应该 resolve
      let resolved = false;
      promise.then(() => { resolved = true; }).catch(() => {});
      
      await new Promise(resolve => setTimeout(resolve, 150));
      expect(resolved).toBe(false);
    });

    it('should handle promise timeouts', async () => {
      const slowPromise = new Promise(resolve => {
        setTimeout(() => resolve('success'), 200);
      });
      
      await expect(AsyncUtils.withTimeout(slowPromise, 100))
        .rejects.toThrow('Operation timed out after 100ms');
    });

    it('should retry failed operations', async () => {
      let attempts = 0;
      const operation = async () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Operation failed');
        }
        return 'success';
      };
      
      const result = await AsyncUtils.retry(operation, 3, 10);
      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });
  });

  describe('Memory Management Integration', () => {
    it('should monitor object lifecycle', () => {
      const testObject = { name: 'test' };
      
      // 这个测试主要确保方法不会抛出错误
      expect(() => {
        MemoryManager.watchForLeaks(testObject, 'test-object');
      }).not.toThrow();
    });

    it('should get memory usage information', () => {
      const memoryInfo = MemoryManager.getMemoryUsage();
      // 在测试环境中可能返回 null
      expect(memoryInfo === null || typeof memoryInfo === 'object').toBe(true);
    });
  });

  describe('Cross-Adapter Communication', () => {
    it('should handle multiple adapters working together', () => {
      const adapter1 = new TestAdapter();
      const adapter2 = new TestAdapter();
      
      adapter1.install(mockKernel as any);
      adapter2.install(mockKernel as any);
      
      adapter1.createLifecycles('app1', { name: 'app1' });
      adapter2.createLifecycles('app2', { name: 'app2' });
      
      expect(adapter1.getStatus().activeApps).toBe(1);
      expect(adapter2.getStatus().activeApps).toBe(1);
      
      adapter1.uninstall(mockKernel as any);
      adapter2.uninstall(mockKernel as any);
    });
  });

  describe('Logger Integration', () => {
    it('should log messages at different levels', () => {
      const consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => {});
      
      Logger.setLogLevel('info');
      Logger.info('Test info message');
      Logger.debug('Test debug message'); // 不应该输出
      
      expect(consoleSpy).toHaveBeenCalledWith('[INFO] Test info message');
      expect(consoleSpy).toHaveBeenCalledTimes(1);
      
      consoleSpy.mockRestore();
    });
  });

  describe('End-to-End Adapter Lifecycle', () => {
    it('should complete full adapter lifecycle successfully', async () => {
      // 安装适配器
      adapter.install(mockKernel as any);
      expect(adapter.getStatus().activeApps).toBe(0);
      
      // 创建应用生命周期
      const appConfig = { name: 'e2e-app', entry: './src/main.js' };
      const lifecycles = adapter.createLifecycles('e2e-app', appConfig);
      expect(adapter.getStatus().activeApps).toBe(1);
      
      // 执行生命周期方法
      await lifecycles.bootstrap();
      await lifecycles.mount();
      await lifecycles.update({ newProp: 'value' });
      await lifecycles.unmount();
      
      // 销毁应用
      adapter.destroyLifecycle('e2e-app');
      expect(adapter.getStatus().activeApps).toBe(0);
      
      // 卸载适配器
      adapter.uninstall(mockKernel as any);
    });
  });
});

describe('Performance Benchmarks', () => {
  it('should benchmark adapter operations', async () => {
    const adapter = new TestAdapter();
    const iterations = 1000;
    
    const startTime = performance.now();
    
    adapter.install(mockKernel as any);
    
    for (let i = 0; i < iterations; i++) {
      const lifecycles = adapter.createLifecycles(`app-${i}`, { name: `app-${i}` });
      await lifecycles.bootstrap();
      await lifecycles.mount();
      await lifecycles.unmount();
      adapter.destroyLifecycle(`app-${i}`);
    }
    
    adapter.uninstall(mockKernel as any);
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`Benchmark: ${iterations} operations in ${totalTime.toFixed(2)}ms`);
    console.log(`Average: ${avgTime.toFixed(2)}ms per operation`);
    
    // 确保性能在合理范围内（每个操作不超过 10ms）
    expect(avgTime).toBeLessThan(10);
  });
});
