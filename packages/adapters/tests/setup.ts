/**
 * @fileoverview Comprehensive Test Setup for All Adapters
 * 为所有适配器提供统一的测试环境配置
 */

import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';

// Global test configuration
beforeAll(() => {
  console.log('🚀 Starting Adapters Test Suite');
  
  // Setup global DOM environment
  setupDOMEnvironment();
  
  // Setup global mocks
  setupGlobalMocks();
  
  // Setup performance monitoring
  setupPerformanceMonitoring();
});

afterAll(() => {
  console.log('✅ Adapters Test Suite Completed');
  
  // Cleanup global resources
  cleanupGlobalResources();
});

beforeEach(() => {
  // Reset all mocks before each test
  vi.clearAllMocks();
  
  // Clear DOM
  document.body.innerHTML = '';
  
  // Reset performance counters
  resetPerformanceCounters();
});

afterEach(() => {
  // Cleanup after each test
  cleanupTestResources();
});

/**
 * Setup DOM Environment
 */
function setupDOMEnvironment(): void {
  // Mock window properties
  Object.defineProperty(window, 'location', {
    value: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: '',
      assign: vi.fn(),
      replace: vi.fn(),
      reload: vi.fn()
    },
    writable: true
  });

  Object.defineProperty(window, 'history', {
    value: {
      length: 1,
      state: null,
      pushState: vi.fn(),
      replaceState: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn()
    },
    writable: true
  });

  // Mock localStorage
  const localStorageMock = {
    data: new Map<string, string>(),
    getItem: vi.fn((key: string) => localStorageMock.data.get(key) || null),
    setItem: vi.fn((key: string, value: string) => {
      localStorageMock.data.set(key, value);
    }),
    removeItem: vi.fn((key: string) => {
      localStorageMock.data.delete(key);
    }),
    clear: vi.fn(() => {
      localStorageMock.data.clear();
    }),
    key: vi.fn((index: number) => {
      const keys = Array.from(localStorageMock.data.keys());
      return keys[index] || null;
    }),
    get length() {
      return localStorageMock.data.size;
    }
  };

  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    data: new Map<string, string>(),
    getItem: vi.fn((key: string) => sessionStorageMock.data.get(key) || null),
    setItem: vi.fn((key: string, value: string) => {
      sessionStorageMock.data.set(key, value);
    }),
    removeItem: vi.fn((key: string) => {
      sessionStorageMock.data.delete(key);
    }),
    clear: vi.fn(() => {
      sessionStorageMock.data.clear();
    }),
    key: vi.fn((index: number) => {
      const keys = Array.from(sessionStorageMock.data.keys());
      return keys[index] || null;
    }),
    get length() {
      return sessionStorageMock.data.size;
    }
  };

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true
  });

  // Mock console methods for testing
  global.console = {
    ...console,
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  };

  // Mock requestAnimationFrame
  global.requestAnimationFrame = vi.fn((callback) => {
    return setTimeout(callback, 16); // 60fps
  });

  global.cancelAnimationFrame = vi.fn((id) => {
    clearTimeout(id);
  });

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));

  // Mock MutationObserver
  global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(() => [])
  }));
}

/**
 * Setup Global Mocks
 */
function setupGlobalMocks(): void {
  // Mock MicroCore Kernel
  const mockKernel = {
    registerAdapter: vi.fn(),
    unregisterAdapter: vi.fn(),
    getEventBus: vi.fn(() => ({
      emit: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      once: vi.fn(),
      removeAllListeners: vi.fn()
    })),
    getGlobalState: vi.fn(() => ({
      get: vi.fn(),
      set: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn()
    })),
    getRouter: vi.fn(() => ({
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      getCurrentRoute: vi.fn(() => ({ path: '/', query: {}, params: {} }))
    })),
    getResourceManager: vi.fn(() => ({
      loadScript: vi.fn(),
      loadStyle: vi.fn(),
      preload: vi.fn()
    })),
    getLogger: vi.fn(() => ({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }))
  };

  // Make mockKernel globally available
  (global as any).mockKernel = mockKernel;

  // Mock fetch
  global.fetch = vi.fn(() =>
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
      blob: () => Promise.resolve(new Blob()),
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
    } as Response)
  );

  // Mock URL constructor
  global.URL = class MockURL {
    href: string;
    origin: string;
    protocol: string;
    host: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;

    constructor(url: string, base?: string) {
      this.href = url;
      this.origin = 'http://localhost:3000';
      this.protocol = 'http:';
      this.host = 'localhost:3000';
      this.hostname = 'localhost';
      this.port = '3000';
      this.pathname = '/';
      this.search = '';
      this.hash = '';
    }

    toString() {
      return this.href;
    }
  } as any;

  // Mock URLSearchParams
  global.URLSearchParams = class MockURLSearchParams {
    private params = new Map<string, string>();

    constructor(init?: string | URLSearchParams | Record<string, string>) {
      if (typeof init === 'string') {
        // Parse query string
        const pairs = init.replace(/^\?/, '').split('&');
        pairs.forEach(pair => {
          const [key, value] = pair.split('=');
          if (key) {
            this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
          }
        });
      }
    }

    get(name: string) {
      return this.params.get(name);
    }

    set(name: string, value: string) {
      this.params.set(name, value);
    }

    has(name: string) {
      return this.params.has(name);
    }

    delete(name: string) {
      this.params.delete(name);
    }

    toString() {
      const pairs: string[] = [];
      this.params.forEach((value, key) => {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      });
      return pairs.join('&');
    }
  } as any;
}

/**
 * Setup Performance Monitoring
 */
function setupPerformanceMonitoring(): void {
  // Mock performance API
  if (!global.performance) {
    global.performance = {
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => []),
      getEntriesByType: vi.fn(() => []),
      clearMarks: vi.fn(),
      clearMeasures: vi.fn()
    } as any;
  }

  // Performance tracking
  (global as any).performanceData = {
    testStartTime: performance.now(),
    testCounts: new Map<string, number>(),
    slowTests: new Set<string>()
  };
}

/**
 * Reset Performance Counters
 */
function resetPerformanceCounters(): void {
  const perfData = (global as any).performanceData;
  if (perfData) {
    perfData.testStartTime = performance.now();
  }
}

/**
 * Cleanup Test Resources
 */
function cleanupTestResources(): void {
  // Clear timers
  vi.clearAllTimers();
  
  // Clear DOM
  document.body.innerHTML = '';
  
  // Reset mocks
  vi.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  window.localStorage.clear();
  window.sessionStorage.clear();
  
  // Track test performance
  const perfData = (global as any).performanceData;
  if (perfData) {
    const testDuration = performance.now() - perfData.testStartTime;
    if (testDuration > 100) { // Tests taking longer than 100ms
      perfData.slowTests.add(`Test took ${testDuration.toFixed(2)}ms`);
    }
  }
}

/**
 * Cleanup Global Resources
 */
function cleanupGlobalResources(): void {
  // Report performance data
  const perfData = (global as any).performanceData;
  if (perfData && perfData.slowTests.size > 0) {
    console.warn('⚠️  Slow tests detected:');
    perfData.slowTests.forEach((test: string) => {
      console.warn(`  - ${test}`);
    });
  }
  
  // Clear global mocks
  delete (global as any).mockKernel;
  delete (global as any).performanceData;
}

/**
 * Test Utilities
 */
export const testUtils = {
  /**
   * Create a mock container element
   */
  createMockContainer(id: string = 'test-container'): HTMLElement {
    const container = document.createElement('div');
    container.id = id;
    container.style.cssText = `
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    `;
    document.body.appendChild(container);
    return container;
  },

  /**
   * Wait for next tick
   */
  async nextTick(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 0));
  },

  /**
   * Wait for specified time
   */
  async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Create mock props
   */
  createMockProps(overrides: Record<string, any> = {}): Record<string, any> {
    return {
      container: this.createMockContainer(),
      name: 'test-app',
      data: { test: 'data' },
      ...overrides
    };
  },

  /**
   * Measure test performance
   */
  measurePerformance<T>(testFn: () => T | Promise<T>, testName: string): Promise<T> {
    return new Promise(async (resolve, reject) => {
      const startTime = performance.now();
      
      try {
        const result = await testFn();
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.debug(`[Performance] ${testName}: ${duration.toFixed(2)}ms`);
        
        const perfData = (global as any).performanceData;
        if (perfData) {
          perfData.testCounts.set(testName, (perfData.testCounts.get(testName) || 0) + 1);
        }
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * Create stress test data
   */
  createStressTestData(size: number): Array<Record<string, any>> {
    return Array.from({ length: size }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      data: `Data for item ${i}`,
      nested: {
        value: i * 2,
        array: Array.from({ length: 10 }, (_, j) => j * i)
      }
    }));
  },

  /**
   * Simulate user interaction
   */
  simulateUserInteraction(element: HTMLElement, eventType: string = 'click'): void {
    const event = new Event(eventType, { bubbles: true, cancelable: true });
    element.dispatchEvent(event);
  },

  /**
   * Check memory usage (simplified)
   */
  checkMemoryUsage(): { used: number; total: number } {
    // Simplified memory check
    return {
      used: (performance as any).memory?.usedJSHeapSize || 0,
      total: (performance as any).memory?.totalJSHeapSize || 0
    };
  }
};

/**
 * Export test configuration
 */
export const testConfig = {
  timeout: 10000, // 10 seconds
  retries: 2,
  concurrent: false,
  setupFilesAfterEnv: [__filename]
};

/**
 * Export mock factories
 */
export const mockFactories = {
  createMockKernel: () => (global as any).mockKernel,
  createMockAdapter: (name: string) => ({
    name,
    version: '1.0.0',
    install: vi.fn(),
    uninstall: vi.fn(),
    createLifecycles: vi.fn(),
    getStatus: vi.fn(() => ({ activeApps: 0, options: {} }))
  }),
  createMockLifecycles: () => ({
    bootstrap: vi.fn(),
    mount: vi.fn(),
    unmount: vi.fn(),
    update: vi.fn()
  })
};

// Export everything for use in tests
export * from 'vitest';
