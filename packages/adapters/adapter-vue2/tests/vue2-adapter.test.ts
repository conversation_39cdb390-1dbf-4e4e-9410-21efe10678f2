/**
 * @fileoverview Vue 2 Adapter Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Vue2Adapter } from '../src/vue2-adapter';
import type { Vue2AdapterConfig } from '../src/types';

// Mock Vue 2
const mockVue = {
  extend: vi.fn(),
  component: vi.fn(),
  directive: vi.fn(),
  mixin: vi.fn(),
  use: vi.fn(),
  prototype: {},
  config: {
    errorHandler: null,
    warnHandler: null
  }
};

const mockVueInstance = {
  $mount: vi.fn().mockReturnThis(),
  $destroy: vi.fn(),
  $el: document.createElement('div'),
  $options: {},
  $data: {},
  $props: {},
  $emit: vi.fn(),
  $on: vi.fn(),
  $off: vi.fn(),
  $once: vi.fn()
};

vi.mock('vue', () => ({
  default: mockVue,
  Vue: mockVue
}));

// Mock Vue constructor
global.Vue = vi.fn().mockImplementation(() => mockVueInstance);
Object.assign(global.Vue, mockVue);

describe('Vue2Adapter', () => {
  let adapter: Vue2Adapter;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    adapter = new Vue2Adapter();
    
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock container
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-container';
    document.body.appendChild(mockContainer);
  });

  afterEach(() => {
    vi.clearAllMocks();
    if (mockContainer.parentNode) {
      mockContainer.parentNode.removeChild(mockContainer);
    }
  });

  describe('canHandle', () => {
    it('should return true for valid Vue 2 config', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const result = await adapter.canHandle(config);
      expect(result).toBe(true);
    });

    it('should return false for invalid config', async () => {
      const config = {} as Vue2AdapterConfig;
      const result = await adapter.canHandle(config);
      expect(result).toBe(false);
    });

    it('should return false when Vue 2 is not available', async () => {
      const originalVue = global.Vue;
      delete (global as any).Vue;

      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App'
      };

      const result = await adapter.canHandle(config);
      expect(result).toBe(false);

      global.Vue = originalVue;
    });
  });

  describe('load', () => {
    it('should load Vue 2 app successfully', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>',
          data: () => ({ message: 'Hello' })
        }
      };

      const appInstance = await adapter.load(config);

      expect(appInstance).toBeDefined();
      expect(appInstance.id).toBe('test-app');
      expect(appInstance.name).toBe('Test App');
      expect(appInstance.status).toBe('loaded');
      expect(global.Vue).toHaveBeenCalled();
    });

    it('should throw error for invalid config', async () => {
      const config = {} as Vue2AdapterConfig;

      await expect(adapter.load(config)).rejects.toThrow('Invalid Vue 2 adapter configuration');
    });

    it('should configure Vue with global properties', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        vue: {
          globalProperties: {
            $test: 'test-value'
          }
        }
      };

      await adapter.load(config);

      expect(mockVue.prototype).toHaveProperty('$test', 'test-value');
    });

    it('should register global components', async () => {
      const testComponent = { template: '<div>Test Component</div>' };
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        vue: {
          components: {
            TestComponent: testComponent
          }
        }
      };

      await adapter.load(config);

      expect(mockVue.component).toHaveBeenCalledWith('TestComponent', testComponent);
    });

    it('should install plugins', async () => {
      const testPlugin = { install: vi.fn() };
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        vue: {
          plugins: [
            { plugin: testPlugin, options: { test: true } }
          ]
        }
      };

      await adapter.load(config);

      expect(mockVue.use).toHaveBeenCalledWith(testPlugin, { test: true });
    });
  });

  describe('mount', () => {
    it('should mount Vue 2 app successfully', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockVueInstance.$mount).toHaveBeenCalledWith(mockContainer);
      
      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.status).toBe('mounted');
      expect(updatedInstance?.container).toBe(mockContainer);
    });

    it('should throw error for non-existent app', async () => {
      await expect(adapter.mount('non-existent', mockContainer))
        .rejects.toThrow('Vue 2 app instance not found: non-existent');
    });
  });

  describe('unmount', () => {
    it('should unmount Vue 2 app successfully', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);
      await adapter.unmount(appInstance.id);

      expect(mockVueInstance.$destroy).toHaveBeenCalled();
      
      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.status).toBe('unmounted');
      expect(updatedInstance?.container).toBeUndefined();
    });

    it('should throw error for non-existent app', async () => {
      await expect(adapter.unmount('non-existent'))
        .rejects.toThrow('Vue 2 app instance not found: non-existent');
    });
  });

  describe('update', () => {
    it('should update Vue 2 app configuration', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const appInstance = await adapter.load(config);
      
      const updateConfig = {
        vue: {
          globalProperties: {
            $updated: 'updated-value'
          }
        }
      };

      await adapter.update(appInstance.id, updateConfig);

      const updatedInstance = adapter.getAppInstance(appInstance.id);
      expect(updatedInstance?.config.vue?.globalProperties).toHaveProperty('$updated', 'updated-value');
    });

    it('should throw error for invalid update config', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const appInstance = await adapter.load(config);
      
      const invalidUpdateConfig = {
        id: '', // Invalid ID
        name: ''  // Invalid name
      };

      await expect(adapter.update(appInstance.id, invalidUpdateConfig))
        .rejects.toThrow('Invalid Vue 2 adapter configuration');
    });
  });

  describe('destroy', () => {
    it('should destroy Vue 2 app successfully', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      const appInstance = await adapter.load(config);
      await adapter.mount(appInstance.id, mockContainer);
      await adapter.destroy(appInstance.id);

      expect(mockVueInstance.$destroy).toHaveBeenCalled();
      expect(adapter.getAppInstance(appInstance.id)).toBeUndefined();
    });

    it('should handle destroying non-existent app gracefully', async () => {
      await expect(adapter.destroy('non-existent')).resolves.not.toThrow();
    });
  });

  describe('getAppInstance', () => {
    it('should return app instance for existing app', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      await adapter.load(config);
      const instance = adapter.getAppInstance('test-app');

      expect(instance).toBeDefined();
      expect(instance?.id).toBe('test-app');
    });

    it('should return undefined for non-existent app', () => {
      const instance = adapter.getAppInstance('non-existent');
      expect(instance).toBeUndefined();
    });
  });

  describe('getAllAppInstances', () => {
    it('should return all app instances', async () => {
      const config1: Vue2AdapterConfig = {
        id: 'test-app-1',
        name: 'Test App 1',
        component: { template: '<div>Test 1</div>' }
      };

      const config2: Vue2AdapterConfig = {
        id: 'test-app-2',
        name: 'Test App 2',
        component: { template: '<div>Test 2</div>' }
      };

      await adapter.load(config1);
      await adapter.load(config2);

      const instances = adapter.getAllAppInstances();
      expect(instances).toHaveLength(2);
      expect(instances.map(i => i.id)).toContain('test-app-1');
      expect(instances.map(i => i.id)).toContain('test-app-2');
    });

    it('should return empty array when no apps loaded', () => {
      const instances = adapter.getAllAppInstances();
      expect(instances).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    it('should handle Vue instance creation errors', async () => {
      global.Vue = vi.fn().mockImplementation(() => {
        throw new Error('Vue instance creation failed');
      });

      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      await expect(adapter.load(config)).rejects.toThrow('Vue instance creation failed');
    });

    it('should handle mount errors', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      mockVueInstance.$mount = vi.fn().mockImplementation(() => {
        throw new Error('Mount failed');
      });

      const appInstance = await adapter.load(config);
      
      await expect(adapter.mount(appInstance.id, mockContainer))
        .rejects.toThrow('Mount failed');
    });

    it('should configure error handler', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>'
        }
      };

      await adapter.load(config);

      expect(mockVue.config.errorHandler).toBeDefined();
      expect(typeof mockVue.config.errorHandler).toBe('function');
    });
  });

  describe('component options', () => {
    it('should handle component with data function', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>{{ message }}</div>',
          data: () => ({ message: 'Hello Vue 2' })
        }
      };

      const appInstance = await adapter.load(config);
      expect(appInstance).toBeDefined();
    });

    it('should handle component with methods', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>',
          methods: {
            handleClick: () => console.log('clicked')
          }
        }
      };

      const appInstance = await adapter.load(config);
      expect(appInstance).toBeDefined();
    });

    it('should handle component with computed properties', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>{{ computedValue }}</div>',
          data: () => ({ value: 'test' }),
          computed: {
            computedValue() {
              return this.value.toUpperCase();
            }
          }
        }
      };

      const appInstance = await adapter.load(config);
      expect(appInstance).toBeDefined();
    });

    it('should handle component with lifecycle hooks', async () => {
      const config: Vue2AdapterConfig = {
        id: 'test-app',
        name: 'Test App',
        component: {
          template: '<div>Test</div>',
          created: vi.fn(),
          mounted: vi.fn(),
          destroyed: vi.fn()
        }
      };

      const appInstance = await adapter.load(config);
      expect(appInstance).toBeDefined();
    });
  });
});
