/**
 * Vue 2 适配器
 * 提供 Vue 2 应用的微前端集成支持
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { BaseAdapter, BaseAdapterOptions, LifecycleManager } from '../../shared/base-adapter';
import { Vue2Lifecycles } from './lifecycles';

/**
 * Vue 2 适配器选项接口
 */
export interface Vue2AdapterOptions extends BaseAdapterOptions {
  /** 是否启用开发者工具 */
  enableDevtools?: boolean;
  /** 是否启用生产提示 */
  enableProductionTip?: boolean;
  /** Vue 2 全局配置 */
  globalProperties?: Record<string, any>;
  /** 是否启用响应式系统调试 */
  enableReactivityDebugging?: boolean;
}

/**
 * Vue 2 适配器插件
 * 负责将 Vue 2 应用集成到微前端框架中
 */
export class Vue2Adapter extends BaseAdapter {
  public readonly name = 'adapter-vue2';
  public readonly version = '0.1.0';

  constructor(options: Vue2AdapterOptions = {}) {
    super({
      enableDevtools: process.env.NODE_ENV === 'development',
      enableProductionTip: false,
      enableReactivityDebugging: false,
      globalProperties: {},
      ...options
    });
  }

  /**
   * 获取适配器类型
   */
  protected getAdapterType(): string {
    return 'vue2';
  }

  /**
   * 创建生命周期管理器
   */
  protected createLifecycleManager(
    appName: string,
    appConfig: any,
    options: BaseAdapterOptions
  ): LifecycleManager {
    this.validateAppConfig(appConfig);
    return new Vue2Lifecycles(appName, appConfig, options as Vue2AdapterOptions);
  }

  /**
   * Vue 2 特定的安装逻辑
   */
  protected override onInstall(kernel: MicroCoreKernel): void {
    // Vue 2 特定的初始化逻辑
    if (this.options.enableDevtools && typeof window !== 'undefined') {
      // 启用 Vue DevTools 支持
      (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ = 
        (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ || {};
    }

    // 设置 Vue 2 全局配置
    if (typeof window !== 'undefined' && (window as any).Vue) {
      const Vue = (window as any).Vue;
      if (Vue.config) {
        Vue.config.productionTip = (this.options as Vue2AdapterOptions).enableProductionTip ?? false;
        Vue.config.devtools = (this.options as Vue2AdapterOptions).enableDevtools ?? false;
      }
    }
  }

  /**
   * Vue 2 特定的卸载逻辑
   */
  protected override onUninstall(kernel: MicroCoreKernel): void {
    // Vue 2 特定的清理逻辑
    // 清理全局混入、指令、过滤器等
  }

  /**
   * 获取 Vue 2 特定的适配器状态
   */
  public getVue2Status(): {
    activeApps: number;
    options: Vue2AdapterOptions;
    vueVersion?: string;
    reactivityDebuggingEnabled: boolean;
  } {
    const baseStatus = this.getStatus();
    return {
      activeApps: baseStatus.activeApps,
      options: this.options as Vue2AdapterOptions,
      vueVersion: this.getVueVersion(),
      reactivityDebuggingEnabled: (this.options as Vue2AdapterOptions).enableReactivityDebugging ?? false
    };
  }

  /**
   * 获取 Vue 版本
   */
  private getVueVersion(): string | undefined {
    try {
      // 尝试获取 Vue 版本
      if (typeof window !== 'undefined' && (window as any).Vue) {
        return (window as any).Vue.version;
      }
      return undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * 添加全局混入
   */
  public addGlobalMixin(mixin: any): void {
    if (typeof window !== 'undefined' && (window as any).Vue) {
      (window as any).Vue.mixin(mixin);
    }
  }

  /**
   * 注册全局组件
   */
  public registerGlobalComponent(name: string, component: any): void {
    if (typeof window !== 'undefined' && (window as any).Vue) {
      (window as any).Vue.component(name, component);
    }
  }

  /**
   * 注册全局指令
   */
  public registerGlobalDirective(name: string, directive: any): void {
    if (typeof window !== 'undefined' && (window as any).Vue) {
      (window as any).Vue.directive(name, directive);
    }
  }

  /**
   * 注册全局过滤器
   */
  public registerGlobalFilter(name: string, filter: Function): void {
    if (typeof window !== 'undefined' && (window as any).Vue) {
      (window as any).Vue.filter(name, filter);
    }
  }
}

// 导出工厂函数
export function createVue2Adapter(options?: Vue2AdapterOptions): Vue2Adapter {
  return new Vue2Adapter(options);
}

// 默认导出
export default Vue2Adapter;

// 导出相关类型和工具
export * from './lifecycles';
