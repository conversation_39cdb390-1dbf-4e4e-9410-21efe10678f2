/**
 * Vue 2 Adapter Implementation
 */

import Vue from 'vue';
import { 
  BaseAdapter, 
  AppConfig, 
  LifecycleManager,
  SandboxManager,
  CommunicationManager,
  ErrorHandler
} from '@micro-core/core';
import { Vue2ComponentWrapper } from './component-wrapper';
import { Vue2ErrorHandler } from './error-handler';
import type { 
  Vue2AdapterConfig, 
  Vue2AppConfig, 
  Vue2AppInstance,
  Vue2LifecycleHooks,
  Vue2ComponentOptions
} from './types';

export class Vue2Adapter extends BaseAdapter {
  private vueInstance: Vue | null = null;
  private appInstance: Vue2AppInstance | null = null;
  private config: Vue2AdapterConfig;

  constructor(
    config: Vue2AdapterConfig = {},
    lifecycleManager: LifecycleManager,
    sandboxManager: SandboxManager,
    communicationManager: CommunicationManager,
    errorHandler: ErrorHandler
  ) {
    super('vue2', lifecycleManager, sandboxManager, communicationManager, errorHandler);
    this.config = {
      vueVersion: '2.7',
      enableDevTools: true,
      globalConfig: {},
      ...config
    };

    this.setupVueGlobalConfig();
  }

  /**
   * Check if the app is a Vue 2 application
   */
  canHandle(appConfig: AppConfig): boolean {
    const vue2Config = appConfig as Vue2AppConfig;
    return !!(
      vue2Config.vue2 ||
      vue2Config.component ||
      vue2Config.vueOptions ||
      this.detectVue2App(appConfig.entry)
    );
  }

  /**
   * Load Vue 2 application
   */
  async load(appConfig: Vue2AppConfig): Promise<Vue2AppInstance> {
    try {
      this.validateConfig(appConfig);
      
      // Create sandbox for Vue 2 app
      const sandbox = await this.sandboxManager.createSandbox(
        appConfig.name,
        appConfig.sandbox || {}
      );

      // Prepare Vue component options
      const componentOptions = await this.prepareComponentOptions(appConfig);
      
      // Create app instance
      this.appInstance = {
        vue: null as any, // Will be set during mount
        container: this.getContainer(appConfig),
        config: appConfig,
        hooks: appConfig.hooks as Vue2LifecycleHooks || {},
        mount: () => this.mount(),
        unmount: () => this.unmount(),
        update: (props: any) => this.update(props)
      };

      // Register lifecycle hooks
      await this.registerLifecycleHooks(appConfig);

      return this.appInstance;
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'Vue2Adapter.load',
        appName: appConfig.name
      });
      throw error;
    }
  }

  /**
   * Mount Vue 2 application
   */
  async mount(): Promise<void> {
    if (!this.appInstance) {
      throw new Error('App instance not loaded');
    }

    try {
      // Execute before mount hook
      if (this.appInstance.hooks.beforeMount) {
        await this.appInstance.hooks.beforeMount();
      }

      // Create Vue component options
      const componentOptions = await this.prepareComponentOptions(this.appInstance.config);

      // Create Vue instance
      this.vueInstance = new Vue({
        ...componentOptions,
        el: this.appInstance.container,
        data: () => ({
          ...componentOptions.data?.() || {},
          microAppProps: this.appInstance!.config.props || {}
        }),
        beforeCreate: () => {
          // Execute before create hook
          if (this.appInstance?.hooks.beforeCreate) {
            this.appInstance.hooks.beforeCreate();
          }
        },
        created: () => {
          // Execute created hook
          if (this.appInstance?.hooks.created) {
            this.appInstance.hooks.created();
          }
        },
        mounted: () => {
          // Execute mounted hook
          if (this.appInstance?.hooks.mounted) {
            this.appInstance.hooks.mounted();
          }
        },
        errorCaptured: (error: Error, vm: Vue, info: string) => {
          // Execute error captured hook
          if (this.appInstance?.hooks.errorCaptured) {
            return this.appInstance.hooks.errorCaptured(error, vm, info);
          }
          
          this.errorHandler.handleError(error, {
            context: 'Vue2Adapter.errorCaptured',
            appName: this.appInstance!.config.name,
            info,
            vm
          });
          
          return false;
        }
      });

      this.appInstance.vue = this.vueInstance;

      // Execute after mount hook
      if (this.appInstance.hooks.mounted) {
        await this.appInstance.hooks.mounted();
      }

      // Notify lifecycle manager
      await this.lifecycleManager.notifyMounted(this.appInstance.config.name);
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'Vue2Adapter.mount',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Unmount Vue 2 application
   */
  async unmount(): Promise<void> {
    if (!this.appInstance || !this.vueInstance) {
      return;
    }

    try {
      // Execute before destroy hook
      if (this.appInstance.hooks.beforeDestroy) {
        await this.appInstance.hooks.beforeDestroy();
      }

      // Destroy Vue instance
      this.vueInstance.$destroy();
      this.vueInstance = null;

      // Execute destroyed hook
      if (this.appInstance.hooks.destroyed) {
        await this.appInstance.hooks.destroyed();
      }

      // Notify lifecycle manager
      await this.lifecycleManager.notifyUnmounted(this.appInstance.config.name);
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'Vue2Adapter.unmount',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Update Vue 2 application
   */
  async update(props: any): Promise<void> {
    if (!this.appInstance || !this.vueInstance) {
      throw new Error('App not mounted');
    }

    try {
      const prevProps = this.appInstance.config.props;
      this.appInstance.config.props = { ...prevProps, ...props };

      // Update Vue instance data
      Object.assign(this.vueInstance.$data.microAppProps, props);

      // Execute update hook
      if (this.appInstance.hooks.updated) {
        await this.appInstance.hooks.updated();
      }
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'Vue2Adapter.update',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Prepare Vue component options from configuration
   */
  private async prepareComponentOptions(appConfig: Vue2AppConfig): Promise<Vue2ComponentOptions> {
    if (appConfig.component) {
      return appConfig.component;
    }

    if (appConfig.vueOptions) {
      return appConfig.vueOptions;
    }

    if (appConfig.entry) {
      // Load component from entry point
      const module = await this.loadModule(appConfig.entry);
      return this.extractVueComponent(module);
    }

    throw new Error('No Vue component or entry point specified');
  }

  /**
   * Load module from entry point
   */
  private async loadModule(entry: string): Promise<any> {
    try {
      // Handle different entry formats
      if (entry.startsWith('http')) {
        // Remote module
        return await this.loadRemoteModule(entry);
      } else {
        // Local module
        return await import(entry);
      }
    } catch (error) {
      throw new Error(`Failed to load Vue module from ${entry}: ${error}`);
    }
  }

  /**
   * Load remote module
   */
  private async loadRemoteModule(url: string): Promise<any> {
    // Implementation for loading remote Vue modules
    const response = await fetch(url);
    const code = await response.text();
    
    // Create a sandbox and execute the module
    const moduleFunction = new Function('exports', 'require', 'module', 'Vue', code);
    const module = { exports: {} };
    moduleFunction(module.exports, require, module, Vue);
    
    return module.exports;
  }

  /**
   * Extract Vue component from module
   */
  private extractVueComponent(module: any): Vue2ComponentOptions {
    if (module.default && this.isVueComponent(module.default)) {
      return module.default;
    }

    // Look for Vue component in module exports
    const componentKeys = Object.keys(module).filter(key => 
      this.isVueComponent(module[key])
    );

    if (componentKeys.length > 0) {
      return module[componentKeys[0]];
    }

    // If no component found, treat the module as component options
    if (module.template || module.render || module.data) {
      return module;
    }

    throw new Error('No Vue component found in module');
  }

  /**
   * Check if a value is a Vue component
   */
  private isVueComponent(value: any): boolean {
    if (!value) return false;

    // Vue component options object
    if (typeof value === 'object' && (
      value.template || 
      value.render || 
      value.data || 
      value.computed || 
      value.methods
    )) {
      return true;
    }

    // Vue constructor
    if (typeof value === 'function' && value.cid !== undefined) {
      return true;
    }

    return false;
  }

  /**
   * Get DOM container for Vue app
   */
  private getContainer(appConfig: Vue2AppConfig): HTMLElement {
    if (appConfig.container) {
      if (typeof appConfig.container === 'string') {
        const element = document.querySelector(appConfig.container);
        if (!element) {
          throw new Error(`Container not found: ${appConfig.container}`);
        }
        return element as HTMLElement;
      }
      return appConfig.container;
    }

    // Create default container
    const container = document.createElement('div');
    container.id = `micro-app-${appConfig.name}`;
    return container;
  }

  /**
   * Detect if entry point is a Vue 2 application
   */
  private detectVue2App(entry?: string): boolean {
    if (!entry) return false;
    
    // Simple heuristics for Vue 2 app detection
    return entry.includes('vue') || 
           entry.includes('.vue') || 
           entry.includes('vue2');
  }

  /**
   * Validate Vue 2 app configuration
   */
  private validateConfig(appConfig: Vue2AppConfig): void {
    if (!appConfig.name) {
      throw new Error('App name is required');
    }

    if (!appConfig.component && !appConfig.vueOptions && !appConfig.entry) {
      throw new Error('Either component, vueOptions, or entry must be specified');
    }
  }

  /**
   * Setup Vue global configuration
   */
  private setupVueGlobalConfig(): void {
    // Apply global Vue configuration
    if (this.config.globalConfig) {
      Object.assign(Vue.config, this.config.globalConfig);
    }

    // Setup global error handler
    if (this.config.errorHandler) {
      Vue.config.errorHandler = this.config.errorHandler;
    } else {
      Vue.config.errorHandler = (error: Error, vm: Vue, info: string) => {
        this.errorHandler.handleError(error, {
          context: 'Vue2.globalErrorHandler',
          info,
          vm
        });
      };
    }

    // Enable Vue DevTools if configured
    if (this.config.enableDevTools) {
      Vue.config.devtools = true;
    }
  }

  /**
   * Register lifecycle hooks with the lifecycle manager
   */
  private async registerLifecycleHooks(appConfig: Vue2AppConfig): Promise<void> {
    const hooks = appConfig.hooks as Vue2LifecycleHooks;
    if (!hooks) return;

    // Register standard lifecycle hooks
    if (hooks.load) {
      this.lifecycleManager.registerHook(appConfig.name, 'load', hooks.load);
    }
    if (hooks.mount) {
      this.lifecycleManager.registerHook(appConfig.name, 'mount', hooks.mount);
    }
    if (hooks.unmount) {
      this.lifecycleManager.registerHook(appConfig.name, 'unmount', hooks.unmount);
    }
    if (hooks.unload) {
      this.lifecycleManager.registerHook(appConfig.name, 'unload', hooks.unload);
    }

    // Register Vue 2-specific hooks
    if (hooks.beforeCreate) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeCreate', hooks.beforeCreate);
    }
    if (hooks.created) {
      this.lifecycleManager.registerHook(appConfig.name, 'created', hooks.created);
    }
    if (hooks.beforeMount) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeMount', hooks.beforeMount);
    }
    if (hooks.mounted) {
      this.lifecycleManager.registerHook(appConfig.name, 'mounted', hooks.mounted);
    }
    if (hooks.beforeUpdate) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeUpdate', hooks.beforeUpdate);
    }
    if (hooks.updated) {
      this.lifecycleManager.registerHook(appConfig.name, 'updated', hooks.updated);
    }
    if (hooks.beforeDestroy) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeDestroy', hooks.beforeDestroy);
    }
    if (hooks.destroyed) {
      this.lifecycleManager.registerHook(appConfig.name, 'destroyed', hooks.destroyed);
    }
    if (hooks.errorCaptured) {
      this.lifecycleManager.registerHook(appConfig.name, 'errorCaptured', hooks.errorCaptured);
    }
  }
}
