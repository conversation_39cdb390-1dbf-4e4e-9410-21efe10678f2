/**
 * Vue 2 Adapter Utilities
 * Helper functions for Vue 2 micro-app integration
 */

import Vue from 'vue';
import type { Vue2Adapter } from './vue2-adapter';
import type { Vue2AdapterConfig, Vue2AppConfig, Vue2ComponentOptions } from './types';

/**
 * Create a Vue 2 adapter instance with default configuration
 */
export function createVue2Adapter(
  config: Vue2AdapterConfig = {},
  dependencies?: {
    lifecycleManager?: any;
    sandboxManager?: any;
    communicationManager?: any;
    errorHandler?: any;
  }
): Vue2Adapter {
  const {
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  } = dependencies || {};

  if (!lifecycleManager || !sandboxManager || !communicationManager || !errorHandler) {
    throw new Error('All dependencies are required to create Vue2Adapter');
  }

  return new Vue2Adapter(
    config,
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  );
}

/**
 * Check if an app configuration is for a Vue 2 application
 */
export function isVue2App(config: any): config is Vue2AppConfig {
  return !!(
    config.vue2 ||
    config.component ||
    config.vueOptions ||
    (config.entry && isVue2Entry(config.entry)) ||
    (config.framework && config.framework.toLowerCase() === 'vue2')
  );
}

/**
 * Check if an entry point indicates a Vue 2 application
 */
export function isVue2Entry(entry: string): boolean {
  const vue2Indicators = [
    'vue',
    '.vue',
    'vue2',
    'vue-app'
  ];

  const lowerEntry = entry.toLowerCase();
  return vue2Indicators.some(indicator => lowerEntry.includes(indicator));
}

/**
 * Get Vue 2 version from the environment
 */
export function getVue2Version(): string | null {
  try {
    if (Vue && Vue.version) {
      return Vue.version;
    }

    // Try to get from package.json if available
    if (typeof require !== 'undefined') {
      try {
        const VuePackage = require('vue/package.json');
        return VuePackage.version;
      } catch {
        // Vue not available
      }
    }

    return null;
  } catch {
    return null;
  }
}

/**
 * Check Vue 2 version compatibility
 */
export function isVue2VersionCompatible(version: string, minVersion: string = '2.6.0'): boolean {
  try {
    const parseVersion = (v: string) => v.split('.').map(Number);
    const current = parseVersion(version);
    const minimum = parseVersion(minVersion);

    for (let i = 0; i < Math.max(current.length, minimum.length); i++) {
      const currentPart = current[i] || 0;
      const minimumPart = minimum[i] || 0;

      if (currentPart > minimumPart) return true;
      if (currentPart < minimumPart) return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Validate Vue 2 app configuration
 */
export function validateVue2Config(config: Vue2AppConfig): void {
  if (!config.name) {
    throw new Error('Vue 2 app name is required');
  }

  if (!config.component && !config.vueOptions && !config.entry) {
    throw new Error('Either component, vueOptions, or entry must be specified for Vue 2 app');
  }

  if (config.vue2?.vueVersion) {
    const currentVersion = getVue2Version();
    if (currentVersion && !isVue2VersionCompatible(currentVersion, config.vue2.vueVersion)) {
      console.warn(
        `Vue 2 version mismatch. Current: ${currentVersion}, Required: ${config.vue2.vueVersion}`
      );
    }
  }
}

/**
 * Create default Vue 2 app configuration
 */
export function createDefaultVue2Config(overrides: Partial<Vue2AppConfig> = {}): Vue2AppConfig {
  return {
    name: 'vue2-app',
    framework: 'vue2',
    vue2: {
      vueVersion: '2.7',
      enableDevTools: process.env.NODE_ENV === 'development',
      globalConfig: {
        productionTip: false
      },
      sandbox: {
        isolateVueGlobal: true,
        preserveDevTools: true,
        plugins: [],
        mixins: []
      }
    },
    sandbox: {
      type: 'proxy',
      isolateGlobals: true,
      isolateStyles: true
    },
    ...overrides
  };
}

/**
 * Extract Vue 2 component from module
 */
export function extractVue2Component(module: any): Vue2ComponentOptions {
  if (!module) {
    throw new Error('Module is required');
  }

  // Check for default export
  if (module.default && isVue2Component(module.default)) {
    return module.default;
  }

  // Check for named exports
  const componentKeys = Object.keys(module).filter(key => 
    isVue2Component(module[key])
  );

  if (componentKeys.length === 1) {
    return module[componentKeys[0]];
  }

  if (componentKeys.length > 1) {
    // Look for common component names
    const commonNames = ['App', 'Main', 'Root', 'Component'];
    for (const name of commonNames) {
      if (componentKeys.includes(name)) {
        return module[name];
      }
    }

    // Return the first one found
    return module[componentKeys[0]];
  }

  // If no component found but has Vue component properties, treat as component options
  if (module.template || module.render || module.data) {
    return module;
  }

  throw new Error('No Vue 2 component found in module');
}

/**
 * Check if a value is a Vue 2 component
 */
export function isVue2Component(value: any): boolean {
  if (!value) return false;

  // Vue component options object
  if (typeof value === 'object' && (
    value.template || 
    value.render || 
    value.data || 
    value.computed || 
    value.methods ||
    value.props ||
    value.components
  )) {
    return true;
  }

  // Vue constructor
  if (typeof value === 'function' && value.cid !== undefined) {
    return true;
  }

  // Vue component instance
  if (value instanceof Vue) {
    return true;
  }

  return false;
}

/**
 * Create Vue 2 app container element
 */
export function createVue2Container(appName: string, parentElement?: HTMLElement): HTMLElement {
  const container = document.createElement('div');
  container.id = `micro-app-${appName}`;
  container.className = `micro-app-container vue2-app-container`;
  container.setAttribute('data-app-name', appName);
  container.setAttribute('data-framework', 'vue2');

  if (parentElement) {
    parentElement.appendChild(container);
  }

  return container;
}

/**
 * Clean up Vue 2 app container
 */
export function cleanupVue2Container(container: HTMLElement): void {
  // Remove all child elements
  while (container.firstChild) {
    container.removeChild(container.firstChild);
  }

  // Remove from parent if it has one
  if (container.parentElement) {
    container.parentElement.removeChild(container);
  }
}

/**
 * Get Vue 2 app container by name
 */
export function getVue2Container(appName: string): HTMLElement | null {
  return document.getElementById(`micro-app-${appName}`);
}

/**
 * Check if Vue DevTools is available
 */
export function isVue2DevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && !!(window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__;
}

/**
 * Enable Vue DevTools for a specific app
 */
export function enableVue2DevTools(appName: string): void {
  if (isVue2DevToolsAvailable()) {
    console.log(`Vue 2 DevTools enabled for app: ${appName}`);
    Vue.config.devtools = true;
  }
}

/**
 * Create Vue 2 error info object
 */
export function createVue2ErrorInfo(error: Error, vm?: Vue, info?: string): any {
  return {
    vm,
    info: info || '',
    componentName: vm?.$options.name || vm?.$options._componentTag || 'Unknown',
    componentHierarchy: getComponentHierarchy(vm),
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    url: typeof window !== 'undefined' ? window.location.href : ''
  };
}

/**
 * Get Vue component hierarchy
 */
function getComponentHierarchy(vm?: Vue): string[] {
  const hierarchy: string[] = [];
  let current = vm;

  while (current) {
    const name = current.$options.name || current.$options._componentTag || 'Anonymous';
    hierarchy.push(name);
    current = current.$parent;
  }

  return hierarchy;
}

/**
 * Format Vue 2 error for logging
 */
export function formatVue2Error(error: Error, vm?: Vue, info?: string): string {
  let formatted = `Vue 2 Error: ${error.message}\n`;
  
  if (error.stack) {
    formatted += `Stack: ${error.stack}\n`;
  }
  
  if (info) {
    formatted += `Info: ${info}\n`;
  }
  
  if (vm) {
    const componentName = vm.$options.name || vm.$options._componentTag || 'Unknown';
    formatted += `Component: ${componentName}\n`;
    
    const hierarchy = getComponentHierarchy(vm);
    if (hierarchy.length > 0) {
      formatted += `Component Hierarchy: ${hierarchy.join(' -> ')}\n`;
    }
  }
  
  return formatted;
}

/**
 * Deep merge Vue 2 configurations
 */
export function mergeVue2Configs(
  base: Vue2AppConfig, 
  override: Partial<Vue2AppConfig>
): Vue2AppConfig {
  return {
    ...base,
    ...override,
    vue2: {
      ...base.vue2,
      ...override.vue2,
      globalConfig: {
        ...base.vue2?.globalConfig,
        ...override.vue2?.globalConfig
      },
      sandbox: {
        ...base.vue2?.sandbox,
        ...override.vue2?.sandbox
      }
    },
    sandbox: {
      ...base.sandbox,
      ...override.sandbox
    },
    props: {
      ...base.props,
      ...override.props
    }
  };
}

/**
 * Create Vue 2 mixin for micro-app integration
 */
export function createVue2MicroAppMixin(context: any) {
  return {
    data() {
      return {
        $microApp: context
      };
    },
    methods: {
      $emitToParent(event: string, data?: any) {
        if (this.$microApp?.communication) {
          this.$microApp.communication.emit(event, data);
        }
      },
      $sendToApp(targetApp: string, message: any) {
        if (this.$microApp?.communication) {
          this.$microApp.communication.sendToApp(targetApp, message);
        }
      },
      $getGlobalState(key: string) {
        if (this.$microApp?.communication) {
          return this.$microApp.communication.getGlobalState(key);
        }
        return undefined;
      },
      $setGlobalState(state: any) {
        if (this.$microApp?.communication) {
          this.$microApp.communication.setGlobalState(state);
        }
      }
    }
  };
}
