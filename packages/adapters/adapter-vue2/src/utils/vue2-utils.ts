/**
 * @fileoverview Vue 2 Utility Functions
 * 提供 Vue 2 适配器专用的工具函数
 */

import Vue from 'vue';
import type { VueConstructor, ComponentOptions, CreateElement, VNode } from 'vue';

/**
 * Vue 2 版本检测工具
 */
export class Vue2VersionDetector {
  private static _version: string | null = null;

  /**
   * 获取 Vue 2 版本
   */
  static getVersion(): string {
    if (this._version) {
      return this._version;
    }

    try {
      this._version = Vue.version || '2.6.14';
      return this._version;
    } catch (error) {
      console.warn('[Vue2Utils] Failed to detect Vue version:', error);
      return '2.6.14'; // 默认版本
    }
  }

  /**
   * 检查是否支持异步组件
   */
  static supportsAsyncComponents(): boolean {
    const version = this.getVersion();
    const [major, minor] = version.split('.').map(v => parseInt(v, 10));
    return major > 2 || (major === 2 && minor >= 3);
  }

  /**
   * 检查是否支持作用域插槽
   */
  static supportsScopedSlots(): boolean {
    const version = this.getVersion();
    const [major, minor] = version.split('.').map(v => parseInt(v, 10));
    return major > 2 || (major === 2 && minor >= 1);
  }

  /**
   * 检查是否支持函数式组件
   */
  static supportsFunctionalComponents(): boolean {
    const version = this.getVersion();
    const [major, minor] = version.split('.').map(v => parseInt(v, 10));
    return major > 2 || (major === 2 && minor >= 0);
  }
}

/**
 * Vue 2 组件工具
 */
export class Vue2ComponentUtils {
  /**
   * 创建动态组件
   */
  static createDynamicComponent(
    componentOptions: ComponentOptions<Vue>,
    props?: Record<string, any>
  ): ComponentOptions<Vue> {
    return {
      ...componentOptions,
      props: {
        ...componentOptions.props,
        ...props
      }
    };
  }

  /**
   * 包装组件以支持错误处理
   */
  static wrapWithErrorHandler(
    componentOptions: ComponentOptions<Vue>,
    errorHandler?: (error: Error, vm: Vue, info: string) => void
  ): ComponentOptions<Vue> {
    const originalErrorCaptured = componentOptions.errorCaptured;

    return {
      ...componentOptions,
      errorCaptured(error: Error, vm: Vue, info: string) {
        console.error('[Vue2Utils] Component error captured:', error, info);
        
        if (errorHandler) {
          errorHandler(error, vm, info);
        }

        if (originalErrorCaptured) {
          return originalErrorCaptured.call(this, error, vm, info);
        }

        return false; // 阻止错误继续传播
      }
    };
  }

  /**
   * 创建高阶组件
   */
  static createHOC(
    name: string,
    enhancer: (WrappedComponent: ComponentOptions<Vue>) => ComponentOptions<Vue>
  ) {
    return function wrapComponent(WrappedComponent: ComponentOptions<Vue>): ComponentOptions<Vue> {
      const enhanced = enhancer(WrappedComponent);
      enhanced.name = `${name}(${WrappedComponent.name || 'Component'})`;
      return enhanced;
    };
  }

  /**
   * 创建混入
   */
  static createMixin(mixinOptions: ComponentOptions<Vue>): ComponentOptions<Vue> {
    return {
      ...mixinOptions,
      created() {
        console.debug('[Vue2Utils] Mixin created');
        if (mixinOptions.created) {
          mixinOptions.created.call(this);
        }
      },
      beforeDestroy() {
        console.debug('[Vue2Utils] Mixin before destroy');
        if (mixinOptions.beforeDestroy) {
          mixinOptions.beforeDestroy.call(this);
        }
      }
    };
  }
}

/**
 * Vue 2 生命周期工具
 */
export class Vue2LifecycleUtils {
  /**
   * 创建生命周期钩子管理器
   */
  static createLifecycleManager() {
    const hooks = {
      beforeCreate: new Set<() => void>(),
      created: new Set<() => void>(),
      beforeMount: new Set<() => void>(),
      mounted: new Set<() => void>(),
      beforeUpdate: new Set<() => void>(),
      updated: new Set<() => void>(),
      beforeDestroy: new Set<() => void>(),
      destroyed: new Set<() => void>()
    };

    return {
      addHook: (lifecycle: keyof typeof hooks, callback: () => void) => {
        hooks[lifecycle].add(callback);
      },
      removeHook: (lifecycle: keyof typeof hooks, callback: () => void) => {
        hooks[lifecycle].delete(callback);
      },
      triggerHooks: (lifecycle: keyof typeof hooks, context?: Vue) => {
        hooks[lifecycle].forEach(callback => {
          try {
            callback.call(context);
          } catch (error) {
            console.error(`[Vue2Utils] Lifecycle hook error (${lifecycle}):`, error);
          }
        });
      },
      cleanup: () => {
        Object.values(hooks).forEach(hookSet => hookSet.clear());
      }
    };
  }

  /**
   * 监听组件生命周期
   */
  static watchLifecycle(
    vm: Vue,
    lifecycle: string,
    callback: (vm: Vue) => void
  ): () => void {
    const originalHook = vm.$options[lifecycle];
    
    vm.$options[lifecycle] = function(this: Vue) {
      if (originalHook) {
        originalHook.call(this);
      }
      callback(this);
    };

    // 返回清理函数
    return () => {
      vm.$options[lifecycle] = originalHook;
    };
  }
}

/**
 * Vue 2 响应式工具
 */
export class Vue2ReactivityUtils {
  /**
   * 深度观察对象
   */
  static deepWatch(
    vm: Vue,
    expression: string | (() => any),
    callback: (newVal: any, oldVal: any) => void,
    options?: { immediate?: boolean; deep?: boolean }
  ): () => void {
    const unwatch = vm.$watch(expression, callback, {
      deep: true,
      immediate: false,
      ...options
    });

    return unwatch;
  }

  /**
   * 批量设置响应式数据
   */
  static batchSet(vm: Vue, data: Record<string, any>): void {
    vm.$nextTick(() => {
      Object.entries(data).forEach(([key, value]) => {
        Vue.set(vm, key, value);
      });
    });
  }

  /**
   * 安全地删除响应式属性
   */
  static safeDelete(vm: Vue, key: string): void {
    if (vm.hasOwnProperty(key)) {
      Vue.delete(vm, key);
    }
  }

  /**
   * 创建计算属性
   */
  static createComputed(
    getter: () => any,
    setter?: (value: any) => void
  ): { get: () => any; set?: (value: any) => void } {
    const computed: any = { get: getter };
    if (setter) {
      computed.set = setter;
    }
    return computed;
  }
}

/**
 * Vue 2 事件工具
 */
export class Vue2EventUtils {
  /**
   * 创建事件总线
   */
  static createEventBus(): Vue {
    return new Vue();
  }

  /**
   * 安全地触发事件
   */
  static safeEmit(vm: Vue, event: string, ...args: any[]): void {
    try {
      vm.$emit(event, ...args);
    } catch (error) {
      console.error(`[Vue2Utils] Error emitting event ${event}:`, error);
    }
  }

  /**
   * 批量监听事件
   */
  static batchOn(
    vm: Vue,
    events: Record<string, (...args: any[]) => void>
  ): () => void {
    const cleanupFunctions: (() => void)[] = [];

    Object.entries(events).forEach(([event, handler]) => {
      vm.$on(event, handler);
      cleanupFunctions.push(() => vm.$off(event, handler));
    });

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }

  /**
   * 一次性事件监听
   */
  static once(vm: Vue, event: string, callback: (...args: any[]) => void): void {
    const wrappedCallback = (...args: any[]) => {
      vm.$off(event, wrappedCallback);
      callback(...args);
    };
    
    vm.$on(event, wrappedCallback);
  }
}

/**
 * Vue 2 DOM 工具
 */
export class Vue2DOMUtils {
  /**
   * 安全地查找元素
   */
  static findElement(vm: Vue, selector: string): Element | null {
    try {
      return vm.$el?.querySelector(selector) || null;
    } catch (error) {
      console.warn('[Vue2Utils] Error finding element:', error);
      return null;
    }
  }

  /**
   * 等待 DOM 更新
   */
  static waitForDOM(vm: Vue): Promise<void> {
    return new Promise(resolve => {
      vm.$nextTick(resolve);
    });
  }

  /**
   * 获取组件根元素
   */
  static getRootElement(vm: Vue): Element | null {
    return vm.$el || null;
  }

  /**
   * 检查元素是否在视口中
   */
  static isInViewport(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }
}

/**
 * Vue 2 性能工具
 */
export class Vue2PerformanceUtils {
  private static performanceData = new Map<string, {
    renderCount: number;
    totalRenderTime: number;
    averageRenderTime: number;
  }>();

  /**
   * 监控组件性能
   */
  static monitorComponent(componentOptions: ComponentOptions<Vue>): ComponentOptions<Vue> {
    const componentName = componentOptions.name || 'AnonymousComponent';
    
    return {
      ...componentOptions,
      beforeUpdate() {
        (this as any)._renderStartTime = performance.now();
        if (componentOptions.beforeUpdate) {
          componentOptions.beforeUpdate.call(this);
        }
      },
      updated() {
        const renderTime = performance.now() - (this as any)._renderStartTime;
        Vue2PerformanceUtils.recordRenderTime(componentName, renderTime);
        
        if (componentOptions.updated) {
          componentOptions.updated.call(this);
        }
      }
    };
  }

  /**
   * 记录渲染时间
   */
  private static recordRenderTime(componentName: string, renderTime: number): void {
    let data = this.performanceData.get(componentName);
    if (!data) {
      data = { renderCount: 0, totalRenderTime: 0, averageRenderTime: 0 };
      this.performanceData.set(componentName, data);
    }

    data.renderCount++;
    data.totalRenderTime += renderTime;
    data.averageRenderTime = data.totalRenderTime / data.renderCount;

    if (renderTime > 16) {
      console.warn(`[Vue2Performance] Slow render for ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取性能报告
   */
  static getPerformanceReport(): Record<string, any> {
    const report: Record<string, any> = {};
    
    this.performanceData.forEach((data, componentName) => {
      report[componentName] = {
        ...data,
        averageRenderTime: parseFloat(data.averageRenderTime.toFixed(2))
      };
    });

    return report;
  }

  /**
   * 清除性能数据
   */
  static clearPerformanceData(): void {
    this.performanceData.clear();
  }
}

/**
 * Vue 2 调试工具
 */
export class Vue2DebugUtils {
  /**
   * 打印组件树
   */
  static printComponentTree(vm: Vue, depth: number = 0): void {
    const indent = '  '.repeat(depth);
    const componentName = vm.$options.name || vm.$options._componentTag || 'Anonymous';
    
    console.log(`${indent}${componentName}`, {
      props: vm.$props,
      data: vm.$data,
      computed: Object.keys(vm.$options.computed || {}),
      methods: Object.keys(vm.$options.methods || {})
    });

    // 递归打印子组件
    vm.$children.forEach(child => {
      this.printComponentTree(child, depth + 1);
    });
  }

  /**
   * 获取组件信息
   */
  static getComponentInfo(vm: Vue): {
    name: string;
    props: any;
    data: any;
    computed: string[];
    methods: string[];
    watchers: number;
    children: number;
  } {
    return {
      name: vm.$options.name || vm.$options._componentTag || 'Anonymous',
      props: vm.$props,
      data: vm.$data,
      computed: Object.keys(vm.$options.computed || {}),
      methods: Object.keys(vm.$options.methods || {}),
      watchers: vm._watchers?.length || 0,
      children: vm.$children.length
    };
  }

  /**
   * 检查组件状态
   */
  static checkComponentHealth(vm: Vue): {
    isDestroyed: boolean;
    hasErrors: boolean;
    memoryUsage: string;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    
    if (vm._watchers && vm._watchers.length > 50) {
      recommendations.push('Too many watchers - consider optimization');
    }
    
    if (vm.$children.length > 20) {
      recommendations.push('Too many child components - consider virtualization');
    }

    return {
      isDestroyed: vm._isDestroyed || false,
      hasErrors: false, // 简化实现
      memoryUsage: 'N/A', // 简化实现
      recommendations
    };
  }
}

/**
 * 导出所有工具类
 */
export {
  Vue2VersionDetector,
  Vue2ComponentUtils,
  Vue2LifecycleUtils,
  Vue2ReactivityUtils,
  Vue2EventUtils,
  Vue2DOMUtils,
  Vue2PerformanceUtils,
  Vue2DebugUtils
};

/**
 * 默认导出工具集合
 */
export default {
  Vue2VersionDetector,
  Vue2ComponentUtils,
  Vue2LifecycleUtils,
  Vue2ReactivityUtils,
  Vue2EventUtils,
  Vue2DOMUtils,
  Vue2PerformanceUtils,
  Vue2DebugUtils
};
