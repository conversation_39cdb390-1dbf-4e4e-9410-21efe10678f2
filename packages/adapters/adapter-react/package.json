{"name": "@micro-core/adapter-react", "version": "0.1.0", "description": "Micro-Core React 适配器", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "react", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-react"}, "peerDependencies": {"@micro-core/core": "workspace:*", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0", "vite": "^7.0.4", "vitest": "^3.2.4"}}