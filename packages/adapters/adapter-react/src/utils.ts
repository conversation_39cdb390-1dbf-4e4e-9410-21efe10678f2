/**
 * React Adapter Utilities
 * Helper functions for React micro-app integration
 */

import type { ReactAdapter } from './react-adapter';
import type { ReactAdapterConfig, ReactAppConfig } from './types';

/**
 * Create a React adapter instance with default configuration
 */
export function createReactAdapter(
  config: ReactAdapterConfig = {},
  dependencies?: {
    lifecycleManager?: any;
    sandboxManager?: any;
    communicationManager?: any;
    errorHandler?: any;
  }
): ReactAdapter {
  const {
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  } = dependencies || {};

  if (!lifecycleManager || !sandboxManager || !communicationManager || !errorHandler) {
    throw new Error('All dependencies are required to create ReactAdapter');
  }

  return new ReactAdapter(
    config,
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  );
}

/**
 * Check if an app configuration is for a React application
 */
export function isReactApp(config: any): config is ReactAppConfig {
  return !!(
    config.react ||
    config.component ||
    (config.entry && isReactEntry(config.entry)) ||
    (config.framework && config.framework.toLowerCase() === 'react')
  );
}

/**
 * Check if an entry point indicates a React application
 */
export function isReactEntry(entry: string): boolean {
  const reactIndicators = [
    'react',
    'jsx',
    'tsx',
    'react-dom',
    'react-app'
  ];

  const lowerEntry = entry.toLowerCase();
  return reactIndicators.some(indicator => lowerEntry.includes(indicator));
}

/**
 * Get React version from the environment
 */
export function getReactVersion(): string | null {
  try {
    if (typeof window !== 'undefined' && (window as any).React) {
      return (window as any).React.version;
    }

    // Try to get from package.json if available
    if (typeof require !== 'undefined') {
      try {
        const React = require('react');
        return React.version;
      } catch {
        // React not available
      }
    }

    return null;
  } catch {
    return null;
  }
}

/**
 * Check React version compatibility
 */
export function isReactVersionCompatible(version: string, minVersion: string = '16.8.0'): boolean {
  try {
    const parseVersion = (v: string) => v.split('.').map(Number);
    const current = parseVersion(version);
    const minimum = parseVersion(minVersion);

    for (let i = 0; i < Math.max(current.length, minimum.length); i++) {
      const currentPart = current[i] || 0;
      const minimumPart = minimum[i] || 0;

      if (currentPart > minimumPart) return true;
      if (currentPart < minimumPart) return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Validate React app configuration
 */
export function validateReactConfig(config: ReactAppConfig): void {
  if (!config.name) {
    throw new Error('React app name is required');
  }

  if (!config.component && !config.entry) {
    throw new Error('Either component or entry must be specified for React app');
  }

  if (config.react?.reactVersion) {
    const currentVersion = getReactVersion();
    if (currentVersion && !isReactVersionCompatible(currentVersion, config.react.reactVersion)) {
      console.warn(
        `React version mismatch. Current: ${currentVersion}, Required: ${config.react.reactVersion}`
      );
    }
  }
}

/**
 * Create default React app configuration
 */
export function createDefaultReactConfig(overrides: Partial<ReactAppConfig> = {}): ReactAppConfig {
  return {
    name: 'react-app',
    framework: 'react',
    react: {
      reactVersion: '18',
      enableDevTools: process.env.NODE_ENV === 'development',
      strictMode: false,
      sandbox: {
        isolateContext: true,
        preserveDevTools: true,
        contextProviders: []
      }
    },
    sandbox: {
      type: 'proxy',
      isolateGlobals: true,
      isolateStyles: true
    },
    ...overrides
  };
}

/**
 * Extract React component from module
 */
export function extractReactComponent(module: any): any {
  if (!module) {
    throw new Error('Module is required');
  }

  // Check for default export
  if (module.default && isReactComponent(module.default)) {
    return module.default;
  }

  // Check for named exports
  const componentKeys = Object.keys(module).filter(key => 
    isReactComponent(module[key])
  );

  if (componentKeys.length === 1) {
    return module[componentKeys[0]];
  }

  if (componentKeys.length > 1) {
    // Look for common component names
    const commonNames = ['App', 'Main', 'Root', 'Component'];
    for (const name of commonNames) {
      if (componentKeys.includes(name)) {
        return module[name];
      }
    }

    // Return the first one found
    return module[componentKeys[0]];
  }

  throw new Error('No React component found in module');
}

/**
 * Check if a value is a React component
 */
export function isReactComponent(value: any): boolean {
  if (!value) return false;

  // Function component
  if (typeof value === 'function') {
    // Check if it has React component characteristics
    return (
      value.prototype === undefined || // Arrow function
      value.prototype.constructor === value || // Regular function
      value.prototype.isReactComponent || // Class component
      value.$$typeof === Symbol.for('react.forward_ref') || // Forward ref
      value.$$typeof === Symbol.for('react.memo') // Memo component
    );
  }

  // Class component
  if (typeof value === 'object' && value.prototype && value.prototype.isReactComponent) {
    return true;
  }

  return false;
}

/**
 * Create React app container element
 */
export function createReactContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
  const container = document.createElement('div');
  container.id = `micro-app-${appName}`;
  container.className = `micro-app-container react-app-container`;
  container.setAttribute('data-app-name', appName);
  container.setAttribute('data-framework', 'react');

  if (parentElement) {
    parentElement.appendChild(container);
  }

  return container;
}

/**
 * Clean up React app container
 */
export function cleanupReactContainer(container: HTMLElement): void {
  // Remove all child elements
  while (container.firstChild) {
    container.removeChild(container.firstChild);
  }

  // Remove from parent if it has one
  if (container.parentElement) {
    container.parentElement.removeChild(container);
  }
}

/**
 * Get React app container by name
 */
export function getReactContainer(appName: string): HTMLElement | null {
  return document.getElementById(`micro-app-${appName}`);
}

/**
 * Check if React DevTools is available
 */
export function isReactDevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && !!(window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
}

/**
 * Enable React DevTools for a specific app
 */
export function enableReactDevTools(appName: string): void {
  if (isReactDevToolsAvailable()) {
    console.log(`React DevTools enabled for app: ${appName}`);
    // Additional DevTools setup can be added here
  }
}

/**
 * Create React error info object
 */
export function createReactErrorInfo(error: Error, componentStack?: string): any {
  return {
    componentStack: componentStack || '',
    errorBoundary: 'ReactAdapter',
    errorBoundaryStack: new Error().stack,
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    url: typeof window !== 'undefined' ? window.location.href : ''
  };
}

/**
 * Format React error for logging
 */
export function formatReactError(error: Error, errorInfo?: any): string {
  let formatted = `React Error: ${error.message}\n`;
  
  if (error.stack) {
    formatted += `Stack: ${error.stack}\n`;
  }
  
  if (errorInfo?.componentStack) {
    formatted += `Component Stack: ${errorInfo.componentStack}\n`;
  }
  
  if (errorInfo?.errorBoundary) {
    formatted += `Error Boundary: ${errorInfo.errorBoundary}\n`;
  }
  
  return formatted;
}

/**
 * Deep merge React configurations
 */
export function mergeReactConfigs(
  base: ReactAppConfig, 
  override: Partial<ReactAppConfig>
): ReactAppConfig {
  return {
    ...base,
    ...override,
    react: {
      ...base.react,
      ...override.react,
      sandbox: {
        ...base.react?.sandbox,
        ...override.react?.sandbox
      }
    },
    sandbox: {
      ...base.sandbox,
      ...override.sandbox
    },
    props: {
      ...base.props,
      ...override.props
    }
  };
}
