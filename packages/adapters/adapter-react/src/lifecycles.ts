/**
 * React 生命周期实现
 */

import type { ReactAdapterOptions } from './index';

interface AppConfig {
    container?: string | HTMLElement;
    entry?: string;
    name?: string;
    [key: string]: any;
}

interface LifecycleProps {
    container?: HTMLElement;
    [key: string]: any;
}

export class ReactLifecycles {
    private appName: string;
    private appConfig: AppConfig;
    private options: ReactAdapterOptions;
    private reactRoot: any = null;
    private containerElement: HTMLElement | null = null;
    private isDestroyed = false;
    private React: any = null;
    private ReactDOM: any = null;

    constructor(appName: string, appConfig: AppConfig, options: ReactAdapterOptions) {
        this.appName = appName;
        this.appConfig = appConfig;
        this.options = options;
    }

    /**
     * 获取生命周期函数
     */
    getLifecycles() {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }

    /**
     * 引导阶段
     */
    async bootstrap(props?: LifecycleProps): Promise<void> {
        if (this.isDestroyed) {
            throw new Error(`[ReactLifecycles] ${this.appName} 已被销毁，无法执行引导`);
        }

        console.log(`[ReactLifecycles] ${this.appName} 开始引导`);

        try {
            // 预加载 React 和 ReactDOM
            await Promise.all([
                this.loadReact(),
                this.loadReactDOM()
            ]);

            console.log(`[ReactLifecycles] ${this.appName} 引导完成`);
        } catch (error) {
            console.error(`[ReactLifecycles] ${this.appName} 引导失败:`, error);
            throw new Error(`引导失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 挂载阶段
     */
    async mount(props?: LifecycleProps): Promise<void> {
        if (this.isDestroyed) {
            throw new Error(`[ReactLifecycles] ${this.appName} 已被销毁，无法执行挂载`);
        }

        console.log(`[ReactLifecycles] ${this.appName} 开始挂载`);

        try {
            // 获取容器元素
            this.containerElement = this.getContainer(props);

            // 确保 React 和 ReactDOM 已加载
            if (!this.React || !this.ReactDOM) {
                await Promise.all([
                    this.loadReact(),
                    this.loadReactDOM()
                ]);
            }

            // 获取应用组件
            const AppComponent = await this.getAppComponent();

            // 创建 React 根节点
            if (this.ReactDOM.createRoot) {
                // React 18+ 使用 createRoot
                this.reactRoot = this.ReactDOM.createRoot(this.containerElement);

                const element = this.createAppElement(AppComponent, props);
                this.reactRoot.render(element);
            } else {
                // React 17 及以下使用 render
                const element = this.createAppElement(AppComponent, props);
                this.ReactDOM.render(element, this.containerElement);
            }

            console.log(`[ReactLifecycles] ${this.appName} 挂载完成`);
        } catch (error) {
            console.error(`[ReactLifecycles] ${this.appName} 挂载失败:`, error);
            // 清理已创建的资源
            this.cleanup();
            throw new Error(`挂载失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 卸载阶段
     */
    async unmount(props?: LifecycleProps): Promise<void> {
        if (this.isDestroyed) {
            return; // 已销毁，无需卸载
        }

        console.log(`[ReactLifecycles] ${this.appName} 开始卸载`);

        try {
            if (this.reactRoot) {
                // React 18+ 使用 unmount
                this.reactRoot.unmount();
                this.reactRoot = null;
            } else if (this.containerElement && this.ReactDOM) {
                // React 17 及以下使用 unmountComponentAtNode
                if (this.ReactDOM.unmountComponentAtNode) {
                    this.ReactDOM.unmountComponentAtNode(this.containerElement);
                }
            }

            this.cleanup();
            console.log(`[ReactLifecycles] ${this.appName} 卸载完成`);
        } catch (error) {
            console.error(`[ReactLifecycles] ${this.appName} 卸载失败:`, error);
            // 即使卸载失败，也要清理资源
            this.cleanup();
            throw new Error(`卸载失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 更新阶段
     */
    async update(props?: LifecycleProps): Promise<void> {
        if (this.isDestroyed) {
            throw new Error(`[ReactLifecycles] ${this.appName} 已被销毁，无法执行更新`);
        }

        console.log(`[ReactLifecycles] ${this.appName} 开始更新`);

        try {
            if (this.reactRoot && this.containerElement && this.React) {
                // 获取应用组件
                const AppComponent = await this.getAppComponent();
                const element = this.createAppElement(AppComponent, props);

                this.reactRoot.render(element);
            } else {
                console.warn(`[ReactLifecycles] ${this.appName} 未挂载，无法更新`);
                return;
            }

            console.log(`[ReactLifecycles] ${this.appName} 更新完成`);
        } catch (error) {
            console.error(`[ReactLifecycles] ${this.appName} 更新失败:`, error);
            throw new Error(`更新失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 获取容器元素
     */
    private getContainer(props?: any): HTMLElement {
        const container = props?.container || this.appConfig.container;

        if (typeof container === 'string') {
            const element = document.querySelector(container);
            if (!element) {
                throw new Error(`容器元素未找到: ${container}`);
            }
            return element as HTMLElement;
        } else if (container instanceof HTMLElement) {
            return container;
        } else {
            throw new Error('无效的容器配置');
        }
    }

    /**
     * 加载 React（缓存版本）
     */
    private async loadReact(): Promise<any> {
        if (this.React) {
            return this.React;
        }

        try {
            // 尝试从全局获取
            if ((window as any).React) {
                this.React = (window as any).React;
                return this.React;
            }

            // 动态导入
            const React = await import('react');
            this.React = React.default || React;
            return this.React;
        } catch (error) {
            throw new Error(`无法加载 React: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 加载 ReactDOM（缓存版本）
     */
    private async loadReactDOM(): Promise<any> {
        if (this.ReactDOM) {
            return this.ReactDOM;
        }

        try {
            // 尝试从全局获取
            if ((window as any).ReactDOM) {
                this.ReactDOM = (window as any).ReactDOM;
                return this.ReactDOM;
            }

            // 动态导入
            const ReactDOM = await import('react-dom');
            this.ReactDOM = ReactDOM.default || ReactDOM;
            return this.ReactDOM;
        } catch (error) {
            throw new Error(`无法加载 ReactDOM: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 创建应用元素
     */
    private createAppElement(AppComponent: any, props?: LifecycleProps): any {
        const element = this.React.createElement(AppComponent, props);

        if (this.options.enableStrictMode) {
            return this.React.createElement(this.React.StrictMode, null, element);
        }

        return element;
    }

    /**
     * 清理资源
     */
    private cleanup(): void {
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
            this.containerElement = null;
        }
    }

    /**
     * 获取应用组件
     */
    private async getAppComponent(): Promise<any> {
        // 这里应该根据应用配置加载实际的 React 组件
        // 目前返回一个简单的示例组件
        if (!this.React) {
            await this.loadReact();
        }

        return (props: any) => this.React.createElement('div', {
            style: {
                padding: '20px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                margin: '10px',
                backgroundColor: '#f9f9f9'
            }
        }, [
            this.React.createElement('h2', { key: 'title' }, `React 应用: ${this.appName}`),
            this.React.createElement('p', { key: 'desc' }, '这是一个通过 Micro-Core React 适配器加载的应用'),
            this.React.createElement('p', { key: 'time' }, `加载时间: ${new Date().toLocaleString()}`),
            this.React.createElement('p', { key: 'props' }, `属性: ${JSON.stringify(props, null, 2)}`)
        ]);
    }

    /**
     * 销毁生命周期实例
     */
    destroy(): void {
        if (this.isDestroyed) {
            return;
        }

        this.isDestroyed = true;

        try {
            if (this.reactRoot) {
                this.reactRoot.unmount();
                this.reactRoot = null;
            }

            this.cleanup();

            // 清理缓存的模块引用
            this.React = null;
            this.ReactDOM = null;

            console.log(`[ReactLifecycles] ${this.appName} 生命周期实例已销毁`);
        } catch (error) {
            console.error(`[ReactLifecycles] ${this.appName} 销毁失败:`, error);
        }
    }
}