/**
 * @fileoverview React Utility Functions
 * 提供 React 适配器专用的工具函数
 */

import React from 'react';
import type { ReactElement, ComponentType, ReactNode } from 'react';

/**
 * React 版本检测工具
 */
export class ReactVersionDetector {
  private static _version: string | null = null;

  /**
   * 获取 React 版本
   */
  static getVersion(): string {
    if (this._version) {
      return this._version;
    }

    try {
      this._version = React.version || '18.0.0';
      return this._version;
    } catch (error) {
      console.warn('[ReactUtils] Failed to detect React version:', error);
      return '18.0.0'; // 默认版本
    }
  }

  /**
   * 检查是否支持并发特性
   */
  static supportsConcurrentFeatures(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 18;
  }

  /**
   * 检查是否支持 Strict Mode
   */
  static supportsStrictMode(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 16;
  }

  /**
   * 检查是否支持 Suspense
   */
  static supportsSuspense(): boolean {
    const version = this.getVersion();
    const majorVersion = parseInt(version.split('.')[0], 10);
    return majorVersion >= 16;
  }
}

/**
 * React 组件包装器工具
 */
export class ReactComponentWrapper {
  /**
   * 包装组件以支持错误边界
   */
  static wrapWithErrorBoundary(
    Component: ComponentType<any>,
    errorBoundaryProps?: {
      fallback?: ComponentType<{ error: Error }>;
      onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
    }
  ): ComponentType<any> {
    const { fallback: FallbackComponent, onError } = errorBoundaryProps || {};

    return class WrappedComponent extends React.Component<any, { hasError: boolean; error?: Error }> {
      constructor(props: any) {
        super(props);
        this.state = { hasError: false };
      }

      static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
      }

      componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('[ReactUtils] Component error caught:', error, errorInfo);
        onError?.(error, errorInfo);
      }

      render() {
        if (this.state.hasError) {
          if (FallbackComponent) {
            return React.createElement(FallbackComponent, { error: this.state.error! });
          }
          return React.createElement('div', {
            style: { 
              padding: '20px', 
              border: '1px solid #ff6b6b', 
              borderRadius: '4px',
              backgroundColor: '#ffe0e0',
              color: '#d63031'
            }
          }, `组件渲染错误: ${this.state.error?.message || '未知错误'}`);
        }

        return React.createElement(Component, this.props);
      }
    };
  }

  /**
   * 包装组件以支持 Suspense
   */
  static wrapWithSuspense(
    Component: ComponentType<any>,
    fallback?: ReactNode
  ): ComponentType<any> {
    if (!ReactVersionDetector.supportsSuspense()) {
      console.warn('[ReactUtils] Suspense not supported in this React version');
      return Component;
    }

    return (props: any) => {
      return React.createElement(
        React.Suspense,
        { fallback: fallback || React.createElement('div', null, 'Loading...') },
        React.createElement(Component, props)
      );
    };
  }

  /**
   * 包装组件以支持 Strict Mode
   */
  static wrapWithStrictMode(Component: ComponentType<any>): ComponentType<any> {
    if (!ReactVersionDetector.supportsStrictMode()) {
      console.warn('[ReactUtils] StrictMode not supported in this React version');
      return Component;
    }

    return (props: any) => {
      return React.createElement(
        React.StrictMode,
        null,
        React.createElement(Component, props)
      );
    };
  }
}

/**
 * React 生命周期工具
 */
export class ReactLifecycleUtils {
  /**
   * 创建生命周期钩子
   */
  static createLifecycleHooks() {
    const hooks = {
      onMount: new Set<() => void>(),
      onUnmount: new Set<() => void>(),
      onUpdate: new Set<(prevProps: any, nextProps: any) => void>()
    };

    return {
      addMountHook: (callback: () => void) => {
        hooks.onMount.add(callback);
      },
      addUnmountHook: (callback: () => void) => {
        hooks.onUnmount.add(callback);
      },
      addUpdateHook: (callback: (prevProps: any, nextProps: any) => void) => {
        hooks.onUpdate.add(callback);
      },
      triggerMountHooks: () => {
        hooks.onMount.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('[ReactUtils] Mount hook error:', error);
          }
        });
      },
      triggerUnmountHooks: () => {
        hooks.onUnmount.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('[ReactUtils] Unmount hook error:', error);
          }
        });
      },
      triggerUpdateHooks: (prevProps: any, nextProps: any) => {
        hooks.onUpdate.forEach(callback => {
          try {
            callback(prevProps, nextProps);
          } catch (error) {
            console.error('[ReactUtils] Update hook error:', error);
          }
        });
      },
      cleanup: () => {
        hooks.onMount.clear();
        hooks.onUnmount.clear();
        hooks.onUpdate.clear();
      }
    };
  }
}

/**
 * React 性能优化工具
 */
export class ReactPerformanceUtils {
  /**
   * 创建记忆化组件
   */
  static memoize<P extends object>(
    Component: ComponentType<P>,
    areEqual?: (prevProps: P, nextProps: P) => boolean
  ): ComponentType<P> {
    return React.memo(Component, areEqual);
  }

  /**
   * 创建性能监控 HOC
   */
  static withPerformanceMonitoring<P extends object>(
    Component: ComponentType<P>,
    componentName?: string
  ): ComponentType<P> {
    const displayName = componentName || Component.displayName || Component.name || 'Component';

    return class PerformanceMonitoredComponent extends React.Component<P> {
      private mountTime: number = 0;
      private renderCount: number = 0;

      componentDidMount() {
        this.mountTime = performance.now();
        console.debug(`[ReactPerformance] ${displayName} mounted in ${this.mountTime}ms`);
      }

      componentDidUpdate() {
        this.renderCount++;
        if (this.renderCount % 10 === 0) {
          console.debug(`[ReactPerformance] ${displayName} rendered ${this.renderCount} times`);
        }
      }

      componentWillUnmount() {
        const unmountTime = performance.now();
        const lifespan = unmountTime - this.mountTime;
        console.debug(`[ReactPerformance] ${displayName} unmounted after ${lifespan}ms lifespan`);
      }

      render() {
        const renderStart = performance.now();
        const result = React.createElement(Component, this.props);
        const renderEnd = performance.now();
        
        if (renderEnd - renderStart > 16) { // > 1 frame at 60fps
          console.warn(`[ReactPerformance] ${displayName} slow render: ${renderEnd - renderStart}ms`);
        }

        return result;
      }
    };
  }
}

/**
 * React DOM 工具
 */
export class ReactDOMUtils {
  /**
   * 安全地查找容器元素
   */
  static findContainer(selector: string | HTMLElement): HTMLElement | null {
    if (typeof selector === 'string') {
      return document.querySelector(selector);
    }
    return selector instanceof HTMLElement ? selector : null;
  }

  /**
   * 创建安全的容器
   */
  static createSafeContainer(containerId: string): HTMLElement {
    let container = document.getElementById(containerId);
    
    if (!container) {
      container = document.createElement('div');
      container.id = containerId;
      container.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
      `;
      document.body.appendChild(container);
    }

    return container;
  }

  /**
   * 清理容器
   */
  static cleanupContainer(container: HTMLElement): void {
    if (container && container.parentNode) {
      // 清理事件监听器
      const clone = container.cloneNode(false);
      container.parentNode.replaceChild(clone, container);
      
      // 清理内容
      (clone as HTMLElement).innerHTML = '';
    }
  }

  /**
   * 检查容器是否可用
   */
  static isContainerValid(container: HTMLElement | null): boolean {
    return !!(
      container &&
      container.nodeType === Node.ELEMENT_NODE &&
      document.contains(container)
    );
  }
}

/**
 * React 事件工具
 */
export class ReactEventUtils {
  /**
   * 创建事件代理
   */
  static createEventProxy() {
    const listeners = new Map<string, Set<Function>>();

    return {
      on: (event: string, callback: Function) => {
        if (!listeners.has(event)) {
          listeners.set(event, new Set());
        }
        listeners.get(event)!.add(callback);
      },
      off: (event: string, callback: Function) => {
        listeners.get(event)?.delete(callback);
      },
      emit: (event: string, ...args: any[]) => {
        listeners.get(event)?.forEach(callback => {
          try {
            callback(...args);
          } catch (error) {
            console.error(`[ReactEventUtils] Event handler error for ${event}:`, error);
          }
        });
      },
      cleanup: () => {
        listeners.clear();
      }
    };
  }

  /**
   * 防抖处理
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * 节流处理
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

/**
 * 导出所有工具类
 */
export {
  ReactVersionDetector,
  ReactComponentWrapper,
  ReactLifecycleUtils,
  ReactPerformanceUtils,
  ReactDOMUtils,
  ReactEventUtils
};

/**
 * 默认导出工具集合
 */
export default {
  ReactVersionDetector,
  ReactComponentWrapper,
  ReactLifecycleUtils,
  ReactPerformanceUtils,
  ReactDOMUtils,
  ReactEventUtils
};
