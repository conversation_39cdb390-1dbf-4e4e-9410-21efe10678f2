/**
 * React 适配器
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { BaseAdapter, BaseAdapterOptions, LifecycleManager } from '../../shared/base-adapter';
import { ReactLifecycles } from './lifecycles';

/**
 * React 适配器选项接口
 */
export interface ReactAdapterOptions extends BaseAdapterOptions {
  /** 是否启用严格模式 */
  enableStrictMode?: boolean;
  /** 是否启用并发特性 */
  enableConcurrentFeatures?: boolean;
  /** 是否启用错误边界 */
  errorBoundary?: boolean;
}

/**
 * React 适配器插件
 * 负责将 React 应用集成到微前端框架中
 */
export class ReactAdapter extends BaseAdapter {
  public readonly name = 'adapter-react';
  public readonly version = '0.1.0';

  constructor(options: ReactAdapterOptions = {}) {
    super({
      enableStrictMode: false,
      enableConcurrentFeatures: true,
      errorBoundary: true,
      ...options
    });
  }

  /**
   * 获取适配器类型
   */
  protected getAdapterType(): string {
    return 'react';
  }

  /**
   * 创建生命周期管理器
   */
  protected createLifecycleManager(
    appName: string,
    appConfig: any,
    options: BaseAdapterOptions
  ): LifecycleManager {
    this.validateAppConfig(appConfig);
    return new ReactLifecycles(appName, appConfig, options as ReactAdapterOptions);
  }

  /**
   * React 特定的安装逻辑
   */
  protected onInstall(kernel: MicroCoreKernel): void {
    // React 特定的初始化逻辑
    if (this.options.enableDevtools && typeof window !== 'undefined') {
      // 启用 React DevTools 支持
      (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ = 
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ || {};
    }
  }

  /**
   * React 特定的卸载逻辑
   */
  protected onUninstall(kernel: MicroCoreKernel): void {
    // React 特定的清理逻辑
    // 清理全局状态、事件监听器等
  }

  /**
   * 获取 React 特定的适配器状态
   */
  public getReactStatus(): {
    activeApps: number;
    options: ReactAdapterOptions;
    reactVersion?: string;
  } {
    const baseStatus = this.getStatus();
    return {
      activeApps: baseStatus.activeApps,
      options: this.options as ReactAdapterOptions,
      reactVersion: this.getReactVersion()
    };
  }

  /**
   * 获取 React 版本
   */
  private getReactVersion(): string | undefined {
    try {
      // 尝试获取 React 版本
      if (typeof window !== 'undefined' && (window as any).React) {
        return (window as any).React.version;
      }
      return undefined;
    } catch {
      return undefined;
    }
  }
}

// 导出工厂函数
export function createReactAdapter(options?: ReactAdapterOptions): ReactAdapter {
  return new ReactAdapter(options);
}

// 默认导出
export default ReactAdapter;

// 导出相关类型和工具
export * from './lifecycles';
export type { ReactAdapterOptions };
