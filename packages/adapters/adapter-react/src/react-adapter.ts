/**
 * React Adapter Implementation
 */

import { createElement, ComponentType } from 'react';
import { createRoot, Root } from 'react-dom/client';
import { 
  BaseAdapter, 
  AppConfig, 
  LifecycleManager,
  SandboxManager,
  CommunicationManager,
  ErrorHandler
} from '@micro-core/core';
import { ReactComponentWrapper } from './component-wrapper';
import { ReactErrorBoundary } from './error-boundary';
import type { 
  ReactAdapterConfig, 
  ReactAppConfig, 
  ReactAppInstance,
  ReactLifecycleHooks 
} from './types';

export class ReactAdapter extends BaseAdapter {
  private reactRoot: Root | null = null;
  private appInstance: ReactAppInstance | null = null;
  private config: ReactAdapterConfig;

  constructor(
    config: ReactAdapterConfig = {},
    lifecycleManager: LifecycleManager,
    sandboxManager: SandboxManager,
    communicationManager: CommunicationManager,
    errorHandler: ErrorHandler
  ) {
    super('react', lifecycle<PERSON>anager, sandboxManager, communication<PERSON>ana<PERSON>, errorHandler);
    this.config = {
      reactVersion: '18',
      enableDevTools: true,
      strictMode: false,
      ...config
    };
  }

  /**
   * Check if the app is a React application
   */
  canHandle(appConfig: AppConfig): boolean {
    const reactConfig = appConfig as ReactAppConfig;
    return !!(
      reactConfig.react ||
      reactConfig.component ||
      this.detectReactApp(appConfig.entry)
    );
  }

  /**
   * Load React application
   */
  async load(appConfig: ReactAppConfig): Promise<ReactAppInstance> {
    try {
      this.validateConfig(appConfig);
      
      // Create sandbox for React app
      const sandbox = await this.sandboxManager.createSandbox(
        appConfig.name,
        appConfig.sandbox || {}
      );

      // Prepare React component
      const component = await this.prepareComponent(appConfig);
      
      // Create app instance
      this.appInstance = {
        component,
        container: this.getContainer(appConfig),
        config: appConfig,
        hooks: appConfig.hooks as ReactLifecycleHooks || {},
        mount: () => this.mount(),
        unmount: () => this.unmount(),
        update: (props: any) => this.update(props)
      };

      // Register lifecycle hooks
      await this.registerLifecycleHooks(appConfig);

      return this.appInstance;
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'ReactAdapter.load',
        appName: appConfig.name
      });
      throw error;
    }
  }

  /**
   * Mount React application
   */
  async mount(): Promise<void> {
    if (!this.appInstance) {
      throw new Error('App instance not loaded');
    }

    try {
      // Execute before mount hook
      if (this.appInstance.hooks.beforeMount) {
        await this.appInstance.hooks.beforeMount(this.appInstance.config.props);
      }

      // Create React root
      this.reactRoot = createRoot(this.appInstance.container);

      // Create wrapped component
      const WrappedComponent = this.createWrappedComponent();

      // Render React app
      this.reactRoot.render(WrappedComponent);

      // Execute after mount hook
      if (this.appInstance.hooks.afterMount) {
        await this.appInstance.hooks.afterMount(this.appInstance);
      }

      // Notify lifecycle manager
      await this.lifecycleManager.notifyMounted(this.appInstance.config.name);
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'ReactAdapter.mount',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Unmount React application
   */
  async unmount(): Promise<void> {
    if (!this.appInstance || !this.reactRoot) {
      return;
    }

    try {
      // Execute before unmount hook
      if (this.appInstance.hooks.beforeUnmount) {
        await this.appInstance.hooks.beforeUnmount(this.appInstance);
      }

      // Unmount React app
      this.reactRoot.unmount();
      this.reactRoot = null;

      // Execute after unmount hook
      if (this.appInstance.hooks.afterUnmount) {
        await this.appInstance.hooks.afterUnmount();
      }

      // Notify lifecycle manager
      await this.lifecycleManager.notifyUnmounted(this.appInstance.config.name);
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'ReactAdapter.unmount',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Update React application
   */
  async update(props: any): Promise<void> {
    if (!this.appInstance || !this.reactRoot) {
      throw new Error('App not mounted');
    }

    try {
      const prevProps = this.appInstance.config.props;
      this.appInstance.config.props = { ...prevProps, ...props };

      // Execute update hook
      if (this.appInstance.hooks.onUpdate) {
        await this.appInstance.hooks.onUpdate(prevProps, this.appInstance.config.props);
      }

      // Re-render with new props
      const WrappedComponent = this.createWrappedComponent();
      this.reactRoot.render(WrappedComponent);
    } catch (error) {
      this.errorHandler.handleError(error as Error, {
        context: 'ReactAdapter.update',
        appName: this.appInstance.config.name
      });
      throw error;
    }
  }

  /**
   * Create wrapped React component with error boundary and providers
   */
  private createWrappedComponent() {
    if (!this.appInstance) {
      throw new Error('App instance not available');
    }

    const { component: Component, config } = this.appInstance;
    const ErrorBoundary = this.config.errorBoundary || ReactErrorBoundary;

    return createElement(
      ErrorBoundary,
      {
        onError: (error: Error, errorInfo: any) => {
          if (this.appInstance?.hooks.onError) {
            this.appInstance.hooks.onError(error, errorInfo);
          }
          this.errorHandler.handleError(error, {
            context: 'ReactErrorBoundary',
            appName: config.name,
            errorInfo
          });
        }
      },
      createElement(
        ReactComponentWrapper,
        {
          config,
          hooks: this.appInstance.hooks,
          adapter: this
        },
        createElement(Component, config.props || {})
      )
    );
  }

  /**
   * Prepare React component from configuration
   */
  private async prepareComponent(appConfig: ReactAppConfig): Promise<ComponentType<any>> {
    if (appConfig.component) {
      return appConfig.component;
    }

    if (appConfig.entry) {
      // Load component from entry point
      const module = await this.loadModule(appConfig.entry);
      return module.default || module;
    }

    throw new Error('No React component or entry point specified');
  }

  /**
   * Load module from entry point
   */
  private async loadModule(entry: string): Promise<any> {
    try {
      // Handle different entry formats
      if (entry.startsWith('http')) {
        // Remote module
        return await this.loadRemoteModule(entry);
      } else {
        // Local module
        return await import(entry);
      }
    } catch (error) {
      throw new Error(`Failed to load React module from ${entry}: ${error}`);
    }
  }

  /**
   * Load remote module
   */
  private async loadRemoteModule(url: string): Promise<any> {
    // Implementation for loading remote React modules
    // This would integrate with the resource manager
    const response = await fetch(url);
    const code = await response.text();
    
    // Create a sandbox and execute the module
    const moduleFunction = new Function('exports', 'require', 'module', code);
    const module = { exports: {} };
    moduleFunction(module.exports, require, module);
    
    return module.exports;
  }

  /**
   * Get DOM container for React app
   */
  private getContainer(appConfig: ReactAppConfig): HTMLElement {
    if (appConfig.container) {
      if (typeof appConfig.container === 'string') {
        const element = document.querySelector(appConfig.container);
        if (!element) {
          throw new Error(`Container not found: ${appConfig.container}`);
        }
        return element as HTMLElement;
      }
      return appConfig.container;
    }

    // Create default container
    const container = document.createElement('div');
    container.id = `micro-app-${appConfig.name}`;
    return container;
  }

  /**
   * Detect if entry point is a React application
   */
  private detectReactApp(entry?: string): boolean {
    if (!entry) return false;
    
    // Simple heuristics for React app detection
    return entry.includes('react') || 
           entry.includes('jsx') || 
           entry.includes('tsx');
  }

  /**
   * Validate React app configuration
   */
  private validateConfig(appConfig: ReactAppConfig): void {
    if (!appConfig.name) {
      throw new Error('App name is required');
    }

    if (!appConfig.component && !appConfig.entry) {
      throw new Error('Either component or entry must be specified');
    }
  }

  /**
   * Register lifecycle hooks with the lifecycle manager
   */
  private async registerLifecycleHooks(appConfig: ReactAppConfig): Promise<void> {
    const hooks = appConfig.hooks as ReactLifecycleHooks;
    if (!hooks) return;

    // Register standard lifecycle hooks
    if (hooks.load) {
      this.lifecycleManager.registerHook(appConfig.name, 'load', hooks.load);
    }
    if (hooks.mount) {
      this.lifecycleManager.registerHook(appConfig.name, 'mount', hooks.mount);
    }
    if (hooks.unmount) {
      this.lifecycleManager.registerHook(appConfig.name, 'unmount', hooks.unmount);
    }
    if (hooks.unload) {
      this.lifecycleManager.registerHook(appConfig.name, 'unload', hooks.unload);
    }

    // Register React-specific hooks
    if (hooks.beforeMount) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeMount', hooks.beforeMount);
    }
    if (hooks.afterMount) {
      this.lifecycleManager.registerHook(appConfig.name, 'afterMount', hooks.afterMount);
    }
    if (hooks.beforeUnmount) {
      this.lifecycleManager.registerHook(appConfig.name, 'beforeUnmount', hooks.beforeUnmount);
    }
    if (hooks.afterUnmount) {
      this.lifecycleManager.registerHook(appConfig.name, 'afterUnmount', hooks.afterUnmount);
    }
    if (hooks.onUpdate) {
      this.lifecycleManager.registerHook(appConfig.name, 'onUpdate', hooks.onUpdate);
    }
    if (hooks.onError) {
      this.lifecycleManager.registerHook(appConfig.name, 'onError', hooks.onError);
    }
  }
}
