# @micro-core/adapters

微前端框架适配器集合 - 支持React、Vue、Angular等主流前端框架的无缝集成。

## 📦 包含适配器

### 🚀 核心适配器

- **React适配器** - 支持React 16.8+，包含Hooks、Suspense、Error Boundary等特性
- **Vue2适配器** - 支持Vue 2.6+，兼容Options API和部分Composition API
- **Vue3适配器** - 支持Vue 3.0+，完整支持Composition API、Teleport、Fragments等
- **Angular适配器** - 支持Angular 12+，包含依赖注入、装饰器、RxJS等特性
- **HTML适配器** - 支持原生HTML/JS应用，提供基础的DOM操作和脚本执行

### 🔧 通用功能

- **基础适配器类** - 提供统一的适配器接口和生命周期管理
- **工具函数库** - 框架检测、容器管理、资源加载等实用工具
- **类型定义** - 完整的TypeScript类型支持
- **错误处理** - 统一的错误处理和异常恢复机制

## 🚀 快速开始

### 安装

```bash
# 使用 pnpm
pnpm add @micro-core/adapters

# 使用 npm
npm install @micro-core/adapters

# 使用 yarn
yarn add @micro-core/adapters
```

### 基本使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { createReactAdapter, createVue3Adapter } from '@micro-core/adapters';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 注册React应用
await kernel.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react',
  adapter: createReactAdapter({
    strictMode: true,
    enableErrorBoundary: true
  })
});

// 注册Vue3应用
await kernel.registerApp({
  name: 'vue3-app',
  entry: 'http://localhost:3002',
  container: '#vue3-container',
  activeWhen: '/vue3',
  adapter: createVue3Adapter({
    enableDevtools: true,
    globalProperties: {
      $http: axios
    }
  })
});

// 启动内核
await kernel.start();
```

## 📖 适配器详细说明

### React适配器

支持React 16.8+版本，提供完整的React生态集成。

```typescript
import { createReactAdapter } from '@micro-core/adapters/react';

const reactAdapter = createReactAdapter({
  // 启用严格模式
  strictMode: true,
  
  // 启用错误边界
  enableErrorBoundary: true,
  
  // 渲染模式：legacy | concurrent
  renderMode: 'concurrent',
  
  // Suspense回退组件
  suspenseFallback: <div>加载中...</div>,
  
  // 错误回退组件
  errorFallback: ({ error, retry }) => (
    <div>
      <h2>出错了：{error.message}</h2>
      <button onClick={retry}>重试</button>
    </div>
  ),
  
  // 自定义属性
  customProps: {
    theme: 'dark',
    locale: 'zh-CN'
  },
  
  // 生命周期回调
  onMount: (appInfo) => {
    console.log(`React应用 ${appInfo.name} 已挂载`);
  },
  
  onUnmount: (appInfo) => {
    console.log(`React应用 ${appInfo.name} 已卸载`);
  },
  
  onError: (error, appInfo) => {
    console.error(`React应用 ${appInfo.name} 发生错误:`, error);
  }
});
```

**特性支持：**
- ✅ React Hooks
- ✅ Suspense & Lazy Loading
- ✅ Error Boundaries
- ✅ Concurrent Mode
- ✅ Context API
- ✅ Portal
- ✅ Hot Reload (开发模式)

### Vue3适配器

支持Vue 3.0+版本，完整支持Composition API和新特性。

```typescript
import { createVue3Adapter } from '@micro-core/adapters/vue3';

const vue3Adapter = createVue3Adapter({
  // 全局属性
  globalProperties: {
    $http: axios,
    $router: router,
    $store: store
  },
  
  // 全局组件
  globalComponents: {
    'el-button': ElButton,
    'el-input': ElInput
  },
  
  // 插件
  plugins: [
    { install: (app) => app.use(ElementPlus) },
    { install: (app) => app.use(VueRouter) }
  ],
  
  // 错误处理器
  errorHandler: (error, instance, info) => {
    console.error('Vue应用错误:', error, info);
  },
  
  // 自定义属性
  customProps: {
    theme: 'light',
    size: 'medium'
  }
});
```

**特性支持：**
- ✅ Composition API
- ✅ Teleport
- ✅ Fragments
- ✅ Suspense
- ✅ Multiple Root Nodes
- ✅ Global API Changes
- ✅ Custom Directives
- ✅ Hot Reload (开发模式)

### Vue2适配器

支持Vue 2.6+版本，兼容Options API和部分Composition API。

```typescript
import { createVue2Adapter } from '@micro-core/adapters/vue2';

const vue2Adapter = createVue2Adapter({
  // 全局属性
  globalProperties: {
    $http: axios
  },
  
  // 全局组件
  globalComponents: {
    'my-component': MyComponent
  },
  
  // 混入
  mixins: [
    {
      created() {
        console.log('Vue2应用创建');
      }
    }
  ],
  
  // 错误处理器
  errorHandler: (error, vm, info) => {
    console.error('Vue2应用错误:', error, info);
  }
});
```

**特性支持：**
- ✅ Options API
- ✅ Mixins
- ✅ Filters
- ✅ Global Components
- ✅ Custom Directives
- ✅ Composition API (通过@vue/composition-api)
- ✅ Hot Reload (开发模式)

### Angular适配器

支持Angular 12+版本，包含完整的Angular生态集成。

```typescript
import { createAngularAdapter } from '@micro-core/adapters/angular';

const angularAdapter = createAngularAdapter({
  // 提供者
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: APP_CONFIG, useValue: appConfig }
  ],
  
  // 导入模块
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule
  ],
  
  // 模式
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  
  // 启用Ivy渲染器
  enableIvy: true,
  
  // 启用AOT编译
  enableAot: true
});
```

**特性支持：**
- ✅ Dependency Injection
- ✅ Decorators
- ✅ RxJS
- ✅ Angular Router
- ✅ HttpClient
- ✅ Forms (Reactive & Template-driven)
- ✅ Ivy Renderer
- ✅ Hot Reload (开发模式)

### HTML适配器

支持原生HTML/JavaScript应用，提供基础的DOM操作和脚本执行。

```typescript
import { createHtmlAdapter } from '@micro-core/adapters/html';

const htmlAdapter = createHtmlAdapter({
  // 启用脚本执行
  enableScriptExecution: true,
  
  // 启用样式注入
  enableStyleInjection: true,
  
  // HTML清理
  sanitizeHtml: true,
  
  // 允许的标签
  allowedTags: ['div', 'span', 'p', 'a', 'img', 'script', 'style'],
  
  // 允许的属性
  allowedAttributes: ['id', 'class', 'src', 'href', 'style']
});
```

**特性支持：**
- ✅ DOM操作
- ✅ 脚本执行
- ✅ 样式注入
- ✅ HTML清理
- ✅ 事件处理
- ✅ 资源加载
- ✅ 安全沙箱

## 🔧 高级用法

### 自定义适配器

```typescript
import { BaseAdapter, AdapterConfig, AdapterStatus } from '@micro-core/adapters';
import type { AppInfo } from '@micro-core/core';

interface CustomAdapterConfig extends AdapterConfig {
  customOption?: string;
}

class CustomAdapter extends BaseAdapter {
  public readonly name = 'custom';
  public readonly version = '1.0.0';
  public readonly framework = 'custom';

  constructor(config: CustomAdapterConfig = {}) {
    super(config);
  }

  protected async doBootstrap(appInfo: AppInfo): Promise<void> {
    // 实现启动逻辑
    console.log(`启动自定义应用: ${appInfo.name}`);
  }

  protected async doMount(appInfo: AppInfo, container: HTMLElement): Promise<void> {
    // 实现挂载逻辑
    container.innerHTML = '<div>自定义应用内容</div>';
  }

  protected async doUnmount(appInfo: AppInfo): Promise<void> {
    // 实现卸载逻辑
    if (this._container) {
      this._container.innerHTML = '';
    }
  }

  protected async doUpdate(appInfo: AppInfo, props?: Record<string, any>): Promise<void> {
    // 实现更新逻辑
    console.log(`更新自定义应用: ${appInfo.name}`, props);
  }

  protected async cleanup(): Promise<void> {
    // 实现清理逻辑
    console.log('清理自定义适配器资源');
  }

  protected checkFrameworkAvailability(): boolean {
    // 检查框架可用性
    return true;
  }
}

export function createCustomAdapter(config?: CustomAdapterConfig): CustomAdapter {
  return new CustomAdapter(config);
}
```

### 框架自动检测

```typescript
import { detectFramework } from '@micro-core/adapters';

const appInfo = {
  name: 'my-app',
  entry: 'http://localhost:3000'
};

const detection = detectFramework(appInfo);
console.log('检测结果:', detection);
// {
//   framework: 'react',
//   version: '18.2.0',
//   confidence: 0.9,
//   features: ['jsx', 'hooks', 'components']
// }
```

### 适配器生命周期钩子

```typescript
import { createReactAdapter } from '@micro-core/adapters/react';

const adapter = createReactAdapter({}, {
  beforeBootstrap: async (appInfo) => {
    console.log('准备启动应用:', appInfo.name);
  },
  
  afterBootstrap: async (appInfo) => {
    console.log('应用启动完成:', appInfo.name);
  },
  
  beforeMount: async (appInfo, container) => {
    console.log('准备挂载应用:', appInfo.name);
    // 可以在这里做一些预处理
  },
  
  afterMount: async (appInfo, container) => {
    console.log('应用挂载完成:', appInfo.name);
    // 可以在这里做一些后处理
  },
  
  beforeUnmount: async (appInfo) => {
    console.log('准备卸载应用:', appInfo.name);
  },
  
  afterUnmount: async (appInfo) => {
    console.log('应用卸载完成:', appInfo.name);
  }
});
```

## 🛠️ 工具函数

### 容器管理

```typescript
import { createContainer, cleanupContainer } from '@micro-core/adapters';

// 创建容器
const container = createContainer(appInfo, parentElement);

// 清理容器
cleanupContainer(container);
```

### 资源加载

```typescript
import { loadScript, loadStyle } from '@micro-core/adapters';

// 加载脚本
await loadScript('https://cdn.example.com/app.js', {
  async: true,
  crossOrigin: 'anonymous'
});

// 加载样式
await loadStyle('https://cdn.example.com/app.css', {
  media: 'screen'
});
```

### 样式管理

```typescript
import { injectStyle, removeStyle } from '@micro-core/adapters';

// 注入样式
const styleElement = injectStyle(`
  .my-app {
    color: red;
    font-size: 14px;
  }
`, 'my-app-styles');

// 移除样式
removeStyle(styleElement);
```

## 🔍 调试和开发

### 开发模式

在开发模式下，适配器会提供额外的调试信息和热重载支持：

```typescript
const adapter = createReactAdapter({
  enableDevtools: process.env.NODE_ENV === 'development',
  enableHotReload: process.env.NODE_ENV === 'development'
});
```

### 错误处理

```typescript
const adapter = createReactAdapter({
  onError: (error, appInfo) => {
    // 发送错误到监控系统
    errorReporting.captureException(error, {
      tags: {
        app: appInfo.name,
        framework: 'react'
      }
    });
  }
});
```

## 📊 性能优化

### 懒加载

```typescript
// React懒加载
const LazyComponent = React.lazy(() => import('./MyComponent'));

const adapter = createReactAdapter({
  suspenseFallback: <div>组件加载中...</div>
});
```

### 代码分割

```typescript
// Vue3代码分割
const AsyncComponent = defineAsyncComponent(() => import('./MyComponent.vue'));
```

### 预加载

```typescript
import { loadScript } from '@micro-core/adapters';

// 预加载应用资源
await Promise.all([
  loadScript('https://cdn.example.com/vendor.js'),
  loadScript('https://cdn.example.com/app.js')
]);
```

## 🤝 贡献指南

欢迎贡献新的适配器或改进现有适配器！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/new-adapter`)
3. 提交更改 (`git commit -am 'Add new adapter'`)
4. 推送到分支 (`git push origin feature/new-adapter`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件。

## 🔗 相关链接

- [微前端核心文档](../core/README.md)
- [插件系统文档](../plugins/README.md)
- [构建工具文档](../builders/README.md)
- [示例应用](../../apps/examples/README.md)