import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MicroCoreAdapterHtml',
      fileName: (format) => `index.${format === 'es' ? 'js' : format}`,
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        '@micro-core/core',
        '@micro-core/shared'
      ],
      output: {
        globals: {
          '@micro-core/core': 'MicroCore',
          '@micro-core/shared': 'MicroCoreShared'
        }
      }
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development')
  },
  test: {
    globals: true,
    environment: 'jsdom'
  }
});
