import type { AppLifecycles, AppProps } from '@micro-core/core';
import { logger } from '@micro-core/core';

/**
 * HTML 适配器配置
 */
export interface HtmlAdapterConfig {
    /** 应用名称 */
    name: string;
    /** HTML 内容或 URL */
    html?: string;
    /** 脚本 URL 列表 */
    scripts?: string[];
    /** 样式 URL 列表 */
    styles?: string[];
    /** 内联脚本 */
    inlineScripts?: string[];
    /** 内联样式 */
    inlineStyles?: string[];
    /** 是否自动清理资源 */
    autoCleanup?: boolean;
    /** 自定义初始化函数 */
    onInit?: () => void | Promise<void>;
    /** 自定义销毁函数 */
    onDestroy?: () => void | Promise<void>;
}

/**
 * HTML 适配器
 * 负责管理原生 HTML/JS/CSS 应用的生命周期
 */
export class HtmlAdapter {
    private config: HtmlAdapterConfig;
    private loadedScripts: HTMLScriptElement[] = [];
    private loadedStyles: HTMLLinkElement[] = [];
    private isBootstrapped = false;
    private isMounted = false;
    private container?: HTMLElement;

    constructor(config: HtmlAdapterConfig) {
        this.config = {
            autoCleanup: true,
            ...config
        };
    }

    /**
     * 创建生命周期函数
     */
    createLifecycles(): AppLifecycles {
        return {
            bootstrap: this.bootstrap.bind(this),
            mount: this.mount.bind(this),
            unmount: this.unmount.bind(this),
            update: this.update.bind(this)
        };
    }

    /**
     * 启动应用
     */
    private async bootstrap(props: AppProps): Promise<void> {
        if (this.isBootstrapped) {
            logger.warn(`HTML 应用 ${this.config.name} 已经启动`);
            return;
        }

        try {
            // 加载样式文件
            if (this.config.styles) {
                await this.loadStyles(this.config.styles);
            }

            // 添加内联样式
            if (this.config.inlineStyles) {
                this.addInlineStyles(this.config.inlineStyles);
            }

            // 加载脚本文件
            if (this.config.scripts) {
                await this.loadScripts(this.config.scripts);
            }

            // 执行内联脚本
            if (this.config.inlineScripts) {
                this.executeInlineScripts(this.config.inlineScripts);
            }

            // 执行自定义初始化
            if (this.config.onInit) {
                await this.config.onInit();
            }

            this.isBootstrapped = true;
            logger.info(`HTML 应用 ${this.config.name} 启动成功`);
        } catch (error) {
            logger.error(`HTML 应用 ${this.config.name} 启动失败:`, error);
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    private async mount(props: AppProps): Promise<void> {
        if (!this.isBootstrapped) {
            throw new Error(`HTML 应用 ${this.config.name} 未启动，无法挂载`);
        }

        if (this.isMounted) {
            logger.warn(`HTML 应用 ${this.config.name} 已经挂载`);
            return;
        }

        try {
            this.container = props.container;

            // 设置 HTML 内容
            if (this.config.html) {
                if (this.isUrl(this.config.html)) {
                    // 从 URL 加载 HTML
                    const html = await this.fetchHtml(this.config.html);
                    props.container.innerHTML = html;
                } else {
                    // 直接设置 HTML 内容
                    props.container.innerHTML = this.config.html;
                }
            }

            this.isMounted = true;
            logger.info(`HTML 应用 ${this.config.name} 挂载成功`);
        } catch (error) {
            logger.error(`HTML 应用 ${this.config.name} 挂载失败:`, error);
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    private async unmount(props: AppProps): Promise<void> {
        if (!this.isMounted) {
            logger.warn(`HTML 应用 ${this.config.name} 未挂载，无需卸载`);
            return;
        }

        try {
            // 清理 DOM 内容
            if (props.container) {
                props.container.innerHTML = '';
            }

            // 执行自定义销毁函数
            if (this.config.onDestroy) {
                await this.config.onDestroy();
            }

            // 自动清理资源
            if (this.config.autoCleanup) {
                this.cleanup();
            }

            this.isMounted = false;
            this.container = undefined;

            logger.info(`HTML 应用 ${this.config.name} 卸载成功`);
        } catch (error) {
            logger.error(`HTML 应用 ${this.config.name} 卸载失败:`, error);
            throw error;
        }
    }

    /**
     * 更新应用
     */
    private async update(props: AppProps): Promise<void> {
        if (!this.isMounted) {
            logger.warn(`HTML 应用 ${this.config.name} 未挂载，无法更新`);
            return;
        }

        try {
            // HTML 应用的更新通常是重新设置内容
            if (this.config.html && props.container) {
                if (this.isUrl(this.config.html)) {
                    const html = await this.fetchHtml(this.config.html);
                    props.container.innerHTML = html;
                } else {
                    props.container.innerHTML = this.config.html;
                }
            }

            logger.info(`HTML 应用 ${this.config.name} 更新成功`);
        } catch (error) {
            logger.error(`HTML 应用 ${this.config.name} 更新失败:`, error);
            throw error;
        }
    }

    /**
     * 加载脚本文件
     */
    private async loadScripts(scripts: string[]): Promise<void> {
        const promises = scripts.map(src => this.loadScript(src));
        await Promise.all(promises);
    }

    /**
     * 加载单个脚本文件
     */
    private loadScript(src: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`加载脚本失败: ${src}`));

            document.head.appendChild(script);
            this.loadedScripts.push(script);
        });
    }

    /**
     * 加载样式文件
     */
    private async loadStyles(styles: string[]): Promise<void> {
        const promises = styles.map(href => this.loadStyle(href));
        await Promise.all(promises);
    }

    /**
     * 加载单个样式文件
     */
    private loadStyle(href: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`加载样式失败: ${href}`));

            document.head.appendChild(link);
            this.loadedStyles.push(link);
        });
    }

    /**
     * 执行内联脚本
     */
    private executeInlineScripts(scripts: string[]): void {
        scripts.forEach(scriptContent => {
            try {
                const script = document.createElement('script');
                script.textContent = scriptContent;
                document.head.appendChild(script);
                this.loadedScripts.push(script);
            } catch (error) {
                logger.error(`执行内联脚本失败:`, error);
            }
        });
    }

    /**
     * 添加内联样式
     */
    private addInlineStyles(styles: string[]): void {
        styles.forEach(styleContent => {
            try {
                const style = document.createElement('style');
                style.textContent = styleContent;
                document.head.appendChild(style);
            } catch (error) {
                logger.error(`添加内联样式失败:`, error);
            }
        });
    }

    /**
     * 从 URL 获取 HTML 内容
     */
    private async fetchHtml(url: string): Promise<string> {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            logger.error(`获取 HTML 内容失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 判断是否为 URL
     */
    private isUrl(str: string): boolean {
        try {
            new URL(str);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 清理加载的资源
     */
    private cleanup(): void {
        // 移除加载的脚本
        this.loadedScripts.forEach(script => {
            if (script.parentNode) {
                script.parentNode.removeChild(script);
            }
        });
        this.loadedScripts = [];

        // 移除加载的样式
        this.loadedStyles.forEach(link => {
            if (link.parentNode) {
                link.parentNode.removeChild(link);
            }
        });
        this.loadedStyles = [];
    }

    /**
     * 检查是否已启动
     */
    isAppBootstrapped(): boolean {
        return this.isBootstrapped;
    }

    /**
     * 检查是否已挂载
     */
    isAppMounted(): boolean {
        return this.isMounted;
    }

    /**
     * 获取容器元素
     */
    getContainer(): HTMLElement | undefined {
        return this.container;
    }
}

/**
 * 创建 HTML 适配器
 */
export function createHtmlAdapter(config: HtmlAdapterConfig): HtmlAdapter {
    return new HtmlAdapter(config);
}

/**
 * 创建 HTML 生命周期函数
 */
export function createHtmlLifecycles(config: HtmlAdapterConfig): AppLifecycles {
    const adapter = new HtmlAdapter(config);
    return adapter.createLifecycles();
}