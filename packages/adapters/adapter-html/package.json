{"name": "@micro-core/adapter-html", "version": "0.1.0", "description": "HTML adapter for Micro-Core micro-frontend architecture", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "html", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-html"}, "dependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vitest": "^1.0.0"}}