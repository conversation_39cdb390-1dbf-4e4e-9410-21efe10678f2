# Micro-Core

> 下一代微前端架构解决方案

[![NPM Version](https://img.shields.io/npm/v/@micro-core/core.svg)](https://www.npmjs.com/package/@micro-core/core)
[![License](https://img.shields.io/npm/l/@micro-core/core.svg)](https://github.com/echo008/micro-core/blob/main/LICENSE)
[![Build Status](https://github.com/echo008/micro-core/workflows/CI/badge.svg)](https://github.com/echo008/micro-core/actions)
[![Coverage Status](https://codecov.io/gh/echo008/micro-core/branch/main/graph/badge.svg)](https://codecov.io/gh/echo008/micro-core)

## 🚀 特性

- **微内核架构**: 核心最小化，所有功能通过插件实现
- **多层沙箱策略**: 提供 Proxy、Iframe、WebComponent 等多种沙箱，支持灵活组合
- **渐进式接入**: 支持 Sidecar 模式一行代码接入，以及路由接管等迁移策略
- **跨框架支持**: 全面支持主流前端框架（React, Vue, Angular 等）
- **高性能**: 微内核设计，核心库小于 15KB
- **兼容性迁移**: 提供 qiankun 和 Wujie 兼容插件，支持无缝迁移

## 📦 安装

```bash
# 使用 npm
npm install @micro-core/core

# 使用 yarn
yarn add @micro-core/core

# 使用 pnpm
pnpm add @micro-core/core
```

## 🎯 快速开始

### 基础用法

```typescript
import { MicroCore } from '@micro-core/core';

// 初始化微前端内核
const microCore = new MicroCore();

// 注册微应用
microCore.registerApplication({
  name: 'react-app',
  entry: 'http://localhost:3000',
  container: '#app-container',
  activeWhen: '/react-app'
});

// 启动微前端
microCore.start();
```

### Sidecar 模式（一行代码接入）

```html
<script src="https://unpkg.com/@micro-core/sidecar@latest/dist/index.min.js"></script>
<script>
  window.MicroCoreSidecar.init({
    apps: [
      {
        name: 'react-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/react-app'
      }
    ]
  });
</script>
```

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                               Browser Environment                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              Micro-Core Runtime                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                           MicroCoreKernel (Core)                            │ │
│ │                                                                             │ │
│ │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │ │
│ │  │ Lifecycle   │  │ Plugin      │  │ Sandbox     │  │ Router      │         │ │
│ │  │ Manager     │  │ System      │  │ Manager     │  │ Manager     │         │ │
│ │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘         │ │
│ │                                                                             │ │
│ │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │ │
│ │  │ App         │  │ Event       │  │ Resource    │  │ Communication │       │ │
│ │  │ Registry    │  │ Bus         │  │ Manager     │  │ Manager       │       │ │
│ │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘         │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                             Plugin & Adapter Layer                              │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────────────┐ │
│ │ @micro-core │ @micro-core │ @micro-core │ @micro-core │ @micro-core         │ │
│ │ /plugin-*   │ /adapter-*  │ /builder-*  │ /compat-*   │ /loader-*           │ │
│ └─────────────┴─────────────┴─────────────┴─────────────┴─────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 📚 文档

- [快速开始](https://micro-core.dev/guide/getting-started)
- [核心概念](https://micro-core.dev/guide/core-concepts)
- [API 参考](https://micro-core.dev/api/)
- [插件开发](https://micro-core.dev/advanced/plugin-development)
- [迁移指南](https://micro-core.dev/migration/)

## 🔧 开发

```bash
# 克隆项目
git clone https://github.com/echo008/micro-core.git
cd micro-core

# 安装依赖
pnpm install

# 构建所有包
pnpm build

# 运行测试
pnpm test

# 启动文档开发服务器
pnpm docs:dev

# 启动示例应用
pnpm dev
```

## 🤝 贡献

我们欢迎所有形式的贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

## 📄 许可证

[MIT License](LICENSE)

## 🙏 致谢

感谢所有为 Micro-Core 做出贡献的开发者！

---

**作者**: Echo (<EMAIL>)  
**GitHub**: https://github.com/echo008/micro-core  
**文档**: https://micro-core.dev