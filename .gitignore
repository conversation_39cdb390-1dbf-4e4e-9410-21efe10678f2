# 依赖
node_modules/
.pnpm-store/

# 构建输出
dist/
build/
out/
.next/
.nuxt/
.vitepress/dist/

# 测试覆盖率
coverage/
.nyc_output/
test-results/
playwright-report/

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.cache/
.parcel-cache/

# 错误日志
*.error.log

# 性能报告
performance-report/

# Docker
.dockerignore

# 发布相关
*.tgz
.npmrc

# 编辑器配置
.editorconfig

# Husky
.husky/_/

# 本地配置
.local/
local.config.*

# 备份文件
*.bak
*.backup

# 文档生成
docs/.vitepress/cache/

# 微前端相关
apps/*/dist/
packages/*/dist/
packages/*/lib/

# 测试相关
junit.xml
test-report.xml