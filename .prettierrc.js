module.exports = {
    // 基础配置
    printWidth: 100,
    tabWidth: 2,
    useTabs: false,
    semi: false,
    singleQuote: true,
    quoteProps: 'as-needed',
    trailingComma: 'none',
    bracketSpacing: true,
    bracketSameLine: false,
    arrowParens: 'avoid',
    endOfLine: 'lf',

    // HTML 配置
    htmlWhitespaceSensitivity: 'css',

    // Vue 配置
    vueIndentScriptAndStyle: false,

    // 覆盖配置
    overrides: [
        {
            files: '*.json',
            options: {
                printWidth: 80,
                tabWidth: 2
            }
        },
        {
            files: '*.md',
            options: {
                printWidth: 80,
                proseWrap: 'preserve'
            }
        },
        {
            files: '*.vue',
            options: {
                printWidth: 100,
                singleQuote: true
            }
        },
        {
            files: ['*.yml', '*.yaml'],
            options: {
                tabWidth: 2,
                singleQuote: true
            }
        }
    ]
}