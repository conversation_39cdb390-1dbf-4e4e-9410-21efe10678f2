# 微前端架构项目深度代码审查系统

## Core Features

- 文档规范解析

- 项目结构完整性检查

- 文件内容规范性验证

- 问题分类和优先级管理

- 标准化检查清单生成

## Tech Stack

{
  "Web": {
    "arch": "react",
    "component": "shadcn"
  }
}

## Design

基于React和shadcn/ui构建的代码审查系统，用于深度检查micro-core微前端项目的完整性和规范性

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 读取并解析目录结构设计.md文档，提取完整的项目结构规范

[X] 读取并解析开发设计指导方案.md文档，提取开发规范和质量标准

[X] 扫描micro-core项目的实际目录结构，建立完整的文件清单

[X] 逐个验证packages子包(core、adapters、builders、plugins、shared、sidecar)的结构完整性

[X] 检查apps应用示例的实现规范性

[X] 验证文档系统、测试框架、构建配置的完整性

[X] 以单文件为粒度检查内容实现与设计规范的符合度

[X] 记录所有发现的缺失、偏差和不规范问题

[X] 按优先级对问题进行分类(高/中/低优先级)

[X] 为每个问题提供具体的修改建议和优化方案

[X] 生成标准格式的检查清单.md文件，包含文件路径→问题描述→修改建议

[X] 确保检查结果的可追溯性和可验证性
