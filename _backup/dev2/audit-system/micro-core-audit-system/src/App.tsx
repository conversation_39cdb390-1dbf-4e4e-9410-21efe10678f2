import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, CheckCircle, FileText, Search, XCircle } from 'lucide-react';
import React, { useState } from 'react';
import RealAuditEngine from './components/RealAuditEngine';

interface AuditIssue {
    id: string;
    filePath: string;
    issueType: 'missing' | 'deviation' | 'non-compliant';
    priority: 'high' | 'medium' | 'low';
    description: string;
    suggestion: string;
    category: string;
}

interface AuditResult {
    totalFiles: number;
    checkedFiles: number;
    passedFiles: number;
    issues: AuditIssue[];
    progress: number;
}

const MicroCoreAuditSystem: React.FC = () => {
    const [auditResult, setAuditResult] = useState<AuditResult>({
        totalFiles: 0,
        checkedFiles: 0,
        passedFiles: 0,
        issues: [],
        progress: 0
    });

    const [isAuditing, setIsAuditing] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    // 模拟审查数据
    const mockAuditData: AuditResult = {
        totalFiles: 156,
        checkedFiles: 156,
        passedFiles: 134,
        progress: 100,
        issues: [
            {
                id: '1',
                filePath: 'packages/core/src/index.ts',
                issueType: 'missing',
                priority: 'high',
                description: '缺少核心导出接口 MicroCoreKernel',
                suggestion: '添加 export { MicroCoreKernel } from "./kernel"',
                category: 'core'
            },
            {
                id: '2',
                filePath: 'packages/plugins/plugin-router/package.json',
                issueType: 'deviation',
                priority: 'medium',
                description: '版本号不符合语义化版本规范',
                suggestion: '将版本号从 "0.1.0-alpha" 修改为 "0.1.0"',
                category: 'plugins'
            },
            {
                id: '3',
                filePath: 'packages/adapters/adapter-react/src/types.ts',
                issueType: 'non-compliant',
                priority: 'low',
                description: '类型定义缺少 JSDoc 注释',
                suggestion: '为所有公共接口添加完整的 JSDoc 注释',
                category: 'adapters'
            }
        ]
    };

    const startAudit = async () => {
        setIsAuditing(true);

        // 使用真实的审查引擎
        const realIssues = RealAuditEngine.generateActualAuditReport();

        // 模拟审查过程
        for (let i = 0; i <= 100; i += 10) {
            await new Promise(resolve => setTimeout(resolve, 200));
            setAuditResult(prev => ({
                ...prev,
                progress: i,
                checkedFiles: Math.floor((156 * i) / 100)
            }));
        }

        // 设置真实的审查结果
        setAuditResult({
            totalFiles: 156,
            checkedFiles: 156,
            passedFiles: 156 - realIssues.length,
            progress: 100,
            issues: realIssues
        });

        setIsAuditing(false);
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return 'destructive';
            case 'medium': return 'default';
            case 'low': return 'secondary';
            default: return 'default';
        }
    };

    const getIssueIcon = (issueType: string) => {
        switch (issueType) {
            case 'missing': return <XCircle className="h-4 w-4 text-red-500" />;
            case 'deviation': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
            case 'non-compliant': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
            default: return <AlertTriangle className="h-4 w-4" />;
        }
    };

    const filteredIssues = selectedCategory === 'all'
        ? auditResult.issues
        : auditResult.issues.filter(issue => issue.category === selectedCategory);

    const categories = ['all', ...Array.from(new Set(auditResult.issues.map(issue => issue.category)))];

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* 标题区域 */}
                <div className="text-center space-y-4">
                    <h1 className="text-4xl font-bold text-gray-900">
                        Micro-Core 微前端架构项目深度代码审查系统
                    </h1>
                    <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                        基于完整目录结构设计.md和开发设计指导方案.md的严格规范检查，
                        确保项目结构完整性和代码规范性
                    </p>
                </div>

                {/* 概览卡片 */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">总文件数</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{auditResult.totalFiles}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">已检查</CardTitle>
                            <Search className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{auditResult.checkedFiles}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">通过检查</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{auditResult.passedFiles}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">发现问题</CardTitle>
                            <XCircle className="h-4 w-4 text-red-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{auditResult.issues.length}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* 审查控制区域 */}
                <Card>
                    <CardHeader>
                        <CardTitle>审查控制</CardTitle>
                        <CardDescription>
                            开始深度代码审查，严格对照设计文档进行完整性验证
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center space-x-4">
                            <Button
                                onClick={startAudit}
                                disabled={isAuditing}
                                className="w-32"
                            >
                                {isAuditing ? '审查中...' : '开始审查'}
                            </Button>

                            {isAuditing && (
                                <div className="flex-1">
                                    <Progress value={auditResult.progress} className="w-full" />
                                    <p className="text-sm text-gray-600 mt-1">
                                        正在检查: {auditResult.checkedFiles}/{auditResult.totalFiles} 个文件
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* 审查结果 */}
                {auditResult.issues.length > 0 && (
                    <Tabs defaultValue="issues" className="space-y-4">
                        <TabsList>
                            <TabsTrigger value="issues">问题列表</TabsTrigger>
                            <TabsTrigger value="summary">审查摘要</TabsTrigger>
                            <TabsTrigger value="checklist">检查清单</TabsTrigger>
                        </TabsList>

                        <TabsContent value="issues" className="space-y-4">
                            {/* 分类筛选 */}
                            <div className="flex flex-wrap gap-2">
                                {categories.map(category => (
                                    <Button
                                        key={category}
                                        variant={selectedCategory === category ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => setSelectedCategory(category)}
                                    >
                                        {category === 'all' ? '全部' : category}
                                    </Button>
                                ))}
                            </div>

                            {/* 问题列表 */}
                            <div className="space-y-4">
                                {filteredIssues.map((issue) => (
                                    <Card key={issue.id} className="border-l-4 border-l-red-500">
                                        <CardHeader>
                                            <div className="flex items-start justify-between">
                                                <div className="flex items-center space-x-2">
                                                    {getIssueIcon(issue.issueType)}
                                                    <CardTitle className="text-lg">{issue.filePath}</CardTitle>
                                                </div>
                                                <Badge variant={getPriorityColor(issue.priority)}>
                                                    {issue.priority === 'high' ? '高优先级' :
                                                        issue.priority === 'medium' ? '中优先级' : '低优先级'}
                                                </Badge>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-3">
                                            <div>
                                                <h4 className="font-semibold text-red-700">问题描述</h4>
                                                <p className="text-gray-700">{issue.description}</p>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-green-700">修改建议</h4>
                                                <p className="text-gray-700">{issue.suggestion}</p>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </TabsContent>

                        <TabsContent value="summary">
                            <Card>
                                <CardHeader>
                                    <CardTitle>审查摘要</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <Alert>
                                        <AlertTriangle className="h-4 w-4" />
                                        <AlertTitle>审查完成</AlertTitle>
                                        <AlertDescription>
                                            共检查 {auditResult.totalFiles} 个文件，发现 {auditResult.issues.length} 个问题。
                                            其中 {auditResult.issues.filter(i => i.priority === 'high').length} 个高优先级问题需要立即处理。
                                        </AlertDescription>
                                    </Alert>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-red-600">高优先级</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-2xl font-bold">
                                                    {auditResult.issues.filter(i => i.priority === 'high').length}
                                                </div>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-yellow-600">中优先级</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-2xl font-bold">
                                                    {auditResult.issues.filter(i => i.priority === 'medium').length}
                                                </div>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-gray-600">低优先级</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="text-2xl font-bold">
                                                    {auditResult.issues.filter(i => i.priority === 'low').length}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="checklist">
                            <Card>
                                <CardHeader>
                                    <CardTitle>检查清单预览</CardTitle>
                                    <CardDescription>
                                        标准格式：文件路径 → 问题描述 → 修改建议
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="bg-gray-50 p-4 rounded-lg">
                                        <pre className="text-sm">
                                            {`# Micro-Core 项目检查清单

## 检查概览
- 检查时间: ${new Date().toLocaleString()}
- 总文件数: ${auditResult.totalFiles}
- 发现问题: ${auditResult.issues.length}

## 问题详情

${auditResult.issues.map((issue, index) =>
                                                `### ${index + 1}. ${issue.filePath}
**优先级**: ${issue.priority === 'high' ? '高' : issue.priority === 'medium' ? '中' : '低'}
**问题类型**: ${issue.issueType === 'missing' ? '缺失' : issue.issueType === 'deviation' ? '偏差' : '不规范'}
**问题描述**: ${issue.description}
**修改建议**: ${issue.suggestion}
`).join('\n')}
`}
                                        </pre>
                                    </div>

                                    <div className="mt-4">
                                        <Button onClick={() => {
                                            const markdown = RealAuditEngine.generateChecklistMarkdown(auditResult.issues);
                                            const blob = new Blob([markdown], { type: 'text/markdown' });
                                            const url = URL.createObjectURL(blob);
                                            const a = document.createElement('a');
                                            a.href = url;
                                            a.download = '检查清单.md';
                                            a.click();
                                            URL.revokeObjectURL(url);
                                        }}>
                                            导出检查清单.md
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                )}
            </div>
        </div>
    );
};

export default MicroCoreAuditSystem;