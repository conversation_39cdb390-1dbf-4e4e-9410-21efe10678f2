{"title": "微前端项目全面检查与完善系统", "features": ["文档规范对比检查", "目录结构完整性验证", "功能模块缺失排查", "代码实现规范校验", "自动化修复建议"], "tech": {"Web": {"arch": "react", "component": "shadcn"}}, "design": "采用Material Design风格的现代化界面，以深蓝色为主色调，卡片式布局设计。包含主检查面板、详细报告页面和实时监控面板三个核心界面，提供直观的项目结构树形视图和详细的检查结果展示。", "plan": {"项目结构分析和文档解析模块开发": "doing", "四个规范文档的解析器实现": "holding", "目录结构对比检查引擎开发": "holding", "功能模块完整性验证器实现": "holding", "代码规范校验工具集成": "holding", "检查结果报告生成器开发": "holding", "自动化修复建议系统实现": "holding", "用户界面和交互逻辑开发": "holding", "测试用例编写和系统测试": "holding", "文档编写和部署配置": "holding"}}