module.exports = {
    // 项目根目录
    rootDir: '.',

    // 测试环境
    testEnvironment: 'jsdom',

    // 项目配置
    projects: [
        {
            displayName: 'core',
            testMatch: ['<rootDir>/packages/core/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        },
        {
            displayName: 'adapters',
            testMatch: ['<rootDir>/packages/adapters/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        },
        {
            displayName: 'builders',
            testMatch: ['<rootDir>/packages/builders/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        },
        {
            displayName: 'plugins',
            testMatch: ['<rootDir>/packages/plugins/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        },
        {
            displayName: 'shared',
            testMatch: ['<rootDir>/packages/shared/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        },
        {
            displayName: 'sidecar',
            testMatch: ['<rootDir>/packages/sidecar/**/*.test.ts'],
            setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
        }
    ],

    // TypeScript 转换
    preset: 'ts-jest',
    transform: {
        '^.+\\.tsx?$': ['ts-jest', {
            tsconfig: {
                strict: true,
                noImplicitAny: true,
                noImplicitReturns: true,
                noUnusedLocals: false, // 测试中允许未使用的变量
                noUnusedParameters: false
            }
        }]
    },

    // 模块解析
    moduleNameMapping: {
        '^@micro-core/core$': '<rootDir>/packages/core/src',
        '^@micro-core/shared$': '<rootDir>/packages/shared/src',
        '^@micro-core/sidecar$': '<rootDir>/packages/sidecar/src',
        '^@micro-core/(.*)$': '<rootDir>/packages/$1/src'
    },

    // 文件扩展名
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

    // 覆盖率配置
    collectCoverage: true,
    collectCoverageFrom: [
        'packages/*/src/**/*.{ts,tsx}',
        '!packages/*/src/**/*.d.ts',
        '!packages/*/src/**/*.test.{ts,tsx}',
        '!packages/*/src/**/*.spec.{ts,tsx}',
        '!packages/*/src/**/index.ts'
    ],
    coverageDirectory: 'coverage',
    coverageReporters: [
        'text',
        'text-summary',
        'html',
        'lcov',
        'json'
    ],
    coverageThreshold: {
        global: {
            branches: 90,
            functions: 90,
            lines: 90,
            statements: 90
        },
        './packages/core/': {
            branches: 95,
            functions: 95,
            lines: 95,
            statements: 95
        }
    },

    // 测试超时
    testTimeout: 10000,

    // 清理模拟
    clearMocks: true,
    restoreMocks: true,

    // 详细输出
    verbose: true,

    // 错误处理
    errorOnDeprecated: true,

    // 监听模式配置
    watchPlugins: [
        'jest-watch-typeahead/filename',
        'jest-watch-typeahead/testname'
    ],

    // 全局设置
    globals: {
        'ts-jest': {
            isolatedModules: true
        }
    }
};