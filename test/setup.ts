import { cleanup } from '@testing-library/react';
import { afterAll, afterEach, beforeAll } from 'vitest';

// 全局测试设置
beforeAll(() => {
    // 设置测试环境
    process.env.NODE_ENV = 'test';

    // 模拟浏览器 API
    Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: (query: string) => ({
            matches: false,
            media: query,
            onchange: null,
            addListener: () => { },
            removeListener: () => { },
            addEventListener: () => { },
            removeEventListener: () => { },
            dispatchEvent: () => { }
        })
    });

    // 模拟 ResizeObserver
    global.ResizeObserver = class ResizeObserver {
        observe() { }
        unobserve() { }
        disconnect() { }
    };

    // 模拟 IntersectionObserver
    global.IntersectionObserver = class IntersectionObserver {
        constructor() { }
        observe() { }
        unobserve() { }
        disconnect() { }
    };
});

// 每个测试后清理
afterEach(() => {
    cleanup();
});

// 全局测试清理
afterAll(() => {
    // 清理全局状态
});