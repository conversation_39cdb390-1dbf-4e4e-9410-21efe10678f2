/**
 * @fileoverview React 组件测试工具
 * <AUTHOR> <<EMAIL>>
 */

import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { ReactElement, ReactNode } from 'react'
import { BrowserRouter } from 'react-router-dom'
import { vi } from 'vitest'

/**
 * 创建带路由的测试渲染器
 */
export function renderWithRouter(
    ui: ReactElement,
    options: RenderOptions & { initialEntries?: string[] } = {}
): RenderResult {
    const { initialEntries = ['/'], ...renderOptions } = options

    function Wrapper({ children }: { children: ReactNode }) {
        return (
            <BrowserRouter>
                {children}
            </BrowserRouter>
        )
    }

    return render(ui, { wrapper: Wrapper, ...renderOptions })
}

/**
 * 创建模拟的 React 组件属性
 */
export function createMockReactProps(overrides: Record<string, any> = {}) {
    return {
        name: 'test-app',
        theme: 'light',
        basename: '/test',
        onMount: vi.fn(),
        onUnmount: vi.fn(),
        onUpdate: vi.fn(),
        ...overrides
    }
}

/**
 * 模拟 React Router hooks
 */
export function mockReactRouterHooks() {
    const mockNavigate = vi.fn()
    const mockLocation = {
        pathname: '/test',
        search: '',
        hash: '',
        state: null,
        key: 'test'
    }
    const mockParams = {}

    vi.mock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
            ...actual,
            useNavigate: () => mockNavigate,
            useLocation: () => mockLocation,
            useParams: () => mockParams
        }
    })

    return {
        mockNavigate,
        mockLocation,
        mockParams
    }
}

/**
 * 创建模拟的 React Context
 */
export function createMockContext<T>(defaultValue: T) {
    const context = {
        Provider: ({ children, value }: { children: ReactNode; value: T }) => (
            <div data-testid="mock-provider">{children}</div>
        ),
        Consumer: ({ children }: { children: (value: T) => ReactNode }) => (
            <div data-testid="mock-consumer">{children(defaultValue)}</div>
        )
    }

    return context
}

/**
 * 等待组件状态更新
 */
export async function waitForStateUpdate() {
    await new Promise(resolve => setTimeout(resolve, 0))
}

/**
 * 模拟 React hooks
 */
export function mockReactHooks() {
    const mockSetState = vi.fn()
    const mockUseEffect = vi.fn()
    const mockUseRef = vi.fn(() => ({ current: null }))

    vi.mock('react', async () => {
        const actual = await vi.importActual('react')
        return {
            ...actual,
            useState: (initial: any) => [initial, mockSetState],
            useEffect: mockUseEffect,
            useRef: mockUseRef
        }
    })

    return {
        mockSetState,
        mockUseEffect,
        mockUseRef
    }
}