# 多阶段构建 Dockerfile for Micro-Core

# 基础镜像
FROM node:18-alpine AS base
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@8.15.0

# 复制 package 文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/*/package.json ./packages/*/
COPY apps/*/package.json ./apps/*/
COPY docs/package.json ./docs/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 开发阶段
FROM base AS development
COPY . .
EXPOSE 3000 3001 3002 3003 5173
CMD ["pnpm", "run", "dev"]

# 构建阶段
FROM base AS builder
COPY . .

# 构建所有包
RUN pnpm run build

# 构建文档
RUN pnpm run docs:build

# 生产阶段 - 主应用
FROM nginx:alpine AS production
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# 文档阶段
FROM nginx:alpine AS docs
COPY --from=builder /app/docs/.vitepress/dist /usr/share/nginx/html
COPY nginx-docs.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# CDN 阶段 - 静态资源服务
FROM nginx:alpine AS cdn
COPY --from=builder /app/dist/assets /usr/share/nginx/html/assets
COPY --from=builder /app/packages/*/dist /usr/share/nginx/html/packages/
COPY nginx-cdn.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# 测试阶段
FROM base AS test
COPY . .
RUN pnpm run build
CMD ["pnpm", "run", "test:all"]

# E2E 测试阶段
FROM mcr.microsoft.com/playwright:v1.40.1-focal AS e2e
WORKDIR /app

# 安装 Node.js 和 pnpm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g pnpm@8.15.0

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/*/package.json ./packages/*/
COPY apps/*/package.json ./apps/*/

RUN pnpm install --frozen-lockfile

COPY . .
RUN pnpm run build

CMD ["pnpm", "run", "test:e2e"]