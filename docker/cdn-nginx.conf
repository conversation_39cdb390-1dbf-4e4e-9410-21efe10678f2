events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # CDN 服务器配置
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;

        # CORS 配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;

        # 安全头
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;

        # 缓存策略 - 长期缓存
        location ~* \.(js|css|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }

        # 版本化资源 - 永久缓存
        location ~* \.(js|css)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }

        # 核心包路由
        location /core/ {
            alias /usr/share/nginx/html/core/;
            try_files $uri $uri/ =404;
        }

        # 插件包路由
        location /plugins/ {
            alias /usr/share/nginx/html/plugins/;
            try_files $uri $uri/ =404;
        }

        # 适配器包路由
        location /adapters/ {
            alias /usr/share/nginx/html/adapters/;
            try_files $uri $uri/ =404;
        }

        # 构建工具包路由
        location /builders/ {
            alias /usr/share/nginx/html/builders/;
            try_files $uri $uri/ =404;
        }

        # 共享工具包路由
        location /shared/ {
            alias /usr/share/nginx/html/shared/;
            try_files $uri $uri/ =404;
        }

        # 边车模式包路由
        location /sidecar/ {
            alias /usr/share/nginx/html/sidecar/;
            try_files $uri $uri/ =404;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 版本信息
        location /version {
            access_log off;
            return 200 '{"version":"1.0.0","timestamp":"2024-01-01T00:00:00Z"}\n';
            add_header Content-Type application/json;
        }

        # 包列表
        location /packages {
            access_log off;
            return 200 '{"packages":["core","plugins","adapters","builders","shared","sidecar"]}\n';
            add_header Content-Type application/json;
        }

        # 错误页面
        error_page 404 /404.json;
        error_page 500 502 503 504 /50x.json;
        
        location = /404.json {
            return 404 '{"error":"Not Found","code":404}\n';
            add_header Content-Type application/json;
        }
        
        location = /50x.json {
            return 500 '{"error":"Internal Server Error","code":500}\n';
            add_header Content-Type application/json;
        }
    }
}