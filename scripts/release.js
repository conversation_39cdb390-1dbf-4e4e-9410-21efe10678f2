#!/usr/bin/env node

/**
 * 发布脚本 - 自动化版本管理和发布流程
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')
const semver = require('semver')

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`)
}

function error(message) {
    log(`❌ ${message}`, 'red')
    process.exit(1)
}

function success(message) {
    log(`✅ ${message}`, 'green')
}

function info(message) {
    log(`ℹ️  ${message}`, 'blue')
}

function warning(message) {
    log(`⚠️  ${message}`, 'yellow')
}

// 执行命令
function exec(command, options = {}) {
    try {
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        })
        return result?.trim()
    } catch (err) {
        error(`执行命令失败: ${command}\n${err.message}`)
    }
}

// 获取当前版本
function getCurrentVersion() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    return packageJson.version
}

// 更新版本号
function updateVersion(newVersion) {
    const packageJsonPath = 'package.json'
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    packageJson.version = newVersion
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n')

    // 更新所有子包的版本号
    const packagesDir = 'packages'
    if (fs.existsSync(packagesDir)) {
        const packages = fs.readdirSync(packagesDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name)

        packages.forEach(pkg => {
            const subPackages = ['', 'plugins', 'adapters', 'builders', 'shared'].map(subDir => {
                const fullPath = path.join(packagesDir, subDir, pkg)
                return fs.existsSync(fullPath) ? fullPath : null
            }).filter(Boolean)

            subPackages.forEach(pkgPath => {
                const pkgJsonPath = path.join(pkgPath, 'package.json')
                if (fs.existsSync(pkgJsonPath)) {
                    const pkgJson = JSON.parse(fs.readFileSync(pkgJsonPath, 'utf8'))
                    pkgJson.version = newVersion
                    fs.writeFileSync(pkgJsonPath, JSON.stringify(pkgJson, null, 2) + '\n')
                }
            })
        })
    }
}

// 检查工作目录是否干净
function checkWorkingDirectory() {
    const status = exec('git status --porcelain', { silent: true })
    if (status) {
        error('工作目录不干净，请先提交或暂存所有更改')
    }
}

// 检查当前分支
function checkCurrentBranch() {
    const branch = exec('git rev-parse --abbrev-ref HEAD', { silent: true })
    if (branch !== 'main' && branch !== 'master') {
        warning(`当前分支是 ${branch}，建议在 main/master 分支进行发布`)
    }
    return branch
}

// 运行测试
function runTests() {
    info('运行测试套件...')
    exec('pnpm run test:all')
    success('所有测试通过')
}

// 构建项目
function buildProject() {
    info('构建项目...')
    exec('pnpm run build')
    success('项目构建完成')
}

// 生成变更日志
function generateChangelog() {
    info('生成变更日志...')
    exec('pnpm run changelog')
    success('变更日志生成完成')
}

// 创建 Git 标签
function createGitTag(version) {
    const tagName = `v${version}`
    info(`创建 Git 标签: ${tagName}`)
    exec(`git add .`)
    exec(`git commit -m "chore: release v${version}"`)
    exec(`git tag -a ${tagName} -m "Release v${version}"`)
    success(`Git 标签 ${tagName} 创建完成`)
}

// 推送到远程仓库
function pushToRemote() {
    info('推送到远程仓库...')
    exec('git push origin --follow-tags')
    success('推送完成')
}

// 发布到 NPM
function publishToNpm(tag = 'latest') {
    info(`发布到 NPM (${tag})...`)
    exec(`pnpm run publish:all --tag ${tag}`)
    success('NPM 发布完成')
}

// 主发布流程
async function release(releaseType = 'patch', options = {}) {
    const { dryRun = false, skipTests = false, skipBuild = false, tag = 'latest' } = options

    log('🚀 开始发布流程...', 'cyan')

    // 检查环境
    checkWorkingDirectory()
    const currentBranch = checkCurrentBranch()

    // 获取当前版本和新版本
    const currentVersion = getCurrentVersion()
    const newVersion = semver.inc(currentVersion, releaseType)

    if (!newVersion) {
        error(`无效的发布类型: ${releaseType}`)
    }

    info(`当前版本: ${currentVersion}`)
    info(`新版本: ${newVersion}`)
    info(`发布类型: ${releaseType}`)
    info(`当前分支: ${currentBranch}`)

    if (dryRun) {
        warning('这是一次试运行，不会实际发布')
    }

    // 确认发布
    if (!dryRun) {
        const readline = require('readline')
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        })

        const answer = await new Promise(resolve => {
            rl.question('确认发布? (y/N): ', resolve)
        })
        rl.close()

        if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
            info('发布已取消')
            process.exit(0)
        }
    }

    try {
        // 运行测试
        if (!skipTests) {
            runTests()
        }

        // 构建项目
        if (!skipBuild) {
            buildProject()
        }

        if (!dryRun) {
            // 更新版本号
            updateVersion(newVersion)

            // 生成变更日志
            generateChangelog()

            // 创建 Git 标签
            createGitTag(newVersion)

            // 推送到远程仓库
            pushToRemote()

            // 发布到 NPM
            publishToNpm(tag)
        }

        success(`🎉 版本 ${newVersion} 发布成功!`)

    } catch (err) {
        error(`发布失败: ${err.message}`)
    }
}

// 命令行参数解析
function parseArgs() {
    const args = process.argv.slice(2)
    const releaseType = args[0] || 'patch'

    const options = {
        dryRun: args.includes('--dry-run'),
        skipTests: args.includes('--skip-tests'),
        skipBuild: args.includes('--skip-build'),
        tag: args.find(arg => arg.startsWith('--tag='))?.split('=')[1] || 'latest'
    }

    return { releaseType, options }
}

// 显示帮助信息
function showHelp() {
    console.log(`
使用方法: node scripts/release.js [release-type] [options]

发布类型:
  patch     补丁版本 (默认)
  minor     次要版本
  major     主要版本
  prerelease 预发布版本

选项:
  --dry-run      试运行，不实际发布
  --skip-tests   跳过测试
  --skip-build   跳过构建
  --tag=<tag>    NPM 发布标签 (默认: latest)
  --help         显示帮助信息

示例:
  node scripts/release.js patch
  node scripts/release.js minor --dry-run
  node scripts/release.js major --skip-tests
  node scripts/release.js prerelease --tag=beta
`)
}

// 主程序
if (require.main === module) {
    const { releaseType, options } = parseArgs()

    if (process.argv.includes('--help')) {
        showHelp()
        process.exit(0)
    }

    // 验证发布类型
    const validReleaseTypes = ['patch', 'minor', 'major', 'prerelease', 'prepatch', 'preminor', 'premajor']
    if (!validReleaseTypes.includes(releaseType)) {
        error(`无效的发布类型: ${releaseType}`)
    }

    release(releaseType, options)
}

module.exports = { release }