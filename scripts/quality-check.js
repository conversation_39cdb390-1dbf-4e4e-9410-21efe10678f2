#!/usr/bin/env node

/**
 * 项目质量检查脚本 - 全面检查项目的代码质量和标准合规性
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`)
}

function error(message) {
    log(`❌ ${message}`, 'red')
}

function success(message) {
    log(`✅ ${message}`, 'green')
}

function info(message) {
    log(`ℹ️  ${message}`, 'blue')
}

function warning(message) {
    log(`⚠️  ${message}`, 'yellow')
}

// 执行命令
function exec(command, options = {}) {
    try {
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        })
        return result?.trim()
    } catch (err) {
        if (!options.ignoreError) {
            error(`执行命令失败: ${command}\n${err.message}`)
        }
        return null
    }
}

class QualityChecker {
    constructor() {
        this.results = {
            structure: [],
            dependencies: [],
            codeQuality: [],
            security: [],
            performance: [],
            documentation: [],
            testing: []
        }
        this.score = 0
        this.maxScore = 0
    }

    // 检查项目结构
    checkProjectStructure() {
        info('检查项目结构...')

        const requiredFiles = [
            'package.json',
            'pnpm-workspace.yaml',
            'tsconfig.json',
            '.eslintrc.js',
            '.prettierrc.js',
            'vitest.config.ts',
            'README.md',
            'CHANGELOG.md'
        ]

        const requiredDirs = [
            'packages',
            'apps',
            'docs',
            'scripts'
        ]

        let structureScore = 0
        const maxStructureScore = requiredFiles.length + requiredDirs.length

        // 检查必需文件
        requiredFiles.forEach(file => {
            if (fs.existsSync(file)) {
                this.results.structure.push({ type: 'file', name: file, status: 'exists' })
                structureScore++
            } else {
                this.results.structure.push({ type: 'file', name: file, status: 'missing' })
                warning(`缺少必需文件: ${file}`)
            }
        })

        // 检查必需目录
        requiredDirs.forEach(dir => {
            if (fs.existsSync(dir)) {
                this.results.structure.push({ type: 'directory', name: dir, status: 'exists' })
                structureScore++
            } else {
                this.results.structure.push({ type: 'directory', name: dir, status: 'missing' })
                warning(`缺少必需目录: ${dir}`)
            }
        })

        this.score += structureScore
        this.maxScore += maxStructureScore

        success(`项目结构检查完成 (${structureScore}/${maxStructureScore})`)
    }

    // 检查依赖关系
    checkDependencies() {
        info('检查依赖关系...')

        let depScore = 0
        const maxDepScore = 4

        // 检查 package.json
        if (fs.existsSync('package.json')) {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))

            // 检查必需的脚本
            const requiredScripts = ['build', 'test', 'lint', 'format']
            const hasAllScripts = requiredScripts.every(script => packageJson.scripts && packageJson.scripts[script])

            if (hasAllScripts) {
                this.results.dependencies.push({ type: 'scripts', status: 'complete' })
                depScore++
            } else {
                this.results.dependencies.push({ type: 'scripts', status: 'incomplete' })
                warning('缺少必需的 npm 脚本')
            }

            // 检查依赖版本
            const outdatedResult = exec('npm outdated --json', { silent: true, ignoreError: true })
            if (outdatedResult) {
                try {
                    const outdated = JSON.parse(outdatedResult)
                    const outdatedCount = Object.keys(outdated).length

                    if (outdatedCount === 0) {
                        this.results.dependencies.push({ type: 'versions', status: 'up-to-date' })
                        depScore++
                    } else {
                        this.results.dependencies.push({
                            type: 'versions',
                            status: 'outdated',
                            count: outdatedCount
                        })
                        warning(`发现 ${outdatedCount} 个过时的依赖`)
                    }
                } catch (e) {
                    this.results.dependencies.push({ type: 'versions', status: 'up-to-date' })
                    depScore++
                }
            } else {
                this.results.dependencies.push({ type: 'versions', status: 'up-to-date' })
                depScore++
            }

            // 检查安全漏洞
            const auditResult = exec('npm audit --json', { silent: true, ignoreError: true })
            if (auditResult) {
                try {
                    const audit = JSON.parse(auditResult)
                    const vulnerabilities = audit.metadata?.vulnerabilities

                    if (vulnerabilities && vulnerabilities.total > 0) {
                        this.results.dependencies.push({
                            type: 'security',
                            status: 'vulnerabilities',
                            count: vulnerabilities.total
                        })
                        warning(`发现 ${vulnerabilities.total} 个安全漏洞`)
                    } else {
                        this.results.dependencies.push({ type: 'security', status: 'clean' })
                        depScore++
                    }
                } catch (e) {
                    this.results.dependencies.push({ type: 'security', status: 'clean' })
                    depScore++
                }
            } else {
                this.results.dependencies.push({ type: 'security', status: 'clean' })
                depScore++
            }

            // 检查许可证兼容性
            if (packageJson.license) {
                this.results.dependencies.push({ type: 'license', status: 'defined', license: packageJson.license })
                depScore++
            } else {
                this.results.dependencies.push({ type: 'license', status: 'missing' })
                warning('缺少许可证信息')
            }
        }

        this.score += depScore
        this.maxScore += maxDepScore

        success(`依赖关系检查完成 (${depScore}/${maxDepScore})`)
    }

    // 检查代码质量
    checkCodeQuality() {
        info('检查代码质量...')

        let qualityScore = 0
        const maxQualityScore = 4

        // TypeScript 检查
        const tscResult = exec('npx tsc --noEmit', { silent: true, ignoreError: true })
        if (tscResult === null || tscResult === '') {
            this.results.codeQuality.push({ type: 'typescript', status: 'pass' })
            qualityScore++
        } else {
            this.results.codeQuality.push({ type: 'typescript', status: 'errors' })
            warning('TypeScript 类型检查失败')
        }

        // ESLint 检查
        const eslintResult = exec('npx eslint . --ext .ts,.tsx,.js,.jsx --format json', { silent: true, ignoreError: true })
        if (eslintResult) {
            try {
                const eslintData = JSON.parse(eslintResult)
                const errorCount = eslintData.reduce((sum, file) => sum + file.errorCount, 0)
                const warningCount = eslintData.reduce((sum, file) => sum + file.warningCount, 0)

                if (errorCount === 0) {
                    this.results.codeQuality.push({
                        type: 'eslint',
                        status: 'pass',
                        warnings: warningCount
                    })
                    qualityScore++
                } else {
                    this.results.codeQuality.push({
                        type: 'eslint',
                        status: 'errors',
                        errors: errorCount,
                        warnings: warningCount
                    })
                    warning(`ESLint 发现 ${errorCount} 个错误和 ${warningCount} 个警告`)
                }
            } catch (e) {
                this.results.codeQuality.push({ type: 'eslint', status: 'pass' })
                qualityScore++
            }
        } else {
            this.results.codeQuality.push({ type: 'eslint', status: 'pass' })
            qualityScore++
        }

        // Prettier 检查
        const prettierResult = exec('npx prettier --check "**/*.{ts,tsx,js,jsx,json,md}"', { silent: true, ignoreError: true })
        if (prettierResult === null) {
            this.results.codeQuality.push({ type: 'prettier', status: 'formatted' })
            qualityScore++
        } else {
            this.results.codeQuality.push({ type: 'prettier', status: 'needs-formatting' })
            warning('代码格式不符合 Prettier 规范')
        }

        // 代码复杂度检查（简化版）
        const complexityScore = this.checkCodeComplexity()
        if (complexityScore >= 0.8) {
            this.results.codeQuality.push({ type: 'complexity', status: 'good', score: complexityScore })
            qualityScore++
        } else {
            this.results.codeQuality.push({ type: 'complexity', status: 'high', score: complexityScore })
            warning('代码复杂度较高，建议重构')
        }

        this.score += qualityScore
        this.maxScore += maxQualityScore

        success(`代码质量检查完成 (${qualityScore}/${maxQualityScore})`)
    }

    // 检查代码复杂度（简化版）
    checkCodeComplexity() {
        const sourceFiles = this.getAllSourceFiles()
        let totalLines = 0
        let totalFunctions = 0
        let complexFunctions = 0

        sourceFiles.forEach(filePath => {
            try {
                const content = fs.readFileSync(filePath, 'utf8')
                const lines = content.split('\n')
                totalLines += lines.length

                // 简单的函数复杂度检查
                const functionMatches = content.match(/function\s+\w+|const\s+\w+\s*=\s*\(|=>\s*{/g) || []
                totalFunctions += functionMatches.length

                // 检查复杂的控制结构
                const complexityIndicators = content.match(/if\s*\(|for\s*\(|while\s*\(|switch\s*\(|catch\s*\(/g) || []
                if (complexityIndicators.length > lines.length * 0.1) {
                    complexFunctions++
                }
            } catch (e) {
                // 忽略读取错误
            }
        })

        return totalFunctions > 0 ? 1 - (complexFunctions / totalFunctions) : 1
    }

    // 获取所有源文件
    getAllSourceFiles() {
        const files = []
        const extensions = ['.ts', '.tsx', '.js', '.jsx']

        function traverse(dir) {
            if (!fs.existsSync(dir)) return

            const items = fs.readdirSync(dir, { withFileTypes: true })

            for (const item of items) {
                const fullPath = path.join(dir, item.name)

                if (item.isDirectory() && !['node_modules', 'dist', 'build', '.git'].includes(item.name)) {
                    traverse(fullPath)
                } else if (item.isFile() && extensions.some(ext => item.name.endsWith(ext))) {
                    files.push(fullPath)
                }
            }
        }

        traverse('packages')
        traverse('apps')

        return files
    }

    // 检查测试覆盖率
    checkTesting() {
        info('检查测试覆盖率...')

        let testScore = 0
        const maxTestScore = 3

        // 检查测试文件存在
        const testFiles = this.getTestFiles()
        if (testFiles.length > 0) {
            this.results.testing.push({ type: 'test-files', status: 'exists', count: testFiles.length })
            testScore++
        } else {
            this.results.testing.push({ type: 'test-files', status: 'missing' })
            warning('未找到测试文件')
        }

        // 运行测试
        const testResult = exec('npm test', { silent: true, ignoreError: true })
        if (testResult !== null) {
            this.results.testing.push({ type: 'test-execution', status: 'pass' })
            testScore++
        } else {
            this.results.testing.push({ type: 'test-execution', status: 'fail' })
            warning('测试执行失败')
        }

        // 检查覆盖率
        const coverageResult = exec('npm run test:coverage', { silent: true, ignoreError: true })
        if (coverageResult !== null) {
            this.results.testing.push({ type: 'coverage', status: 'available' })
            testScore++
        } else {
            this.results.testing.push({ type: 'coverage', status: 'unavailable' })
            warning('无法获取测试覆盖率')
        }

        this.score += testScore
        this.maxScore += maxTestScore

        success(`测试检查完成 (${testScore}/${maxTestScore})`)
    }

    // 获取测试文件
    getTestFiles() {
        const files = []
        const testPatterns = ['.test.', '.spec.']

        function traverse(dir) {
            if (!fs.existsSync(dir)) return

            const items = fs.readdirSync(dir, { withFileTypes: true })

            for (const item of items) {
                const fullPath = path.join(dir, item.name)

                if (item.isDirectory() && !['node_modules', 'dist', 'build'].includes(item.name)) {
                    traverse(fullPath)
                } else if (item.isFile() && testPatterns.some(pattern => item.name.includes(pattern))) {
                    files.push(fullPath)
                }
            }
        }

        traverse('packages')
        traverse('apps')
        traverse('tests')

        return files
    }

    // 检查文档质量
    checkDocumentation() {
        info('检查文档质量...')

        let docScore = 0
        const maxDocScore = 4

        // 检查 README
        if (fs.existsSync('README.md')) {
            const readme = fs.readFileSync('README.md', 'utf8')
            if (readme.length > 500) {
                this.results.documentation.push({ type: 'readme', status: 'comprehensive' })
                docScore++
            } else {
                this.results.documentation.push({ type: 'readme', status: 'basic' })
                warning('README.md 内容较少')
            }
        } else {
            this.results.documentation.push({ type: 'readme', status: 'missing' })
            warning('缺少 README.md')
        }

        // 检查 CHANGELOG
        if (fs.existsSync('CHANGELOG.md')) {
            this.results.documentation.push({ type: 'changelog', status: 'exists' })
            docScore++
        } else {
            this.results.documentation.push({ type: 'changelog', status: 'missing' })
            warning('缺少 CHANGELOG.md')
        }

        // 检查 API 文档
        if (fs.existsSync('docs') && fs.readdirSync('docs').length > 0) {
            this.results.documentation.push({ type: 'api-docs', status: 'exists' })
            docScore++
        } else {
            this.results.documentation.push({ type: 'api-docs', status: 'missing' })
            warning('缺少 API 文档')
        }

        // 检查代码注释
        const commentRatio = this.checkCodeComments()
        if (commentRatio >= 0.1) {
            this.results.documentation.push({ type: 'code-comments', status: 'adequate', ratio: commentRatio })
            docScore++
        } else {
            this.results.documentation.push({ type: 'code-comments', status: 'insufficient', ratio: commentRatio })
            warning('代码注释不足')
        }

        this.score += docScore
        this.maxScore += maxDocScore

        success(`文档质量检查完成 (${docScore}/${maxDocScore})`)
    }

    // 检查代码注释比例
    checkCodeComments() {
        const sourceFiles = this.getAllSourceFiles()
        let totalLines = 0
        let commentLines = 0

        sourceFiles.forEach(filePath => {
            try {
                const content = fs.readFileSync(filePath, 'utf8')
                const lines = content.split('\n')
                totalLines += lines.length

                lines.forEach(line => {
                    const trimmed = line.trim()
                    if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*')) {
                        commentLines++
                    }
                })
            } catch (e) {
                // 忽略读取错误
            }
        })

        return totalLines > 0 ? commentLines / totalLines : 0
    }

    // 生成质量报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            score: this.score,
            maxScore: this.maxScore,
            percentage: Math.round((this.score / this.maxScore) * 100),
            grade: this.getGrade(),
            results: this.results,
            recommendations: this.generateRecommendations()
        }

        const reportPath = path.join(process.cwd(), 'quality-report.json')
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

        // 生成 HTML 报告
        this.generateHtmlReport(report)

        console.log(`\n📋 质量报告已生成: ${reportPath}`)
        return report
    }

    // 获取等级
    getGrade() {
        const percentage = (this.score / this.maxScore) * 100

        if (percentage >= 90) return 'A+'
        if (percentage >= 80) return 'A'
        if (percentage >= 70) return 'B'
        if (percentage >= 60) return 'C'
        return 'D'
    }

    // 生成改进建议
    generateRecommendations() {
        const recommendations = []

        // 结构建议
        const missingStructure = this.results.structure.filter(item => item.status === 'missing')
        if (missingStructure.length > 0) {
            recommendations.push({
                category: '项目结构',
                priority: 'high',
                items: missingStructure.map(item => `补充${item.type === 'file' ? '文件' : '目录'}: ${item.name}`)
            })
        }

        // 代码质量建议
        const qualityIssues = this.results.codeQuality.filter(item => item.status !== 'pass' && item.status !== 'formatted' && item.status !== 'good')
        if (qualityIssues.length > 0) {
            recommendations.push({
                category: '代码质量',
                priority: 'high',
                items: qualityIssues.map(item => {
                    switch (item.type) {
                        case 'typescript': return '修复 TypeScript 类型错误'
                        case 'eslint': return `修复 ESLint 错误 (${item.errors || 0} 个错误, ${item.warnings || 0} 个警告)`
                        case 'prettier': return '格式化代码以符合 Prettier 规范'
                        case 'complexity': return '降低代码复杂度，考虑重构'
                        default: return `修复 ${item.type} 问题`
                    }
                })
            })
        }

        // 测试建议
        const testIssues = this.results.testing.filter(item => item.status === 'missing' || item.status === 'fail' || item.status === 'unavailable')
        if (testIssues.length > 0) {
            recommendations.push({
                category: '测试',
                priority: 'medium',
                items: testIssues.map(item => {
                    switch (item.type) {
                        case 'test-files': return '添加单元测试文件'
                        case 'test-execution': return '修复测试执行问题'
                        case 'coverage': return '配置测试覆盖率报告'
                        default: return `改进 ${item.type}`
                    }
                })
            })
        }

        // 文档建议
        const docIssues = this.results.documentation.filter(item => item.status === 'missing' || item.status === 'basic' || item.status === 'insufficient')
        if (docIssues.length > 0) {
            recommendations.push({
                category: '文档',
                priority: 'low',
                items: docIssues.map(item => {
                    switch (item.type) {
                        case 'readme': return item.status === 'missing' ? '创建 README.md' : '完善 README.md 内容'
                        case 'changelog': return '创建 CHANGELOG.md'
                        case 'api-docs': return '添加 API 文档'
                        case 'code-comments': return '增加代码注释'
                        default: return `改进 ${item.type}`
                    }
                })
            })
        }

        return recommendations
    }

    // 生成 HTML 报告
    generateHtmlReport(report) {
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目质量报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .score { font-size: 3em; font-weight: bold; color: ${this.getScoreColor(report.percentage)}; }
        .grade { font-size: 1.5em; margin-top: 10px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #2196F3; }
        .metric-label { color: #666; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-pass { color: #4caf50; }
        .status-fail { color: #f44336; }
        .status-warning { color: #ff9800; }
        .recommendation { padding: 15px; margin-bottom: 10px; border-radius: 8px; }
        .priority-high { background: #ffebee; border-left: 4px solid #f44336; }
        .priority-medium { background: #fff3e0; border-left: 4px solid #ff9800; }
        .priority-low { background: #e8f5e8; border-left: 4px solid #4caf50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 项目质量报告</h1>
            <div class="score">${report.percentage}%</div>
            <div class="grade">等级: ${report.grade}</div>
            <p>生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">${report.score}</div>
                <div class="metric-label">总得分</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.maxScore}</div>
                <div class="metric-label">满分</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.recommendations.length}</div>
                <div class="metric-label">改进建议</div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 改进建议</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation priority-${rec.priority}">
                    <h3>${rec.category}</h3>
                    <ul>
                        ${rec.items.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>
            `).join('')}
        </div>

        <div class="section">
            <h2>📋 详细结果</h2>
            ${Object.entries(report.results).map(([category, items]) => `
                <h3>${this.getCategoryName(category)}</h3>
                <ul>
                    ${items.map(item => `
                        <li class="${this.getStatusClass(item.status)}">
                            ${item.type}: ${item.status}
                            ${item.count ? ` (${item.count})` : ''}
                            ${item.errors ? ` - ${item.errors} 错误` : ''}
                            ${item.warnings ? ` - ${item.warnings} 警告` : ''}
                        </li>
                    `).join('')}
                </ul>
            `).join('')}
        </div>
    </div>
</body>
</html>
        `

        const htmlPath = path.join(process.cwd(), 'quality-report.html')
        fs.writeFileSync(htmlPath, html)
        console.log(`📋 HTML 报告已生成: ${htmlPath}`)
    }

    // 获取分数颜色
    getScoreColor(percentage) {
        if (percentage >= 80) return '#4caf50'
        if (percentage >= 60) return '#ff9800'
        return '#f44336'
    }

    // 获取分类名称
    getCategoryName(category) {
        const names = {
            structure: '项目结构',
            dependencies: '依赖关系',
            codeQuality: '代码质量',
            security: '安全性',
            performance: '性能',
            documentation: '文档',
            testing: '测试'
        }
        return names[category] || category
    }

    // 获取状态样式类
    getStatusClass(status) {
        if (['pass', 'exists', 'complete', 'clean', 'up-to-date', 'formatted', 'good', 'comprehensive', 'adequate'].includes(status)) {
            return 'status-pass'
        }
        if (['fail', 'missing', 'errors', 'vulnerabilities', 'high', 'insufficient'].includes(status)) {
            return 'status-fail'
        }
        return 'status-warning'
    }

    // 运行所有检查
    async runAllChecks() {
        log('🚀 开始项目质量检查...', 'cyan')

        this.checkProjectStructure()
        this.checkDependencies()
        this.checkCodeQuality()
        this.checkTesting()
        this.checkDocumentation()

        const report = this.generateReport()

        log(`\n🎉 质量检查完成！`, 'cyan')
        log(`总分: ${report.score}/${report.maxScore} (${report.percentage}%)`, 'green')
        log(`等级: ${report.grade}`, 'green')

        if (report.recommendations.length > 0) {
            log(`\n📝 改进建议:`, 'yellow')
            report.recommendations.forEach(rec => {
                log(`  ${rec.category}:`, 'cyan')
                rec.items.forEach(item => {
                    log(`    - ${item}`, 'yellow')
                })
            })
        }

        return report
    }
}

// 命令行工具
if (require.main === module) {
    const checker = new QualityChecker()

    const command = process.argv[2]

    switch (command) {
        case 'check':
            checker.runAllChecks()
            break
        case 'structure':
            checker.checkProjectStructure()
            break
        case 'dependencies':
            checker.checkDependencies()
            break
        case 'quality':
            checker.checkCodeQuality()
            break
        case 'test':
            checker.checkTesting()
            break
        case 'docs':
            checker.checkDocumentation()
            break
        case 'report':
            checker.generateReport()
            break
        default:
            console.log(`
使用方法: node scripts/quality-check.js <command>

命令:
  check         运行所有质量检查
  structure     检查项目结构
  dependencies  检查依赖关系
  quality       检查代码质量
  test          检查测试覆盖率
  docs          检查文档质量
  report        生成质量报告

示例:
  node scripts/quality-check.js check
  node scripts/quality-check.js structure
  node scripts/quality-check.js report
`)
    }
}

module.exports = QualityChecker
