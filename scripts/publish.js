#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 批量发布脚本
 * 按依赖顺序发布所有包到 NPM
 */

const PACKAGES_DIR = path.join(__dirname, '../packages');

// 包发布顺序 (按依赖关系排序)
const PUBLISH_ORDER = [
    'shared',      // 基础工具包，无依赖
    'core',        // 核心包，依赖 shared
    'plugins',     // 插件包，依赖 core
    'adapters',    // 适配器包，依赖 core
    'builders',    // 构建工具包，依赖 core
    'sidecar'      // 边车模式包，依赖 core
];

function execCommand(command, options = {}) {
    try {
        return execSync(command, {
            stdio: 'inherit',
            encoding: 'utf8',
            ...options
        });
    } catch (error) {
        console.error(`执行命令失败: ${command}`);
        throw error;
    }
}

function getPackageInfo(packageName) {
    const packagePath = path.join(PACKAGES_DIR, packageName, 'package.json');

    if (!fs.existsSync(packagePath)) {
        throw new Error(`包不存在: ${packageName}`);
    }

    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    return {
        name: packageJson.name,
        version: packageJson.version,
        private: packageJson.private,
        path: path.dirname(packagePath)
    };
}

function isPackagePublished(packageName, version) {
    try {
        const result = execCommand(`npm view ${packageName}@${version} version`, {
            stdio: 'pipe'
        });
        return result.trim() === version;
    } catch (error) {
        return false;
    }
}

function buildPackage(packagePath) {
    console.log(`🔨 构建包: ${path.basename(packagePath)}`);

    const buildScript = path.join(packagePath, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(buildScript, 'utf8'));

    if (packageJson.scripts && packageJson.scripts.build) {
        execCommand('pnpm run build', { cwd: packagePath });
        console.log('  ✅ 构建完成');
    } else {
        console.log('  ⏭️ 无需构建');
    }
}

function runPackageTests(packagePath) {
    console.log(`🧪 运行测试: ${path.basename(packagePath)}`);

    const packageJson = JSON.parse(fs.readFileSync(path.join(packagePath, 'package.json'), 'utf8'));

    if (packageJson.scripts && packageJson.scripts.test) {
        execCommand('pnpm run test', { cwd: packagePath });
        console.log('  ✅ 测试通过');
    } else {
        console.log('  ⏭️ 无测试脚本');
    }
}

function publishPackage(packageInfo, options = {}) {
    const { name, version, path: packagePath, private } = packageInfo;

    if (private) {
        console.log(`⏭️ 跳过私有包: ${name}`);
        return;
    }

    console.log(`📦 发布包: ${name}@${version}`);

    // 检查是否已发布
    if (isPackagePublished(name, version) && !options.force) {
        console.log(`  ⏭️ 版本 ${version} 已存在，跳过发布`);
        return;
    }

    try {
        // 构建包
        buildPackage(packagePath);

        // 运行测试
        if (!options.skipTests) {
            runPackageTests(packagePath);
        }

        // 发布到 NPM
        const publishCommand = [
            'npm publish',
            options.tag ? `--tag ${options.tag}` : '',
            options.access ? `--access ${options.access}` : '--access public',
            options.dryRun ? '--dry-run' : ''
        ].filter(Boolean).join(' ');

        execCommand(publishCommand, { cwd: packagePath });

        console.log(`  ✅ ${name}@${version} 发布成功`);

        // 等待一段时间确保包在 NPM 上可用
        if (!options.dryRun) {
            console.log('  ⏳ 等待包在 NPM 上生效...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }

    } catch (error) {
        console.error(`  ❌ ${name} 发布失败:`, error.message);
        throw error;
    }
}

function validateEnvironment() {
    // 检查 NPM 认证
    try {
        execCommand('npm whoami', { stdio: 'pipe' });
    } catch (error) {
        throw new Error('NPM 认证失败，请先运行 npm login');
    }

    // 检查工作目录
    try {
        execCommand('git diff --exit-code', { stdio: 'pipe' });
        execCommand('git diff --cached --exit-code', { stdio: 'pipe' });
    } catch (error) {
        console.warn('⚠️ 工作目录不干净，建议先提交所有变更');
    }
}

async function main() {
    const args = process.argv.slice(2);
    const options = {
        dryRun: args.includes('--dry-run'),
        force: args.includes('--force'),
        skipTests: args.includes('--skip-tests'),
        tag: args.find(arg => arg.startsWith('--tag='))?.split('=')[1],
        access: args.find(arg => arg.startsWith('--access='))?.split('=')[1] || 'public'
    };

    console.log('📦 开始批量发布流程');
    console.log('='.repeat(50));

    if (options.dryRun) {
        console.log('🔍 运行模式: 预演 (不会实际发布)');
    }

    try {
        // 验证环境
        if (!options.dryRun) {
            validateEnvironment();
        }

        // 按顺序发布包
        for (const packageName of PUBLISH_ORDER) {
            try {
                const packageInfo = getPackageInfo(packageName);
                await publishPackage(packageInfo, options);
            } catch (error) {
                console.error(`❌ 包 ${packageName} 处理失败:`, error.message);

                if (!options.force) {
                    console.error('💥 发布流程中断，使用 --force 参数忽略错误');
                    process.exit(1);
                } else {
                    console.warn('⚠️ 忽略错误，继续发布下一个包');
                }
            }
        }

        console.log('='.repeat(50));
        console.log('🎉 所有包发布完成！');

        // 显示发布摘要
        console.log('\n📋 发布摘要:');
        for (const packageName of PUBLISH_ORDER) {
            try {
                const packageInfo = getPackageInfo(packageName);
                if (!packageInfo.private) {
                    console.log(`  ✅ ${packageInfo.name}@${packageInfo.version}`);
                }
            } catch (error) {
                console.log(`  ❌ ${packageName}: 获取信息失败`);
            }
        }

        console.log('\n🔗 相关链接:');
        console.log('  NPM: https://www.npmjs.com/org/micro-core');
        console.log('  文档: https://micro-core.dev');
        console.log('  GitHub: https://github.com/your-org/micro-core');

    } catch (error) {
        console.error('❌ 发布过程中出现错误:', error.message);
        process.exit(1);
    }
}

// 添加 Promise 支持
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ 未处理的错误:', error);
        process.exit(1);
    });
}

module.exports = {
    publishPackage,
    getPackageInfo,
    PUBLISH_ORDER
};