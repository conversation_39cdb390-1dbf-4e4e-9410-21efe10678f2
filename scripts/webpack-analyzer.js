#!/usr/bin/env node

/**
 * Webpack 打包分析工具 - 分析打包结果并提供优化建议
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class WebpackAnalyzer {
    constructor() {
        this.analysisResults = {}
        this.recommendations = []
    }

    // 分析打包结果
    async analyzeBundle(appPath) {
        const appName = path.basename(appPath)
        console.log(`🔍 分析应用: ${appName}`)

        const distPath = path.join(appPath, 'dist')
        if (!fs.existsSync(distPath)) {
            console.warn(`⚠️  ${appName} 的 dist 目录不存在，请先构建应用`)
            return null
        }

        const analysis = {
            appName,
            totalSize: 0,
            files: [],
            chunks: {},
            assets: {},
            dependencies: {}
        }

        // 分析文件大小
        this.analyzeFileSize(distPath, analysis)

        // 分析依赖关系
        this.analyzeDependencies(appPath, analysis)

        // 生成优化建议
        this.generateOptimizationSuggestions(analysis)

        this.analysisResults[appName] = analysis
        return analysis
    }

    // 分析文件大小
    analyzeFileSize(distPath, analysis) {
        const files = this.getAllFiles(distPath)

        files.forEach(filePath => {
            const relativePath = path.relative(distPath, filePath)
            const stats = fs.statSync(filePath)
            const size = stats.size

            analysis.totalSize += size
            analysis.files.push({
                path: relativePath,
                size,
                sizeFormatted: this.formatSize(size)
            })

            // 分类文件
            const ext = path.extname(filePath).toLowerCase()
            if (ext === '.js') {
                analysis.chunks[relativePath] = size
            } else if (['.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2'].includes(ext)) {
                analysis.assets[relativePath] = size
            }
        })

        // 排序文件（按大小降序）
        analysis.files.sort((a, b) => b.size - a.size)
    }

    // 分析依赖关系
    analyzeDependencies(appPath, analysis) {
        const packageJsonPath = path.join(appPath, 'package.json')
        if (!fs.existsSync(packageJsonPath)) {
            return
        }

        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
        const dependencies = {
            ...packageJson.dependencies,
            ...packageJson.devDependencies
        }

        // 分析依赖大小（简化版本）
        for (const [name, version] of Object.entries(dependencies)) {
            const nodeModulesPath = path.join(appPath, 'node_modules', name)
            if (fs.existsSync(nodeModulesPath)) {
                const size = this.getDirectorySize(nodeModulesPath)
                analysis.dependencies[name] = {
                    version,
                    size,
                    sizeFormatted: this.formatSize(size)
                }
            }
        }
    }

    // 生成优化建议
    generateOptimizationSuggestions(analysis) {
        const suggestions = []

        // 检查总体积
        if (analysis.totalSize > 1024 * 1024) { // 1MB
            suggestions.push({
                type: 'size',
                priority: 'high',
                message: `总打包体积过大 (${this.formatSize(analysis.totalSize)})`,
                solutions: [
                    '启用代码分割 (Code Splitting)',
                    '使用动态导入 (Dynamic Imports)',
                    '移除未使用的代码 (Tree Shaking)',
                    '压缩图片和静态资源'
                ]
            })
        }

        // 检查大文件
        const largeFiles = analysis.files.filter(file => file.size > 200 * 1024) // 200KB
        if (largeFiles.length > 0) {
            suggestions.push({
                type: 'large-files',
                priority: 'medium',
                message: `发现 ${largeFiles.length} 个大文件`,
                files: largeFiles.map(f => `${f.path} (${f.sizeFormatted})`),
                solutions: [
                    '对大文件进行代码分割',
                    '使用懒加载',
                    '压缩和优化资源'
                ]
            })
        }

        // 检查重复依赖
        const heavyDependencies = Object.entries(analysis.dependencies)
            .filter(([name, info]) => info.size > 100 * 1024) // 100KB
            .sort((a, b) => b[1].size - a[1].size)

        if (heavyDependencies.length > 0) {
            suggestions.push({
                type: 'dependencies',
                priority: 'medium',
                message: `发现 ${heavyDependencies.length} 个大依赖`,
                dependencies: heavyDependencies.slice(0, 5).map(([name, info]) =>
                    `${name} (${info.sizeFormatted})`
                ),
                solutions: [
                    '考虑使用更轻量的替代方案',
                    '按需导入依赖的部分功能',
                    '使用 externals 配置排除大依赖',
                    '升级到更新版本的依赖'
                ]
            })
        }

        // 检查 CSS 文件
        const cssFiles = analysis.files.filter(file => file.path.endsWith('.css'))
        const totalCssSize = cssFiles.reduce((sum, file) => sum + file.size, 0)
        if (totalCssSize > 50 * 1024) { // 50KB
            suggestions.push({
                type: 'css',
                priority: 'low',
                message: `CSS 文件总大小: ${this.formatSize(totalCssSize)}`,
                solutions: [
                    '移除未使用的 CSS 规则',
                    '使用 CSS 压缩',
                    '考虑使用 CSS-in-JS 方案',
                    '启用 CSS 代码分割'
                ]
            })
        }

        analysis.suggestions = suggestions
        this.recommendations.push(...suggestions)
    }

    // 获取目录下所有文件
    getAllFiles(dirPath) {
        const files = []

        function traverse(currentPath) {
            const items = fs.readdirSync(currentPath, { withFileTypes: true })

            for (const item of items) {
                const fullPath = path.join(currentPath, item.name)

                if (item.isDirectory()) {
                    traverse(fullPath)
                } else {
                    files.push(fullPath)
                }
            }
        }

        traverse(dirPath)
        return files
    }

    // 获取目录大小
    getDirectorySize(dirPath) {
        let totalSize = 0

        try {
            const files = this.getAllFiles(dirPath)
            files.forEach(filePath => {
                const stats = fs.statSync(filePath)
                totalSize += stats.size
            })
        } catch (error) {
            // 忽略权限错误等
        }

        return totalSize
    }

    // 格式化文件大小
    formatSize(bytes) {
        if (bytes === 0) return '0 B'

        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 分析所有应用
    async analyzeAllApps() {
        console.log('🚀 开始分析所有应用...')

        const appsDir = path.join(process.cwd(), 'apps')
        if (!fs.existsSync(appsDir)) {
            console.error('❌ apps 目录不存在')
            return
        }

        const apps = fs.readdirSync(appsDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name)

        for (const app of apps) {
            const appPath = path.join(appsDir, app)
            await this.analyzeBundle(appPath)
        }

        this.generateReport()
        console.log('\n✅ 分析完成')
    }

    // 生成分析报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.generateSummary(),
            applications: this.analysisResults,
            recommendations: this.recommendations
        }

        const reportPath = path.join(process.cwd(), 'webpack-analysis-report.json')
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

        // 生成 HTML 报告
        this.generateHtmlReport(report)

        console.log(`📋 分析报告已生成: ${reportPath}`)
        return report
    }

    // 生成摘要
    generateSummary() {
        const apps = Object.values(this.analysisResults)

        return {
            totalApps: apps.length,
            totalSize: apps.reduce((sum, app) => sum + app.totalSize, 0),
            averageSize: apps.length > 0 ? apps.reduce((sum, app) => sum + app.totalSize, 0) / apps.length : 0,
            largestApp: apps.reduce((largest, app) =>
                app.totalSize > (largest?.totalSize || 0) ? app : largest, null
            ),
            totalRecommendations: this.recommendations.length,
            highPriorityIssues: this.recommendations.filter(r => r.priority === 'high').length
        }
    }

    // 生成 HTML 报告
    generateHtmlReport(report) {
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webpack 分析报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #2196F3; }
        .metric-label { color: #666; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .app-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
        .file-list { max-height: 300px; overflow-y: auto; }
        .file-item { display: flex; justify-content: between; padding: 5px 0; border-bottom: 1px solid #eee; }
        .recommendation { padding: 15px; margin-bottom: 10px; border-radius: 8px; }
        .priority-high { background: #ffebee; border-left: 4px solid #f44336; }
        .priority-medium { background: #fff3e0; border-left: 4px solid #ff9800; }
        .priority-low { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .solutions { margin-top: 10px; }
        .solutions li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Webpack 分析报告</h1>
            <p>生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">${report.summary.totalApps}</div>
                <div class="metric-label">应用总数</div>
            </div>
            <div class="metric">
                <div class="metric-value">${this.formatSize(report.summary.totalSize)}</div>
                <div class="metric-label">总打包大小</div>
            </div>
            <div class="metric">
                <div class="metric-value">${this.formatSize(report.summary.averageSize)}</div>
                <div class="metric-label">平均大小</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.totalRecommendations}</div>
                <div class="metric-label">优化建议</div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 优化建议</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation priority-${rec.priority}">
                    <h3>${rec.message}</h3>
                    <div class="solutions">
                        <strong>解决方案:</strong>
                        <ul>
                            ${rec.solutions.map(solution => `<li>${solution}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="section">
            <h2>📱 应用详情</h2>
            ${Object.values(report.applications).map(app => `
                <div class="app-card">
                    <h3>${app.appName}</h3>
                    <p><strong>总大小:</strong> ${this.formatSize(app.totalSize)}</p>
                    <p><strong>文件数量:</strong> ${app.files.length}</p>
                    
                    <h4>最大文件 (前5个):</h4>
                    <div class="file-list">
                        ${app.files.slice(0, 5).map(file => `
                            <div class="file-item">
                                <span>${file.path}</span>
                                <span>${file.sizeFormatted}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>
        `

        const htmlPath = path.join(process.cwd(), 'webpack-analysis-report.html')
        fs.writeFileSync(htmlPath, html)
        console.log(`📋 HTML 报告已生成: ${htmlPath}`)
    }
}

// 命令行工具
if (require.main === module) {
    const analyzer = new WebpackAnalyzer()

    const command = process.argv[2]
    const appPath = process.argv[3]

    switch (command) {
        case 'analyze':
            if (appPath) {
                analyzer.analyzeBundle(appPath)
            } else {
                analyzer.analyzeAllApps()
            }
            break
        case 'report':
            analyzer.generateReport()
            break
        default:
            console.log(`
使用方法: node scripts/webpack-analyzer.js <command> [app-path]

命令:
  analyze [app-path]    分析指定应用或所有应用
  report               生成分析报告

示例:
  node scripts/webpack-analyzer.js analyze
  node scripts/webpack-analyzer.js analyze ./apps/main-app
  node scripts/webpack-analyzer.js report
`)
    }
}

module.exports = WebpackAnalyzer