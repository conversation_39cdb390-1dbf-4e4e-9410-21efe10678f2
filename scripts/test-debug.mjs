#!/usr/bin/env node

/**
 * 测试调试脚本
 * 用于诊断测试运行问题
 */

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔍 开始测试环境诊断...\n');

// 1. 检查 Node.js 版本
console.log('📋 环境信息:');
console.log(`Node.js: ${process.version}`);
console.log(`平台: ${process.platform}`);
console.log(`架构: ${process.arch}\n`);

// 2. 检查包管理器
try {
    const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
    console.log(`pnpm: ${pnpmVersion}`);
} catch (error) {
    console.log('❌ pnpm 未安装或不可用');
}

// 3. 检查项目结构
console.log('\n📁 项目结构检查:');
const requiredDirs = [
    'packages/core',
    'packages/shared',
    'packages/adapters',
    'packages/plugins',
    'packages/builders',
    'apps',
    'docs'
];

requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`✅ ${dir}`);
    } else {
        console.log(`❌ ${dir} (缺失)`);
    }
});

// 4. 检查测试文件
console.log('\n🧪 测试文件检查:');
const testFiles = [
    'packages/core/__tests__/kernel.test.ts',
    'packages/core/__tests__/app-registry.test.ts',
    'packages/core/__tests__/lifecycle-manager.test.ts',
    'packages/core/__tests__/plugin-system.test.ts',
    'packages/shared/__tests__/utils.test.ts',
    'packages/adapters/__tests__/base-adapter.test.ts',
    'packages/plugins/__tests__/router-plugin.test.ts'
];

testFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} (缺失)`);
    }
});

// 5. 检查依赖
console.log('\n📦 依赖检查:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const devDeps = packageJson.devDependencies || {};

    const requiredDeps = ['vitest', 'typescript', '@types/node'];
    requiredDeps.forEach(dep => {
        if (devDeps[dep]) {
            console.log(`✅ ${dep}: ${devDeps[dep]}`);
        } else {
            console.log(`❌ ${dep} (缺失)`);
        }
    });
} catch (error) {
    console.log('❌ 无法读取 package.json');
}

// 6. 检查配置文件
console.log('\n⚙️ 配置文件检查:');
const configFiles = [
    'vitest.config.ts',
    'tsconfig.json',
    'pnpm-workspace.yaml',
    'turbo.json'
];

configFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} (缺失)`);
    }
});

// 7. 检查playground应用的依赖问题
console.log('\n🎮 Playground应用检查:');
const playgroundConfig = 'apps/playground/vite.config.ts';
const playgroundPackage = 'apps/playground/package.json';

if (fs.existsSync(playgroundConfig)) {
    console.log(`✅ ${playgroundConfig}`);
} else {
    console.log(`❌ ${playgroundConfig} (缺失)`);
}

if (fs.existsSync(playgroundPackage)) {
    try {
        const pkg = JSON.parse(fs.readFileSync(playgroundPackage, 'utf8'));
        const deps = { ...pkg.dependencies, ...pkg.devDependencies };

        if (deps['@vitejs/plugin-react']) {
            console.log(`✅ @vitejs/plugin-react: ${deps['@vitejs/plugin-react']}`);
        } else {
            console.log(`❌ @vitejs/plugin-react (缺失) - 这是测试失败的原因`);
        }
    } catch (error) {
        console.log(`❌ 无法读取 ${playgroundPackage}`);
    }
} else {
    console.log(`❌ ${playgroundPackage} (缺失)`);
}

console.log('\n✨ 诊断完成!');
console.log('\n💡 建议修复步骤:');
console.log('1. cd apps/playground && pnpm add @vitejs/plugin-react -D');
console.log('2. 或者从测试中排除 playground 应用');