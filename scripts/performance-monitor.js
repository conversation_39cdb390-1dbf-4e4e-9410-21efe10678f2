#!/usr/bin/env node

/**
 * 性能监控脚本 - 监控微前端应用的性能指标
 */

const fs = require('fs')
const path = require('path')
const { performance } = require('perf_hooks')

// 性能指标收集器
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: {},
            bundleSize: {},
            memoryUsage: {},
            renderTime: {},
            networkRequests: []
        }
        this.thresholds = {
            loadTime: 3000, // 3秒
            bundleSize: 500 * 1024, // 500KB
            memoryUsage: 50 * 1024 * 1024, // 50MB
            renderTime: 100 // 100ms
        }
    }

    // 监控应用加载时间
    measureLoadTime(appName, startTime, endTime) {
        const loadTime = endTime - startTime
        this.metrics.loadTime[appName] = loadTime

        console.log(`📊 ${appName} 加载时间: ${loadTime}ms`)

        if (loadTime > this.thresholds.loadTime) {
            console.warn(`⚠️  ${appName} 加载时间超过阈值 (${this.thresholds.loadTime}ms)`)
        }

        return loadTime
    }

    // 监控打包大小
    measureBundleSize(appName, bundlePath) {
        if (!fs.existsSync(bundlePath)) {
            console.error(`❌ 打包文件不存在: ${bundlePath}`)
            return 0
        }

        const stats = fs.statSync(bundlePath)
        const size = stats.size
        this.metrics.bundleSize[appName] = size

        console.log(`📦 ${appName} 打包大小: ${(size / 1024).toFixed(2)}KB`)

        if (size > this.thresholds.bundleSize) {
            console.warn(`⚠️  ${appName} 打包大小超过阈值 (${(this.thresholds.bundleSize / 1024).toFixed(2)}KB)`)
        }

        return size
    }

    // 监控内存使用
    measureMemoryUsage(appName) {
        const memUsage = process.memoryUsage()
        this.metrics.memoryUsage[appName] = memUsage

        console.log(`🧠 ${appName} 内存使用:`)
        console.log(`   RSS: ${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`)
        console.log(`   Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`)
        console.log(`   Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`)

        if (memUsage.heapUsed > this.thresholds.memoryUsage) {
            console.warn(`⚠️  ${appName} 内存使用超过阈值 (${(this.thresholds.memoryUsage / 1024 / 1024).toFixed(2)}MB)`)
        }

        return memUsage
    }

    // 监控渲染时间
    measureRenderTime(appName, renderStart, renderEnd) {
        const renderTime = renderEnd - renderStart
        this.metrics.renderTime[appName] = renderTime

        console.log(`🎨 ${appName} 渲染时间: ${renderTime}ms`)

        if (renderTime > this.thresholds.renderTime) {
            console.warn(`⚠️  ${appName} 渲染时间超过阈值 (${this.thresholds.renderTime}ms)`)
        }

        return renderTime
    }

    // 记录网络请求
    recordNetworkRequest(url, method, duration, size) {
        const request = {
            url,
            method,
            duration,
            size,
            timestamp: Date.now()
        }

        this.metrics.networkRequests.push(request)
        console.log(`🌐 网络请求: ${method} ${url} (${duration}ms, ${size}B)`)
    }

    // 生成性能报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.generateSummary(),
            details: this.metrics,
            recommendations: this.generateRecommendations()
        }

        const reportPath = path.join(process.cwd(), 'performance-report.json')
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

        console.log(`📋 性能报告已生成: ${reportPath}`)
        return report
    }

    // 生成摘要
    generateSummary() {
        const summary = {
            totalApps: Object.keys(this.metrics.loadTime).length,
            averageLoadTime: 0,
            totalBundleSize: 0,
            slowestApp: null,
            largestApp: null
        }

        // 计算平均加载时间
        const loadTimes = Object.values(this.metrics.loadTime)
        if (loadTimes.length > 0) {
            summary.averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length
        }

        // 计算总打包大小
        summary.totalBundleSize = Object.values(this.metrics.bundleSize).reduce((a, b) => a + b, 0)

        // 找出最慢的应用
        let maxLoadTime = 0
        for (const [app, time] of Object.entries(this.metrics.loadTime)) {
            if (time > maxLoadTime) {
                maxLoadTime = time
                summary.slowestApp = { name: app, loadTime: time }
            }
        }

        // 找出最大的应用
        let maxBundleSize = 0
        for (const [app, size] of Object.entries(this.metrics.bundleSize)) {
            if (size > maxBundleSize) {
                maxBundleSize = size
                summary.largestApp = { name: app, bundleSize: size }
            }
        }

        return summary
    }

    // 生成优化建议
    generateRecommendations() {
        const recommendations = []

        // 检查加载时间
        for (const [app, time] of Object.entries(this.metrics.loadTime)) {
            if (time > this.thresholds.loadTime) {
                recommendations.push({
                    type: 'performance',
                    app,
                    issue: '加载时间过长',
                    current: `${time}ms`,
                    threshold: `${this.thresholds.loadTime}ms`,
                    suggestions: [
                        '启用代码分割和懒加载',
                        '优化打包配置',
                        '使用CDN加速静态资源',
                        '启用HTTP/2和压缩'
                    ]
                })
            }
        }

        // 检查打包大小
        for (const [app, size] of Object.entries(this.metrics.bundleSize)) {
            if (size > this.thresholds.bundleSize) {
                recommendations.push({
                    type: 'bundle',
                    app,
                    issue: '打包体积过大',
                    current: `${(size / 1024).toFixed(2)}KB`,
                    threshold: `${(this.thresholds.bundleSize / 1024).toFixed(2)}KB`,
                    suggestions: [
                        '移除未使用的依赖',
                        '启用Tree Shaking',
                        '使用动态导入',
                        '优化图片和静态资源'
                    ]
                })
            }
        }

        // 检查内存使用
        for (const [app, usage] of Object.entries(this.metrics.memoryUsage)) {
            if (usage.heapUsed > this.thresholds.memoryUsage) {
                recommendations.push({
                    type: 'memory',
                    app,
                    issue: '内存使用过高',
                    current: `${(usage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
                    threshold: `${(this.thresholds.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
                    suggestions: [
                        '检查内存泄漏',
                        '优化数据结构',
                        '及时清理事件监听器',
                        '使用对象池模式'
                    ]
                })
            }
        }

        return recommendations
    }

    // 监控所有应用
    async monitorAllApps() {
        console.log('🚀 开始性能监控...')

        const appsDir = path.join(process.cwd(), 'apps')
        if (!fs.existsSync(appsDir)) {
            console.error('❌ apps 目录不存在')
            return
        }

        const apps = fs.readdirSync(appsDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name)

        for (const app of apps) {
            console.log(`\n📱 监控应用: ${app}`)

            // 模拟加载时间监控
            const loadStart = performance.now()
            await this.simulateAppLoad(app)
            const loadEnd = performance.now()

            this.measureLoadTime(app, loadStart, loadEnd)

            // 监控打包大小
            const distPath = path.join(appsDir, app, 'dist')
            if (fs.existsSync(distPath)) {
                const files = fs.readdirSync(distPath, { recursive: true })
                const jsFiles = files.filter(file => file.endsWith('.js'))

                for (const file of jsFiles) {
                    const filePath = path.join(distPath, file)
                    if (fs.statSync(filePath).isFile()) {
                        this.measureBundleSize(`${app}/${file}`, filePath)
                    }
                }
            }

            // 监控内存使用
            this.measureMemoryUsage(app)
        }

        // 生成报告
        this.generateReport()
        console.log('\n✅ 性能监控完成')
    }

    // 模拟应用加载
    async simulateAppLoad(appName) {
        // 模拟异步加载过程
        const loadTime = Math.random() * 2000 + 500 // 500-2500ms
        await new Promise(resolve => setTimeout(resolve, loadTime))
    }
}

// 命令行工具
if (require.main === module) {
    const monitor = new PerformanceMonitor()

    const command = process.argv[2]

    switch (command) {
        case 'monitor':
            monitor.monitorAllApps()
            break
        case 'report':
            monitor.generateReport()
            break
        default:
            console.log(`
使用方法: node scripts/performance-monitor.js <command>

命令:
  monitor    监控所有应用性能
  report     生成性能报告

示例:
  node scripts/performance-monitor.js monitor
  node scripts/performance-monitor.js report
`)
    }
}

module.exports = PerformanceMonitor