import { resolve } from 'path'
import { defineConfig } from 'vitest/config'

export default defineConfig({
    test: {
        // 测试环境
        environment: 'jsdom',

        // 全局设置
        globals: true,

        // 设置文件
        setupFiles: ['./tests/setup.ts'],

        // 包含的测试文件
        include: [
            'packages/**/*.{test,spec}.{js,ts,tsx}',
            'apps/**/*.{test,spec}.{js,ts,tsx}',
            'tests/**/*.{test,spec}.{js,ts,tsx}'
        ],

        // 排除的文件
        exclude: [
            'node_modules',
            'dist',
            'build',
            '.nuxt',
            '.next',
            '.vitepress'
        ],

        // 覆盖率配置
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html', 'lcov'],
            reportsDirectory: './coverage',
            exclude: [
                'node_modules/',
                'tests/',
                '**/*.d.ts',
                '**/*.config.*',
                '**/dist/**',
                '**/build/**',
                '**/*.test.*',
                '**/*.spec.*'
            ],
            thresholds: {
                global: {
                    branches: 80,
                    functions: 80,
                    lines: 80,
                    statements: 80
                }
            }
        },

        // 测试超时
        testTimeout: 10000,

        // 钩子超时
        hookTimeout: 10000,

        // 最大并发数
        maxConcurrency: 5,

        // 监听模式配置
        watchExclude: ['node_modules/**', 'dist/**', 'build/**'],

        // 报告器
        reporters: ['verbose'],

        // 输出文件
        outputFile: {
            json: './test-results/results.json'
        }
    },

    // 解析配置
    resolve: {
        alias: {
            '@': resolve(__dirname, './src'),
            '@micro-core/core': resolve(__dirname, './packages/core/src'),
            '@micro-core/shared': resolve(__dirname, './packages/shared/src'),
            '@micro-core/plugin-router': resolve(__dirname, './packages/plugins/plugin-router/src'),
            '@micro-core/plugin-communication': resolve(__dirname, './packages/plugins/plugin-communication/src'),
            '@micro-core/plugin-auth': resolve(__dirname, './packages/plugins/plugin-auth/src'),
            '@micro-core/adapter-react': resolve(__dirname, './packages/adapters/adapter-react/src'),
            '@micro-core/adapter-vue': resolve(__dirname, './packages/adapters/adapter-vue/src'),
            '@micro-core/adapter-angular': resolve(__dirname, './packages/adapters/adapter-angular/src'),
            '@micro-core/builder-webpack': resolve(__dirname, './packages/builders/builder-webpack/src'),
            '@micro-core/builder-vite': resolve(__dirname, './packages/builders/builder-vite/src'),
            '@micro-core/builder-rollup': resolve(__dirname, './packages/builders/builder-rollup/src')
        }
    },

    // 定义全局变量
    define: {
        __DEV__: true,
        __TEST__: true,
        __VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0')
    }
})