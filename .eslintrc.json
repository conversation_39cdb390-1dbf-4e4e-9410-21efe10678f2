{"root": true, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn"}, "overrides": [{"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}, {"files": ["scripts/**/*", "*.config.*"], "rules": {"no-console": "off", "@typescript-eslint/no-var-requires": "off"}}], "ignorePatterns": ["dist", "node_modules", "coverage", "*.d.ts"]}