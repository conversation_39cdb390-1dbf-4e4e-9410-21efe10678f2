{
  "compilerOptions": {
    // 基础配置
    "target": "ES2020",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "allowJs": true,
    "checkJs": false,
    // 严格模式
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    // 模块系统
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    // 输出配置
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    // 路径映射
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@micro-core/core": [
        "./packages/core/src"
      ],
      "@micro-core/shared": [
        "./packages/shared/src"
      ],
      "@micro-core/plugin-*": [
        "./packages/plugins/plugin-*/src"
      ],
      "@micro-core/adapter-*": [
        "./packages/adapters/adapter-*/src"
      ],
      "@micro-core/builder-*": [
        "./packages/builders/builder-*/src"
      ]
    },
    // JSX 配置
    "jsx": "react-jsx",
    "jsxImportSource": "react",
    // 实验性功能
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    // 跳过库检查
    "skipLibCheck": true
  },
  "include": [
    "packages/*/src/**/*",
    "apps/*/src/**/*",
    "scripts/**/*",
    "*.config.*",
    "*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "coverage",
    "**/*.min.js",
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**"
  ],
  "references": [
    {
      "path": "./packages/core"
    },
    {
      "path": "./packages/shared"
    },
    {
      "path": "./packages/plugins/plugin-router"
    },
    {
      "path": "./packages/adapters/adapter-react"
    },
    {
      "path": "./packages/adapters/adapter-angular"
    }
  ]
}