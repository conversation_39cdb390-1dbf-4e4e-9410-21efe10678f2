{"name": "@micro-core/docs", "version": "1.0.0", "description": "micro-core微前端架构文档系统", "type": "module", "scripts": {"dev": "vitepress dev", "build": "vitepress build", "preview": "vitepress preview", "serve": "vitepress serve", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts,.vue,.js", "lint:fix": "eslint . --ext .ts,.vue,.js --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vite": "^7.0.4"}, "devDependencies": {"vitepress": "2.0.0-alpha.8", "@types/node": "^20.0.0", "typescript": "^5.3.0", "vitest": "^3.2.4", "vue-tsc": "^2.0.0", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint-plugin-vue": "^9.20.0", "@vitejs/plugin-vue": "^5.0.0", "sass": "^1.70.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "keywords": ["micro-frontend", "微前端", "documentation", "vitepress", "qiankun", "wujie", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}