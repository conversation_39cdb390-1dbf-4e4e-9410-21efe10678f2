# 基础概念

本章节将介绍 Micro-Core 微前端架构的核心概念，帮助您更好地理解和使用这个框架。

## 什么是微前端

微前端是一种将前端应用分解为更小、更简单的能够独立开发、测试、部署的微应用的架构风格。它将微服务的理念扩展到前端开发中。

### 微前端的优势

- **技术栈无关**: 不同的微应用可以使用不同的技术栈
- **独立开发**: 团队可以独立开发和部署各自的微应用
- **增量升级**: 可以逐步升级或重写应用的某些部分
- **团队自治**: 不同团队可以独立工作，减少协调成本

## Micro-Core 核心概念

### 1. 微应用 (Micro Application)

微应用是微前端架构中的基本单元，每个微应用都是一个独立的前端应用。

```typescript
interface MicroApp {
  // 应用唯一标识
  name: string
  
  // 应用入口地址
  entry: string
  
  // 挂载容器
  container: string | HTMLElement
  
  // 激活条件
  activeWhen: string | ((location: Location) => boolean)
  
  // 应用属性
  props?: Record<string, any>
  
  // 生命周期钩子
  loader?: () => Promise<LifecycleFns>
}
```

#### 微应用生命周期

每个微应用都有完整的生命周期：

```typescript
interface LifecycleFns {
  // 启动阶段 - 应用初始化
  bootstrap?: (props: any) => Promise<void>
  
  // 挂载阶段 - 应用渲染到DOM
  mount?: (props: any) => Promise<void>
  
  // 卸载阶段 - 应用从DOM中移除
  unmount?: (props: any) => Promise<void>
  
  // 更新阶段 - 应用属性更新
  update?: (props: any) => Promise<void>
}
```

### 2. 主应用 (Main Application)

主应用是微前端架构的容器和协调者，负责：

- 注册和管理微应用
- 处理路由分发
- 提供应用间通信机制
- 管理全局状态和资源

```typescript
import { MicroCoreKernel } from '@micro-core/core'

const kernel = new MicroCoreKernel({
  debug: true
})

// 注册微应用
kernel.registerApplication({
  name: 'user-center',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user'
})

// 启动主应用
kernel.start()
```

### 3. 应用容器 (Application Container)

应用容器是微应用的挂载点，可以是：

- **DOM 选择器**: `'#app'`, `'.container'`
- **DOM 元素**: `document.getElementById('app')`
- **动态容器**: 根据路由动态创建

```typescript
// 静态容器
container: '#react-app'

// 动态容器
container: () => {
  const container = document.createElement('div')
  container.id = 'dynamic-app'
  document.body.appendChild(container)
  return container
}
```

### 4. 路由系统

Micro-Core 提供了灵活的路由系统来控制微应用的激活和切换。

#### 路由匹配规则

```typescript
// 字符串匹配
activeWhen: '/user'

// 正则表达式匹配
activeWhen: /^\/user/

// 函数匹配
activeWhen: (location) => {
  return location.pathname.startsWith('/user')
}

// 数组匹配（多个条件）
activeWhen: ['/user', '/profile', /^\/settings/]
```

#### 路由模式

```typescript
// Hash 模式
router: {
  mode: 'hash',
  base: '/'
}

// History 模式
router: {
  mode: 'history',
  base: '/app/'
}

// Memory 模式（用于测试）
router: {
  mode: 'memory',
  initialEntries: ['/']
}
```

### 5. 应用间通信

Micro-Core 提供多种应用间通信方式：

#### 事件总线 (Event Bus)

```typescript
import { eventBus } from '@micro-core/core'

// 发送事件
eventBus.emit('user-login', { userId: 123 })

// 监听事件
eventBus.on('user-login', (data) => {
  console.log('用户登录:', data.userId)
})
```

#### 全局状态 (Global State)

```typescript
import { globalState } from '@micro-core/core'

// 设置全局状态
globalState.set('user', { name: '张三', role: 'admin' })

// 获取全局状态
const user = globalState.get('user')

// 监听状态变化
globalState.watch('user', (newUser, oldUser) => {
  console.log('用户信息更新:', newUser)
})
```

#### Props 传递

```typescript
// 主应用向微应用传递 props
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  props: {
    theme: 'dark',
    apiBaseUrl: 'https://api.example.com'
  }
})

// 微应用接收 props
export async function mount(props) {
  const { theme, apiBaseUrl } = props
  // 使用传递的属性
}
```

### 6. 插件系统

Micro-Core 采用插件化架构，核心功能通过插件提供：

```typescript
import { MicroCoreKernel } from '@micro-core/core'
import { RouterPlugin } from '@micro-core/plugin-router'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

const kernel = new MicroCoreKernel()

// 注册插件
kernel.use(RouterPlugin, {
  mode: 'history'
})

kernel.use(CommunicationPlugin, {
  enableGlobalState: true
})
```

#### 自定义插件

```typescript
import { MicroCorePlugin } from '@micro-core/core'

class CustomPlugin implements MicroCorePlugin {
  name = 'custom-plugin'
  version = '1.0.0'
  
  async install(kernel) {
    // 插件安装逻辑
    kernel.on('app-mount', this.handleAppMount)
  }
  
  async uninstall(kernel) {
    // 插件卸载逻辑
    kernel.off('app-mount', this.handleAppMount)
  }
  
  private handleAppMount = (app) => {
    console.log(`应用 ${app.name} 已挂载`)
  }
}
```

### 7. 沙箱隔离

为了确保微应用之间的隔离，Micro-Core 提供了多种沙箱机制：

#### JavaScript 沙箱

```typescript
// 快照沙箱 - 适用于单个微应用
sandbox: {
  type: 'snapshot',
  strictGlobal: true
}

// 代理沙箱 - 适用于多个微应用
sandbox: {
  type: 'proxy',
  multiMode: true
}
```

#### CSS 沙箱

```typescript
// 样式隔离
sandbox: {
  css: {
    // 严格样式隔离
    strictStyleIsolation: true,
    
    // 实验性样式隔离
    experimentalStyleIsolation: true,
    
    // 自定义样式前缀
    prefix: 'micro-app'
  }
}
```

### 8. 资源加载

Micro-Core 支持多种资源加载方式：

#### 静态资源

```typescript
// HTML 入口
entry: 'http://localhost:3001/index.html'

// JavaScript 入口
entry: 'http://localhost:3001/main.js'

// 多入口配置
entry: {
  scripts: ['http://localhost:3001/main.js'],
  styles: ['http://localhost:3001/main.css']
}
```

#### 动态导入

```typescript
// 动态导入微应用
const app = await import('http://localhost:3001/main.js')

// 懒加载
loader: () => import('./lazy-app')
```

### 9. 错误处理

Micro-Core 提供了完善的错误处理机制：

```typescript
// 全局错误处理
kernel.onError((error, app) => {
  console.error(`应用 ${app.name} 发生错误:`, error)
  
  // 错误上报
  reportError(error, app)
  
  // 降级处理
  showErrorFallback(app)
})

// 应用级错误边界
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  errorBoundary: {
    fallback: '<div>应用加载失败，请刷新重试</div>',
    onError: (error) => {
      console.error('用户应用错误:', error)
    }
  }
})
```

### 10. 性能优化

#### 预加载

```typescript
// 预加载微应用
kernel.preloadApp('user-app')

// 预取资源
kernel.prefetchApp('order-app')
```

#### 缓存策略

```typescript
// 资源缓存
cache: {
  // 启用缓存
  enabled: true,
  
  // 缓存策略
  strategy: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
  
  // 缓存时间
  maxAge: 1000 * 60 * 30 // 30分钟
}
```

## 架构模式

### 1. 基座模式 (Shell Pattern)

主应用作为基座，提供公共功能和基础设施：

```typescript
// 主应用基座
const shell = new MicroCoreKernel({
  // 公共配置
  theme: 'default',
  locale: 'zh-CN',
  
  // 共享依赖
  shared: {
    react: '^18.0.0',
    'react-dom': '^18.0.0'
  }
})
```

### 2. 自组织模式 (Self-Organizing Pattern)

微应用自主注册和管理：

```typescript
// 微应用自注册
if (window.__MICRO_CORE__) {
  window.__MICRO_CORE__.registerApp({
    name: 'self-app',
    mount: () => { /* 挂载逻辑 */ },
    unmount: () => { /* 卸载逻辑 */ }
  })
}
```

### 3. 联邦模式 (Federation Pattern)

使用 Module Federation 实现微应用联邦：

```typescript
// webpack.config.js
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'shell',
      remotes: {
        userApp: 'user_app@http://localhost:3001/remoteEntry.js',
        orderApp: 'order_app@http://localhost:3002/remoteEntry.js'
      }
    })
  ]
}
```

## 最佳实践

1. **单一职责**: 每个微应用应该有明确的业务边界
2. **技术栈统一**: 在可能的情况下，保持技术栈的一致性
3. **依赖管理**: 合理管理共享依赖，避免重复加载
4. **通信规范**: 建立清晰的应用间通信协议
5. **错误隔离**: 确保单个微应用的错误不会影响整个系统
6. **性能监控**: 建立完善的性能监控和错误追踪机制

## 下一步

了解了基础概念后，您可以：

1. 查看[应用管理](/guide/features/app-management)了解如何管理微应用
2. 学习[路由系统](/guide/features/routing)掌握路由配置
3. 探索[应用间通信](/guide/features/communication)实现数据共享
4. 阅读[最佳实践](/guide/best-practices/architecture)获取架构建议

如果您对某个概念有疑问，请查看[常见问题](/guide/faq)或在社区中提问。