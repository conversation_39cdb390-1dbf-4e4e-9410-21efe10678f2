# Micro-Core 介绍

Micro-Core 是一个下一代微前端架构解决方案，专为构建高性能、高扩展性、高可靠性的企业级前端应用而设计。它深度整合了业界前沿的设计模式与工程化实践，提供完整的微前端生态系统。

## 什么是微前端

微前端是一种将大型前端应用拆分为多个小型、独立的前端应用的架构模式。每个微前端应用可以：

- **独立开发和部署** - 各团队可以独立工作，提高开发效率
- **使用不同的技术栈** - React、Vue、Angular 等框架可以共存
- **由不同的团队维护** - 支持大型组织的分工协作
- **在运行时组合** - 动态加载和卸载，提供统一的用户体验

## 为什么选择 Micro-Core

### 🚀 高性能架构
- **微内核设计** - 核心库小于 15KB，启动速度极快
- **智能预加载** - 基于路由预测和视口检测的资源加载
- **Worker/WASM 支持** - 高性能资源加载策略
- **多层缓存** - 智能缓存机制，避免重复加载

### 🔧 高扩展性设计
- **100% 插件化** - 所有功能通过插件实现，按需组合
- **丰富的钩子系统** - 基于 tapable 的完整生命周期钩子
- **多沙箱策略** - 6种沙箱实现，支持灵活组合
- **构建工具适配** - 深度适配 7 种主流构建工具

### 🛡️ 高可靠性保障
- **完善的错误隔离** - 应用级错误边界，确保系统稳定
- **多重沙箱保护** - Proxy、Iframe、WebComponent 等多种隔离策略
- **全面测试覆盖** - 100% 测试覆盖率，质量有保障
- **生产级监控** - 内置性能监控和错误追踪

### 🌐 跨框架兼容
- **主流框架支持** - React 16.8+/17.x/18.x、Vue 2.7+/3.x、Angular 12+
- **统一开发体验** - 框架无关的核心架构
- **渐进式迁移** - 支持从传统应用平滑过渡
- **兼容性插件** - 提供 qiankun 和 Wujie 兼容插件

## 适用场景

### 大型企业应用
- 多团队协作开发
- 不同业务模块独立部署
- 技术栈迁移和升级

### 平台型产品
- 插件化的功能扩展
- 第三方应用集成
- 多租户架构支持

### 遗留系统改造
- 渐进式微前端改造
- 新老系统并存
- 平滑的技术栈迁移

## 核心概念

### 微应用（Micro App）
微应用是 micro-core 中的基本单元，每个微应用都是一个独立的前端应用，具有自己的：
- 独立的代码仓库
- 独立的构建和部署流程
- 独立的技术栈选择
- 独立的开发团队

### 主应用（Main App）
主应用是微前端架构的容器和协调者，负责：
- 微应用的注册和管理
- 路由分发和导航
- 应用间通信协调
- 全局状态管理

### 适配器（Adapter）
适配器是连接不同技术栈应用的桥梁，提供：
- 统一的应用生命周期接口
- 框架特定的优化
- 兼容性处理

### 插件（Plugin）
插件扩展了 micro-core 的核心功能：
- 路由插件：处理应用间路由
- 通信插件：管理应用间消息传递
- 认证插件：统一身份认证
- 自定义插件：业务特定功能

## 架构优势

### 技术独立性
```mermaid
graph TB
    A[主应用] --> B[React 应用]
    A --> C[Vue 应用]
    A --> D[Angular 应用]
    A --> E[原生 JS 应用]
    
    B --> F[React 18]
    C --> G[Vue 3]
    D --> H[Angular 15]
    E --> I[Vanilla JS]
```

### 团队独立性
- **独立开发**：各团队可以独立开发自己的微应用
- **独立部署**：微应用可以独立发布和部署
- **独立测试**：完整的测试隔离和独立测试流程

### 渐进式升级
- **平滑迁移**：可以逐步将单体应用拆分为微应用
- **技术升级**：可以逐个升级微应用的技术栈
- **风险控制**：降低大规模重构的风险

## 与其他方案对比

| 特性 | micro-core | single-spa | qiankun | Module Federation |
|------|------------|------------|---------|-------------------|
| 技术栈支持 | ✅ 全面 | ✅ 全面 | ✅ 全面 | ⚠️ 限制 |
| 沙箱隔离 | ✅ 完善 | ❌ 无 | ✅ 有 | ❌ 无 |
| 开发体验 | ✅ 优秀 | ⚠️ 一般 | ✅ 良好 | ✅ 良好 |
| 插件系统 | ✅ 丰富 | ⚠️ 基础 | ⚠️ 基础 | ❌ 无 |
| 构建集成 | ✅ 多种 | ⚠️ 基础 | ⚠️ 基础 | ✅ Webpack |
| 学习成本 | ✅ 低 | ⚠️ 中等 | ✅ 低 | ⚠️ 中等 |

## 快速开始

准备好开始使用 micro-core 了吗？

::: tip 推荐路径
1. 阅读 [快速开始](./getting-started.md) 了解基本用法
2. 查看 [核心概念](./concepts.md) 深入理解架构
3. 跟随 [教程](./tutorial.md) 构建第一个微前端应用
4. 探索 [示例项目](../examples/) 学习最佳实践
:::

## 社区支持

- **GitHub**: [micro-core 仓库](https://github.com/your-org/micro-core)
- **文档**: [在线文档](https://micro-core.dev)
- **讨论**: [GitHub Discussions](https://github.com/your-org/micro-core/discussions)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/micro-core/issues)

## 贡献指南

我们欢迎社区贡献！请查看 [贡献指南](../contributing.md) 了解如何参与项目开发。

---

接下来，让我们从 [快速开始](./getting-started.md) 开始你的 micro-core 之旅！