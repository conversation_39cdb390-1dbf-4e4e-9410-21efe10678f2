# 高性能加载器

Micro-Core 提供了多种高性能加载器，包括 Worker 加载器、WebAssembly 加载器、智能预加载等，显著提升微前端应用的加载性能和用户体验。

## 🎯 加载器概述

### 加载器类型

| 加载器类型 | 用途 | 性能提升 | 兼容性 |
|------------|------|----------|--------|
| Worker 加载器 | 后台资源加载 | 🚀🚀🚀 | 现代浏览器 |
| WebAssembly 加载器 | 原生性能计算 | 🚀🚀🚀🚀 | 现代浏览器 |
| 智能预加载器 | 基于路由预测 | 🚀🚀 | 全兼容 |
| 流式加载器 | 渐进式加载 | 🚀🚀 | 全兼容 |
| 缓存加载器 | 多层缓存策略 | 🚀🚀🚀 | 全兼容 |
| 并行加载器 | 并发资源加载 | 🚀🚀 | 全兼容 |

### 核心特性

- **零阻塞加载** - 不影响主线程执行
- **智能调度** - 基于优先级的资源调度
- **自适应策略** - 根据网络状况自动调整
- **错误恢复** - 完善的错误处理和重试机制
- **性能监控** - 实时性能指标收集

## 👷 Worker 加载器

### 基础使用

```typescript
import { WorkerLoader } from '@micro-core/loader-worker';

// 创建 Worker 加载器
const workerLoader = new WorkerLoader({
  // Worker 脚本路径
  workerScript: '/workers/micro-loader.js',
  
  // 并发数量
  concurrency: 4,
  
  // 超时时间
  timeout: 30000,
  
  // 重试次数
  retries: 3
});

// 加载微应用
const app = await workerLoader.load({
  name: 'my-micro-app',
  entry: 'http://localhost:3001',
  
  // 资源列表
  resources: [
    { url: '/static/js/main.js', type: 'script', priority: 'high' },
    { url: '/static/css/main.css', type: 'style', priority: 'medium' },
    { url: '/static/js/chunk.js', type: 'script', priority: 'low' }
  ]
});
```

### Worker 脚本实现

```javascript
// workers/micro-loader.js
class MicroLoaderWorker {
  constructor() {
    this.cache = new Map();
    this.setupMessageHandler();
  }
  
  setupMessageHandler() {
    self.addEventListener('message', async (event) => {
      const { id, type, payload } = event.data;
      
      try {
        let result;
        
        switch (type) {
          case 'LOAD_RESOURCE':
            result = await this.loadResource(payload);
            break;
          case 'PRELOAD_RESOURCES':
            result = await this.preloadResources(payload);
            break;
          case 'CLEAR_CACHE':
            result = this.clearCache();
            break;
          default:
            throw new Error(`Unknown message type: ${type}`);
        }
        
        self.postMessage({
          id,
          type: 'SUCCESS',
          payload: result
        });
      } catch (error) {
        self.postMessage({
          id,
          type: 'ERROR',
          payload: {
            message: error.message,
            stack: error.stack
          }
        });
      }
    });
  }
  
  async loadResource(resource) {
    const { url, type, priority } = resource;
    
    // 检查缓存
    if (this.cache.has(url)) {
      return this.cache.get(url);
    }
    
    // 发起请求
    const response = await fetch(url, {
      priority: priority === 'high' ? 'high' : 'auto'
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load ${url}: ${response.status}`);
    }
    
    let content;
    
    switch (type) {
      case 'script':
        content = await response.text();
        break;
      case 'style':
        content = await response.text();
        break;
      case 'json':
        content = await response.json();
        break;
      default:
        content = await response.arrayBuffer();
    }
    
    // 缓存结果
    this.cache.set(url, {
      url,
      type,
      content,
      size: content.length || content.byteLength,
      timestamp: Date.now()
    });
    
    return this.cache.get(url);
  }
  
  async preloadResources(resources) {
    const results = await Promise.allSettled(
      resources.map(resource => this.loadResource(resource))
    );
    
    return results.map((result, index) => ({
      resource: resources[index],
      status: result.status,
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }));
  }
  
  clearCache() {
    const size = this.cache.size;
    this.cache.clear();
    return { cleared: size };
  }
}

// 初始化 Worker
new MicroLoaderWorker();
```

### 高级配置

```typescript
const workerLoader = new WorkerLoader({
  // 多 Worker 配置
  workers: {
    count: navigator.hardwareConcurrency || 4,
    
    // 负载均衡策略
    loadBalancing: 'round-robin', // 'round-robin' | 'least-busy' | 'random'
    
    // Worker 池管理
    pool: {
      min: 2,
      max: 8,
      idleTimeout: 60000
    }
  },
  
  // 缓存配置
  cache: {
    // 缓存大小限制
    maxSize: 100 * 1024 * 1024, // 100MB
    
    // 缓存策略
    strategy: 'lru', // 'lru' | 'lfu' | 'fifo'
    
    // 缓存过期时间
    ttl: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // 网络配置
  network: {
    // 并发限制
    maxConcurrency: 6,
    
    // 请求优先级
    priorityQueue: true,
    
    // 自适应加载
    adaptive: {
      enabled: true,
      
      // 网络状况检测
      networkAware: true,
      
      // 设备性能检测
      deviceAware: true
    }
  }
});
```

## 🔧 WebAssembly 加载器

### 基础使用

```typescript
import { WasmLoader } from '@micro-core/loader-wasm';

// 创建 WASM 加载器
const wasmLoader = new WasmLoader({
  // WASM 模块路径
  wasmPath: '/wasm',
  
  // 内存配置
  memory: {
    initial: 256, // 初始页数
    maximum: 1024 // 最大页数
  }
});

// 加载 WASM 模块
const wasmModule = await wasmLoader.load({
  name: 'image-processor',
  url: '/wasm/image-processor.wasm',
  
  // 导入对象
  imports: {
    env: {
      memory: new WebAssembly.Memory({ initial: 256 }),
      
      // JavaScript 函数导入
      log: (ptr, len) => {
        const bytes = new Uint8Array(wasmModule.memory.buffer, ptr, len);
        const str = new TextDecoder().decode(bytes);
        console.log('[WASM]', str);
      }
    }
  }
});

// 使用 WASM 函数
const result = wasmModule.exports.processImage(imageData, width, height);
```

### 流式编译

```typescript
// 流式 WASM 加载
class StreamingWasmLoader {
  async loadStreaming(url: string): Promise<WebAssembly.Module> {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch WASM: ${response.status}`);
    }
    
    // 检查是否支持流式编译
    if (WebAssembly.compileStreaming) {
      return await WebAssembly.compileStreaming(response);
    }
    
    // 降级到普通编译
    const bytes = await response.arrayBuffer();
    return await WebAssembly.compile(bytes);
  }
  
  async instantiateStreaming(url: string, imports: any): Promise<WebAssembly.Instance> {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch WASM: ${response.status}`);
    }
    
    // 检查是否支持流式实例化
    if (WebAssembly.instantiateStreaming) {
      const result = await WebAssembly.instantiateStreaming(response, imports);
      return result.instance;
    }
    
    // 降级到普通实例化
    const bytes = await response.arrayBuffer();
    const result = await WebAssembly.instantiate(bytes, imports);
    return result.instance;
  }
}
```

### WASM 模块池

```typescript
// WASM 模块池管理
class WasmModulePool {
  private pools = new Map<string, WebAssembly.Instance[]>();
  private configs = new Map<string, WasmPoolConfig>();
  
  async createPool(name: string, config: WasmPoolConfig): Promise<void> {
    this.configs.set(name, config);
    this.pools.set(name, []);
    
    // 预创建实例
    for (let i = 0; i < config.minInstances; i++) {
      const instance = await this.createInstance(config);
      this.pools.get(name)!.push(instance);
    }
  }
  
  async getInstance(name: string): Promise<WebAssembly.Instance> {
    const pool = this.pools.get(name);
    const config = this.configs.get(name);
    
    if (!pool || !config) {
      throw new Error(`WASM pool ${name} not found`);
    }
    
    // 从池中获取实例
    if (pool.length > 0) {
      return pool.pop()!;
    }
    
    // 池为空，创建新实例
    if (this.getTotalInstances(name) < config.maxInstances) {
      return await this.createInstance(config);
    }
    
    // 等待实例归还
    return await this.waitForInstance(name);
  }
  
  returnInstance(name: string, instance: WebAssembly.Instance): void {
    const pool = this.pools.get(name);
    const config = this.configs.get(name);
    
    if (!pool || !config) {
      return;
    }
    
    // 重置实例状态
    this.resetInstance(instance);
    
    // 归还到池中
    if (pool.length < config.maxInstances) {
      pool.push(instance);
    }
  }
  
  private async createInstance(config: WasmPoolConfig): Promise<WebAssembly.Instance> {
    const module = await WebAssembly.compile(config.wasmBytes);
    return await WebAssembly.instantiate(module, config.imports);
  }
  
  private resetInstance(instance: WebAssembly.Instance): void {
    // 重置 WASM 实例的内存和状态
    if (instance.exports.reset && typeof instance.exports.reset === 'function') {
      (instance.exports.reset as Function)();
    }
  }
}
```

## 🧠 智能预加载器

### 基于路由预测

```typescript
import { IntelligentPreloader } from '@micro-core/loader-intelligent';

// 创建智能预加载器
const preloader = new IntelligentPreloader({
  // 预测算法
  prediction: {
    algorithm: 'markov-chain', // 'markov-chain' | 'neural-network' | 'rule-based'
    
    // 历史数据权重
    historyWeight: 0.7,
    
    // 实时数据权重
    realtimeWeight: 0.3,
    
    // 预测阈值
    threshold: 0.6
  },
  
  // 预加载策略
  strategy: {
    // 预加载时机
    timing: 'idle', // 'immediate' | 'idle' | 'hover' | 'viewport'
    
    // 预加载数量
    maxPreload: 3,
    
    // 网络感知
    networkAware: true,
    
    // 设备感知
    deviceAware: true
  }
});

// 训练预测模型
preloader.train({
  // 用户行为数据
  userBehavior: [
    { from: '/home', to: '/products', probability: 0.8 },
    { from: '/products', to: '/product-detail', probability: 0.6 },
    { from: '/product-detail', to: '/cart', probability: 0.4 }
  ],
  
  // 时间模式
  timePatterns: {
    morning: ['/news', '/weather'],
    afternoon: ['/products', '/services'],
    evening: ['/entertainment', '/social']
  },
  
  // 用户画像
  userProfile: {
    interests: ['technology', 'shopping'],
    behavior: 'power-user',
    device: 'desktop'
  }
});

// 开始智能预加载
preloader.start();
```

### 机器学习预测

```typescript
// 神经网络预测模型
class NeuralNetworkPredictor {
  private model: tf.LayersModel;
  private features: string[] = [];
  
  async initialize(): Promise<void> {
    // 创建神经网络模型
    this.model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [10], units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 16, activation: 'relu' }),
        tf.layers.dense({ units: 1, activation: 'sigmoid' })
      ]
    });
    
    // 编译模型
    this.model.compile({
      optimizer: 'adam',
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });
  }
  
  async train(data: TrainingData[]): Promise<void> {
    // 特征工程
    const features = data.map(item => this.extractFeatures(item));
    const labels = data.map(item => item.visited ? 1 : 0);
    
    // 转换为张量
    const xs = tf.tensor2d(features);
    const ys = tf.tensor2d(labels, [labels.length, 1]);
    
    // 训练模型
    await this.model.fit(xs, ys, {
      epochs: 100,
      batchSize: 32,
      validationSplit: 0.2,
      callbacks: {
        onEpochEnd: (epoch, logs) => {
          console.log(`Epoch ${epoch}: loss = ${logs?.loss}, accuracy = ${logs?.acc}`);
        }
      }
    });
    
    // 清理张量
    xs.dispose();
    ys.dispose();
  }
  
  async predict(context: PredictionContext): Promise<number> {
    const features = this.extractFeatures(context);
    const input = tf.tensor2d([features]);
    
    const prediction = this.model.predict(input) as tf.Tensor;
    const probability = await prediction.data();
    
    // 清理张量
    input.dispose();
    prediction.dispose();
    
    return probability[0];
  }
  
  private extractFeatures(context: any): number[] {
    return [
      context.currentRoute ? this.encodeRoute(context.currentRoute) : 0,
      context.timeOfDay / 24,
      context.dayOfWeek / 7,
      context.userType ? this.encodeUserType(context.userType) : 0,
      context.deviceType ? this.encodeDeviceType(context.deviceType) : 0,
      context.networkSpeed / 100,
      context.batteryLevel / 100,
      context.scrollPosition / 100,
      context.timeOnPage / 300,
      context.clickCount / 10
    ];
  }
}
```

## 📊 性能监控

### 加载性能指标

```typescript
// 性能监控器
class LoaderPerformanceMonitor {
  private metrics = new Map<string, PerformanceMetric[]>();
  
  startMeasurement(loaderId: string, resourceUrl: string): string {
    const measurementId = `${loaderId}-${Date.now()}`;
    
    // 记录开始时间
    performance.mark(`${measurementId}-start`);
    
    return measurementId;
  }
  
  endMeasurement(measurementId: string, result: LoadResult): void {
    // 记录结束时间
    performance.mark(`${measurementId}-end`);
    
    // 计算耗时
    performance.measure(
      measurementId,
      `${measurementId}-start`,
      `${measurementId}-end`
    );
    
    const measure = performance.getEntriesByName(measurementId)[0];
    
    // 记录指标
    const metric: PerformanceMetric = {
      id: measurementId,
      duration: measure.duration,
      size: result.size,
      success: result.success,
      timestamp: Date.now(),
      
      // 网络指标
      networkInfo: this.getNetworkInfo(),
      
      // 设备指标
      deviceInfo: this.getDeviceInfo()
    };
    
    this.recordMetric(result.loaderId, metric);
    
    // 清理性能标记
    performance.clearMarks(`${measurementId}-start`);
    performance.clearMarks(`${measurementId}-end`);
    performance.clearMeasures(measurementId);
  }
  
  getMetrics(loaderId: string): PerformanceMetric[] {
    return this.metrics.get(loaderId) || [];
  }
  
  getAverageLoadTime(loaderId: string): number {
    const metrics = this.getMetrics(loaderId);
    if (metrics.length === 0) return 0;
    
    const totalTime = metrics.reduce((sum, metric) => sum + metric.duration, 0);
    return totalTime / metrics.length;
  }
  
  getSuccessRate(loaderId: string): number {
    const metrics = this.getMetrics(loaderId);
    if (metrics.length === 0) return 0;
    
    const successCount = metrics.filter(metric => metric.success).length;
    return successCount / metrics.length;
  }
  
  private recordMetric(loaderId: string, metric: PerformanceMetric): void {
    if (!this.metrics.has(loaderId)) {
      this.metrics.set(loaderId, []);
    }
    
    const metrics = this.metrics.get(loaderId)!;
    metrics.push(metric);
    
    // 限制指标数量
    if (metrics.length > 1000) {
      metrics.splice(0, metrics.length - 1000);
    }
  }
  
  private getNetworkInfo(): NetworkInfo {
    const connection = (navigator as any).connection;
    
    return {
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      rtt: connection?.rtt || 0,
      saveData: connection?.saveData || false
    };
  }
  
  private getDeviceInfo(): DeviceInfo {
    return {
      hardwareConcurrency: navigator.hardwareConcurrency || 1,
      deviceMemory: (navigator as any).deviceMemory || 0,
      userAgent: navigator.userAgent
    };
  }
}
```

高性能加载器系统为 Micro-Core 提供了卓越的加载性能，通过多种先进的加载策略和优化技术，显著提升了微前端应用的用户体验。
