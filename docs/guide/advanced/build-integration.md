# 构建集成

Micro-Core 提供了完善的构建工具集成，支持 Webpack、Vite、Rollup 等主流构建工具，实现微前端应用的自动化构建和部署。

## 构建工具概览

### 支持的构建工具

```typescript
// Webpack 插件
import { MicroCoreWebpackPlugin } from '@micro-core/builder-webpack';

// Vite 插件
import { microCoreVitePlugin } from '@micro-core/builder-vite';

// Rollup 插件
import { microCoreRollupPlugin } from '@micro-core/builder-rollup';

// Parcel 插件
import { microCoreParcelPlugin } from '@micro-core/builder-parcel';

// ESBuild 插件
import { microCoreESBuildPlugin } from '@micro-core/builder-esbuild';
```

### 构建目标

1. **模块联邦** - 基于 Webpack Module Federation
2. **UMD 模块** - 通用模块定义格式
3. **ESM 模块** - ES6 模块格式
4. **SystemJS** - 动态模块加载器
5. **自定义格式** - 支持自定义输出格式

## Webpack 集成

### 基础配置

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  mode: 'development',
  entry: './src/index.js',
  
  plugins: [
    new MicroCoreWebpackPlugin({
      // 应用名称
      name: 'my-micro-app',
      
      // 入口文件
      entry: './src/index.js',
      
      // 暴露的模块
      exposes: {
        './App': './src/App.jsx',
        './Button': './src/components/Button.jsx'
      },
      
      // 共享依赖
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.0.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.0.0'
        }
      },
      
      // 输出格式
      format: 'module-federation', // 'umd' | 'esm' | 'systemjs'
      
      // 远程入口
      remoteEntry: 'remoteEntry.js',
      
      // 公共路径
      publicPath: 'auto'
    })
  ],
  
  devServer: {
    port: 3001,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
};
```

### 高级配置

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'advanced-micro-app',
      entry: './src/index.js',
      
      // 高级暴露配置
      exposes: {
        './App': {
          import: './src/App.jsx',
          name: 'App'
        },
        './utils': {
          import: './src/utils/index.js',
          name: 'Utils'
        }
      },
      
      // 高级共享配置
      shared: {
        react: {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^18.0.0',
          eager: false
        },
        lodash: {
          singleton: false,
          requiredVersion: '^4.17.0'
        }
      },
      
      // 运行时配置
      runtime: {
        // 自定义运行时名称
        name: 'micro-core-runtime',
        
        // 运行时优化
        optimization: {
          splitChunks: {
            chunks: 'all',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all'
              }
            }
          }
        }
      },
      
      // 构建优化
      optimization: {
        // 代码分割
        codeSplitting: true,
        
        // 树摇优化
        treeShaking: true,
        
        // 压缩配置
        minimize: process.env.NODE_ENV === 'production',
        
        // 外部依赖
        externals: {
          'react': 'React',
          'react-dom': 'ReactDOM'
        }
      },
      
      // 开发配置
      development: {
        // 热更新
        hotReload: true,
        
        // 源码映射
        sourceMap: true,
        
        // 开发服务器
        devServer: {
          port: 3001,
          hot: true,
          liveReload: true
        }
      },
      
      // 生产配置
      production: {
        // 输出优化
        optimization: true,
        
        // 资源压缩
        compression: 'gzip',
        
        // 缓存策略
        caching: {
          type: 'filesystem',
          buildDependencies: {
            config: [__filename]
          }
        }
      }
    })
  ]
};
```

### TypeScript 支持

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx']
  },
  
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'typescript-micro-app',
      entry: './src/index.ts',
      
      // TypeScript 配置
      typescript: {
        configFile: './tsconfig.json',
        
        // 类型检查
        typeCheck: true,
        
        // 声明文件生成
        declaration: true,
        declarationDir: './dist/types'
      },
      
      exposes: {
        './App': './src/App.tsx',
        './types': './src/types/index.ts'
      }
    })
  ]
};
```

## Vite 集成

### 基础配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    react(),
    microCoreVitePlugin({
      // 应用名称
      name: 'vite-micro-app',
      
      // 入口文件
      entry: './src/main.tsx',
      
      // 暴露的模块
      exposes: {
        './App': './src/App.tsx',
        './components': './src/components/index.ts'
      },
      
      // 共享依赖
      shared: ['react', 'react-dom'],
      
      // 输出格式
      format: 'esm',
      
      // 构建目标
      target: 'es2020'
    })
  ],
  
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 资源内联阈值
    assetsInlineLimit: 4096,
    
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom']
        }
      }
    }
  },
  
  server: {
    port: 3002,
    cors: true
  }
});
```

### 高级配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    microCoreVitePlugin({
      name: 'advanced-vite-app',
      entry: './src/main.tsx',
      
      // 多入口配置
      entries: {
        main: './src/main.tsx',
        admin: './src/admin.tsx'
      },
      
      // 条件暴露
      exposes: {
        './App': {
          import: './src/App.tsx',
          condition: 'production'
        },
        './DevApp': {
          import: './src/DevApp.tsx',
          condition: 'development'
        }
      },
      
      // 动态导入
      dynamicImports: {
        './LazyComponent': () => import('./src/components/LazyComponent.tsx')
      },
      
      // 预构建配置
      prebuild: {
        // 预构建依赖
        include: ['react', 'react-dom', 'lodash'],
        
        // 排除预构建
        exclude: ['@micro-core/core']
      },
      
      // 开发配置
      dev: {
        // 热更新
        hmr: true,
        
        // 源码映射
        sourcemap: true,
        
        // 代理配置
        proxy: {
          '/api': {
            target: 'http://localhost:8080',
            changeOrigin: true
          }
        }
      },
      
      // 生产配置
      prod: {
        // 压缩配置
        minify: 'terser',
        
        // 资源优化
        assetsOptimization: true,
        
        // 输出分析
        bundleAnalyzer: process.env.ANALYZE === 'true'
      }
    })
  ]
});
```

### Vue 项目配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    vue(),
    microCoreVitePlugin({
      name: 'vue-micro-app',
      entry: './src/main.ts',
      
      // Vue 特定配置
      framework: 'vue',
      
      exposes: {
        './App': './src/App.vue',
        './components': './src/components/index.ts'
      },
      
      // Vue 共享配置
      shared: {
        vue: {
          singleton: true
        },
        'vue-router': {
          singleton: true
        }
      }
    })
  ]
});
```

## Rollup 集成

### 基础配置

```javascript
// rollup.config.js
import { microCoreRollupPlugin } from '@micro-core/builder-rollup';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';

export default {
  input: 'src/index.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: true
  },
  
  plugins: [
    resolve(),
    commonjs(),
    typescript(),
    microCoreRollupPlugin({
      name: 'rollup-micro-app',
      
      exposes: {
        './App': './src/App.tsx',
        './utils': './src/utils/index.ts'
      },
      
      shared: ['react', 'react-dom'],
      
      // Rollup 特定配置
      external: ['react', 'react-dom'],
      
      // 输出配置
      output: {
        format: 'esm',
        preserveModules: true
      }
    })
  ]
};
```

## 模块联邦配置

### 主应用配置

```javascript
// webpack.config.js (主应用)
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'main-app',
      
      // 主应用模式
      mode: 'host',
      
      // 远程应用配置
      remotes: {
        'micro-app-1': 'microApp1@http://localhost:3001/remoteEntry.js',
        'micro-app-2': 'microApp2@http://localhost:3002/remoteEntry.js'
      },
      
      // 共享依赖
      shared: {
        react: {
          singleton: true,
          eager: true
        },
        'react-dom': {
          singleton: true,
          eager: true
        }
      }
    })
  ]
};
```

### 微应用配置

```javascript
// webpack.config.js (微应用)
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'micro-app-1',
      
      // 微应用模式
      mode: 'remote',
      
      // 暴露模块
      exposes: {
        './App': './src/App.jsx',
        './Button': './src/components/Button.jsx'
      },
      
      // 共享依赖
      shared: {
        react: {
          singleton: true
        },
        'react-dom': {
          singleton: true
        }
      }
    })
  ]
};
```

## 构建优化

### 代码分割

```javascript
// webpack.config.js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // 第三方库
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        
        // 公共代码
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5
        },
        
        // 微前端运行时
        microCore: {
          test: /[\\/]node_modules[\\/]@micro-core[\\/]/,
          name: 'micro-core',
          chunks: 'all',
          priority: 15
        }
      }
    }
  }
};
```

### 缓存策略

```javascript
// webpack.config.js
module.exports = {
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    },
    cacheDirectory: path.resolve(__dirname, '.webpack-cache')
  },
  
  output: {
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].[contenthash].chunk.js'
  }
};
```

### 压缩优化

```javascript
// webpack.config.js
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

module.exports = {
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          }
        }
      }),
      new CssMinimizerPlugin()
    ]
  }
};
```

## 环境配置

### 开发环境

```javascript
// webpack.dev.js
const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');

module.exports = merge(common, {
  mode: 'development',
  
  devtool: 'eval-source-map',
  
  devServer: {
    hot: true,
    port: 3001,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    
    // 代理配置
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  },
  
  plugins: [
    new webpack.HotModuleReplacementPlugin()
  ]
});
```

### 生产环境

```javascript
// webpack.prod.js
const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = merge(common, {
  mode: 'production',
  
  devtool: 'source-map',
  
  optimization: {
    minimize: true,
    sideEffects: false
  },
  
  plugins: [
    // 分析包大小
    process.env.ANALYZE && new BundleAnalyzerPlugin()
  ].filter(Boolean)
});
```

## 部署配置

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx 配置

```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # 微前端资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # 远程入口文件不缓存
        location ~* remoteEntry\.js$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # CORS 配置
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
}
```

### CI/CD 配置

```yaml
# .github/workflows/deploy.yml
name: Deploy Micro App

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
      env:
        NODE_ENV: production
        PUBLIC_PATH: https://cdn.example.com/micro-app/
    
    - name: Run tests
      run: npm test
    
    - name: Deploy to CDN
      run: |
        aws s3 sync dist/ s3://micro-apps-bucket/micro-app/ --delete
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_ID }} --paths "/micro-app/*"
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
```

## 构建脚本

### Package.json 脚本

```json
{
  "scripts": {
    "dev": "webpack serve --config webpack.dev.js",
    "build": "webpack --config webpack.prod.js",
    "build:analyze": "ANALYZE=true webpack --config webpack.prod.js",
    "build:dev": "webpack --config webpack.dev.js",
    "preview": "serve dist -s -p 3001",
    "type-check": "tsc --noEmit",
    "lint": "eslint src --ext .ts,.tsx,.js,.jsx",
    "test": "jest",
    "test:watch": "jest --watch",
    "clean": "rimraf dist"
  }
}
```

### 构建脚本

```bash
#!/bin/bash
# build.sh

set -e

echo "🚀 开始构建微前端应用..."

# 清理旧的构建文件
echo "🧹 清理构建目录..."
rm -rf dist

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 类型检查
echo "🔍 执行类型检查..."
npm run type-check

# 代码检查
echo "🔍 执行代码检查..."
npm run lint

# 运行测试
echo "🧪 运行测试..."
npm test

# 构建应用
echo "🏗️ 构建应用..."
npm run build

# 构建完成
echo "✅ 构建完成！"
echo "📁 构建文件位于 dist/ 目录"

# 显示构建统计
echo "📊 构建统计:"
du -sh dist/*
```

## 最佳实践

### 1. 依赖管理

```javascript
// 推荐的共享依赖配置
shared: {
  // 核心框架库 - 单例模式
  react: {
    singleton: true,
    requiredVersion: '^18.0.0',
    eager: true
  },
  
  // 工具库 - 允许多版本
  lodash: {
    singleton: false,
    requiredVersion: '^4.17.0'
  },
  
  // 微前端核心 - 严格版本
  '@micro-core/core': {
    singleton: true,
    strictVersion: true,
    eager: true
  }
}
```

### 2. 构建性能优化

```javascript
// 构建性能优化配置
module.exports = {
  // 缓存配置
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  
  // 并行构建
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        parallel: true
      })
    ]
  },
  
  // 解析优化
  resolve: {
    modules: ['node_modules'],
    extensions: ['.js', '.jsx', '.ts', '.tsx']
  }
};
```

### 3. 错误处理

```javascript
// 构建错误处理
class BuildErrorPlugin {
  apply(compiler) {
    compiler.hooks.done.tap('BuildErrorPlugin', (stats) => {
      if (stats.hasErrors()) {
        const errors = stats.toJson().errors;
        console.error('构建失败:', errors);
        process.exit(1);
      }
    });
  }
}
```

构建集成是微前端架构的重要组成部分，通过合理的构建配置可以：

1. **提高开发效率** - 热更新、源码映射、代理配置
2. **优化生产性能** - 代码分割、压缩优化、缓存策略
3. **简化部署流程** - 自动化构建、容器化部署、CI/CD集成
4. **确保代码质量** - 类型检查、代码检查、自动化测试
5. **支持多种场景** - 开发、测试、生产环境配置
