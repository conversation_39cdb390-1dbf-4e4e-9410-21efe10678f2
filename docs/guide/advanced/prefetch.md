# 智能预加载

Micro-Core 的智能预加载系统基于机器学习和用户行为分析，能够预测用户的下一步操作，提前加载相关资源，显著提升应用响应速度和用户体验。

## 🎯 预加载概述

### 预加载策略

| 策略类型 | 触发条件 | 预测准确率 | 性能提升 |
|----------|----------|------------|----------|
| 基于路由的预加载 | 路由变化 | 85% | 🚀🚀🚀 |
| 基于用户行为的预加载 | 鼠标悬停/点击 | 90% | 🚀🚀🚀🚀 |
| 基于时间模式的预加载 | 时间周期 | 75% | 🚀🚀 |
| 基于设备状态的预加载 | 网络/电量 | 80% | 🚀🚀 |
| 基于机器学习的预加载 | AI 预测 | 95% | 🚀🚀🚀🚀🚀 |

### 核心特性

- **智能预测** - 基于多维度数据的智能预测算法
- **自适应调整** - 根据网络和设备状况自动调整策略
- **用户感知优化** - 不影响当前页面的用户体验
- **资源优先级管理** - 智能的资源加载优先级调度
- **缓存策略优化** - 多层缓存和缓存失效策略

## 🧠 智能预测引擎

### 基础配置

```typescript
import { IntelligentPrefetcher } from '@micro-core/prefetch';

// 创建智能预加载器
const prefetcher = new IntelligentPrefetcher({
  // 预测引擎配置
  prediction: {
    // 预测算法
    algorithm: 'hybrid', // 'markov' | 'neural' | 'rule-based' | 'hybrid'
    
    // 预测阈值
    threshold: 0.7,
    
    // 学习率
    learningRate: 0.01,
    
    // 历史数据窗口
    historyWindow: 1000
  },
  
  // 预加载策略
  strategy: {
    // 预加载时机
    timing: {
      idle: true,        // 浏览器空闲时
      hover: true,       // 鼠标悬停时
      viewport: true,    // 进入视口时
      interaction: true  // 用户交互时
    },
    
    // 资源优先级
    priority: {
      critical: ['main.js', 'main.css'],
      high: ['chunk-*.js'],
      medium: ['images/*'],
      low: ['fonts/*']
    },
    
    // 预加载限制
    limits: {
      maxConcurrent: 3,     // 最大并发数
      maxSize: 5 * 1024 * 1024, // 最大预加载大小 5MB
      maxTime: 10000        // 最大预加载时间 10s
    }
  },
  
  // 环境感知
  environment: {
    // 网络感知
    networkAware: true,
    
    // 设备感知
    deviceAware: true,
    
    // 电量感知
    batteryAware: true,
    
    // 数据节省模式
    dataSaver: true
  }
});

// 启动预加载器
prefetcher.start();
```

### 机器学习预测模型

```typescript
// 神经网络预测模型
class NeuralPredictionModel {
  private model: tf.LayersModel;
  private featureExtractor: FeatureExtractor;
  private trainingData: TrainingData[] = [];
  
  constructor() {
    this.featureExtractor = new FeatureExtractor();
    this.initializeModel();
  }
  
  private initializeModel(): void {
    // 构建神经网络
    this.model = tf.sequential({
      layers: [
        // 输入层
        tf.layers.dense({
          inputShape: [20], // 20个特征
          units: 128,
          activation: 'relu',
          kernelInitializer: 'heNormal'
        }),
        
        // 隐藏层
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({ units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        
        // 输出层
        tf.layers.dense({ units: 1, activation: 'sigmoid' })
      ]
    });
    
    // 编译模型
    this.model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });
  }
  
  async train(data: UserBehaviorData[]): Promise<void> {
    // 特征提取
    const features = data.map(item => this.featureExtractor.extract(item));
    const labels = data.map(item => item.navigated ? 1 : 0);
    
    // 数据预处理
    const { normalizedFeatures, scaler } = this.normalizeFeatures(features);
    
    // 转换为张量
    const xs = tf.tensor2d(normalizedFeatures);
    const ys = tf.tensor2d(labels, [labels.length, 1]);
    
    // 训练模型
    const history = await this.model.fit(xs, ys, {
      epochs: 50,
      batchSize: 32,
      validationSplit: 0.2,
      shuffle: true,
      callbacks: {
        onEpochEnd: (epoch, logs) => {
          console.log(`Epoch ${epoch + 1}: loss=${logs?.loss?.toFixed(4)}, accuracy=${logs?.acc?.toFixed(4)}`);
        }
      }
    });
    
    // 保存缩放器
    this.featureScaler = scaler;
    
    // 清理内存
    xs.dispose();
    ys.dispose();
    
    return history;
  }
  
  async predict(context: PredictionContext): Promise<PredictionResult> {
    // 提取特征
    const features = this.featureExtractor.extract(context);
    const normalizedFeatures = this.normalizeWithScaler(features);
    
    // 预测
    const input = tf.tensor2d([normalizedFeatures]);
    const prediction = this.model.predict(input) as tf.Tensor;
    const probability = await prediction.data();
    
    // 清理内存
    input.dispose();
    prediction.dispose();
    
    return {
      probability: probability[0],
      confidence: this.calculateConfidence(probability[0]),
      features: features,
      timestamp: Date.now()
    };
  }
  
  private calculateConfidence(probability: number): number {
    // 计算预测置信度
    return Math.abs(probability - 0.5) * 2;
  }
}
```

### 特征提取器

```typescript
// 特征提取器
class FeatureExtractor {
  extract(context: PredictionContext): number[] {
    return [
      // 时间特征
      this.extractTimeFeatures(context),
      
      // 用户行为特征
      this.extractBehaviorFeatures(context),
      
      // 页面特征
      this.extractPageFeatures(context),
      
      // 设备特征
      this.extractDeviceFeatures(context),
      
      // 网络特征
      this.extractNetworkFeatures(context)
    ].flat();
  }
  
  private extractTimeFeatures(context: PredictionContext): number[] {
    const now = new Date();
    
    return [
      now.getHours() / 24,           // 小时 (0-1)
      now.getDay() / 7,              // 星期 (0-1)
      now.getDate() / 31,            // 日期 (0-1)
      context.timeOnPage / 300,      // 页面停留时间 (0-1, 最大5分钟)
      context.sessionDuration / 3600 // 会话时长 (0-1, 最大1小时)
    ];
  }
  
  private extractBehaviorFeatures(context: PredictionContext): number[] {
    return [
      context.scrollDepth / 100,           // 滚动深度 (0-1)
      context.clickCount / 20,             // 点击次数 (0-1, 最大20次)
      context.hoverCount / 10,             // 悬停次数 (0-1, 最大10次)
      context.backButtonUsage ? 1 : 0,     // 是否使用后退按钮
      context.searchUsage ? 1 : 0,         // 是否使用搜索
      context.menuInteraction ? 1 : 0      // 是否与菜单交互
    ];
  }
  
  private extractPageFeatures(context: PredictionContext): number[] {
    return [
      this.encodePageType(context.currentPage),  // 页面类型编码
      context.pageLoadTime / 5000,               // 页面加载时间 (0-1, 最大5秒)
      context.pageSize / (1024 * 1024),          // 页面大小 (MB)
      context.imageCount / 50,                   // 图片数量 (0-1, 最大50张)
      context.linkCount / 100                    // 链接数量 (0-1, 最大100个)
    ];
  }
  
  private extractDeviceFeatures(context: PredictionContext): number[] {
    return [
      this.encodeDeviceType(context.deviceType),     // 设备类型编码
      context.screenWidth / 2560,                    // 屏幕宽度 (0-1)
      context.screenHeight / 1440,                   // 屏幕高度 (0-1)
      context.batteryLevel / 100,                    // 电量 (0-1)
      context.memoryUsage / 100                      // 内存使用率 (0-1)
    ];
  }
  
  private extractNetworkFeatures(context: PredictionContext): number[] {
    return [
      this.encodeConnectionType(context.connectionType), // 连接类型编码
      context.downloadSpeed / 100,                       // 下载速度 (Mbps, 0-1)
      context.rtt / 1000,                               // 往返时间 (0-1, 最大1秒)
      context.dataSaverMode ? 1 : 0                     // 数据节省模式
    ];
  }
  
  private encodePageType(pageType: string): number {
    const types = ['home', 'list', 'detail', 'form', 'profile', 'settings'];
    const index = types.indexOf(pageType);
    return index >= 0 ? index / types.length : 0;
  }
  
  private encodeDeviceType(deviceType: string): number {
    const types = ['mobile', 'tablet', 'desktop'];
    const index = types.indexOf(deviceType);
    return index >= 0 ? index / types.length : 0;
  }
  
  private encodeConnectionType(connectionType: string): number {
    const types = ['slow-2g', '2g', '3g', '4g', '5g'];
    const index = types.indexOf(connectionType);
    return index >= 0 ? index / types.length : 0;
  }
}
```

## 🎯 预加载策略

### 基于路由的预加载

```typescript
// 路由预加载器
class RoutePrefetcher {
  private routeGraph: RouteGraph;
  private transitionProbabilities: Map<string, Map<string, number>>;
  
  constructor() {
    this.routeGraph = new RouteGraph();
    this.transitionProbabilities = new Map();
  }
  
  // 学习路由转换模式
  learnRouteTransitions(transitions: RouteTransition[]): void {
    transitions.forEach(transition => {
      this.routeGraph.addTransition(transition.from, transition.to);
      this.updateTransitionProbability(transition.from, transition.to);
    });
  }
  
  // 预测下一个可能的路由
  predictNextRoutes(currentRoute: string, limit: number = 3): PredictedRoute[] {
    const probabilities = this.transitionProbabilities.get(currentRoute);
    
    if (!probabilities) {
      return [];
    }
    
    // 按概率排序
    const sortedRoutes = Array.from(probabilities.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([route, probability]) => ({
        route,
        probability,
        resources: this.getRouteResources(route)
      }));
    
    return sortedRoutes;
  }
  
  // 开始预加载
  async startPrefetching(currentRoute: string): Promise<void> {
    const predictedRoutes = this.predictNextRoutes(currentRoute);
    
    for (const predicted of predictedRoutes) {
      if (predicted.probability > 0.6) {
        await this.prefetchRoute(predicted);
      }
    }
  }
  
  private async prefetchRoute(predicted: PredictedRoute): Promise<void> {
    const { route, resources, probability } = predicted;
    
    console.log(`预加载路由 ${route} (概率: ${probability.toFixed(2)})`);
    
    // 按优先级预加载资源
    for (const resource of resources) {
      await this.prefetchResource(resource, probability);
    }
  }
  
  private async prefetchResource(resource: Resource, priority: number): Promise<void> {
    // 检查网络状况
    if (!this.shouldPrefetch(priority)) {
      return;
    }
    
    try {
      // 使用 link 标签预加载
      const link = document.createElement('link');
      link.rel = resource.type === 'script' ? 'prefetch' : 'preload';
      link.as = resource.type;
      link.href = resource.url;
      
      // 设置优先级
      if (priority > 0.8) {
        link.setAttribute('importance', 'high');
      } else if (priority > 0.6) {
        link.setAttribute('importance', 'auto');
      } else {
        link.setAttribute('importance', 'low');
      }
      
      document.head.appendChild(link);
      
      console.log(`预加载资源: ${resource.url}`);
    } catch (error) {
      console.warn(`预加载失败: ${resource.url}`, error);
    }
  }
  
  private shouldPrefetch(priority: number): boolean {
    // 检查网络状况
    const connection = (navigator as any).connection;
    if (connection) {
      // 慢速网络下只预加载高优先级资源
      if (connection.effectiveType === '2g' && priority < 0.9) {
        return false;
      }
      
      // 数据节省模式下不预加载
      if (connection.saveData) {
        return false;
      }
    }
    
    // 检查电量状况
    const battery = (navigator as any).battery;
    if (battery && battery.level < 0.2) {
      return false;
    }
    
    return true;
  }
}
```

### 基于用户行为的预加载

```typescript
// 用户行为预加载器
class BehaviorPrefetcher {
  private hoverTimer: number | null = null;
  private interactionHistory: InteractionEvent[] = [];
  
  constructor() {
    this.setupEventListeners();
  }
  
  private setupEventListeners(): void {
    // 鼠标悬停预加载
    document.addEventListener('mouseover', this.handleMouseOver.bind(this));
    document.addEventListener('mouseout', this.handleMouseOut.bind(this));
    
    // 点击预测预加载
    document.addEventListener('mousedown', this.handleMouseDown.bind(this));
    
    // 滚动预加载
    document.addEventListener('scroll', this.handleScroll.bind(this));
    
    // 视口预加载
    this.setupIntersectionObserver();
  }
  
  private handleMouseOver(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const link = target.closest('a[href]') as HTMLAnchorElement;
    
    if (!link) return;
    
    // 延迟预加载，避免误触
    this.hoverTimer = window.setTimeout(() => {
      this.prefetchLink(link, 'hover');
    }, 100);
  }
  
  private handleMouseOut(): void {
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer);
      this.hoverTimer = null;
    }
  }
  
  private handleMouseDown(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const link = target.closest('a[href]') as HTMLAnchorElement;
    
    if (!link) return;
    
    // 鼠标按下时立即预加载
    this.prefetchLink(link, 'mousedown');
  }
  
  private handleScroll(): void {
    // 滚动时预加载即将进入视口的链接
    const links = document.querySelectorAll('a[href]');
    
    links.forEach(link => {
      if (this.isNearViewport(link as HTMLElement)) {
        this.prefetchLink(link as HTMLAnchorElement, 'scroll');
      }
    });
  }
  
  private setupIntersectionObserver(): void {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const link = entry.target as HTMLAnchorElement;
            this.prefetchLink(link, 'viewport');
          }
        });
      },
      {
        rootMargin: '100px' // 提前100px开始预加载
      }
    );
    
    // 观察所有链接
    document.querySelectorAll('a[href]').forEach(link => {
      observer.observe(link);
    });
  }
  
  private async prefetchLink(link: HTMLAnchorElement, trigger: string): Promise<void> {
    const href = link.href;
    
    // 避免重复预加载
    if (this.isPrefetched(href)) {
      return;
    }
    
    // 记录交互事件
    this.recordInteraction({
      type: trigger,
      target: href,
      timestamp: Date.now()
    });
    
    // 预测用户意图
    const intention = await this.predictUserIntention(link, trigger);
    
    if (intention.probability > 0.5) {
      await this.performPrefetch(href, intention.priority);
    }
  }
  
  private async predictUserIntention(link: HTMLAnchorElement, trigger: string): Promise<UserIntention> {
    // 基于多种因素预测用户意图
    let probability = 0;
    let priority = 'low';
    
    // 触发方式权重
    const triggerWeights = {
      hover: 0.3,
      mousedown: 0.9,
      scroll: 0.2,
      viewport: 0.1
    };
    
    probability += triggerWeights[trigger] || 0;
    
    // 链接位置权重
    const rect = link.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    
    if (rect.top < viewportHeight * 0.5) {
      probability += 0.2; // 上半屏权重更高
    }
    
    // 链接文本分析
    const linkText = link.textContent?.toLowerCase() || '';
    const highPriorityKeywords = ['详情', '查看', '进入', '立即', '马上'];
    
    if (highPriorityKeywords.some(keyword => linkText.includes(keyword))) {
      probability += 0.3;
      priority = 'high';
    }
    
    // 历史行为分析
    const similarInteractions = this.interactionHistory.filter(
      interaction => interaction.target === link.href
    );
    
    if (similarInteractions.length > 0) {
      probability += Math.min(similarInteractions.length * 0.1, 0.4);
    }
    
    return {
      probability: Math.min(probability, 1),
      priority: priority as 'low' | 'medium' | 'high'
    };
  }
  
  private async performPrefetch(url: string, priority: string): Promise<void> {
    try {
      // 创建预加载链接
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      
      if (priority === 'high') {
        link.setAttribute('importance', 'high');
      }
      
      document.head.appendChild(link);
      
      // 标记为已预加载
      this.markAsPrefetched(url);
      
      console.log(`预加载页面: ${url} (优先级: ${priority})`);
    } catch (error) {
      console.warn(`预加载失败: ${url}`, error);
    }
  }
  
  private isPrefetched(url: string): boolean {
    return document.querySelector(`link[rel="prefetch"][href="${url}"]`) !== null;
  }
  
  private markAsPrefetched(url: string): void {
    // 实现预加载标记逻辑
  }
  
  private isNearViewport(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const threshold = 200; // 200px 阈值
    
    return rect.top < window.innerHeight + threshold && rect.bottom > -threshold;
  }
  
  private recordInteraction(event: InteractionEvent): void {
    this.interactionHistory.push(event);
    
    // 限制历史记录数量
    if (this.interactionHistory.length > 1000) {
      this.interactionHistory.splice(0, 100);
    }
  }
}
```

## 📊 性能监控与优化

### 预加载效果分析

```typescript
// 预加载性能分析器
class PrefetchAnalyzer {
  private metrics: PrefetchMetric[] = [];
  private hitRate: number = 0;
  private wasteRate: number = 0;
  
  // 记录预加载指标
  recordPrefetch(url: string, trigger: string): void {
    this.metrics.push({
      url,
      trigger,
      prefetchTime: Date.now(),
      used: false,
      wasteTime: null
    });
  }
  
  // 记录资源使用
  recordUsage(url: string): void {
    const metric = this.metrics.find(m => m.url === url && !m.used);
    
    if (metric) {
      metric.used = true;
      metric.useTime = Date.now();
      metric.savedTime = metric.useTime - metric.prefetchTime;
    }
  }
  
  // 计算命中率
  calculateHitRate(): number {
    const totalPrefetches = this.metrics.length;
    const usedPrefetches = this.metrics.filter(m => m.used).length;
    
    this.hitRate = totalPrefetches > 0 ? usedPrefetches / totalPrefetches : 0;
    return this.hitRate;
  }
  
  // 计算浪费率
  calculateWasteRate(): number {
    const now = Date.now();
    const wasteThreshold = 5 * 60 * 1000; // 5分钟
    
    const wastedPrefetches = this.metrics.filter(m => 
      !m.used && (now - m.prefetchTime) > wasteThreshold
    ).length;
    
    this.wasteRate = this.metrics.length > 0 ? wastedPrefetches / this.metrics.length : 0;
    return this.wasteRate;
  }
  
  // 生成性能报告
  generateReport(): PrefetchReport {
    return {
      totalPrefetches: this.metrics.length,
      hitRate: this.calculateHitRate(),
      wasteRate: this.calculateWasteRate(),
      averageSavedTime: this.calculateAverageSavedTime(),
      topTriggers: this.getTopTriggers(),
      recommendations: this.generateRecommendations()
    };
  }
  
  private calculateAverageSavedTime(): number {
    const usedMetrics = this.metrics.filter(m => m.used && m.savedTime);
    
    if (usedMetrics.length === 0) return 0;
    
    const totalSavedTime = usedMetrics.reduce((sum, m) => sum + (m.savedTime || 0), 0);
    return totalSavedTime / usedMetrics.length;
  }
  
  private getTopTriggers(): Array<{ trigger: string; count: number; hitRate: number }> {
    const triggerStats = new Map<string, { total: number; used: number }>();
    
    this.metrics.forEach(metric => {
      const stats = triggerStats.get(metric.trigger) || { total: 0, used: 0 };
      stats.total++;
      if (metric.used) stats.used++;
      triggerStats.set(metric.trigger, stats);
    });
    
    return Array.from(triggerStats.entries())
      .map(([trigger, stats]) => ({
        trigger,
        count: stats.total,
        hitRate: stats.total > 0 ? stats.used / stats.total : 0
      }))
      .sort((a, b) => b.hitRate - a.hitRate);
  }
  
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.hitRate < 0.3) {
      recommendations.push('预加载命中率较低，建议优化预测算法');
    }
    
    if (this.wasteRate > 0.5) {
      recommendations.push('预加载浪费率较高，建议降低预加载阈值');
    }
    
    const topTriggers = this.getTopTriggers();
    const bestTrigger = topTriggers[0];
    
    if (bestTrigger && bestTrigger.hitRate > 0.8) {
      recommendations.push(`${bestTrigger.trigger} 触发器效果最佳，建议增加权重`);
    }
    
    return recommendations;
  }
}
```

智能预加载系统通过先进的机器学习算法和用户行为分析，为 Micro-Core 提供了卓越的性能优化能力，显著提升了微前端应用的响应速度和用户体验。
