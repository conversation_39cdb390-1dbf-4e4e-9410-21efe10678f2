# 插件系统

Micro-Core 提供了强大的插件系统，允许开发者扩展框架功能，实现自定义的微前端解决方案。

## 插件概览

### 内置插件

Micro-Core 提供了多个内置插件来支持常见的微前端需求：

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { CommunicationPlugin } from '@micro-core/plugin-communication';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';
import { PerformancePlugin } from '@micro-core/plugin-performance';

const kernel = new MicroCoreKernel();

// 注册内置插件
kernel
  .use(RouterPlugin, { mode: 'history' })
  .use(CommunicationPlugin)
  .use(ProxySandboxPlugin, { strict: true })
  .use(PerformancePlugin, { enableMetrics: true });
```

### 插件类型

1. **核心插件** - 扩展内核功能
2. **沙箱插件** - 提供不同的沙箱策略
3. **通信插件** - 增强应用间通信能力
4. **路由插件** - 扩展路由管理功能
5. **构建插件** - 集成构建工具
6. **监控插件** - 提供性能和错误监控

## 创建自定义插件

### 基础插件结构

```typescript
import { Plugin, PluginContext, ApplicationInstance } from '@micro-core/core';

interface MyPluginOptions {
  enabled?: boolean;
  config?: Record<string, any>;
}

class MyPlugin implements Plugin {
  name = 'my-plugin';
  version = '1.0.0';
  
  private options: MyPluginOptions;
  private context: PluginContext;

  constructor(options: MyPluginOptions = {}) {
    this.options = { enabled: true, ...options };
  }

  // 插件安装时调用
  install(context: PluginContext): void {
    this.context = context;
    
    console.log(`插件 ${this.name} 已安装`);
    
    // 注册插件功能
    this.registerHooks();
    this.registerServices();
    this.registerMiddleware();
  }

  // 插件卸载时调用
  uninstall(): void {
    console.log(`插件 ${this.name} 已卸载`);
    
    // 清理资源
    this.cleanup();
  }

  // 注册生命周期钩子
  private registerHooks(): void {
    this.context.hooks.beforeApplicationLoad.tap(this.name, (app) => {
      console.log(`[${this.name}] 应用 ${app.name} 即将加载`);
    });

    this.context.hooks.afterApplicationMount.tap(this.name, (app) => {
      console.log(`[${this.name}] 应用 ${app.name} 挂载完成`);
    });
  }

  // 注册服务
  private registerServices(): void {
    this.context.services.register('myService', {
      doSomething: () => {
        console.log('执行自定义服务');
      }
    });
  }

  // 注册中间件
  private registerMiddleware(): void {
    this.context.middleware.use((req, res, next) => {
      console.log(`[${this.name}] 处理请求:`, req.url);
      next();
    });
  }

  private cleanup(): void {
    // 清理定时器、事件监听器等资源
  }
}

export default MyPlugin;
```

### 使用自定义插件

```typescript
import MyPlugin from './plugins/MyPlugin';

const kernel = new MicroCoreKernel();

// 使用自定义插件
kernel.use(MyPlugin, {
  enabled: true,
  config: {
    apiUrl: 'https://api.example.com'
  }
});
```

## 插件开发指南

### 1. 生命周期钩子

插件可以监听应用的各个生命周期阶段：

```typescript
class LifecyclePlugin implements Plugin {
  name = 'lifecycle-plugin';

  install(context: PluginContext): void {
    const { hooks } = context;

    // 应用注册时
    hooks.applicationRegister.tap(this.name, (config) => {
      console.log('应用注册:', config.name);
    });

    // 应用加载前
    hooks.beforeApplicationLoad.tap(this.name, (app) => {
      console.log('应用加载前:', app.name);
    });

    // 应用加载后
    hooks.afterApplicationLoad.tap(this.name, (app) => {
      console.log('应用加载后:', app.name);
    });

    // 应用挂载前
    hooks.beforeApplicationMount.tap(this.name, (app) => {
      console.log('应用挂载前:', app.name);
    });

    // 应用挂载后
    hooks.afterApplicationMount.tap(this.name, (app) => {
      console.log('应用挂载后:', app.name);
    });

    // 应用卸载前
    hooks.beforeApplicationUnmount.tap(this.name, (app) => {
      console.log('应用卸载前:', app.name);
    });

    // 应用卸载后
    hooks.afterApplicationUnmount.tap(this.name, (app) => {
      console.log('应用卸载后:', app.name);
    });

    // 应用错误时
    hooks.applicationError.tap(this.name, (error, app) => {
      console.error('应用错误:', error, app?.name);
    });
  }
}
```

### 2. 服务注册

插件可以注册服务供其他插件或应用使用：

```typescript
class ServicePlugin implements Plugin {
  name = 'service-plugin';

  install(context: PluginContext): void {
    // 注册HTTP服务
    context.services.register('http', {
      get: async (url: string) => {
        const response = await fetch(url);
        return response.json();
      },
      post: async (url: string, data: any) => {
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        return response.json();
      }
    });

    // 注册缓存服务
    context.services.register('cache', {
      set: (key: string, value: any, ttl?: number) => {
        const item = {
          value,
          expiry: ttl ? Date.now() + ttl : null
        };
        localStorage.setItem(`cache_${key}`, JSON.stringify(item));
      },
      get: (key: string) => {
        const item = localStorage.getItem(`cache_${key}`);
        if (!item) return null;

        const parsed = JSON.parse(item);
        if (parsed.expiry && Date.now() > parsed.expiry) {
          localStorage.removeItem(`cache_${key}`);
          return null;
        }

        return parsed.value;
      },
      remove: (key: string) => {
        localStorage.removeItem(`cache_${key}`);
      }
    });
  }
}

// 使用服务
const httpService = kernel.getService('http');
const data = await httpService.get('/api/users');

const cacheService = kernel.getService('cache');
cacheService.set('user', data, 5 * 60 * 1000); // 缓存5分钟
```

### 3. 中间件系统

插件可以注册中间件来拦截和处理请求：

```typescript
class MiddlewarePlugin implements Plugin {
  name = 'middleware-plugin';

  install(context: PluginContext): void {
    // 认证中间件
    context.middleware.use('auth', (req, res, next) => {
      const token = req.headers.authorization;
      
      if (!token) {
        res.status = 401;
        res.body = { error: 'Unauthorized' };
        return;
      }

      // 验证token
      if (this.validateToken(token)) {
        req.user = this.getUserFromToken(token);
        next();
      } else {
        res.status = 401;
        res.body = { error: 'Invalid token' };
      }
    });

    // 日志中间件
    context.middleware.use('logger', (req, res, next) => {
      const startTime = Date.now();
      
      next();
      
      const duration = Date.now() - startTime;
      console.log(`${req.method} ${req.url} - ${res.status} (${duration}ms)`);
    });

    // 错误处理中间件
    context.middleware.use('error', (req, res, next) => {
      try {
        next();
      } catch (error) {
        console.error('中间件错误:', error);
        res.status = 500;
        res.body = { error: 'Internal Server Error' };
      }
    });
  }

  private validateToken(token: string): boolean {
    // 实现token验证逻辑
    return true;
  }

  private getUserFromToken(token: string): any {
    // 从token中提取用户信息
    return { id: '123', name: 'John' };
  }
}
```

### 4. 配置管理

插件可以提供配置选项和默认值：

```typescript
interface ConfigPluginOptions {
  apiUrl?: string;
  timeout?: number;
  retries?: number;
  enableCache?: boolean;
}

class ConfigPlugin implements Plugin {
  name = 'config-plugin';
  
  private config: Required<ConfigPluginOptions>;

  constructor(options: ConfigPluginOptions = {}) {
    // 合并默认配置
    this.config = {
      apiUrl: 'https://api.example.com',
      timeout: 5000,
      retries: 3,
      enableCache: true,
      ...options
    };
  }

  install(context: PluginContext): void {
    // 注册配置服务
    context.services.register('config', {
      get: (key: string) => this.config[key as keyof ConfigPluginOptions],
      set: (key: string, value: any) => {
        (this.config as any)[key] = value;
      },
      getAll: () => ({ ...this.config })
    });

    // 验证配置
    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.config.apiUrl) {
      throw new Error('API URL is required');
    }

    if (this.config.timeout <= 0) {
      throw new Error('Timeout must be positive');
    }
  }
}
```

## 高级插件示例

### 1. 性能监控插件

```typescript
class PerformanceMonitorPlugin implements Plugin {
  name = 'performance-monitor';
  
  private metrics = new Map<string, any>();
  private observers: PerformanceObserver[] = [];

  install(context: PluginContext): void {
    this.setupPerformanceObservers();
    this.registerHooks(context);
    this.registerServices(context);
  }

  private setupPerformanceObservers(): void {
    // 监控导航性能
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('navigation', {
          name: entry.name,
          duration: entry.duration,
          startTime: entry.startTime
        });
      }
    });
    navObserver.observe({ entryTypes: ['navigation'] });
    this.observers.push(navObserver);

    // 监控资源加载
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('resource', {
          name: entry.name,
          duration: entry.duration,
          size: (entry as any).transferSize
        });
      }
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);
  }

  private registerHooks(context: PluginContext): void {
    context.hooks.beforeApplicationLoad.tap(this.name, (app) => {
      this.startTimer(`app-load-${app.name}`);
    });

    context.hooks.afterApplicationLoad.tap(this.name, (app) => {
      const duration = this.endTimer(`app-load-${app.name}`);
      this.recordMetric('app-load', {
        name: app.name,
        duration
      });
    });

    context.hooks.beforeApplicationMount.tap(this.name, (app) => {
      this.startTimer(`app-mount-${app.name}`);
    });

    context.hooks.afterApplicationMount.tap(this.name, (app) => {
      const duration = this.endTimer(`app-mount-${app.name}`);
      this.recordMetric('app-mount', {
        name: app.name,
        duration
      });
    });
  }

  private registerServices(context: PluginContext): void {
    context.services.register('performance', {
      getMetrics: () => Array.from(this.metrics.entries()),
      getMetric: (type: string) => this.metrics.get(type),
      clearMetrics: () => this.metrics.clear(),
      exportMetrics: () => this.exportMetrics()
    });
  }

  private startTimer(name: string): void {
    performance.mark(`${name}-start`);
  }

  private endTimer(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name)[0];
    return measure.duration;
  }

  private recordMetric(type: string, data: any): void {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    this.metrics.get(type).push({
      ...data,
      timestamp: Date.now()
    });
  }

  private exportMetrics(): string {
    const data = {};
    for (const [type, metrics] of this.metrics.entries()) {
      data[type] = metrics;
    }
    return JSON.stringify(data, null, 2);
  }

  uninstall(): void {
    // 清理性能观察器
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    // 清理性能标记
    performance.clearMarks();
    performance.clearMeasures();
  }
}
```

### 2. 错误监控插件

```typescript
class ErrorMonitorPlugin implements Plugin {
  name = 'error-monitor';
  
  private errors: Array<{
    type: string;
    message: string;
    stack?: string;
    app?: string;
    timestamp: number;
  }> = [];

  install(context: PluginContext): void {
    this.setupGlobalErrorHandlers();
    this.registerHooks(context);
    this.registerServices(context);
  }

  private setupGlobalErrorHandlers(): void {
    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now()
      });
    });

    // 捕获Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        timestamp: Date.now()
      });
    });

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource',
          message: `Failed to load: ${(event.target as any)?.src || (event.target as any)?.href}`,
          timestamp: Date.now()
        });
      }
    }, true);
  }

  private registerHooks(context: PluginContext): void {
    context.hooks.applicationError.tap(this.name, (error, app) => {
      this.recordError({
        type: 'application',
        message: error.message,
        stack: error.stack,
        app: app?.name,
        timestamp: Date.now()
      });
    });
  }

  private registerServices(context: PluginContext): void {
    context.services.register('errorMonitor', {
      getErrors: () => [...this.errors],
      getErrorsByType: (type: string) => this.errors.filter(e => e.type === type),
      getErrorsByApp: (appName: string) => this.errors.filter(e => e.app === appName),
      clearErrors: () => { this.errors = []; },
      reportError: (error: any) => this.recordError(error)
    });
  }

  private recordError(error: any): void {
    this.errors.push(error);
    
    // 限制错误数量
    if (this.errors.length > 1000) {
      this.errors = this.errors.slice(-500);
    }

    // 发送到监控服务
    this.sendToMonitoringService(error);
  }

  private async sendToMonitoringService(error: any): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(error)
      });
    } catch (e) {
      console.warn('Failed to send error to monitoring service:', e);
    }
  }
}
```

## 插件测试

### 单元测试

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MicroCoreKernel } from '@micro-core/core';
import MyPlugin from '../src/MyPlugin';

describe('MyPlugin', () => {
  let kernel: MicroCoreKernel;
  let plugin: MyPlugin;

  beforeEach(() => {
    kernel = new MicroCoreKernel();
    plugin = new MyPlugin({ enabled: true });
  });

  it('should install successfully', () => {
    expect(() => {
      kernel.use(plugin);
    }).not.toThrow();
  });

  it('should register services', () => {
    kernel.use(plugin);
    
    const service = kernel.getService('myService');
    expect(service).toBeDefined();
    expect(typeof service.doSomething).toBe('function');
  });

  it('should handle lifecycle hooks', async () => {
    const mockApp = {
      name: 'test-app',
      entry: 'http://localhost:3000',
      container: '#app',
      activeWhen: '/test'
    };

    kernel.use(plugin);
    
    // 模拟应用注册
    kernel.registerApplication(mockApp);
    
    // 验证钩子是否被调用
    // 这里需要根据具体实现进行测试
  });
});
```

### 集成测试

```typescript
import { describe, it, expect } from 'vitest';
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import MyPlugin from '../src/MyPlugin';

describe('Plugin Integration', () => {
  it('should work with other plugins', () => {
    const kernel = new MicroCoreKernel();
    
    // 安装多个插件
    kernel
      .use(RouterPlugin, { mode: 'history' })
      .use(MyPlugin, { enabled: true });

    // 验证插件间的协作
    expect(kernel.getPlugin('router')).toBeDefined();
    expect(kernel.getPlugin('my-plugin')).toBeDefined();
  });
});
```

## 插件发布

### 1. 包结构

```
my-plugin/
├── src/
│   ├── index.ts
│   ├── plugin.ts
│   └── types.ts
├── dist/
├── tests/
├── package.json
├── README.md
└── tsconfig.json
```

### 2. package.json

```json
{
  "name": "@micro-core/plugin-my-plugin",
  "version": "1.0.0",
  "description": "My custom Micro-Core plugin",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist"
  ],
  "keywords": [
    "micro-core",
    "plugin",
    "microfrontend"
  ],
  "peerDependencies": {
    "@micro-core/core": "^1.0.0"
  },
  "devDependencies": {
    "@micro-core/core": "^1.0.0",
    "typescript": "^5.0.0"
  }
}
```

### 3. 文档

创建详细的README.md文档，包含：
- 插件功能介绍
- 安装和使用方法
- 配置选项说明
- API文档
- 示例代码
- 更新日志

## 最佳实践

1. **命名规范** - 使用清晰的插件名称和版本号
2. **错误处理** - 妥善处理插件内部错误，避免影响主应用
3. **资源清理** - 在uninstall时清理所有资源
4. **配置验证** - 验证插件配置的有效性
5. **文档完善** - 提供详细的使用文档和示例
6. **测试覆盖** - 编写完整的单元测试和集成测试
7. **性能考虑** - 避免插件影响应用性能
8. **兼容性** - 确保插件与不同版本的Micro-Core兼容
