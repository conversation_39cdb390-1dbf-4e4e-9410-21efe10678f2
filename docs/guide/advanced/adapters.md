# 多框架适配

Micro-Core 提供了完善的多框架适配系统，支持 React、Vue、Angular、Svelte 等主流前端框架，实现真正的技术栈无关的微前端架构。

## 适配器概览

### 内置适配器

Micro-Core 提供了多个官方适配器：

```typescript
// React 适配器
import { ReactAdapter } from '@micro-core/adapter-react';

// Vue 适配器
import { VueAdapter } from '@micro-core/adapter-vue3';
import { Vue2Adapter } from '@micro-core/adapter-vue2';

// Angular 适配器
import { AngularAdapter } from '@micro-core/adapter-angular';

// Svelte 适配器
import { SvelteAdapter } from '@micro-core/adapter-svelte';

// Solid 适配器
import { SolidAdapter } from '@micro-core/adapter-solid';

// 原生 JavaScript 适配器
import { VanillaAdapter } from '@micro-core/adapter-vanilla';
```

### 适配器作用

1. **生命周期标准化** - 将不同框架的生命周期统一为标准接口
2. **Props 传递** - 处理主应用向微应用传递的属性
3. **错误边界** - 提供框架特定的错误处理机制
4. **性能优化** - 针对不同框架的性能优化策略
5. **开发体验** - 提供框架特定的开发工具和调试支持

## React 适配器

### 基础使用

```typescript
// src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactAdapter } from '@micro-core/adapter-react';
import App from './App';

let root: ReactDOM.Root | null = null;

export const mount = ReactAdapter.mount(async (props) => {
  const { container, ...appProps } = props;
  
  root = ReactDOM.createRoot(container);
  root.render(<App {...appProps} />);
  
  return { root, App };
});

export const unmount = ReactAdapter.unmount(async () => {
  if (root) {
    root.unmount();
    root = null;
  }
});

export const update = ReactAdapter.update(async (props) => {
  if (root) {
    const { container, ...appProps } = props;
    root.render(<App {...appProps} />);
  }
});

// 独立运行模式
if (!window.__MICRO_CORE__) {
  root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);
}
```

### 高级特性

```typescript
// 带错误边界的 React 适配器
import { ReactAdapter, ErrorBoundary } from '@micro-core/adapter-react';

export const mount = ReactAdapter.mount(async (props) => {
  const { container, onError, ...appProps } = props;
  
  root = ReactDOM.createRoot(container);
  root.render(
    <ErrorBoundary onError={onError}>
      <App {...appProps} />
    </ErrorBoundary>
  );
  
  return { root, App };
});

// 使用 React 18 并发特性
export const mount = ReactAdapter.mount(async (props) => {
  const { container, ...appProps } = props;
  
  root = ReactDOM.createRoot(container, {
    // 启用并发特性
    unstable_concurrentUpdatesByDefault: true
  });
  
  // 使用 startTransition 优化渲染
  React.startTransition(() => {
    root.render(<App {...appProps} />);
  });
  
  return { root, App };
});
```

### React Hooks 集成

```typescript
// useMicroCore Hook
import { useEffect, useState } from 'react';
import { EventBus, GlobalState } from '@micro-core/core';

export function useMicroCore() {
  const [isInMicroCore] = useState(() => !!window.__MICRO_CORE__);
  
  return {
    isInMicroCore,
    eventBus: isInMicroCore ? EventBus : null,
    globalState: isInMicroCore ? GlobalState : null
  };
}

// useGlobalState Hook
export function useGlobalState<T>(key: string, defaultValue?: T) {
  const [state, setState] = useState<T>(() => {
    return GlobalState?.get(key) ?? defaultValue;
  });

  useEffect(() => {
    if (!GlobalState) return;

    const unwatch = GlobalState.watch(key, (newValue: T) => {
      setState(newValue);
    });

    return unwatch;
  }, [key]);

  const updateState = (value: T) => {
    if (GlobalState) {
      GlobalState.set(key, value);
    } else {
      setState(value);
    }
  };

  return [state, updateState] as const;
}

// 在组件中使用
function MyComponent() {
  const { isInMicroCore, eventBus } = useMicroCore();
  const [theme, setTheme] = useGlobalState('theme', 'light');

  useEffect(() => {
    if (eventBus) {
      eventBus.on('theme-change', setTheme);
      return () => eventBus.off('theme-change', setTheme);
    }
  }, [eventBus, setTheme]);

  return (
    <div className={`theme-${theme}`}>
      {isInMicroCore ? '运行在微前端环境' : '独立运行'}
    </div>
  );
}
```

## Vue 适配器

### Vue 3 适配器

```typescript
// src/main.ts
import { createApp, App as VueApp } from 'vue';
import { VueAdapter } from '@micro-core/adapter-vue3';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';

let app: VueApp | null = null;

export const mount = VueAdapter.mount(async (props) => {
  const { container, basename, ...appProps } = props;
  
  app = createApp(App);
  
  // 配置路由基础路径
  if (basename && router.options.history) {
    router.options.history.base = basename;
  }
  
  // 注入 props
  app.provide('microProps', appProps);
  
  // 使用插件
  app.use(router);
  app.use(createPinia());
  
  app.mount(container);
  
  return { app, router };
});

export const unmount = VueAdapter.unmount(async () => {
  if (app) {
    app.unmount();
    app = null;
  }
});

export const update = VueAdapter.update(async (props) => {
  if (app) {
    // 更新注入的 props
    app.provide('microProps', props);
  }
});
```

### Vue Composition API 集成

```typescript
// composables/useMicroCore.ts
import { ref, onMounted, onUnmounted, inject } from 'vue';
import { EventBus, GlobalState } from '@micro-core/core';

export function useMicroCore() {
  const isInMicroCore = ref(!!window.__MICRO_CORE__);
  const microProps = inject('microProps', {});

  return {
    isInMicroCore,
    microProps,
    eventBus: isInMicroCore.value ? EventBus : null,
    globalState: isInMicroCore.value ? GlobalState : null
  };
}

export function useGlobalState<T>(key: string, defaultValue?: T) {
  const state = ref<T>(defaultValue as T);
  const { globalState } = useMicroCore();

  onMounted(() => {
    if (globalState) {
      const currentValue = globalState.get(key);
      if (currentValue !== undefined) {
        state.value = currentValue;
      }

      const unwatch = globalState.watch(key, (newValue: T) => {
        state.value = newValue;
      });

      onUnmounted(() => {
        unwatch();
      });
    }
  });

  const updateState = (newValue: T) => {
    if (globalState) {
      globalState.set(key, newValue);
    } else {
      state.value = newValue;
    }
  };

  return {
    state: readonly(state),
    updateState
  };
}
```

### Vue 2 适配器

```javascript
// src/main.js
import Vue from 'vue';
import { Vue2Adapter } from '@micro-core/adapter-vue2';
import App from './App.vue';
import router from './router';
import store from './store';

let instance = null;

export const mount = Vue2Adapter.mount(async (props) => {
  const { container, basename, ...appProps } = props;
  
  // 配置路由基础路径
  if (basename) {
    router.options.base = basename;
  }
  
  instance = new Vue({
    router,
    store,
    data() {
      return {
        microProps: appProps
      };
    },
    render: h => h(App)
  }).$mount(container.querySelector('#app') || container);
  
  return instance;
});

export const unmount = Vue2Adapter.unmount(async () => {
  if (instance) {
    instance.$destroy();
    instance.$el.innerHTML = '';
    instance = null;
  }
});
```

## Angular 适配器

### 基础配置

```typescript
// src/main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { NgModuleRef } from '@angular/core';
import { AngularAdapter } from '@micro-core/adapter-angular';
import { AppModule } from './app/app.module';

let ngModuleRef: NgModuleRef<any> | null = null;

export const mount = AngularAdapter.mount(async (props) => {
  const { container, basename, ...appProps } = props;
  
  // 设置全局属性
  (window as any).__MICRO_PROPS__ = appProps;
  (window as any).__MICRO_BASENAME__ = basename;
  
  ngModuleRef = await platformBrowserDynamic()
    .bootstrapModule(AppModule)
    .catch(err => {
      console.error('Angular 应用启动失败:', err);
      throw err;
    });
  
  return ngModuleRef;
});

export const unmount = AngularAdapter.unmount(async () => {
  if (ngModuleRef) {
    ngModuleRef.destroy();
    ngModuleRef = null;
  }
  
  delete (window as any).__MICRO_PROPS__;
  delete (window as any).__MICRO_BASENAME__;
});
```

### Angular 服务集成

```typescript
// src/app/services/micro-core.service.ts
import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { EventBus, GlobalState } from '@micro-core/core';

@Injectable({
  providedIn: 'root'
})
export class MicroCoreService {
  private isInMicroCore = !!window.__MICRO_CORE__;
  private eventSubjects = new Map<string, Subject<any>>();
  private stateSubjects = new Map<string, BehaviorSubject<any>>();

  // 获取微应用属性
  getMicroProps(): any {
    return (window as any).__MICRO_PROPS__ || {};
  }

  // 事件通信
  emitEvent(event: string, data?: any): void {
    if (this.isInMicroCore && EventBus) {
      EventBus.emit(event, data);
    }
  }

  onEvent(event: string): Observable<any> {
    if (!this.eventSubjects.has(event)) {
      const subject = new Subject<any>();
      this.eventSubjects.set(event, subject);

      if (this.isInMicroCore && EventBus) {
        EventBus.on(event, (data: any) => {
          subject.next(data);
        });
      }
    }

    return this.eventSubjects.get(event)!.asObservable();
  }

  // 全局状态管理
  setGlobalState(key: string, value: any): void {
    if (this.isInMicroCore && GlobalState) {
      GlobalState.set(key, value);
    }
  }

  getGlobalState(key: string): any {
    if (this.isInMicroCore && GlobalState) {
      return GlobalState.get(key);
    }
    return null;
  }

  watchGlobalState(key: string): Observable<any> {
    if (!this.stateSubjects.has(key)) {
      const currentValue = this.getGlobalState(key);
      const subject = new BehaviorSubject<any>(currentValue);
      this.stateSubjects.set(key, subject);

      if (this.isInMicroCore && GlobalState) {
        GlobalState.watch(key, (newValue: any) => {
          subject.next(newValue);
        });
      }
    }

    return this.stateSubjects.get(key)!.asObservable();
  }
}
```

## Svelte 适配器

### 基础使用

```typescript
// src/main.ts
import { SvelteAdapter } from '@micro-core/adapter-svelte';
import App from './App.svelte';

let app: any = null;

export const mount = SvelteAdapter.mount(async (props) => {
  const { container, ...appProps } = props;
  
  app = new App({
    target: container,
    props: appProps
  });
  
  return app;
});

export const unmount = SvelteAdapter.unmount(async () => {
  if (app) {
    app.$destroy();
    app = null;
  }
});

export const update = SvelteAdapter.update(async (props) => {
  if (app) {
    app.$set(props);
  }
});

// 独立运行模式
if (!window.__MICRO_CORE__) {
  app = new App({
    target: document.body
  });
}
```

### Svelte Store 集成

```typescript
// src/stores/microCore.ts
import { writable, derived } from 'svelte/store';
import { EventBus, GlobalState } from '@micro-core/core';

// 检查是否在微前端环境中
export const isInMicroCore = writable(!!window.__MICRO_CORE__);

// 创建全局状态 store
export function createGlobalStateStore<T>(key: string, defaultValue?: T) {
  const { subscribe, set, update } = writable<T>(defaultValue as T);

  // 如果在微前端环境中，同步全局状态
  if (window.__MICRO_CORE__ && GlobalState) {
    const currentValue = GlobalState.get(key);
    if (currentValue !== undefined) {
      set(currentValue);
    }

    GlobalState.watch(key, (newValue: T) => {
      set(newValue);
    });
  }

  return {
    subscribe,
    set: (value: T) => {
      if (window.__MICRO_CORE__ && GlobalState) {
        GlobalState.set(key, value);
      } else {
        set(value);
      }
    },
    update: (updater: (value: T) => T) => {
      update((currentValue) => {
        const newValue = updater(currentValue);
        if (window.__MICRO_CORE__ && GlobalState) {
          GlobalState.set(key, newValue);
        }
        return newValue;
      });
    }
  };
}

// 使用示例
export const theme = createGlobalStateStore('theme', 'light');
export const user = createGlobalStateStore('currentUser', null);
```

## 自定义适配器

### 创建适配器

```typescript
import { Adapter, AdapterContext, LifecycleFunction } from '@micro-core/core';

interface CustomFrameworkInstance {
  mount: (container: HTMLElement, props: any) => void;
  unmount: () => void;
  update: (props: any) => void;
}

class CustomFrameworkAdapter implements Adapter {
  name = 'custom-framework';
  version = '1.0.0';

  // 包装 mount 生命周期
  mount<T = any>(mountFn: LifecycleFunction<T>): LifecycleFunction<T> {
    return async (props) => {
      try {
        console.log(`[${this.name}] 开始挂载应用`);
        
        // 预处理 props
        const processedProps = this.preprocessProps(props);
        
        // 调用原始 mount 函数
        const result = await mountFn(processedProps);
        
        // 后处理
        this.postMount(result, props);
        
        console.log(`[${this.name}] 应用挂载完成`);
        return result;
      } catch (error) {
        console.error(`[${this.name}] 应用挂载失败:`, error);
        throw error;
      }
    };
  }

  // 包装 unmount 生命周期
  unmount(unmountFn: LifecycleFunction<void>): LifecycleFunction<void> {
    return async () => {
      try {
        console.log(`[${this.name}] 开始卸载应用`);
        
        // 预清理
        this.preUnmount();
        
        // 调用原始 unmount 函数
        await unmountFn();
        
        // 后清理
        this.postUnmount();
        
        console.log(`[${this.name}] 应用卸载完成`);
      } catch (error) {
        console.error(`[${this.name}] 应用卸载失败:`, error);
        throw error;
      }
    };
  }

  // 包装 update 生命周期
  update<T = any>(updateFn: LifecycleFunction<T>): LifecycleFunction<T> {
    return async (props) => {
      try {
        console.log(`[${this.name}] 开始更新应用`);
        
        // 预处理 props
        const processedProps = this.preprocessProps(props);
        
        // 调用原始 update 函数
        const result = await updateFn(processedProps);
        
        console.log(`[${this.name}] 应用更新完成`);
        return result;
      } catch (error) {
        console.error(`[${this.name}] 应用更新失败:`, error);
        throw error;
      }
    };
  }

  private preprocessProps(props: any): any {
    // 处理 props，添加框架特定的属性
    return {
      ...props,
      frameworkVersion: this.version,
      adapterName: this.name
    };
  }

  private postMount(result: any, props: any): void {
    // 挂载后的处理逻辑
    if (result && typeof result.onMounted === 'function') {
      result.onMounted();
    }
  }

  private preUnmount(): void {
    // 卸载前的清理逻辑
    this.cleanup();
  }

  private postUnmount(): void {
    // 卸载后的清理逻辑
  }

  private cleanup(): void {
    // 清理框架特定的资源
  }
}

export default CustomFrameworkAdapter;
```

### 使用自定义适配器

```typescript
// 在微应用中使用
import CustomFrameworkAdapter from './adapters/CustomFrameworkAdapter';
import { createCustomApp } from 'custom-framework';

const adapter = new CustomFrameworkAdapter();
let appInstance: any = null;

export const mount = adapter.mount(async (props) => {
  const { container, ...appProps } = props;
  
  appInstance = createCustomApp({
    container,
    props: appProps
  });
  
  appInstance.mount();
  
  return appInstance;
});

export const unmount = adapter.unmount(async () => {
  if (appInstance) {
    appInstance.unmount();
    appInstance = null;
  }
});

export const update = adapter.update(async (props) => {
  if (appInstance) {
    appInstance.updateProps(props);
  }
});
```

## 适配器最佳实践

### 1. 错误处理

```typescript
class RobustAdapter implements Adapter {
  mount<T>(mountFn: LifecycleFunction<T>): LifecycleFunction<T> {
    return async (props) => {
      try {
        return await mountFn(props);
      } catch (error) {
        // 记录错误
        this.logError('mount', error);
        
        // 尝试恢复
        const fallbackResult = await this.fallbackMount(props);
        
        // 通知错误监控
        this.notifyErrorMonitoring(error);
        
        return fallbackResult;
      }
    };
  }

  private async fallbackMount(props: any): Promise<any> {
    // 实现降级挂载逻辑
    const container = props.container;
    container.innerHTML = '<div>应用加载失败，请刷新页面重试</div>';
    
    return {
      isFallback: true,
      container
    };
  }

  private logError(phase: string, error: Error): void {
    console.error(`[Adapter] ${phase} 阶段发生错误:`, error);
  }

  private notifyErrorMonitoring(error: Error): void {
    // 发送错误到监控系统
    if (window.__ERROR_MONITOR__) {
      window.__ERROR_MONITOR__.reportError(error);
    }
  }
}
```

### 2. 性能优化

```typescript
class PerformantAdapter implements Adapter {
  private performanceMarks = new Map<string, number>();

  mount<T>(mountFn: LifecycleFunction<T>): LifecycleFunction<T> {
    return async (props) => {
      // 开始性能测量
      this.startPerformanceMark('mount');
      
      try {
        const result = await mountFn(props);
        
        // 结束性能测量
        const duration = this.endPerformanceMark('mount');
        
        // 记录性能指标
        this.recordPerformance('mount', duration);
        
        return result;
      } catch (error) {
        this.endPerformanceMark('mount');
        throw error;
      }
    };
  }

  private startPerformanceMark(name: string): void {
    this.performanceMarks.set(name, performance.now());
  }

  private endPerformanceMark(name: string): number {
    const startTime = this.performanceMarks.get(name);
    if (!startTime) return 0;
    
    const duration = performance.now() - startTime;
    this.performanceMarks.delete(name);
    
    return duration;
  }

  private recordPerformance(phase: string, duration: number): void {
    // 记录到性能监控系统
    if (window.__PERFORMANCE_MONITOR__) {
      window.__PERFORMANCE_MONITOR__.record({
        type: 'adapter',
        phase,
        duration,
        timestamp: Date.now()
      });
    }
  }
}
```

### 3. 开发体验优化

```typescript
class DevFriendlyAdapter implements Adapter {
  private isDevelopment = process.env.NODE_ENV === 'development';

  mount<T>(mountFn: LifecycleFunction<T>): LifecycleFunction<T> {
    return async (props) => {
      if (this.isDevelopment) {
        // 开发模式下的额外检查
        this.validateProps(props);
        this.checkDependencies();
      }
      
      const result = await mountFn(props);
      
      if (this.isDevelopment) {
        // 开发模式下的调试信息
        this.logDebugInfo(props, result);
      }
      
      return result;
    };
  }

  private validateProps(props: any): void {
    if (!props.container) {
      console.warn('[Adapter] 缺少 container 属性');
    }
    
    if (typeof props.container === 'string') {
      const element = document.querySelector(props.container);
      if (!element) {
        console.warn(`[Adapter] 找不到容器元素: ${props.container}`);
      }
    }
  }

  private checkDependencies(): void {
    // 检查必要的依赖是否存在
    const requiredGlobals = ['React', 'ReactDOM'];
    
    for (const global of requiredGlobals) {
      if (!(global in window)) {
        console.warn(`[Adapter] 缺少全局依赖: ${global}`);
      }
    }
  }

  private logDebugInfo(props: any, result: any): void {
    console.group('[Adapter] 挂载信息');
    console.log('Props:', props);
    console.log('Result:', result);
    console.log('Container:', props.container);
    console.groupEnd();
  }
}
```

## 总结

多框架适配系统是 Micro-Core 的核心特性之一，它：

1. **统一接口** - 为不同框架提供统一的生命周期接口
2. **框架特定优化** - 针对每个框架的特点进行优化
3. **开发体验** - 提供框架特定的开发工具和调试支持
4. **错误处理** - 框架级别的错误边界和恢复机制
5. **性能监控** - 框架特定的性能监控和优化
6. **扩展性** - 支持自定义适配器扩展新框架

通过适配器系统，开发者可以：
- 使用熟悉的框架进行微应用开发
- 享受框架特定的开发体验
- 获得统一的微前端能力
- 实现真正的技术栈无关架构
