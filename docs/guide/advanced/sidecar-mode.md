# Sidecar 边车模式

Sidecar 模式是 Micro-Core 的创新特性，允许开发者通过一行代码将现有应用快速接入微前端架构，实现零配置的渐进式迁移。

## 🎯 什么是 Sidecar 模式

Sidecar 模式借鉴了微服务架构中的边车模式概念，通过在应用旁边运行一个轻量级的代理服务，自动处理微前端相关的复杂配置和集成逻辑。

### 核心特性

- **一行代码接入** - 无需复杂配置，一行代码即可启用微前端
- **零配置启动** - 自动检测应用类型和配置
- **渐进式迁移** - 支持逐步迁移现有应用
- **智能代理** - 自动处理路由、通信、状态管理
- **热插拔** - 支持运行时动态启用/禁用

## 🚀 快速开始

### 安装 Sidecar

```bash
npm install @micro-core/sidecar
```

### 一行代码接入

```typescript
// 在应用入口文件中添加一行代码
import '@micro-core/sidecar/auto';

// 或者手动初始化
import { enableSidecar } from '@micro-core/sidecar';

enableSidecar();
```

### 自动检测和配置

Sidecar 会自动检测应用环境并进行配置：

```typescript
// 自动检测结果示例
{
  framework: 'react',        // 检测到的框架
  version: '18.2.0',        // 框架版本
  buildTool: 'vite',        // 构建工具
  router: 'react-router',   // 路由库
  stateManager: 'redux',    // 状态管理库
  entry: './src/main.tsx',  // 入口文件
  port: 3000               // 开发服务器端口
}
```

## ⚙️ 配置选项

### 基础配置

```typescript
// sidecar.config.ts
import { defineSidecarConfig } from '@micro-core/sidecar';

export default defineSidecarConfig({
  // 应用基础信息
  app: {
    name: 'my-app',
    version: '1.0.0',
    description: '我的微前端应用'
  },
  
  // 自动检测配置
  detection: {
    // 是否启用自动检测
    enabled: true,
    
    // 检测超时时间
    timeout: 5000,
    
    // 自定义检测规则
    rules: {
      framework: {
        react: /react/i,
        vue: /vue/i,
        angular: /@angular/i
      }
    }
  },
  
  // 代理配置
  proxy: {
    // 是否启用代理
    enabled: true,
    
    // 代理端口
    port: 'auto', // 'auto' | number
    
    // 代理规则
    rules: [
      {
        path: '/api/*',
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    ]
  }
});
```

### 高级配置

```typescript
// sidecar.config.ts
export default defineSidecarConfig({
  // 微前端配置
  microFrontend: {
    // 主应用配置
    main: {
      url: 'http://localhost:3000',
      container: '#micro-container'
    },
    
    // 路由配置
    routing: {
      mode: 'history', // 'history' | 'hash'
      base: '/my-app',
      
      // 路由守卫
      guards: {
        beforeEnter: (to, from, next) => {
          // 路由进入前的逻辑
          console.log('进入路由:', to.path);
          next();
        }
      }
    },
    
    // 通信配置
    communication: {
      // 事件总线
      eventBus: {
        enabled: true,
        namespace: 'my-app'
      },
      
      // 全局状态
      globalState: {
        enabled: true,
        initialState: {
          theme: 'light',
          user: null
        }
      }
    },
    
    // 沙箱配置
    sandbox: {
      type: 'proxy', // 'proxy' | 'iframe' | 'webcomponent'
      
      // 样式隔离
      styleIsolation: true,
      
      // 脚本隔离
      scriptIsolation: true
    }
  },
  
  // 开发配置
  development: {
    // 热更新
    hmr: {
      enabled: true,
      port: 3001
    },
    
    // 调试工具
    devtools: {
      enabled: true,
      panel: true
    },
    
    // 错误处理
    errorHandling: {
      showOverlay: true,
      logLevel: 'debug'
    }
  },
  
  // 生产配置
  production: {
    // 性能优化
    optimization: {
      // 代码分割
      codeSplitting: true,
      
      // 资源压缩
      minify: true,
      
      // Tree shaking
      treeShaking: true
    },
    
    // 监控配置
    monitoring: {
      enabled: true,
      endpoint: 'https://monitoring.example.com'
    }
  }
});
```

## 🔧 自动检测机制

### 框架检测

```typescript
// 框架检测逻辑
class FrameworkDetector {
  detect(): FrameworkInfo {
    // React 检测
    if (this.hasReact()) {
      return {
        name: 'react',
        version: this.getReactVersion(),
        adapter: '@micro-core/adapter-react'
      };
    }
    
    // Vue 检测
    if (this.hasVue()) {
      return {
        name: 'vue',
        version: this.getVueVersion(),
        adapter: this.getVueVersion().startsWith('3') 
          ? '@micro-core/adapter-vue3'
          : '@micro-core/adapter-vue2'
      };
    }
    
    // Angular 检测
    if (this.hasAngular()) {
      return {
        name: 'angular',
        version: this.getAngularVersion(),
        adapter: '@micro-core/adapter-angular'
      };
    }
    
    // 默认为原生 JavaScript
    return {
      name: 'vanilla',
      version: '1.0.0',
      adapter: '@micro-core/adapter-vanilla'
    };
  }
  
  private hasReact(): boolean {
    return !!(window as any).React || 
           this.hasPackage('react') ||
           this.hasImport(/from ['"]react['"]/);
  }
  
  private hasVue(): boolean {
    return !!(window as any).Vue ||
           this.hasPackage('vue') ||
           this.hasImport(/from ['"]vue['"]/);
  }
  
  private hasAngular(): boolean {
    return !!(window as any).ng ||
           this.hasPackage('@angular/core') ||
           this.hasImport(/from ['"]@angular/);
  }
}
```

### 构建工具检测

```typescript
// 构建工具检测
class BuildToolDetector {
  detect(): BuildToolInfo {
    // Vite 检测
    if (this.hasViteConfig()) {
      return {
        name: 'vite',
        version: this.getViteVersion(),
        configFile: this.findViteConfig()
      };
    }
    
    // Webpack 检测
    if (this.hasWebpackConfig()) {
      return {
        name: 'webpack',
        version: this.getWebpackVersion(),
        configFile: this.findWebpackConfig()
      };
    }
    
    // Create React App 检测
    if (this.isCRA()) {
      return {
        name: 'create-react-app',
        version: this.getCRAVersion(),
        configFile: null
      };
    }
    
    return {
      name: 'unknown',
      version: '0.0.0',
      configFile: null
    };
  }
  
  private hasViteConfig(): boolean {
    return this.fileExists('vite.config.ts') ||
           this.fileExists('vite.config.js') ||
           this.hasPackage('vite');
  }
  
  private hasWebpackConfig(): boolean {
    return this.fileExists('webpack.config.js') ||
           this.fileExists('webpack.config.ts') ||
           this.hasPackage('webpack');
  }
  
  private isCRA(): boolean {
    return this.hasPackage('react-scripts');
  }
}
```

## 🎮 智能代理服务

### 代理服务器

```typescript
// Sidecar 代理服务器
class SidecarProxy {
  private app: Express;
  private config: SidecarConfig;
  
  constructor(config: SidecarConfig) {
    this.config = config;
    this.app = express();
    this.setupMiddleware();
  }
  
  private setupMiddleware(): void {
    // CORS 中间件
    this.app.use(cors({
      origin: this.config.proxy.allowedOrigins,
      credentials: true
    }));
    
    // 静态资源代理
    this.app.use('/assets', express.static('dist/assets'));
    
    // API 代理
    this.config.proxy.rules.forEach(rule => {
      this.app.use(rule.path, createProxyMiddleware({
        target: rule.target,
        changeOrigin: rule.changeOrigin,
        pathRewrite: rule.pathRewrite
      }));
    });
    
    // 微前端注入中间件
    this.app.use(this.injectMicroFrontendScript.bind(this));
    
    // 错误处理中间件
    this.app.use(this.errorHandler.bind(this));
  }
  
  private injectMicroFrontendScript(req: Request, res: Response, next: NextFunction): void {
    // 检查是否为 HTML 请求
    if (req.accepts('html')) {
      // 注入微前端初始化脚本
      const originalSend = res.send;
      res.send = function(body: any) {
        if (typeof body === 'string' && body.includes('</head>')) {
          const script = `
            <script>
              window.__MICRO_CORE_SIDECAR__ = true;
              window.__MICRO_CORE_CONFIG__ = ${JSON.stringify(this.config)};
            </script>
            <script src="/@micro-core/sidecar/runtime.js"></script>
          `;
          body = body.replace('</head>', `${script}</head>`);
        }
        return originalSend.call(this, body);
      }.bind(this);
    }
    
    next();
  }
  
  start(): Promise<void> {
    return new Promise((resolve) => {
      const port = this.config.proxy.port === 'auto' 
        ? this.findAvailablePort() 
        : this.config.proxy.port;
        
      this.app.listen(port, () => {
        console.log(`🚀 Sidecar 代理服务器启动在端口 ${port}`);
        resolve();
      });
    });
  }
}
```

### 运行时注入

```typescript
// 运行时脚本注入
class RuntimeInjector {
  inject(): void {
    // 检查是否已经注入
    if (window.__MICRO_CORE_INJECTED__) {
      return;
    }
    
    // 标记已注入
    window.__MICRO_CORE_INJECTED__ = true;
    
    // 获取配置
    const config = window.__MICRO_CORE_CONFIG__;
    
    // 初始化微前端运行时
    this.initializeMicroFrontend(config);
    
    // 设置通信桥梁
    this.setupCommunicationBridge(config);
    
    // 启动路由监听
    this.startRouteListener(config);
    
    // 注入开发工具
    if (config.development?.devtools?.enabled) {
      this.injectDevtools();
    }
  }
  
  private initializeMicroFrontend(config: SidecarConfig): void {
    // 动态加载微前端核心库
    const script = document.createElement('script');
    script.src = '/@micro-core/core/runtime.js';
    script.onload = () => {
      // 初始化微前端应用
      window.__MICRO_CORE__.init(config);
    };
    document.head.appendChild(script);
  }
  
  private setupCommunicationBridge(config: SidecarConfig): void {
    // 设置事件总线
    if (config.microFrontend.communication.eventBus.enabled) {
      window.__MICRO_CORE_EVENT_BUS__ = new EventBus();
    }
    
    // 设置全局状态
    if (config.microFrontend.communication.globalState.enabled) {
      window.__MICRO_CORE_GLOBAL_STATE__ = new GlobalState(
        config.microFrontend.communication.globalState.initialState
      );
    }
  }
}
```

## 🔄 渐进式迁移

### 迁移策略

```typescript
// 渐进式迁移配置
const migrationConfig = {
  // 迁移阶段
  phases: [
    {
      name: 'preparation',
      description: '准备阶段 - 安装 Sidecar',
      tasks: [
        'install-sidecar',
        'detect-environment',
        'generate-config'
      ]
    },
    {
      name: 'integration',
      description: '集成阶段 - 启用微前端功能',
      tasks: [
        'enable-routing',
        'setup-communication',
        'configure-sandbox'
      ]
    },
    {
      name: 'optimization',
      description: '优化阶段 - 性能和体验优化',
      tasks: [
        'code-splitting',
        'lazy-loading',
        'performance-monitoring'
      ]
    }
  ],
  
  // 回滚策略
  rollback: {
    enabled: true,
    backupConfig: true,
    restorePoints: ['before-integration', 'before-optimization']
  }
};
```

### 兼容性检查

```typescript
// 兼容性检查器
class CompatibilityChecker {
  check(): CompatibilityReport {
    const report: CompatibilityReport = {
      compatible: true,
      warnings: [],
      errors: [],
      suggestions: []
    };
    
    // 检查浏览器兼容性
    this.checkBrowserCompatibility(report);
    
    // 检查框架版本兼容性
    this.checkFrameworkCompatibility(report);
    
    // 检查依赖冲突
    this.checkDependencyConflicts(report);
    
    // 检查构建工具兼容性
    this.checkBuildToolCompatibility(report);
    
    return report;
  }
  
  private checkBrowserCompatibility(report: CompatibilityReport): void {
    // 检查 ES6 支持
    if (!this.supportsES6()) {
      report.errors.push('浏览器不支持 ES6，请升级浏览器');
      report.compatible = false;
    }
    
    // 检查 Proxy 支持
    if (!this.supportsProxy()) {
      report.warnings.push('浏览器不支持 Proxy，将使用兼容性沙箱');
    }
  }
}
```

Sidecar 边车模式为 Micro-Core 提供了革命性的接入体验，通过智能检测和自动配置，让微前端架构的采用变得前所未有的简单。
