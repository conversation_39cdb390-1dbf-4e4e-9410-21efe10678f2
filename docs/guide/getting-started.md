# 快速开始

本指南将帮助你在 5 分钟内快速体验 Micro-Core 的核心功能，构建你的第一个微前端应用。

## 环境要求

在开始之前，请确保你的开发环境满足以下要求：

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0 (推荐) 或 npm >= 9.0.0
- **现代浏览器** Chrome 80+, Firefox 75+, Safari 13+

## 快速安装

### 使用 pnpm (推荐)

```bash
pnpm add @micro-core/core
```

### 使用 npm

```bash
npm install @micro-core/core
```

## 方式一：Sidecar 模式 (零配置)

最简单的方式是使用 Sidecar 模式，只需一行代码即可接入：

```html
<!DOCTYPE html>
<html>
<head>
  <title>Micro-Core 主应用</title>
</head>
<body>
  <div id="app"></div>
  
  <!-- 引入 Sidecar -->
  <script src="https://unpkg.com/@micro-core/sidecar@latest/dist/index.min.js"></script>
  <script>
    window.MicroCoreSidecar.init({
      apps: [
        {
          name: 'react-app',
          entry: 'http://localhost:3001',
          container: '#react-app-container',
          activeWhen: '/react-app'
        },
        {
          name: 'vue-app', 
          entry: 'http://localhost:3002',
          container: '#vue-app-container',
          activeWhen: '/vue-app'
        }
      ]
    });
  </script>
  
  <div id="react-app-container"></div>
  <div id="vue-app-container"></div>
</body>
</html>
```

## 方式二：标准模式

### 1. 创建主应用

```typescript
// main.ts
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { CommunicationPlugin } from '@micro-core/plugin-communication';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 注册插件
kernel.use(RouterPlugin);
kernel.use(CommunicationPlugin);
kernel.use(ProxySandboxPlugin);

// 注册微应用
kernel.registerApplication({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-app-container',
  activeWhen: '/react-app',
  sandbox: 'proxy'
});

kernel.registerApplication({
  name: 'vue-app',
  entry: 'http://localhost:3002', 
  container: '#vue-app-container',
  activeWhen: '/vue-app',
  sandbox: 'proxy'
});

// 启动微前端
kernel.start();
```

## 下一步

🎉 恭喜！你已经成功创建了第一个 Micro-Core 应用。接下来你可以：

- 📚 了解 [核心概念](./concepts.md) - 深入理解架构设计
- �� 查看 [安装配置](./installation.md) - 详细的配置选项
- 💡 学习 [最佳实践](./best-practices/) - 生产环境指南
- 🚀 探索 [高级特性](./advanced/) - 插件系统和扩展能力
- 📖 查看 [完整示例](../examples/) - 更多实际案例
- 🔄 了解 [迁移指南](../migration/) - 从其他框架迁移
