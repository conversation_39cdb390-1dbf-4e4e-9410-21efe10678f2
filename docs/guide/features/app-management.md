# 应用管理

应用管理是 micro-core 的核心功能，负责微应用的注册、加载、挂载、卸载等完整生命周期管理。

## 应用注册

### 基础注册

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore();

// 注册微应用
microCore.registerApp({
  name: 'user-management',           // 应用名称（必须唯一）
  entry: 'http://localhost:3001/index.js', // 应用入口
  container: '#user-container',      // 挂载容器
  activeWhen: '/users',             // 激活条件
  framework: 'react'                // 框架类型
});
```

### 高级配置

```typescript
microCore.registerApp({
  name: 'order-system',
  entry: 'http://localhost:3002/index.js',
  container: '#order-container',
  activeWhen: ['/orders', '/order-detail/:id'],
  framework: 'vue',
  
  // 应用属性
  props: {
    apiBaseUrl: 'https://api.example.com',
    theme: 'dark'
  },
  
  // 沙箱配置
  sandbox: {
    enabled: true,
    strictStyleIsolation: true,
    experimentalStyleIsolation: true
  },
  
  // 预加载配置
  prefetch: {
    enabled: true,
    timing: 'idle',
    priority: 'high'
  },
  
  // 错误处理
  errorHandler: (error) => {
    console.error('应用错误:', error);
    // 自定义错误处理逻辑
  },
  
  // 生命周期钩子
  beforeMount: (app) => {
    console.log('应用即将挂载:', app.name);
  },
  
  afterMount: (app) => {
    console.log('应用挂载完成:', app.name);
  },
  
  beforeUnmount: (app) => {
    console.log('应用即将卸载:', app.name);
  },
  
  afterUnmount: (app) => {
    console.log('应用卸载完成:', app.name);
  }
});
```

## 激活条件

### 路径匹配

```typescript
// 精确匹配
activeWhen: '/users'

// 前缀匹配
activeWhen: '/users/*'

// 多路径匹配
activeWhen: ['/users', '/user-profile', '/user-settings']

// 正则表达式
activeWhen: /^\/users?(\/.*)?$/
```

### 函数匹配

```typescript
// 自定义匹配逻辑
activeWhen: (location) => {
  // 根据用户权限决定是否激活
  const hasPermission = checkUserPermission('user-management');
  return location.pathname.startsWith('/users') && hasPermission;
}

// 复杂条件匹配
activeWhen: (location) => {
  const { pathname, search } = location;
  
  // 路径匹配
  const pathMatch = pathname.startsWith('/orders');
  
  // 查询参数匹配
  const params = new URLSearchParams(search);
  const moduleMatch = params.get('module') === 'order-management';
  
  return pathMatch || moduleMatch;
}
```

## 应用生命周期

### 生命周期状态

```typescript
enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',           // 未加载
  LOADING_SOURCE_CODE = 'LOADING_SOURCE_CODE', // 加载中
  NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED', // 未引导
  BOOTSTRAPPING = 'BOOTSTRAPPING',     // 引导中
  NOT_MOUNTED = 'NOT_MOUNTED',         // 未挂载
  MOUNTING = 'MOUNTING',               // 挂载中
  MOUNTED = 'MOUNTED',                 // 已挂载
  UNMOUNTING = 'UNMOUNTING',           // 卸载中
  UNLOADING = 'UNLOADING',            // 卸载中
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN', // 跳过（损坏）
  LOAD_ERROR = 'LOAD_ERROR'            // 加载错误
}
```

### 生命周期监听

```typescript
// 监听应用状态变化
microCore.onAppStatusChange((app, newStatus, oldStatus) => {
  console.log(`应用 ${app.name} 状态变化: ${oldStatus} -> ${newStatus}`);
  
  // 根据状态执行相应逻辑
  switch (newStatus) {
    case AppStatus.MOUNTED:
      // 应用挂载完成
      analytics.track('app_mounted', { appName: app.name });
      break;
      
    case AppStatus.LOAD_ERROR:
      // 应用加载失败
      errorReporter.report('app_load_error', {
        appName: app.name,
        error: app.error
      });
      break;
  }
});

// 监听特定应用的状态
microCore.onAppStatusChange('user-management', (app, newStatus) => {
  if (newStatus === AppStatus.MOUNTED) {
    // 用户管理应用挂载完成，执行初始化逻辑
    initializeUserManagement();
  }
});
```

## 应用操作

### 手动控制

```typescript
// 启动应用
await microCore.startApp('user-management');

// 停止应用
await microCore.stopApp('user-management');

// 重新挂载应用
await microCore.remountApp('user-management');

// 卸载应用
await microCore.unloadApp('user-management');

// 重新加载应用
await microCore.reloadApp('user-management');
```

### 批量操作

```typescript
// 启动多个应用
await microCore.startApps(['user-management', 'order-system']);

// 停止所有应用
await microCore.stopAllApps();

// 重新加载所有应用
await microCore.reloadAllApps();
```

### 条件操作

```typescript
// 根据条件启动应用
const appsToStart = microCore.getApps().filter(app => {
  return app.status === AppStatus.NOT_MOUNTED && 
         checkUserPermission(app.name);
});

await microCore.startApps(appsToStart.map(app => app.name));
```

## 应用查询

### 获取应用信息

```typescript
// 获取所有应用
const allApps = microCore.getApps();

// 获取特定应用
const userApp = microCore.getApp('user-management');

// 获取已挂载的应用
const mountedApps = microCore.getMountedApps();

// 获取活跃应用
const activeApps = microCore.getActiveApps();
```

### 应用状态查询

```typescript
// 检查应用是否存在
const exists = microCore.hasApp('user-management');

// 检查应用状态
const isLoaded = microCore.isAppLoaded('user-management');
const isMounted = microCore.isAppMounted('user-management');
const isActive = microCore.isAppActive('user-management');

// 获取应用状态
const status = microCore.getAppStatus('user-management');
```

### 应用过滤

```typescript
// 按状态过滤
const loadingApps = microCore.getApps().filter(app => 
  app.status === AppStatus.LOADING_SOURCE_CODE
);

// 按框架过滤
const reactApps = microCore.getApps().filter(app => 
  app.framework === 'react'
);

// 按激活条件过滤
const userRelatedApps = microCore.getApps().filter(app => 
  app.activeWhen.toString().includes('/user')
);
```

## 动态应用管理

### 动态注册

```typescript
// 从配置文件动态注册应用
async function loadAppsFromConfig() {
  const config = await fetch('/api/app-config').then(res => res.json());
  
  config.apps.forEach(appConfig => {
    microCore.registerApp(appConfig);
  });
}

// 根据用户权限动态注册
async function loadAppsForUser(userId: string) {
  const userPermissions = await getUserPermissions(userId);
  const availableApps = await getAvailableApps();
  
  availableApps
    .filter(app => hasPermission(userPermissions, app.requiredPermission))
    .forEach(app => microCore.registerApp(app));
}
```

### 动态卸载

```typescript
// 卸载不需要的应用
function unloadUnusedApps() {
  const unusedApps = microCore.getApps().filter(app => {
    const lastActiveTime = app.metadata?.lastActiveTime;
    const now = Date.now();
    
    // 超过1小时未使用的应用
    return lastActiveTime && (now - lastActiveTime) > 60 * 60 * 1000;
  });
  
  unusedApps.forEach(app => {
    microCore.unregisterApp(app.name);
  });
}
```

### 热更新

```typescript
// 开发环境热更新
if (process.env.NODE_ENV === 'development') {
  // 监听应用更新
  microCore.onAppUpdate((appName, newEntry) => {
    console.log(`应用 ${appName} 更新，新入口: ${newEntry}`);
    
    // 重新加载应用
    microCore.reloadApp(appName);
  });
  
  // WebSocket 连接用于接收更新通知
  const ws = new WebSocket('ws://localhost:3000/hot-reload');
  ws.onmessage = (event) => {
    const { type, appName, entry } = JSON.parse(event.data);
    
    if (type === 'app-updated') {
      microCore.updateAppEntry(appName, entry);
    }
  };
}
```

## 应用配置管理

### 配置更新

```typescript
// 更新应用配置
microCore.updateAppConfig('user-management', {
  props: {
    apiBaseUrl: 'https://new-api.example.com',
    theme: 'light'
  }
});

// 批量更新配置
const configUpdates = {
  'user-management': { props: { theme: 'dark' } },
  'order-system': { props: { pageSize: 20 } }
};

Object.entries(configUpdates).forEach(([appName, config]) => {
  microCore.updateAppConfig(appName, config);
});
```

### 配置验证

```typescript
// 配置验证器
const configValidator = {
  validateAppConfig(config: AppConfig): ValidationResult {
    const errors: string[] = [];
    
    // 必填字段检查
    if (!config.name) errors.push('应用名称不能为空');
    if (!config.entry) errors.push('应用入口不能为空');
    if (!config.container) errors.push('容器选择器不能为空');
    
    // 格式检查
    if (config.entry && !isValidUrl(config.entry)) {
      errors.push('应用入口必须是有效的 URL');
    }
    
    // 唯一性检查
    if (microCore.hasApp(config.name)) {
      errors.push(`应用名称 ${config.name} 已存在`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};

// 注册前验证
function registerAppWithValidation(config: AppConfig) {
  const validation = configValidator.validateAppConfig(config);
  
  if (!validation.valid) {
    throw new Error(`应用配置无效: ${validation.errors.join(', ')}`);
  }
  
  microCore.registerApp(config);
}
```

## 错误处理与恢复

### 错误类型

```typescript
enum AppErrorType {
  LOAD_ERROR = 'LOAD_ERROR',         // 加载错误
  MOUNT_ERROR = 'MOUNT_ERROR',       // 挂载错误
  UNMOUNT_ERROR = 'UNMOUNT_ERROR',   // 卸载错误
  RUNTIME_ERROR = 'RUNTIME_ERROR',   // 运行时错误
  TIMEOUT_ERROR = 'TIMEOUT_ERROR'    // 超时错误
}
```

### 错误处理策略

```typescript
// 全局错误处理
microCore.onError((error, app) => {
  console.error(`应用 ${app.name} 发生错误:`, error);
  
  switch (error.type) {
    case AppErrorType.LOAD_ERROR:
      handleLoadError(error, app);
      break;
      
    case AppErrorType.MOUNT_ERROR:
      handleMountError(error, app);
      break;
      
    case AppErrorType.RUNTIME_ERROR:
      handleRuntimeError(error, app);
      break;
  }
});

// 加载错误处理
function handleLoadError(error: AppError, app: AppConfig) {
  // 重试加载
  if (error.retryCount < 3) {
    setTimeout(() => {
      microCore.reloadApp(app.name);
    }, 1000 * Math.pow(2, error.retryCount)); // 指数退避
  } else {
    // 加载降级应用
    loadFallbackApp(app);
  }
}

// 挂载错误处理
function handleMountError(error: AppError, app: AppConfig) {
  // 清理容器
  const container = document.querySelector(app.container);
  if (container) {
    container.innerHTML = `
      <div class="app-error">
        <h3>应用加载失败</h3>
        <p>应用 ${app.name} 暂时不可用，请稍后重试。</p>
        <button onclick="window.microCore.reloadApp('${app.name}')">
          重新加载
        </button>
      </div>
    `;
  }
}
```

### 自动恢复

```typescript
// 自动恢复配置
const recoveryConfig = {
  // 启用自动恢复
  enabled: true,
  
  // 重试次数
  maxRetries: 3,
  
  // 重试间隔（毫秒）
  retryInterval: 2000,
  
  // 健康检查间隔
  healthCheckInterval: 30000,
  
  // 恢复策略
  strategies: {
    [AppErrorType.LOAD_ERROR]: 'retry',
    [AppErrorType.MOUNT_ERROR]: 'fallback',
    [AppErrorType.RUNTIME_ERROR]: 'restart'
  }
};

// 应用健康检查
function startHealthCheck() {
  setInterval(() => {
    microCore.getMountedApps().forEach(app => {
      if (!isAppHealthy(app)) {
        console.warn(`应用 ${app.name} 健康检查失败`);
        microCore.restartApp(app.name);
      }
    });
  }, recoveryConfig.healthCheckInterval);
}

// 应用健康检查函数
function isAppHealthy(app: AppConfig): boolean {
  try {
    // 检查应用容器是否存在
    const container = document.querySelector(app.container);
    if (!container) return false;
    
    // 检查应用是否响应
    const appInstance = window[app.name];
    if (!appInstance || typeof appInstance.ping !== 'function') {
      return false;
    }
    
    // 调用应用的健康检查方法
    return appInstance.ping();
  } catch (error) {
    return false;
  }
}
```

## 性能监控

### 性能指标收集

```typescript
// 性能监控配置
microCore.enablePerformanceMonitoring({
  // 收集指标
  metrics: ['loadTime', 'mountTime', 'memoryUsage', 'bundleSize'],
  
  // 采样率
  sampleRate: 0.1,
  
  // 上报间隔
  reportInterval: 30000,
  
  // 性能阈值
  thresholds: {
    loadTime: 3000,    // 加载时间阈值
    mountTime: 1000,   // 挂载时间阈值
    memoryUsage: 50    // 内存使用阈值（MB）
  }
});

// 性能数据处理
microCore.onPerformanceData((data) => {
  const { appName, metrics, timestamp } = data;
  
  // 检查是否超过阈值
  Object.entries(metrics).forEach(([metric, value]) => {
    const threshold = recoveryConfig.thresholds[metric];
    if (threshold && value > threshold) {
      console.warn(`应用 ${appName} 的 ${metric} 超过阈值: ${value} > ${threshold}`);
      
      // 触发性能告警
      performanceAlert(appName, metric, value, threshold);
    }
  });
  
  // 上报性能数据
  analytics.track('app_performance', {
    appName,
    ...metrics,
    timestamp
  });
});
```

### 内存监控

```typescript
// 内存使用监控
function monitorMemoryUsage() {
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'measure') {
        const appName = entry.name.split(':')[0];
        const memoryInfo = (performance as any).memory;
        
        if (memoryInfo) {
          const memoryUsage = {
            used: memoryInfo.usedJSHeapSize,
            total: memoryInfo.totalJSHeapSize,
            limit: memoryInfo.jsHeapSizeLimit
          };
          
          // 检查内存泄漏
          checkMemoryLeak(appName, memoryUsage);
        }
      }
    });
  });
  
  observer.observe({ entryTypes: ['measure'] });
}

// 内存泄漏检测
function checkMemoryLeak(appName: string, memoryUsage: MemoryInfo) {
  const app = microCore.getApp(appName);
  if (!app.memoryHistory) {
    app.memoryHistory = [];
  }
  
  app.memoryHistory.push({
    timestamp: Date.now(),
    usage: memoryUsage.used
  });
  
  // 保留最近10次记录
  if (app.memoryHistory.length > 10) {
    app.memoryHistory.shift();
  }
  
  // 检查内存增长趋势
  if (app.memoryHistory.length >= 5) {
    const trend = calculateMemoryTrend(app.memoryHistory);
    if (trend > 0.1) { // 内存增长超过10%
      console.warn(`应用 ${appName} 可能存在内存泄漏，增长趋势: ${trend}`);
      
      // 触发内存清理
      triggerMemoryCleanup(appName);
    }
  }
}
```

## 应用元数据管理

### 元数据定义

```typescript
interface AppMetadata {
  // 基础信息
  version: string;
  description: string;
  author: string;
  homepage: string;
  
  // 技术信息
  framework: string;
  frameworkVersion: string;
  buildTool: string;
  dependencies: string[];
  
  // 运行时信息
  lastActiveTime: number;
  mountCount: number;
  errorCount: number;
  performanceMetrics: PerformanceMetrics;
  
  // 业务信息
  module: string;
  permissions: string[];
  features: string[];
  
  // 自定义字段
  [key: string]: any;
}
```

### 元数据操作

```typescript
// 设置应用元数据
microCore.setAppMetadata('user-management', {
  version: '1.2.0',
  description: '用户管理模块',
  author: 'Frontend Team',
  module: 'user',
  permissions: ['user:read', 'user:write'],
  features: ['user-list', 'user-detail', 'user-edit']
});

// 获取应用元数据
const metadata = microCore.getAppMetadata('user-management');

// 更新元数据
microCore.updateAppMetadata('user-management', {
  lastActiveTime: Date.now(),
  mountCount: metadata.mountCount + 1
});

// 批量获取元数据
const allMetadata = microCore.getAllAppMetadata();
```

## 应用依赖管理

### 依赖声明

```typescript
// 注册应用时声明依赖
microCore.registerApp({
  name: 'order-detail',
  entry: 'http://localhost:3003/index.js',
  container: '#order-detail-container',
  activeWhen: '/order/:id',
  
  // 依赖声明
  dependencies: {
    // 应用依赖
    apps: ['user-management', 'product-catalog'],
    
    // 服务依赖
    services: ['user-service', 'order-service'],
    
    // 资源依赖
    resources: ['shared-styles', 'common-utils']
  },
  
  // 依赖加载策略
  dependencyStrategy: {
    // 并行加载
    parallel: true,
    
    // 超时时间
    timeout: 10000,
    
    // 失败策略
    onFailure: 'continue' // continue | abort | fallback
  }
});
```

### 依赖解析

```typescript
// 依赖解析器
class DependencyResolver {
  async resolveDependencies(app: AppConfig): Promise<void> {
    const { dependencies } = app;
    
    if (!dependencies) return;
    
    // 解析应用依赖
    if (dependencies.apps) {
      await this.resolveAppDependencies(dependencies.apps);
    }
    
    // 解析服务依赖
    if (dependencies.services) {
      await this.resolveServiceDependencies(dependencies.services);
    }
    
    // 解析资源依赖
    if (dependencies.resources) {
      await this.resolveResourceDependencies(dependencies.resources);
    }
  }
  
  private async resolveAppDependencies(appDeps: string[]): Promise<void> {
    const loadPromises = appDeps.map(async (depName) => {
      const depApp = microCore.getApp(depName);
      
      if (!depApp) {
        throw new Error(`依赖应用 ${depName} 不存在`);
      }
      
      if (!microCore.isAppLoaded(depName)) {
        await microCore.loadApp(depName);
      }
    });
    
    await Promise.all(loadPromises);
  }
  
  private async resolveServiceDependencies(serviceDeps: string[]): Promise<void> {
    // 检查服务可用性
    const serviceChecks = serviceDeps.map(async (serviceName) => {
      const isAvailable = await checkServiceAvailability(serviceName);
      if (!isAvailable) {
        throw new Error(`依赖服务 ${serviceName} 不可用`);
      }
    });
    
    await Promise.all(serviceChecks);
  }
}
```

## 应用版本管理

### 版本控制

```typescript
// 版本管理配置
interface VersionConfig {
  // 版本策略
  strategy: 'latest' | 'pinned' | 'range';
  
  // 版本号
  version?: string;
  
  // 版本范围
  range?: string;
  
  // 自动更新
  autoUpdate?: boolean;
  
  // 更新检查间隔
  updateCheckInterval?: number;
}

// 注册应用时指定版本
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001/index.js',
  container: '#user-container',
  activeWhen: '/users',
  
  // 版本配置
  version: {
    strategy: 'range',
    range: '^1.2.0',
    autoUpdate: true,
    updateCheckInterval: 60000 // 1分钟检查一次
  }
});
```

### 版本更新

```typescript
// 检查应用更新
async function checkAppUpdates() {
  const apps = microCore.getApps();
  
  for (const app of apps) {
    if (app.version?.autoUpdate) {
      const latestVersion = await getLatestVersion(app.name);
      const currentVersion = app.metadata?.version;
      
      if (shouldUpdate(currentVersion, latestVersion, app.version.range)) {
        console.log(`应用 ${app.name} 有新版本: ${latestVersion}`);
        
        // 执行更新
        await updateApp(app.name, latestVersion);
      }
    }
  }
}

// 应用更新
async function updateApp(appName: string, newVersion: string) {
  try {
    // 获取新版本入口
    const newEntry = await getVersionEntry(appName, newVersion);
    
    // 预加载新版本
    await preloadApp(newEntry);
    
    // 停止当前版本
    await microCore.stopApp(appName);
    
    // 更新应用配置
    microCore.updateAppConfig(appName, {
      entry: newEntry,
      metadata: { version: newVersion }
    });
    
    // 启动新版本
    await microCore.startApp(appName);
    
    console.log(`应用 ${appName} 更新到版本 ${newVersion}`);
  } catch (error) {
    console.error(`应用 ${appName} 更新失败:`, error);
    
    // 回滚到原版本
    await rollbackApp(appName);
  }
}
```

## 最佳实践

### 应用设计原则

1. **单一职责**：每个微应用只负责一个明确的业务功能
2. **独立部署**：应用可以独立开发、测试、部署
3. **技术无关**：不同应用可以使用不同的技术栈
4. **松耦合**：应用间通过定义良好的接口通信
5. **容错性**：单个应用的故障不影响整体系统

### 命名规范

```typescript
// 应用命名规范
const namingConvention = {
  // 使用 kebab-case
  format: 'kebab-case',
  
  // 包含业务域
  pattern: '{domain}-{module}-{type}',
  
  // 示例
  examples: [
    'user-management-app',
    'order-processing-service',
    'product-catalog-widget'
  ]
};

// 容器命名规范
const containerNaming = {
  // 使用应用名作为前缀
  pattern: '#{app-name}-container',
  
  // 示例
  examples: [
    '#user-management-container',
    '#order-processing-container'
  ]
};
```

### 配置管理

```typescript
// 配置文件结构
interface AppConfigFile {
  apps: AppConfig[];
  global: {
    debug: boolean;
    sandbox: boolean;
    prefetch: boolean;
  };
  environments: {
    [env: string]: {
      apiBaseUrl: string;
      cdnUrl: string;
    };
  };
}

// 环境配置
const config: AppConfigFile = {
  apps: [
    {
      name: 'user-management',
      entry: '{{CDN_URL}}/user-management/{{VERSION}}/index.js',
      container: '#user-container',
      activeWhen: '/users',
      props: {
        apiBaseUrl: '{{API_BASE_URL}}'
      }
    }
  ],
  
  global: {
    debug: false,
    sandbox: true,
    prefetch: true
  },
  
  environments: {
    development: {
      apiBaseUrl: 'http://localhost:8080/api',
      cdnUrl: 'http://localhost:3000'
    },
    production: {
      apiBaseUrl: 'https://api.example.com',
      cdnUrl: 'https://cdn.example.com'
    }
  }
};
```

### 监控告警

```typescript
// 监控配置
const monitoringConfig = {
  // 性能监控
  performance: {
    enabled: true,
    thresholds: {
      loadTime: 3000,
      mountTime: 1000,
      memoryUsage: 100
    }
  },
  
  // 错误监控
  error: {
    enabled: true,
    maxErrors: 10,
    timeWindow: 300000 // 5分钟
  },
  
  // 可用性监控
  availability: {
    enabled: true,
    checkInterval: 30000,
    timeout: 5000
  }
};

// 告警处理
function setupAlerts() {
  // 性能告警
  microCore.onPerformanceAlert((alert) => {
    sendAlert({
      type: 'performance',
      app: alert.appName,
      metric: alert.metric,
      value: alert.value,
      threshold: alert.threshold
    });
  });
  
  // 错误告警
  microCore.onErrorAlert((alert) => {
    sendAlert({
      type: 'error',
      app: alert.appName,
      errorCount: alert.errorCount,
      timeWindow: alert.timeWindow
    });
  });
  
  // 可用性告警
  microCore.onAvailabilityAlert((alert) => {
    sendAlert({
      type: 'availability',
      app: alert.appName,
      status: alert.status,
      lastCheck: alert.lastCheck
    });
  });
}
```

## 总结

应用管理是 micro-core 的核心功能，提供了完整的微应用生命周期管理能力：

- **注册管理**：灵活的应用注册和配置
- **生命周期**：完整的应用生命周期控制
- **动态管理**：支持动态加载和卸载
- **错误处理**：完善的错误处理和恢复机制
- **性能监控**：实时的性能监控和告警
- **版本管理**：支持应用版本控制和更新
- **依赖管理**：智能的依赖解析和加载

通过合理使用这些功能，你可以构建出稳定、高效、可维护的微前端应用系统。

接下来，你可以学习 [路由系统](./routing.md) 了解如何管理微应用间的路由。
