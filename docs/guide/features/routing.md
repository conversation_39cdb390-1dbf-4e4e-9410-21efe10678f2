# 路由系统

micro-core 的路由系统负责协调多个微应用的路由，提供统一的导航体验和灵活的路由管理能力。

## 路由配置

### 基础配置

```typescript
import { RouterPlugin } from '@micro-core/plugins';

const routerPlugin = new RouterPlugin({
  // 路由模式
  mode: 'history',        // 'hash' | 'history' | 'memory'
  
  // 基础路径
  base: '/',
  
  // 激活链接的CSS类
  linkActiveClass: 'router-link-active',
  
  // 精确激活链接的CSS类
  linkExactActiveClass: 'router-link-exact-active',
  
  // 滚动行为
  scrollBehavior: 'smooth'  // 'auto' | 'smooth' | function
});

// 注册路由插件
microCore.use(routerPlugin);
```

### 高级配置

```typescript
const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/micro-app',
  
  // 路由表
  routes: [
    {
      path: '/users/*',
      app: 'user-management',
      meta: {
        title: '用户管理',
        requiresAuth: true,
        permissions: ['user:read']
      }
    },
    {
      path: '/orders/*',
      app: 'order-system',
      meta: {
        title: '订单系统',
        requiresAuth: true,
        permissions: ['order:read']
      }
    }
  ],
  
  // 路由守卫
  beforeEach: (to, from, next) => {
    // 全局前置守卫逻辑
    if (to.meta?.requiresAuth && !isAuthenticated()) {
      next('/login');
    } else {
      next();
    }
  },
  
  afterEach: (to, from) => {
    // 全局后置守卫逻辑
    document.title = to.meta?.title || 'Micro App';
    
    // 页面访问统计
    analytics.track('page_view', {
      path: to.path,
      from: from.path
    });
  },
  
  // 错误处理
  onError: (error) => {
    console.error('路由错误:', error);
    // 错误上报
    errorReporter.report(error);
  }
});
```

## 路由匹配

### 路径匹配规则

```typescript
// 精确匹配
activeWhen: '/users'

// 前缀匹配
activeWhen: '/users/*'

// 多路径匹配
activeWhen: ['/users', '/user-profile', '/user-settings']

// 正则表达式匹配
activeWhen: /^\/users?(\/.*)?$/

// 参数匹配
activeWhen: '/users/:id'
activeWhen: '/users/:id/orders/:orderId'

// 可选参数
activeWhen: '/users/:id?'

// 通配符匹配
activeWhen: '/admin/*'
```

### 函数匹配

```typescript
// 自定义匹配逻辑
activeWhen: (location) => {
  const { pathname, search, hash } = location;
  
  // 路径匹配
  if (!pathname.startsWith('/users')) {
    return false;
  }
  
  // 查询参数匹配
  const params = new URLSearchParams(search);
  const module = params.get('module');
  
  // 权限检查
  const hasPermission = checkUserPermission('user-management');
  
  return module === 'user' && hasPermission;
}

// 复杂条件匹配
activeWhen: (location) => {
  // 时间条件
  const now = new Date();
  const isBusinessHours = now.getHours() >= 9 && now.getHours() <= 18;
  
  // 用户角色
  const userRole = getCurrentUserRole();
  const isAdmin = userRole === 'admin';
  
  // 路径匹配
  const pathMatch = location.pathname.startsWith('/admin');
  
  return pathMatch && (isAdmin || isBusinessHours);
}
```

## 路由导航

### 编程式导航

```typescript
// 基础导航
microCore.router.push('/users');
microCore.router.push('/users/123');

// 带查询参数
microCore.router.push('/users?page=2&size=10');

// 带状态数据
microCore.router.push('/users', { 
  state: { from: 'dashboard' } 
});

// 替换当前路由
microCore.router.replace('/users/456');

// 前进/后退
microCore.router.go(-1);  // 后退
microCore.router.go(1);   // 前进
microCore.router.back();  // 后退
microCore.router.forward(); // 前进
```

### 声明式导航

```html
<!-- 基础链接 -->
<a href="/users" data-micro-link>用户管理</a>

<!-- 带参数链接 -->
<a href="/users/123" data-micro-link>用户详情</a>

<!-- 外部链接（不会被路由拦截） -->
<a href="https://example.com" target="_blank">外部链接</a>

<!-- 带样式的激活链接 -->
<nav>
  <a href="/users" data-micro-link class="nav-link">用户</a>
  <a href="/orders" data-micro-link class="nav-link">订单</a>
</nav>
```

### React 组件导航

```tsx
import { useMicroRouter } from '@micro-core/react';

function Navigation() {
  const router = useMicroRouter();
  
  const handleNavigation = (path: string) => {
    router.push(path);
  };
  
  return (
    <nav>
      <button onClick={() => handleNavigation('/users')}>
        用户管理
      </button>
      <button onClick={() => handleNavigation('/orders')}>
        订单系统
      </button>
    </nav>
  );
}

// 路由链接组件
import { MicroLink } from '@micro-core/react';

function App() {
  return (
    <div>
      <MicroLink to="/users" activeClass="active">
        用户管理
      </MicroLink>
      <MicroLink to="/orders" activeClass="active">
        订单系统
      </MicroLink>
    </div>
  );
}
```

### Vue 组件导航

```vue
<template>
  <nav>
    <micro-link to="/users" active-class="active">
      用户管理
    </micro-link>
    <micro-link to="/orders" active-class="active">
      订单系统
    </micro-link>
  </nav>
</template>

<script setup>
import { useMicroRouter } from '@micro-core/vue';

const router = useMicroRouter();

const navigateToUsers = () => {
  router.push('/users');
};
</script>
```

## 路由守卫

### 全局守卫

```typescript
// 全局前置守卫
microCore.router.beforeEach((to, from, next) => {
  console.log(`导航到: ${to.path}`);
  
  // 权限检查
  if (to.meta?.requiresAuth) {
    const isAuthenticated = checkAuthentication();
    if (!isAuthenticated) {
      next('/login');
      return;
    }
  }
  
  // 权限检查
  if (to.meta?.permissions) {
    const hasPermission = checkPermissions(to.meta.permissions);
    if (!hasPermission) {
      next('/403');
      return;
    }
  }
  
  // 继续导航
  next();
});

// 全局解析守卫
microCore.router.beforeResolve((to, from, next) => {
  // 在导航被确认之前，同时在所有组件内守卫和异步路由组件被解析之后调用
  console.log('路由解析完成');
  next();
});

// 全局后置守卫
microCore.router.afterEach((to, from) => {
  // 导航完成后调用
  console.log(`从 ${from.path} 导航到 ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta?.title || 'Default Title';
  
  // 页面访问统计
  analytics.track('page_view', {
    path: to.path,
    title: to.meta?.title
  });
  
  // 滚动到顶部
  window.scrollTo(0, 0);
});
```

### 路由级守卫

```typescript
// 注册应用时配置路由守卫
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001/index.js',
  container: '#user-container',
  activeWhen: '/users/*',
  
  // 路由守卫
  beforeEnter: (to, from, next) => {
    // 进入用户管理应用前的检查
    const hasUserPermission = checkPermission('user:read');
    
    if (!hasUserPermission) {
      next('/403');
      return;
    }
    
    // 预加载用户数据
    preloadUserData().then(() => {
      next();
    }).catch(() => {
      next('/error');
    });
  },
  
  beforeLeave: (to, from, next) => {
    // 离开用户管理应用前的检查
    const hasUnsavedChanges = checkUnsavedChanges();
    
    if (hasUnsavedChanges) {
      const confirmed = confirm('有未保存的更改，确定要离开吗？');
      if (!confirmed) {
        next(false); // 取消导航
        return;
      }
    }
    
    next();
  }
});
```

### 组件内守卫

```typescript
// 微应用内的组件守卫
export default {
  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被确认前调用
    // 不能获取组件实例 `this`，因为当守卫执行前，组件实例还没被创建
    next(vm => {
      // 通过 `vm` 访问组件实例
    });
  },
  
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但是该组件被复用时调用
    // 可以访问组件实例 `this`
    this.fetchData(to.params.id);
    next();
  },
  
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    // 可以访问组件实例 `this`
    if (this.hasUnsavedChanges) {
      const answer = window.confirm('确定要离开吗？未保存的更改将丢失。');
      if (!answer) {
        next(false);
      } else {
        next();
      }
    } else {
      next();
    }
  }
};
```

## 动态路由

### 动态添加路由

```typescript
// 动态添加单个路由
microCore.router.addRoute({
  path: '/dynamic/:id',
  app: 'dynamic-app',
  meta: {
    title: '动态页面',
    dynamic: true
  }
});

// 动态添加嵌套路由
microCore.router.addRoute({
  path: '/admin',
  app: 'admin-shell',
  children: [
    {
      path: 'users',
      app: 'admin-users'
    },
    {
      path: 'settings',
      app: 'admin-settings'
    }
  ]
});

// 批量添加路由
const dynamicRoutes = [
  { path: '/module1', app: 'module1-app' },
  { path: '/module2', app: 'module2-app' },
  { path: '/module3', app: 'module3-app' }
];

microCore.router.addRoutes(dynamicRoutes);
```

### 动态删除路由

```typescript
// 删除指定路由
microCore.router.removeRoute('/dynamic/:id');

// 批量删除路由
microCore.router.removeRoutes(['/module1', '/module2']);

// 根据条件删除路由
microCore.router.removeRoutes((route) => {
  return route.meta?.dynamic === true;
});
```

### 基于权限的动态路由

```typescript
// 根据用户权限动态生成路由
async function setupDynamicRoutes(userId: string) {
  const userPermissions = await getUserPermissions(userId);
  const availableModules = await getAvailableModules();
  
  const allowedRoutes = availableModules
    .filter(module => hasPermission(userPermissions, module.permission))
    .map(module => ({
      path: module.path,
      app: module.appName,
      meta: {
        title: module.title,
        permission: module.permission
      }
    }));
  
  microCore.router.addRoutes(allowedRoutes);
}

// 用户登录后设置路由
microCore.on('user:login', (user) => {
  setupDynamicRoutes(user.id);
});

// 用户登出后清理路由
microCore.on('user:logout', () => {
  microCore.router.removeRoutes((route) => {
    return route.meta?.permission;
  });
});
```

## 嵌套路由

### 嵌套路由配置

```typescript
// 配置嵌套路由
microCore.router.addRoute({
  path: '/workspace',
  app: 'workspace-shell',
  children: [
    {
      path: '',
      app: 'workspace-home'
    },
    {
      path: 'projects',
      app: 'project-management',
      children: [
        {
          path: '',
          app: 'project-list'
        },
        {
          path: ':id',
          app: 'project-detail'
        },
        {
          path: ':id/tasks',
          app: 'task-management'
        }
      ]
    },
    {
      path: 'settings',
      app: 'workspace-settings'
    }
  ]
});
```

### 嵌套应用通信

```typescript
// 父应用向子应用传递数据
microCore.registerApp({
  name: 'workspace-shell',
  entry: 'http://localhost:3001/workspace.js',
  container: '#workspace-container',
  activeWhen: '/workspace/*',
  
  // 向子应用传递数据
  props: {
    currentUser: () => getCurrentUser(),
    workspaceId: () => getCurrentWorkspaceId(),
    permissions: () => getUserPermissions()
  },
  
  // 子应用路由变化监听
  onRouteChange: (route) => {
    // 通知其他应用路由变化
    microCore.broadcast({
      type: 'WORKSPACE_ROUTE_CHANGE',
      data: { route }
    });
  }
});

// 子应用接收父应用数据
// 在子应用中
export async function mount(props) {
  const { currentUser, workspaceId, permissions } = props;
  
  // 使用父应用传递的数据
  const user = await currentUser();
  const workspace = await workspaceId();
  const perms = await permissions();
  
  // 初始化子应用
  initApp({ user, workspace, permissions: perms });
}
```

## 路由参数

### 路径参数

```typescript
// 定义带参数的路由
microCore.router.addRoute({
  path: '/users/:id',
  app: 'user-detail',
  meta: {
    title: '用户详情'
  }
});

// 多个参数
microCore.router.addRoute({
  path: '/users/:userId/orders/:orderId',
  app: 'order-detail'
});

// 可选参数
microCore.router.addRoute({
  path: '/users/:id?',
  app: 'user-list-or-detail'
});

// 通配符参数
microCore.router.addRoute({
  path: '/files/*',
  app: 'file-browser'
});
```

### 参数获取

```typescript
// 在微应用中获取路由参数
export async function mount(props) {
  const { route } = props;
  
  // 获取路径参数
  const userId = route.params.id;
  const orderId = route.params.orderId;
  
  // 获取查询参数
  const page = route.query.page || 1;
  const size = route.query.size || 10;
  
  // 获取哈希
  const hash = route.hash;
  
  console.log('路由参数:', { userId, orderId, page, size, hash });
}

// React Hook 方式获取参数
import { useMicroRoute } from '@micro-core/react';

function UserDetail() {
  const route = useMicroRoute();
  const { id } = route.params;
  const { tab } = route.query;
  
  return (
    <div>
      <h1>用户详情 - {id}</h1>
      <div>当前标签: {tab}</div>
    </div>
  );
}

// Vue Composition API 方式
import { useMicroRoute } from '@micro-core/vue';

export default {
  setup() {
    const route = useMicroRoute();
    
    const userId = computed(() => route.params.id);
    const currentTab = computed(() => route.query.tab || 'basic');
    
    return {
      userId,
      currentTab
    };
  }
};
```

### 参数验证

```typescript
// 路由参数验证
microCore.router.addRoute({
  path: '/users/:id',
  app: 'user-detail',
  
  // 参数验证
  beforeEnter: (to, from, next) => {
    const userId = to.params.id;
    
    // 验证用户ID格式
    if (!/^\d+$/.test(userId)) {
      next('/404');
      return;
    }
    
    // 验证用户是否存在
    checkUserExists(userId).then(exists => {
      if (exists) {
        next();
      } else {
        next('/404');
      }
    });
  }
});
```

## 路由元信息

### 元信息定义

```typescript
// 定义路由元信息
microCore.router.addRoute({
  path: '/admin/*',
  app: 'admin-panel',
  meta: {
    // 页面标题
    title: '管理后台',
    
    // 权限要求
    requiresAuth: true,
    permissions: ['admin:access'],
    
    // 面包屑导航
    breadcrumb: [
      { name: '首页', path: '/' },
      { name: '管理后台', path: '/admin' }
    ],
    
    // 页面图标
    icon: 'admin',
    
    // 缓存配置
    keepAlive: true,
    
    // 自定义数据
    layout: 'admin-layout',
    sidebar: true,
    analytics: {
      category: 'admin',
      action: 'page_view'
    }
  }
});
```

### 元信息使用

```typescript
// 在路由守卫中使用元信息
microCore.router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title;
  }
  
  // 权限检查
  if (to.meta?.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  if (to.meta?.permissions) {
    const hasPermission = checkPermissions(to.meta.permissions);
    if (!hasPermission) {
      next('/403');
      return;
    }
  }
  
  // 页面统计
  if (to.meta?.analytics) {
    analytics.track(to.meta.analytics.action, {
      category: to.meta.analytics.category,
      path: to.path
    });
  }
  
  next();
});

// 在组件中使用元信息
import { useMicroRoute } from '@micro-core/react';

function PageHeader() {
  const route = useMicroRoute();
  const { title, breadcrumb, icon } = route.meta || {};
  
  return (
    <header>
      {icon && <Icon name={icon} />}
      <h1>{title}</h1>
      {breadcrumb && (
        <nav>
          {breadcrumb.map((item, index) => (
            <span key={index}>
              <a href={item.path}>{item.name}</a>
              {index < breadcrumb.length - 1 && ' > '}
            </span>
          ))}
        </nav>
      )}
    </header>
  );
}
```

## 路由缓存

### 应用级缓存

```typescript
// 配置应用缓存
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001/index.js',
  container: '#user-container',
  activeWhen: '/users/*',
  
  // 缓存配置
  cache: {
    // 启用缓存
    enabled: true,
    
    // 缓存策略
    strategy: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
    
    // 缓存时间（毫秒）
    maxAge: 5 * 60 * 1000, // 5分钟
    
    // 缓存条件
    condition: (route) => {
      // 只缓存列表页面
      return route.path === '/users';
    }
  }
});
```

### 页面级缓存

```typescript
// 页面缓存配置
microCore.router.addRoute({
  path: '/users',
  app: 'user-list',
  meta: {
    // 启用页面缓存
    keepAlive: true,
    
    // 缓存键
    cacheKey: (route) => `user-list-${route.query.page || 1}`,
    
    // 缓存条件
    cacheable: (route) => {
      // 只有在特定条件下才缓存
      return !route.query.refresh;
    }
  }
});

// 缓存管理
class RouteCache {
  private cache = new Map();
  
  // 设置缓存
  set(key: string, data: any, maxAge?: number) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      maxAge: maxAge || 5 * 60 * 1000
    });
  }
  
  // 获取缓存
  get(key: string) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.maxAge) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  // 清除缓存
  clear(pattern?: RegExp) {
    if (pattern) {
      for (const [key] of this.cache) {
        if (pattern.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}
```

## 路由懒加载

### 应用懒加载

```typescript
// 配置应用懒加载
microCore.registerApp({
  name: 'heavy-app',
  entry: () => import('http://localhost:3001/heavy-app.js'),
  container: '#heavy-container',
  activeWhen: '/heavy/*',
  
  // 懒加载配置
  lazy: {
    // 加载时机
    trigger: 'visible', // 'immediate' | 'visible' | 'hover' | 'click'
    
    // 预加载
    preload: true,
    
    // 加载优先级
    priority: 'low', // 'high' | 'normal' | 'low'
    
    // 加载超时
    timeout: 10000,
    
    // 加载失败重试
    retry: {
      times: 3,
      interval: 2000
    }
  }
});
```

### 组件懒加载

```typescript
// React 组件懒加载
import { lazy, Suspense } from 'react';

const LazyUserDetail = lazy(() => import('./components/UserDetail'));

function App() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <LazyUserDetail />
    </Suspense>
  );
}

// Vue 组件懒加载
const AsyncUserDetail = defineAsyncComponent({
  loader: () => import('./components/UserDetail.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
});
```

## 路由过渡动画

### 基础过渡

```css
/* 路由过渡动画 */
.route-enter-active,
.route-leave-active {
  transition: all 0.3s ease;
}

.route-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.route-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 不同类型的过渡动画 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
```

### 动态过渡

```typescript
// 配置路由过渡动画
microCore.router.addRoute({
  path: '/users/:id',
  app: 'user-detail',
  meta: {
    // 过渡动画配置
    transition: {
      name: 'slide-left',
      mode: 'out-in',
      duration: 300
    }
  }
});

// 根据路由方向选择动画
microCore.router.beforeEach((to, from, next) => {
  const toDepth = to.path.split('/').length;
  const fromDepth = from.path.split('/').length;
  
  // 根据路由深度确定动画方向
  if (toDepth > fromDepth) {
    to.meta.transition = { name: 'slide-left' };
  } else if (toDepth < fromDepth) {
    to.meta.transition = { name: 'slide-right' };
  } else {
    to.meta.transition = { name: 'fade' };
  }
  
  next();
});
```

## 路由状态管理

### 路由状态同步

```typescript
// 路由状态管理
class RouteStateManager {
  private state = {
    currentRoute: null,
    history: [],
    canGoBack: false,
    canGoForward: false
  };
  
  private listeners = new Set();
  
  // 更新路由状态
  updateState(route: Route) {
    this.state.currentRoute = route;
    this.state.history.push(route);
    this.state.canGoBack = this.state.history.length > 1;
    
    // 通知监听器
    this.notifyListeners();
  }
  
  // 添加监听器
  subscribe(listener: Function) {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }
  
  // 通知监听器
  private notifyListeners() {
    this.listeners.forEach(listener => {
      listener(this.state);
    });
  }
  
  // 获取当前状态
  getState() {
    return { ...this.state };
  }
}

// 使用路由状态
const routeStateManager = new RouteStateManager();

microCore.router.afterEach((to, from) => {
  routeStateManager.updateState(to);
});

// React Hook
function useRouteState() {
  const [state, setState] = useState(routeStateManager.getState());
  
  useEffect(() => {
    const unsubscribe = routeStateManager.subscribe(setState);
    return unsubscribe;
  }, []);
  
  return state;
}
```

### 路由历史管理

```typescript
// 路由历史管理
class RouteHistory {
  private history: Route[] = [];
  private currentIndex = -1;
  private maxSize = 50;
  
  // 添加路由记录
  push(route: Route) {
    // 如果当前不在历史末尾，删除后续记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }
    
    this.history.push(route);
    this.currentIndex++;
    
    // 限制历史记录大小
    if (this.history.length > this.maxSize) {
      this.history.shift();
      this.currentIndex--;
    }
  }
  
  // 后退
  back(): Route | null {
    if (this.canGoBack()) {
      this.currentIndex--;
      return this.history[this.currentIndex];
    }
    return null;
  }
  
  // 前进
  forward(): Route | null {
    if (this.canGoForward()) {
      this.currentIndex++;
      return this.history[this.currentIndex];
    }
    return null;
  }
  
  // 检查是否可以后退
  canGoBack(): boolean {
    return this.currentIndex > 0;
  }
  
  // 检查是否可以前进
  canGoForward(): boolean {
    return this.currentIndex < this.history.length - 1;
  }
  
  // 获取当前路由
  current(): Route | null {
    return this.history[this.currentIndex] || null;
  }
  
  // 清空历史
  clear() {
    this.history = [];
    this.currentIndex = -1;
  }
}
```

## 路由性能优化

### 路由预加载

```typescript
// 路由预加载策略
const preloadStrategy = {
  // 预加载时机
  timing: 'idle', // 'immediate' | 'visible' | 'hover' | 'idle'
  
  // 预加载优先级
  priority: 'low',
  
  // 预加载条件
  condition: (route) => {
    // 只预加载常用路由
    const commonRoutes = ['/users', '/orders', '/dashboard'];
    return commonRoutes.includes(route.path);
  },
  
  // 网络条件检查
  networkCheck: true
};

// 实现预加载
class RoutePreloader {
  private preloadedRoutes = new Set();
  
  async preloadRoute(path: string) {
    if (this.preloadedRoutes.has(path)) {
      return;
    }
    
    // 检查网络条件
    if (preloadStrategy.networkCheck && !this.isGoodNetwork()) {
      return;
    }
    
    try {
      const route = microCore.router.resolve(path);
      const app = microCore.getApp(route.app);
      
      if (app && !microCore.isAppLoaded(app.name)) {
        await microCore.preloadApp(app.name);
        this.preloadedRoutes.add(path);
      }
    } catch (error) {
      console.warn('路由预加载失败:', path, error);
    }
  }
  
  private isGoodNetwork(): boolean {
    const connection = (navigator as any).connection;
    if (!connection) return true;
    
    // 检查网络类型和速度
    return connection.effectiveType !== 'slow-2g' && 
           connection.effectiveType !== '2g';
  }
}

// 使用预加载
const preloader = new RoutePreloader();

// 鼠标悬停时预加载
document.addEventListener('mouseover', (event) => {
  const link = event.target.closest('[data-micro-link]');
  if (link) {
    const href = link.getAttribute('href');
    if (href) {
      preloader.preloadRoute(href);
    }
  }
});
```

### 路由缓存优化

```typescript
// 智能路由缓存
class SmartRouteCache {
  private cache = new Map();
  private accessCount = new Map();
  private lastAccess = new Map();
  
  // 设置缓存
  set(key: string, data: any) {
    this.cache.set(key, data);
    this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
    this.lastAccess.set(key, Date.now());
    
    // 清理过期缓存
    this.cleanup();
  }
  
  // 获取缓存
  get(key: string) {
    if (this.cache.has(key)) {
      this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
      this.lastAccess.set(key, Date.now());
      return this.cache.get(key);
    }
    return null;
  }
  
  // 智能清理
  private cleanup() {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟
    const maxSize = 20;
    
    // 删除过期项
    for (const [key, timestamp] of this.lastAccess) {
      if (now - timestamp > maxAge) {
        this.delete(key);
      }
    }
    
    // 如果缓存过多，删除最少使用的项
    if (this.cache.size > maxSize) {
      const entries = Array.from(this.accessCount.entries())
        .sort((a, b) => a[1] - b[1])
        .slice(0, this.cache.size - maxSize);
      
      entries.forEach(([key]) => this.delete(key));
    }
  }
  
  private delete(key: string) {
    this.cache.delete(key);
    this.accessCount.delete(key);
    this.lastAccess.delete(key);
  }
}
```

## 路由调试

### 调试工具

```typescript
// 路由调试器
class RouteDebugger {
  private enabled = process.env.NODE_ENV === 'development';
  private logs: RouteLog[] = [];
  
  constructor() {
    if (this.enabled) {
      this.setupDebugger();
    }
  }
  
  private setupDebugger() {
    // 监听路由变化
    microCore.router.beforeEach((to, from, next) => {
      const log: RouteLog = {
        type: 'navigation',
        from: from.path,
        to: to.path,
        timestamp: Date.now(),
        params: to.params,
        query: to.query,
        meta: to.meta
      };
      
      this.addLog(log);
      console.group(`🧭 路由导航: ${from.path} → ${to.path}`);
      console.log('参数:', to.params);
      console.log('查询:', to.query);
      console.log('元信息:', to.meta);
      console.groupEnd();
      
      next();
    });
    
    // 监听路由错误
    microCore.router.onError((error) => {
      const log: RouteLog = {
        type: 'error',
        error: error.message,
        timestamp: Date.now()
      };
      
      this.addLog(log);
      console.error('🚨 路由错误:', error);
    });
  }
  
  private addLog(log: RouteLog) {
    this.logs.push(log);
    
    // 限制日志数量
    if (this.logs.length > 100) {
      this.logs.shift();
    }
  }
  
  // 获取调试信息
  getDebugInfo() {
    return {
      logs: this.logs,
      currentRoute: microCore.router.currentRoute,
      registeredRoutes: microCore.router.getRoutes(),
      performance: this.getPerformanceMetrics()
    };
  }
  
  private getPerformanceMetrics() {
    const entries = performance.getEntriesByType('navigation');
    return entries.map(entry => ({
      name: entry.name,
      duration: entry.duration,
      loadEventEnd: entry.loadEventEnd
    }));
  }
}

// 开发工具面板集成
if (process.env.NODE_ENV === 'development') {
  const debugger = new RouteDebugger();
  
  // 添加到全局对象
  window.__MICRO_CORE_ROUTER_DEBUG__ = debugger;
  
  // 添加调试命令
  console.log('路由调试命令:');
  console.log('- __MICRO_CORE_ROUTER_DEBUG__.getDebugInfo() - 获取调试信息');
  console.log('- microCore.router.getRoutes() - 获取所有路由');
  console.log('- microCore.router.currentRoute - 获取当前路由');
}
```

## 最佳实践

### 路由设计原则

1. **RESTful 设计**：遵循 RESTful API 设计原则
2. **语义化路径**：路径应该清晰表达页面功能
3. **层级结构**：合理的路由层级结构
4. **参数设计**：合理使用路径参数和查询参数
5. **向后兼容**：保持路由的向后兼容性

### 路由命名规范

```typescript
// 路由命名规范
const routeNamingConvention = {
  // 使用小写字母和连字符
  format: 'kebab-case',
  
  // 资源路由
  resources: {
    list: '/users',           // 列表页
    detail: '/users/:id',     // 详情页
    create: '/users/new',     // 创建页
    edit: '/users/:id/edit'   // 编辑页
  },
  
  // 嵌套资源
  nested: {
    pattern: '/users/:userId/orders/:orderId',
    example: '/users/123/orders/456'
  },
  
  // 查询参数
  query: {
    pagination: '?page=1&size=10',
    filter: '?status=active&type=premium',
    search: '?q=keyword'
  }
};
```

### 权限控制

```typescript
// 基于路由的权限控制
const permissionConfig = {
  // 公开路由（无需认证）
  public: ['/login', '/register', '/forgot-password'],
  
  // 需要认证的路由
  authenticated: ['/dashboard', '/profile'],
  
  // 需要特定权限的路由
  permissions: {
    '/admin/*': ['admin:access'],
    '/users/*': ['user:read'],
    '/users/*/edit': ['user:write'],
    '/orders/*': ['order:read'],
    '/reports/*': ['report:read']
  }
};

// 权限检查中间件
function createPermissionGuard(config: typeof permissionConfig) {
  return (to: Route, from: Route, next: Function) => {
    const path = to.path;
    
    // 检查是否为公开路由
    if (config.public.some(route => matchRoute(path, route))) {
      next();
      return;
    }
    
    // 检查是否已认证
    if (!isAuthenticated()) {
      next('/login');
      return;
    }
    
    // 检查特定权限
    const requiredPermissions = getRequiredPermissions(path, config.permissions);
    if (requiredPermissions.length > 0) {
      const hasPermission = checkPermissions(requiredPermissions);
      if (!hasPermission) {
        next('/403');
        return;
      }
    }
    
    next();
  };
}
```

### 性能优化建议

```typescript
// 路由性能优化配置
const performanceConfig = {
  // 预加载策略
  preload: {
    // 预加载常用路由
    routes: ['/dashboard', '/users', '/orders'],
    
    // 预加载时机
    timing: 'idle',
    
    // 网络条件检查
    networkAware: true
  },
  
  // 缓存策略
  cache: {
    // 启用路由缓存
    enabled: true,
    
    // 缓存时间
    maxAge: 5 * 60 * 1000,
    
    // 缓存大小
    maxSize: 20
  },
  
  // 懒加载配置
  lazyLoad: {
    // 懒加载阈值
    threshold: 0.1,
    
    // 加载超时
    timeout: 10000,
    
    // 重试配置
    retry: {
      times: 3,
      interval: 2000
    }
  }
};
```

## 总结

micro-core 的路由系统提供了完整的微前端路由解决方案：

- **灵活配置**：支持多种路由模式和匹配规则
- **路由守卫**：完善的权限控制和导航守卫
- **动态路由**：支持动态添加和删除路由
- **嵌套路由**：支持复杂的嵌套路由结构
- **性能优化**：内置预加载、缓存等优化策略
- **调试支持**：丰富的调试工具和日志记录

通过合理使用这些功能，你可以构建出用户体验良好、性能优秀的微前端路由系统。

接下来，你可以学习 [应用间通信](./communication.md) 了解如何实现微应用间的数据交换。
