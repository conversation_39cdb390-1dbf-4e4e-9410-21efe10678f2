# 沙箱隔离

Micro-Core 提供了多种沙箱隔离策略，确保微应用之间的完全隔离，防止全局变量污染、样式冲突和脚本干扰。

## 沙箱类型概览

### 1. Proxy 沙箱 (推荐)

基于 ES6 Proxy 实现的高性能沙箱，提供完整的 JavaScript 隔离。

```typescript
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

kernel.use(ProxySandboxPlugin, {
  strict: true,                    // 严格模式
  whitelist: ['console', 'fetch'], // 白名单全局变量
  blacklist: ['eval'],             // 黑名单全局变量
  enablePerformanceMonitor: true   // 启用性能监控
});

kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3001',
  container: '#app1',
  activeWhen: '/app1',
  sandbox: 'proxy' // 使用 Proxy 沙箱
});
```

### 2. Iframe 沙箱

基于 iframe 的强隔离沙箱，提供最高级别的隔离。

```typescript
import { IframeSandboxPlugin } from '@micro-core/plugin-sandbox-iframe';

kernel.use(IframeSandboxPlugin, {
  allowSameOrigin: false,          // 是否允许同源
  allowScripts: true,              // 是否允许脚本执行
  allowForms: true,                // 是否允许表单提交
  allowPopups: false,              // 是否允许弹窗
  communicationProtocol: 'postMessage' // 通信协议
});

kernel.registerApplication({
  name: 'app2',
  entry: 'http://localhost:3002',
  container: '#app2',
  activeWhen: '/app2',
  sandbox: 'iframe' // 使用 Iframe 沙箱
});
```

### 3. WebComponent 沙箱

基于 Web Components 的样式隔离沙箱。

```typescript
import { WebComponentSandboxPlugin } from '@micro-core/plugin-sandbox-webcomponent';

kernel.use(WebComponentSandboxPlugin, {
  shadowDOM: true,                 // 启用 Shadow DOM
  styleIsolation: true,            // 样式隔离
  customElements: true,            // 支持自定义元素
  slotSupport: true                // 支持 slot
});

kernel.registerApplication({
  name: 'app3',
  entry: 'http://localhost:3003',
  container: '#app3',
  activeWhen: '/app3',
  sandbox: 'webcomponent' // 使用 WebComponent 沙箱
});
```

### 4. DefineProperty 沙箱

基于 Object.defineProperty 的兼容性沙箱。

```typescript
import { DefinePropertySandboxPlugin } from '@micro-core/plugin-sandbox-defineproperty';

kernel.use(DefinePropertySandboxPlugin, {
  proxyWindow: true,               // 代理 window 对象
  proxyDocument: false,            // 不代理 document 对象
  restoreGlobals: true,            // 卸载时恢复全局变量
  trackChanges: true               // 跟踪变化
});

kernel.registerApplication({
  name: 'app4',
  entry: 'http://localhost:3004',
  container: '#app4',
  activeWhen: '/app4',
  sandbox: 'defineProperty' // 使用 DefineProperty 沙箱
});
```

### 5. 命名空间沙箱

轻量级的命名空间隔离沙箱。

```typescript
import { NamespaceSandboxPlugin } from '@micro-core/plugin-sandbox-namespace';

kernel.use(NamespaceSandboxPlugin, {
  namespace: 'MicroApps',          // 命名空间名称
  autoCleanup: true,               // 自动清理
  conflictResolution: 'warn'       // 冲突解决策略
});

kernel.registerApplication({
  name: 'app5',
  entry: 'http://localhost:3005',
  container: '#app5',
  activeWhen: '/app5',
  sandbox: 'namespace' // 使用命名空间沙箱
});
```

### 6. 联邦组件沙箱

支持模块联邦的组件级沙箱。

```typescript
import { FederatedSandboxPlugin } from '@micro-core/plugin-sandbox-federated';

kernel.use(FederatedSandboxPlugin, {
  moduleMap: {                     // 模块映射
    'shared-components': 'http://localhost:4000/remoteEntry.js',
    'shared-utils': 'http://localhost:4001/remoteEntry.js'
  },
  sharedDependencies: ['react', 'react-dom'], // 共享依赖
  isolationLevel: 'component'      // 隔离级别
});

kernel.registerApplication({
  name: 'app6',
  entry: 'http://localhost:3006',
  container: '#app6',
  activeWhen: '/app6',
  sandbox: 'federated' // 使用联邦组件沙箱
});
```

## Proxy 沙箱详解

### 基本原理

Proxy 沙箱通过创建一个 window 对象的代理，拦截所有的属性访问和修改操作。

```typescript
class ProxySandbox {
  private proxyWindow: Window;
  private fakeWindow: Record<string, any> = {};
  private addedPropsMap = new Map<string, any>();
  private modifiedPropsMap = new Map<string, any>();

  constructor(name: string, options: ProxySandboxOptions) {
    this.proxyWindow = new Proxy(window, {
      get: (target, prop) => {
        // 优先从沙箱环境获取
        if (this.fakeWindow.hasOwnProperty(prop)) {
          return this.fakeWindow[prop];
        }
        
        // 从原始 window 获取
        const value = target[prop];
        
        // 绑定函数的 this 指向
        if (typeof value === 'function' && !value.bind) {
          return value.bind(target);
        }
        
        return value;
      },
      
      set: (target, prop, value) => {
        // 记录新增的属性
        if (!target.hasOwnProperty(prop)) {
          this.addedPropsMap.set(prop, value);
        } else {
          // 记录修改的属性
          if (!this.modifiedPropsMap.has(prop)) {
            this.modifiedPropsMap.set(prop, target[prop]);
          }
        }
        
        // 设置到沙箱环境
        this.fakeWindow[prop] = value;
        return true;
      },
      
      has: (target, prop) => {
        return this.fakeWindow.hasOwnProperty(prop) || target.hasOwnProperty(prop);
      },
      
      deleteProperty: (target, prop) => {
        if (this.fakeWindow.hasOwnProperty(prop)) {
          delete this.fakeWindow[prop];
          return true;
        }
        return false;
      }
    });
  }

  activate() {
    // 激活沙箱
    this.active = true;
  }

  deactivate() {
    // 停用沙箱，恢复全局环境
    this.active = false;
    
    // 清理新增的属性
    this.addedPropsMap.forEach((_, prop) => {
      delete window[prop];
    });
    
    // 恢复修改的属性
    this.modifiedPropsMap.forEach((originalValue, prop) => {
      window[prop] = originalValue;
    });
  }

  getProxy() {
    return this.proxyWindow;
  }
}
```

### 高级配置

```typescript
kernel.use(ProxySandboxPlugin, {
  // 严格模式配置
  strict: true,
  
  // 白名单：允许访问的全局变量
  whitelist: [
    'console',
    'fetch',
    'setTimeout',
    'setInterval',
    'clearTimeout',
    'clearInterval',
    'Promise',
    'URL',
    'URLSearchParams'
  ],
  
  // 黑名单：禁止访问的全局变量
  blacklist: [
    'eval',
    'Function',
    'WebAssembly'
  ],
  
  // 自定义属性拦截器
  interceptors: {
    get: (target, prop, receiver, sandbox) => {
      // 自定义获取逻辑
      if (prop === 'location') {
        return createVirtualLocation(sandbox.name);
      }
      return Reflect.get(target, prop, receiver);
    },
    
    set: (target, prop, value, receiver, sandbox) => {
      // 自定义设置逻辑
      if (prop === 'document') {
        console.warn(`应用 ${sandbox.name} 尝试修改 document 对象`);
        return false;
      }
      return Reflect.set(target, prop, value, receiver);
    }
  },
  
  // 性能监控
  enablePerformanceMonitor: true,
  performanceThreshold: 10, // 超过10ms的操作会被记录
  
  // 错误处理
  onError: (error, sandbox) => {
    console.error(`沙箱 ${sandbox.name} 发生错误:`, error);
  }
});
```

## Iframe 沙箱详解

### 基本实现

```typescript
class IframeSandbox {
  private iframe: HTMLIFrameElement;
  private iframeWindow: Window;
  private messageHandlers = new Map();

  constructor(name: string, options: IframeSandboxOptions) {
    this.iframe = document.createElement('iframe');
    this.iframe.src = 'about:blank';
    this.iframe.style.display = 'none';
    
    // 设置沙箱属性
    const sandboxFlags = [];
    if (options.allowScripts) sandboxFlags.push('allow-scripts');
    if (options.allowSameOrigin) sandboxFlags.push('allow-same-origin');
    if (options.allowForms) sandboxFlags.push('allow-forms');
    if (options.allowPopups) sandboxFlags.push('allow-popups');
    
    this.iframe.sandbox = sandboxFlags.join(' ');
    
    document.body.appendChild(this.iframe);
    this.iframeWindow = this.iframe.contentWindow!;
    
    // 设置通信机制
    this.setupCommunication();
  }

  private setupCommunication() {
    // 监听来自 iframe 的消息
    window.addEventListener('message', (event) => {
      if (event.source === this.iframeWindow) {
        this.handleMessage(event.data);
      }
    });
  }

  private handleMessage(data: any) {
    const { type, payload, id } = data;
    
    switch (type) {
      case 'API_CALL':
        this.handleApiCall(payload, id);
        break;
      case 'DOM_OPERATION':
        this.handleDomOperation(payload, id);
        break;
      case 'EVENT_EMIT':
        this.handleEventEmit(payload);
        break;
    }
  }

  private handleApiCall(payload: any, id: string) {
    // 处理 API 调用
    const { method, args } = payload;
    
    try {
      const result = this.executeApiCall(method, args);
      this.postMessage({
        type: 'API_RESPONSE',
        payload: result,
        id
      });
    } catch (error) {
      this.postMessage({
        type: 'API_ERROR',
        payload: error.message,
        id
      });
    }
  }

  private postMessage(data: any) {
    this.iframeWindow.postMessage(data, '*');
  }

  activate() {
    this.iframe.style.display = 'block';
  }

  deactivate() {
    this.iframe.style.display = 'none';
  }

  destroy() {
    if (this.iframe.parentNode) {
      this.iframe.parentNode.removeChild(this.iframe);
    }
  }
}
```

### 通信桥接

```typescript
// 在 iframe 内部的通信桥接代码
class IframeBridge {
  private messageId = 0;
  private pendingCalls = new Map();

  constructor() {
    this.setupMessageListener();
    this.injectApis();
  }

  private setupMessageListener() {
    window.addEventListener('message', (event) => {
      const { type, payload, id } = event.data;
      
      switch (type) {
        case 'API_RESPONSE':
          this.resolveApiCall(id, payload);
          break;
        case 'API_ERROR':
          this.rejectApiCall(id, payload);
          break;
      }
    });
  }

  private injectApis() {
    // 注入主应用 API
    window.parent.fetch = this.createApiProxy('fetch');
    window.parent.localStorage = this.createStorageProxy('localStorage');
    window.parent.sessionStorage = this.createStorageProxy('sessionStorage');
  }

  private createApiProxy(apiName: string) {
    return (...args: any[]) => {
      return new Promise((resolve, reject) => {
        const id = `${apiName}_${++this.messageId}`;
        
        this.pendingCalls.set(id, { resolve, reject });
        
        parent.postMessage({
          type: 'API_CALL',
          payload: { method: apiName, args },
          id
        }, '*');
      });
    };
  }

  private resolveApiCall(id: string, result: any) {
    const call = this.pendingCalls.get(id);
    if (call) {
      call.resolve(result);
      this.pendingCalls.delete(id);
    }
  }

  private rejectApiCall(id: string, error: any) {
    const call = this.pendingCalls.get(id);
    if (call) {
      call.reject(new Error(error));
      this.pendingCalls.delete(id);
    }
  }
}

// 在 iframe 中初始化桥接
new IframeBridge();
```

## WebComponent 沙箱详解

### Shadow DOM 隔离

```typescript
class WebComponentSandbox {
  private customElement: HTMLElement;
  private shadowRoot: ShadowRoot;

  constructor(name: string, container: HTMLElement, options: WebComponentSandboxOptions) {
    // 创建自定义元素
    this.customElement = document.createElement(`micro-app-${name}`);
    
    // 创建 Shadow DOM
    this.shadowRoot = this.customElement.attachShadow({ 
      mode: options.shadowDOM ? 'closed' : 'open' 
    });
    
    // 设置样式隔离
    if (options.styleIsolation) {
      this.setupStyleIsolation();
    }
    
    // 挂载到容器
    container.appendChild(this.customElement);
  }

  private setupStyleIsolation() {
    // 创建样式隔离
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        width: 100%;
        height: 100%;
        contain: layout style paint;
      }
      
      /* 重置样式，防止外部样式影响 */
      * {
        box-sizing: border-box;
      }
    `;
    
    this.shadowRoot.appendChild(style);
  }

  loadApplication(html: string, css: string, js: string) {
    // 加载 CSS
    if (css) {
      const style = document.createElement('style');
      style.textContent = css;
      this.shadowRoot.appendChild(style);
    }
    
    // 加载 HTML
    if (html) {
      const container = document.createElement('div');
      container.innerHTML = html;
      this.shadowRoot.appendChild(container);
    }
    
    // 加载 JavaScript
    if (js) {
      this.executeScript(js);
    }
  }

  private executeScript(js: string) {
    // 在隔离的上下文中执行脚本
    const script = document.createElement('script');
    script.textContent = `
      (function() {
        // 创建隔离的执行环境
        const isolatedWindow = {
          document: this.shadowRoot,
          console: window.console,
          setTimeout: window.setTimeout.bind(window),
          setInterval: window.setInterval.bind(window),
          fetch: window.fetch.bind(window)
        };
        
        // 在隔离环境中执行代码
        with (isolatedWindow) {
          ${js}
        }
      }).call(this);
    `;
    
    this.shadowRoot.appendChild(script);
  }

  destroy() {
    if (this.customElement.parentNode) {
      this.customElement.parentNode.removeChild(this.customElement);
    }
  }
}
```

## 沙箱性能优化

### 1. 懒加载沙箱

```typescript
class LazySandboxManager {
  private sandboxCache = new Map();
  private activeSandboxes = new Set();

  async getSandbox(name: string, type: string, options: any) {
    const cacheKey = `${name}-${type}`;
    
    // 检查缓存
    if (this.sandboxCache.has(cacheKey)) {
      const sandbox = this.sandboxCache.get(cacheKey);
      this.activeSandboxes.add(sandbox);
      return sandbox;
    }
    
    // 动态加载沙箱实现
    const SandboxClass = await this.loadSandboxClass(type);
    const sandbox = new SandboxClass(name, options);
    
    // 缓存沙箱实例
    this.sandboxCache.set(cacheKey, sandbox);
    this.activeSandboxes.add(sandbox);
    
    return sandbox;
  }

  private async loadSandboxClass(type: string) {
    switch (type) {
      case 'proxy':
        return (await import('@micro-core/plugin-sandbox-proxy')).ProxySandbox;
      case 'iframe':
        return (await import('@micro-core/plugin-sandbox-iframe')).IframeSandbox;
      case 'webcomponent':
        return (await import('@micro-core/plugin-sandbox-webcomponent')).WebComponentSandbox;
      default:
        throw new Error(`未知的沙箱类型: ${type}`);
    }
  }

  releaseSandbox(sandbox: any) {
    this.activeSandboxes.delete(sandbox);
    
    // 如果没有活跃的沙箱实例，可以考虑清理缓存
    if (this.activeSandboxes.size === 0) {
      this.cleanupInactiveSandboxes();
    }
  }

  private cleanupInactiveSandboxes() {
    // 清理不活跃的沙箱实例
    setTimeout(() => {
      if (this.activeSandboxes.size === 0) {
        this.sandboxCache.clear();
      }
    }, 30000); // 30秒后清理
  }
}
```

### 2. 沙箱池管理

```typescript
class SandboxPool {
  private pools = new Map();
  private maxPoolSize = 5;

  getFromPool(type: string, options: any) {
    const pool = this.pools.get(type) || [];
    
    if (pool.length > 0) {
      const sandbox = pool.pop();
      sandbox.reset(options);
      return sandbox;
    }
    
    return null;
  }

  returnToPool(type: string, sandbox: any) {
    const pool = this.pools.get(type) || [];
    
    if (pool.length < this.maxPoolSize) {
      sandbox.cleanup();
      pool.push(sandbox);
      this.pools.set(type, pool);
    } else {
      sandbox.destroy();
    }
  }

  warmupPool(type: string, count: number, options: any) {
    const pool = [];
    
    for (let i = 0; i < count; i++) {
      const sandbox = this.createSandbox(type, options);
      pool.push(sandbox);
    }
    
    this.pools.set(type, pool);
  }

  private createSandbox(type: string, options: any) {
    // 创建沙箱实例的工厂方法
    switch (type) {
      case 'proxy':
        return new ProxySandbox('pool-instance', options);
      case 'iframe':
        return new IframeSandbox('pool-instance', options);
      default:
        throw new Error(`不支持的沙箱类型: ${type}`);
    }
  }
}
```

## 沙箱调试工具

### 沙箱监控面板

```typescript
class SandboxMonitor {
  private sandboxes = new Map();
  private performanceData = new Map();

  registerSandbox(name: string, sandbox: any) {
    this.sandboxes.set(name, {
      sandbox,
      createdAt: Date.now(),
      activations: 0,
      errors: [],
      performance: {
        activationTime: [],
        deactivationTime: [],
        memoryUsage: []
      }
    });
  }

  recordActivation(name: string) {
    const data = this.sandboxes.get(name);
    if (data) {
      const startTime = performance.now();
      data.activations++;
      
      return () => {
        const endTime = performance.now();
        data.performance.activationTime.push(endTime - startTime);
      };
    }
  }

  recordError(name: string, error: Error) {
    const data = this.sandboxes.get(name);
    if (data) {
      data.errors.push({
        error: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
    }
  }

  getMonitorData(name?: string) {
    if (name) {
      return this.sandboxes.get(name);
    }
    
    return Array.from(this.sandboxes.entries()).map(([name, data]) => ({
      name,
      ...data
    }));
  }

  generateReport() {
    const report = {
      totalSandboxes: this.sandboxes.size,
      activeSandboxes: 0,
      totalErrors: 0,
      averageActivationTime: 0,
      memoryUsage: 0
    };

    let totalActivationTime = 0;
    let activationCount = 0;

    this.sandboxes.forEach((data, name) => {
      if (data.sandbox.active) {
        report.activeSandboxes++;
      }
      
      report.totalErrors += data.errors.length;
      
      data.performance.activationTime.forEach(time => {
        totalActivationTime += time;
        activationCount++;
      });
    });

    if (activationCount > 0) {
      report.averageActivationTime = totalActivationTime / activationCount;
    }

    return report;
  }
}

// 全局监控实例
const sandboxMonitor = new SandboxMonitor();

// 在开发环境中暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.__SANDBOX_MONITOR__ = sandboxMonitor;
}
```

## 最佳实践

### 1. 沙箱选择指南

```typescript
// 根据应用特性选择合适的沙箱
function selectSandbox(appConfig: ApplicationConfig) {
  const { 
    trustLevel,      // 信任级别
    performanceReq,  // 性能要求
    isolationLevel,  // 隔离级别
    compatibility    // 兼容性要求
  } = appConfig;

  if (isolationLevel === 'maximum') {
    return 'iframe'; // 最高隔离级别
  }
  
  if (performanceReq === 'high' && compatibility === 'modern') {
    return 'proxy'; // 高性能现代浏览器
  }
  
  if (appConfig.hasStyleConflicts) {
    return 'webcomponent'; // 样式冲突严重
  }
  
  if (compatibility === 'legacy') {
    return 'defineProperty'; // 兼容老旧浏览器
  }
  
  return 'proxy'; // 默认选择
}
```

### 2. 沙箱配置优化

```typescript
// 生产环境沙箱配置
const productionSandboxConfig = {
  proxy: {
    strict: true,
    whitelist: [
      'console', 'fetch', 'setTimeout', 'setInterval',
      'clearTimeout', 'clearInterval', 'Promise'
    ],
    blacklist: ['eval', 'Function'],
    enablePerformanceMonitor: false
  },
  
  iframe: {
    allowSameOrigin: false,
    allowScripts: true,
    allowForms: true,
    allowPopups: false
  },
  
  webcomponent: {
    shadowDOM: true,
    styleIsolation: true,
    customElements: true
  }
};

// 开发环境沙箱配置
const developmentSandboxConfig = {
  proxy: {
    strict: false,
    enablePerformanceMonitor: true,
    onError: (error, sandbox) => {
      console.error(`沙箱错误 [${sandbox.name}]:`, error);
    }
  }
};
```

### 3. 错误处理和恢复

```typescript
// 沙箱错误处理策略
class SandboxErrorHandler {
  private retryCount = new Map();
  private maxRetries = 3;

  handleSandboxError(error: Error, sandbox: any, app: any) {
    const retries = this.retryCount.get(app.name) || 0;
    
    console.error(`沙箱错误 [${app.name}]:`, error);
    
    if (retries < this.maxRetries) {
      // 尝试重新创建沙箱
      this.recreateSandbox(sandbox, app);
      this.retryCount.set(app.name, retries + 1);
    } else {
      // 降级到更简单的沙箱
      this.fallbackToSimpleSandbox(app);
      this.retryCount.delete(app.name);
    }
  }

  private async recreateSandbox(sandbox: any, app: any) {
    try {
      // 销毁当前沙箱
      sandbox.destroy();
      
      // 创建新沙箱
      const newSandbox = await this.createSandbox(app.sandboxType, app.sandboxOptions);
      
      // 替换沙箱实例
      app.sandbox = newSandbox;
      
      console.log(`沙箱 [${app.name}] 重新创建成功`);
    } catch (error) {
      console.error(`沙箱 [${app.name}] 重新创建失败:`, error);
      throw error;
    }
  }

  private async fallbackToSimpleSandbox(app: any) {
    console.warn(`沙箱 [${app.name}] 降级到命名空间沙箱`);
    
    // 降级到最简单的命名空间沙箱
    const fallbackSandbox = await this.createSandbox('namespace', {
      namespace: `fallback_${app.name}`
    });
    
    app.sandbox = fallbackSandbox;
    app.sandboxType = 'namespace';
  }
}
```
