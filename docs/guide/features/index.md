# 功能指南

micro-core 提供了丰富的功能特性，帮助你构建强大的微前端应用。本节将详细介绍各项功能的使用方法和最佳实践。

## 核心功能

### [应用管理](./app-management.md)
- 应用注册与配置
- 生命周期管理
- 动态加载与卸载
- 应用状态监控

### [路由系统](./routing.md)
- 路由配置与管理
- 动态路由
- 路由守卫
- 嵌套路由

### [应用间通信](./communication.md)
- 消息传递
- 事件总线
- 状态共享
- 数据同步

### [状态管理](./state-management.md)
- 全局状态
- 状态订阅
- 状态持久化
- 状态同步

## 高级功能

### [插件系统](./plugins.md)
- 内置插件使用
- 自定义插件开发
- 插件配置
- 插件生态

### [适配器系统](./adapters.md)
- React 适配器
- Vue 适配器
- Angular 适配器
- 自定义适配器

### [沙箱隔离](./sandbox.md)
- JS 沙箱
- CSS 沙箱
- 沙箱配置
- 安全策略

### [构建集成](./build-integration.md)
- Webpack 集成
- Vite 集成
- Rollup 集成
- 自定义构建

## 开发工具

### [调试工具](./debugging.md)
- 开发者面板
- 性能监控
- 错误追踪
- 日志系统

### [测试支持](./testing.md)
- 单元测试
- 集成测试
- E2E 测试
- 测试工具

## 部署与运维

### [部署策略](./deployment.md)
- 静态部署
- 动态部署
- CDN 优化
- 版本管理

### [监控告警](./monitoring.md)
- 性能监控
- 错误监控
- 业务监控
- 告警配置

## 最佳实践

### [架构设计](./architecture.md)
- 应用拆分策略
- 技术栈选择
- 团队协作
- 代码规范

### [性能优化](./performance.md)
- 加载优化
- 运行时优化
- 内存管理
- 缓存策略

### [安全防护](./security.md)
- XSS 防护
- CSRF 防护
- 内容安全策略
- 权限控制

---

选择你感兴趣的功能开始深入学习，或者按照推荐顺序逐步掌握 micro-core 的各项能力。