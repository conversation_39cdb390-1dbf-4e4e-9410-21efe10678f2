# 安装配置

本章节将指导您如何安装和配置 Micro-Core 微前端架构解决方案。

## 环境要求

在开始之前，请确保您的开发环境满足以下要求：

- **Node.js**: >= 16.0.0
- **包管理器**: npm >= 8.0.0 或 pnpm >= 7.0.0 或 yarn >= 1.22.0
- **TypeScript**: >= 4.5.0 (可选，但推荐)

## 安装方式

### 使用 npm

```bash
npm install @micro-core/core
```

### 使用 pnpm

```bash
pnpm add @micro-core/core
```

### 使用 yarn

```bash
yarn add @micro-core/core
```

## 核心包说明

Micro-Core 采用模块化设计，您可以根据需要安装不同的包：

### 核心包

```bash
# 核心功能包（必需）
npm install @micro-core/core

# 插件系统
npm install @micro-core/plugins

# 框架适配器
npm install @micro-core/adapters

# 构建工具集成
npm install @micro-core/builders
```

### 插件包

```bash
# 路由插件
npm install @micro-core/plugin-router

# 通信插件
npm install @micro-core/plugin-communication

# 认证插件
npm install @micro-core/plugin-auth

# 开发工具插件
npm install @micro-core/plugin-devtools
```

### 适配器包

```bash
# React 适配器
npm install @micro-core/adapter-react

# Vue 适配器
npm install @micro-core/adapter-vue

# Angular 适配器
npm install @micro-core/adapter-angular

# HTML 适配器
npm install @micro-core/adapter-html
```

### 构建工具包

```bash
# Webpack 构建器
npm install @micro-core/builder-webpack

# Vite 构建器
npm install @micro-core/builder-vite

# Rollup 构建器
npm install @micro-core/builder-rollup
```

## 基础配置

### 1. 创建配置文件

在项目根目录创建 `micro-core.config.ts` 配置文件：

```typescript
import { defineConfig } from '@micro-core/core'
import { RouterPlugin } from '@micro-core/plugin-router'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

export default defineConfig({
  // 基础配置
  debug: process.env.NODE_ENV === 'development',
  
  // 插件配置
  plugins: [
    RouterPlugin({
      mode: 'history',
      base: '/'
    }),
    CommunicationPlugin({
      enableGlobalState: true
    })
  ],
  
  // 应用配置
  apps: [
    {
      name: 'main-app',
      entry: './src/main.ts',
      container: '#app'
    }
  ]
})
```

### 2. TypeScript 配置

如果您使用 TypeScript，请在 `tsconfig.json` 中添加类型声明：

```json
{
  "compilerOptions": {
    "types": ["@micro-core/core/types"]
  },
  "include": [
    "src/**/*",
    "micro-core.config.ts"
  ]
}
```

### 3. 构建工具集成

#### Webpack 集成

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack')

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      configFile: './micro-core.config.ts'
    })
  ]
}
```

#### Vite 集成

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/builder-vite'

export default defineConfig({
  plugins: [
    microCore({
      configFile: './micro-core.config.ts'
    })
  ]
})
```

#### Rollup 集成

```javascript
// rollup.config.js
import { microCore } from '@micro-core/builder-rollup'

export default {
  plugins: [
    microCore({
      configFile: './micro-core.config.ts'
    })
  ]
}
```

## 开发环境配置

### 1. 开发服务器

创建开发启动脚本：

```json
{
  "scripts": {
    "dev": "micro-core dev",
    "build": "micro-core build",
    "preview": "micro-core preview"
  }
}
```

### 2. 环境变量

创建 `.env` 文件配置环境变量：

```bash
# 开发环境
NODE_ENV=development
MICRO_CORE_DEBUG=true
MICRO_CORE_PORT=3000

# API 配置
API_BASE_URL=http://localhost:8080
```

### 3. 代理配置

在开发环境中配置 API 代理：

```typescript
// micro-core.config.ts
export default defineConfig({
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

## 生产环境配置

### 1. 构建优化

```typescript
// micro-core.config.ts
export default defineConfig({
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 资源优化
    minify: true,
    sourcemap: false,
    
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          core: ['@micro-core/core']
        }
      }
    }
  }
})
```

### 2. CDN 配置

```typescript
export default defineConfig({
  build: {
    // CDN 配置
    cdn: {
      enabled: true,
      baseUrl: 'https://cdn.example.com/',
      modules: ['react', 'react-dom', 'vue']
    }
  }
})
```

## 验证安装

创建一个简单的测试文件来验证安装是否成功：

```typescript
// test-installation.ts
import { MicroCoreKernel } from '@micro-core/core'

const kernel = new MicroCoreKernel({
  debug: true
})

console.log('Micro-Core 安装成功！', kernel.version)
```

运行测试：

```bash
npx ts-node test-installation.ts
```

如果看到版本信息输出，说明安装成功。

## 常见问题

### 1. 版本兼容性问题

如果遇到版本兼容性问题，请检查：

- Node.js 版本是否满足要求
- 依赖包版本是否匹配
- TypeScript 版本是否兼容

### 2. 模块解析问题

如果遇到模块解析问题，请检查：

- `node_modules` 是否正确安装
- `tsconfig.json` 配置是否正确
- 路径映射是否配置

### 3. 构建工具集成问题

如果构建工具集成有问题，请：

- 检查插件版本兼容性
- 确认配置文件路径正确
- 查看构建日志错误信息

## 下一步

安装配置完成后，您可以：

1. 阅读[快速开始](/guide/getting-started)了解基本用法
2. 查看[基础概念](/guide/concepts)理解核心概念
3. 浏览[示例代码](/examples/)学习最佳实践

如果在安装过程中遇到问题，请查看[常见问题](/guide/faq)或在 [GitHub Issues](https://github.com/micro-core/micro-core/issues) 中提交问题。