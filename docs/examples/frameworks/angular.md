# Angular 微应用示例

本示例展示如何创建和集成 Angular 微应用到 Micro-Core 系统中，支持 Angular 12+ 版本。

## 完整示例

### 主应用配置

```typescript
// main.ts
import { MicroCoreKernel } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { CommunicationPlugin } from '@micro-core/plugin-communication';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

// 创建内核实例
const kernel = new MicroCoreKernel({
  devMode: process.env.NODE_ENV === 'development',
  errorHandler: (error, app) => {
    console.error(`应用 ${app?.name} 发生错误:`, error);
  }
});

// 注册插件
kernel
  .use(RouterPlugin, { mode: 'history' })
  .use(CommunicationPlugin)
  .use(ProxySandboxPlugin, { strict: true });

// 注册 Angular 微应用
kernel.registerApplication({
  name: 'angular-app',
  entry: 'http://localhost:4200',
  container: '#angular-app-container',
  activeWhen: '/angular-app',
  sandbox: 'proxy',
  props: {
    basename: '/angular-app',
    theme: 'light',
    apiConfig: {
      baseUrl: 'https://api.example.com',
      timeout: 5000
    }
  },
  lifecycle: {
    beforeMount: async (app) => {
      console.log('Angular 应用即将挂载:', app.name);
    },
    afterMount: async (app) => {
      console.log('Angular 应用挂载完成:', app.name);
    }
  }
});

// 启动系统
kernel.start();
```

## Angular 微应用

### 1. 项目结构

```
angular-app/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── header/
│   │   │   └── product-list/
│   │   ├── services/
│   │   │   ├── micro-core.service.ts
│   │   │   └── api.service.ts
│   │   ├── guards/
│   │   │   └── auth.guard.ts
│   │   ├── app-routing.module.ts
│   │   ├── app.component.ts
│   │   ├── app.component.html
│   │   ├── app.module.ts
│   │   └── types.ts
│   ├── main.ts
│   └── environments/
├── angular.json
├── package.json
└── tsconfig.json
```

### 2. 入口文件

```typescript
// src/main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { NgModuleRef } from '@angular/core';
import { AngularAdapter } from '@micro-core/adapter-angular';
import { AppModule } from './app/app.module';
import { MicroCoreProps } from './app/types';

let ngModuleRef: NgModuleRef<any> | null = null;

// 微前端生命周期函数
export const mount = AngularAdapter.mount<MicroCoreProps>(async (props) => {
  const { container, basename, theme, apiConfig, ...otherProps } = props;
  
  // 设置全局属性
  (window as any).__MICRO_PROPS__ = {
    basename,
    theme,
    apiConfig,
    ...otherProps
  };
  
  // 创建 Angular 应用
  ngModuleRef = await platformBrowserDynamic()
    .bootstrapModule(AppModule)
    .catch(err => {
      console.error('Angular 应用启动失败:', err);
      throw err;
    });
  
  return {
    instance: ngModuleRef,
    destroy: () => ngModuleRef?.destroy()
  };
});

export const unmount = AngularAdapter.unmount(async () => {
  if (ngModuleRef) {
    ngModuleRef.destroy();
    ngModuleRef = null;
  }
  
  // 清理全局属性
  delete (window as any).__MICRO_PROPS__;
});

export const update = AngularAdapter.update<MicroCoreProps>(async (props) => {
  // 更新全局属性
  (window as any).__MICRO_PROPS__ = {
    ...(window as any).__MICRO_PROPS__,
    ...props
  };
  
  // 通知 Angular 应用属性更新
  if (ngModuleRef) {
    const appRef = ngModuleRef.injector.get('ApplicationRef');
    appRef.tick(); // 触发变更检测
  }
});

// 独立运行模式
if (!window.__MICRO_CORE__) {
  platformBrowserDynamic()
    .bootstrapModule(AppModule)
    .catch(err => console.error(err));
}
```

### 3. 应用模块

```typescript
// src/app/app.module.ts
import { NgModule, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HeaderComponent } from './components/header/header.component';
import { ProductListComponent } from './components/product-list/product-list.component';

import { MicroCoreService } from './services/micro-core.service';
import { ApiService } from './services/api.service';

// 应用初始化函数
export function initializeApp(microCoreService: MicroCoreService) {
  return () => microCoreService.initialize();
}

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    ProductListComponent
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    RouterModule,
    AppRoutingModule
  ],
  providers: [
    MicroCoreService,
    ApiService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [MicroCoreService],
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
```

### 4. 主组件

```typescript
// src/app/app.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MicroCoreService } from './services/micro-core.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'angular-app';
  theme = 'light';
  user: any = null;
  loading = false;
  
  private destroy$ = new Subject<void>();

  constructor(private microCoreService: MicroCoreService) {}

  ngOnInit() {
    // 获取初始状态
    this.theme = this.microCoreService.getGlobalState('theme') || 'light';
    this.user = this.microCoreService.getGlobalState('currentUser');
    
    // 监听主题变化
    this.microCoreService.watchGlobalState('theme')
      .pipe(takeUntil(this.destroy$))
      .subscribe((newTheme: string) => {
        this.theme = newTheme;
      });
    
    // 监听用户状态变化
    this.microCoreService.watchGlobalState('currentUser')
      .pipe(takeUntil(this.destroy$))
      .subscribe((newUser: any) => {
        this.user = newUser;
      });
    
    // 监听主应用事件
    this.microCoreService.onEvent('theme:changed')
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme: string) => {
        this.theme = theme;
      });
    
    this.microCoreService.onEvent('user:login-success')
      .pipe(takeUntil(this.destroy$))
      .subscribe((userData: any) => {
        this.user = userData;
      });
    
    // 向主应用报告应用状态
    this.microCoreService.emitEvent('app:ready', {
      name: 'angular-app',
      version: '1.0.0',
      features: ['routing', 'dependency-injection', 'rxjs']
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onThemeChange(theme: string) {
    this.theme = theme;
    
    // 通知主应用主题变化
    this.microCoreService.emitEvent('child:theme-change', theme);
  }

  onUserLogin(userData: any) {
    this.user = userData;
    
    // 通知主应用用户登录
    this.microCoreService.emitEvent('child:user-login', userData);
  }
}
```

```html
<!-- src/app/app.component.html -->
<div class="angular-app" [class]="'theme-' + theme">
  <app-header 
    [theme]="theme" 
    [user]="user"
    (themeChange)="onThemeChange($event)"
    (userLogin)="onUserLogin($event)">
  </app-header>
  
  <main class="main-content">
    <router-outlet></router-outlet>
  </main>
  
  <div *ngIf="loading" class="loading-overlay">
    <div class="loading-spinner">加载中...</div>
  </div>
</div>
```

### 5. Micro-Core 服务

```typescript
// src/app/services/micro-core.service.ts
import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { EventBus, GlobalState } from '@micro-core/core';

@Injectable({
  providedIn: 'root'
})
export class MicroCoreService {
  private eventSubjects = new Map<string, Subject<any>>();
  private stateSubjects = new Map<string, BehaviorSubject<any>>();
  private isInMicroCore = false;

  constructor() {
    this.isInMicroCore = !!window.__MICRO_CORE__;
  }

  initialize(): Promise<void> {
    return new Promise((resolve) => {
      if (this.isInMicroCore) {
        console.log('Angular 应用在 Micro-Core 环境中运行');
        this.setupEventListeners();
        this.setupStateWatchers();
      } else {
        console.log('Angular 应用独立运行');
      }
      resolve();
    });
  }

  // 事件相关方法
  emitEvent(event: string, data?: any): void {
    if (this.isInMicroCore && EventBus) {
      EventBus.emit(event, data);
    }
  }

  onEvent(event: string): Observable<any> {
    if (!this.eventSubjects.has(event)) {
      const subject = new Subject<any>();
      this.eventSubjects.set(event, subject);

      if (this.isInMicroCore && EventBus) {
        EventBus.on(event, (data: any) => {
          subject.next(data);
        });
      }
    }

    return this.eventSubjects.get(event)!.asObservable();
  }

  // 全局状态相关方法
  setGlobalState(key: string, value: any): void {
    if (this.isInMicroCore && GlobalState) {
      GlobalState.set(key, value);
    }
  }

  getGlobalState(key: string): any {
    if (this.isInMicroCore && GlobalState) {
      return GlobalState.get(key);
    }
    return null;
  }

  watchGlobalState(key: string): Observable<any> {
    if (!this.stateSubjects.has(key)) {
      const currentValue = this.getGlobalState(key);
      const subject = new BehaviorSubject<any>(currentValue);
      this.stateSubjects.set(key, subject);

      if (this.isInMicroCore && GlobalState) {
        GlobalState.watch(key, (newValue: any) => {
          subject.next(newValue);
        });
      }
    }

    return this.stateSubjects.get(key)!.asObservable();
  }

  // 获取微应用属性
  getMicroProps(): any {
    return (window as any).__MICRO_PROPS__ || {};
  }

  private setupEventListeners(): void {
    // 设置通用事件监听器
    if (EventBus) {
      EventBus.onAny((event: string, ...args: any[]) => {
        const subject = this.eventSubjects.get(event);
        if (subject) {
          subject.next(args.length === 1 ? args[0] : args);
        }
      });
    }
  }

  private setupStateWatchers(): void {
    // 设置通用状态监听器
    if (GlobalState) {
      GlobalState.watchAll((key: string, newValue: any) => {
        const subject = this.stateSubjects.get(key);
        if (subject) {
          subject.next(newValue);
        }
      });
    }
  }
}
```

### 6. 路由配置

```typescript
// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

import { HomeComponent } from './components/home/<USER>';
import { ProductListComponent } from './components/product-list/product-list.component';
import { CartComponent } from './components/cart/cart.component';
import { ProfileComponent } from './components/profile/profile.component';

const routes: Routes = [
  { 
    path: '', 
    component: HomeComponent,
    data: { title: '首页' }
  },
  { 
    path: 'products', 
    component: ProductListComponent,
    data: { title: '商品列表' }
  },
  { 
    path: 'cart', 
    component: CartComponent,
    canActivate: [AuthGuard],
    data: { title: '购物车', requiresAuth: true }
  },
  { 
    path: 'profile', 
    component: ProfileComponent,
    canActivate: [AuthGuard],
    data: { title: '个人资料', requiresAuth: true }
  },
  { 
    path: '**', 
    redirectTo: '' 
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    // 使用 Hash 路由避免与主应用路由冲突
    useHash: false,
    // 启用路由跟踪
    enableTracing: false
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
```

### 7. 路由守卫

```typescript
// src/app/guards/auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { MicroCoreService } from '../services/micro-core.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private microCoreService: MicroCoreService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    // 检查全局状态中的用户信息
    return this.microCoreService.watchGlobalState('currentUser').pipe(
      map(user => {
        if (user) {
          return true;
        } else {
          // 通知主应用需要登录
          this.microCoreService.emitEvent('auth:login-required', {
            from: state.url,
            app: 'angular-app'
          });
          
          // 导航到首页
          this.router.navigate(['/']);
          return false;
        }
      })
    );
  }
}
```

### 8. Angular.json 配置

```json
{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "angular-app": {
      "projectType": "application",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        }
      },
      "root": "",
      "sourceRoot": "src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/angular-app",
            "index": "src/index.html",
            "main": "src/main.ts",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "tsconfig.app.json",
            "assets": [
              "src/favicon.ico",
              "src/assets"
            ],
            "styles": [
              "src/styles.scss"
            ],
            "scripts": [],
            "vendorChunk": false,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true
          },
          "configurations": {
            "production": {
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.prod.ts"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true
            }
          }
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "port": 4200,
            "host": "0.0.0.0",
            "disableHostCheck": true
          }
        }
      }
    }
  }
}
```

### 9. Package.json

```json
{
  "name": "angular-micro-app",
  "version": "1.0.0",
  "scripts": {
    "ng": "ng",
    "start": "ng serve",
    "build": "ng build",
    "watch": "ng build --watch --configuration development",
    "test": "ng test"
  },
  "dependencies": {
    "@angular/animations": "^17.0.0",
    "@angular/common": "^17.0.0",
    "@angular/compiler": "^17.0.0",
    "@angular/core": "^17.0.0",
    "@angular/forms": "^17.0.0",
    "@angular/platform-browser": "^17.0.0",
    "@angular/platform-browser-dynamic": "^17.0.0",
    "@angular/router": "^17.0.0",
    "rxjs": "~7.8.0",
    "tslib": "^2.3.0",
    "zone.js": "~0.14.0"
  },
  "devDependencies": {
    "@angular-devkit/build-angular": "^17.0.0",
    "@angular/cli": "^17.0.0",
    "@angular/compiler-cli": "^17.0.0",
    "@micro-core/adapter-angular": "^1.0.0",
    "@types/node": "^18.18.0",
    "typescript": "~5.2.0"
  }
}
```

## 高级特性

### 1. 错误处理

```typescript
// src/app/services/error-handler.service.ts
import { Injectable, ErrorHandler } from '@angular/core';
import { MicroCoreService } from './micro-core.service';

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  constructor(private microCoreService: MicroCoreService) {}

  handleError(error: any): void {
    console.error('Angular 应用错误:', error);
    
    // 向主应用报告错误
    this.microCoreService.emitEvent('app:error', {
      name: 'angular-app',
      error: error.message,
      stack: error.stack
    });
  }
}

// 在 app.module.ts 中注册
import { ErrorHandler } from '@angular/core';
import { GlobalErrorHandler } from './services/error-handler.service';

@NgModule({
  // ...
  providers: [
    // ...
    { provide: ErrorHandler, useClass: GlobalErrorHandler }
  ]
})
export class AppModule { }
```

### 2. 懒加载模块

```typescript
// src/app/app-routing.module.ts
const routes: Routes = [
  // ...
  {
    path: 'admin',
    loadChildren: () => import('./modules/admin/admin.module').then(m => m.AdminModule),
    canLoad: [AuthGuard]
  },
  {
    path: 'reports',
    loadChildren: () => import('./modules/reports/reports.module').then(m => m.ReportsModule)
  }
];
```

### 3. 性能监控

```typescript
// src/app/services/performance.service.ts
import { Injectable } from '@angular/core';
import { MicroCoreService } from './micro-core.service';

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  constructor(private microCoreService: MicroCoreService) {}

  reportPerformance(): void {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    this.microCoreService.emitEvent('app:performance', {
      name: 'angular-app',
      loadTime: navigation.loadEventEnd - navigation.fetchStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      framework: 'Angular'
    });
  }
}
```

## 运行示例

### 1. 启动主应用

```bash
cd main-app
pnpm dev
```

### 2. 启动 Angular 微应用

```bash
cd angular-app
npm start
```

### 3. 访问应用

在浏览器中访问 `http://localhost:3000`，然后导航到 `/angular-app` 路径。

## 最佳实践

1. **依赖注入** - 充分利用 Angular 的依赖注入系统
2. **RxJS** - 使用 Observable 处理异步操作和事件
3. **路由守卫** - 使用路由守卫进行权限控制
4. **懒加载** - 使用模块懒加载优化性能
5. **错误处理** - 实现全局错误处理器
6. **性能监控** - 监控应用性能指标
7. **类型安全** - 充分利用 TypeScript 的类型系统

## 故障排除

### 常见问题

1. **Zone.js 冲突** - 确保 Zone.js 版本兼容
2. **路由冲突** - 正确配置路由基础路径
3. **依赖注入问题** - 检查服务的提供者配置
4. **变更检测问题** - 手动触发变更检测

### 调试技巧

1. 使用 Angular DevTools 调试组件
2. 检查 RxJS 操作符的使用
3. 验证依赖注入的配置
4. 使用 console.log 跟踪生命周期
