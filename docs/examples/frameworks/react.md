# React 微应用开发指南

本指南详细介绍如何开发和集成 React 微应用到 micro-core 微前端架构中。

## 🎯 概述

React 微应用是运行在 micro-core 框架中的独立 React 应用，具有完整的生命周期管理、状态隔离和通信能力。

## 📋 前置要求

- React 16.8+ (推荐 18+)
- TypeScript 4.5+
- 现代构建工具 (Vite/Webpack)

## 🚀 快速开始

### 1. 项目初始化

```bash
# 创建项目
mkdir my-react-micro-app
cd my-react-micro-app
npm init -y

# 安装核心依赖
npm install react react-dom
npm install -D @types/react @types/react-dom typescript

# 安装构建工具
npm install -D vite @vitejs/plugin-react
```

### 2. 微前端生命周期实现

创建 `src/micro-app.tsx`：

```tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// 微前端生命周期接口
interface MicroAppLifecycle {
  mount: (props: MountProps) => Promise<void>;
  unmount: (props: UnmountProps) => Promise<void>;
  bootstrap?: () => Promise<void>;
  update?: (props: UpdateProps) => Promise<void>;
}

interface MountProps {
  container: HTMLElement;
  props?: Record<string, any>;
  globalState?: Record<string, any>;
}

interface UnmountProps {
  container: HTMLElement;
}

interface UpdateProps {
  props?: Record<string, any>;
  globalState?: Record<string, any>;
}

let root: ReactDOM.Root | null = null;

// 微前端生命周期实现
const microApp: MicroAppLifecycle = {
  async bootstrap() {
    console.log('[React App] 应用引导开始');
    
    // 可以在这里进行应用初始化
    // 例如：预加载数据、初始化第三方库等
    
    console.log('[React App] 应用引导完成');
  },

  async mount(props: MountProps) {
    console.log('[React App] 应用挂载开始', props);
    
    const { container, props: appProps, globalState } = props;
    
    // 创建 React 根节点
    root = ReactDOM.createRoot(container);
    
    // 渲染应用，传递属性和全局状态
    root.render(
      <React.StrictMode>
        <App 
          {...appProps} 
          globalState={globalState}
        />
      </React.StrictMode>
    );
    
    console.log('[React App] 应用挂载完成');
  },

  async unmount(props: UnmountProps) {
    console.log('[React App] 应用卸载开始', props);
    
    if (root) {
      root.unmount();
      root = null;
    }
    
    console.log('[React App] 应用卸载完成');
  },

  async update(props: UpdateProps) {
    console.log('[React App] 应用更新', props);
    
    // 处理属性或全局状态更新
    if (root && props) {
      const { props: appProps, globalState } = props;
      root.render(
        <React.StrictMode>
          <App 
            {...appProps} 
            globalState={globalState}
          />
        </React.StrictMode>
      );
    }
  }
};

export default microApp;
```

### 3. React 应用组件

创建 `src/App.tsx`：

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { useMicroCore } from './hooks/useMicroCore';
import './App.css';

interface AppProps {
  globalState?: Record<string, any>;
  [key: string]: any;
}

const App: React.FC<AppProps> = ({ globalState, ...otherProps }) => {
  const microCore = useMicroCore();
  const [localState, setLocalState] = useState({
    count: 0,
    messages: [] as string[]
  });

  // 处理消息接收
  const handleMessage = useCallback((message: any) => {
    console.log('收到消息:', message);
    setLocalState(prev => ({
      ...prev,
      messages: [...prev.messages, `${new Date().toLocaleTimeString()}: ${JSON.stringify(message)}`]
    }));
  }, []);

  useEffect(() => {
    if (!microCore) return;

    // 监听消息
    const unsubscribes = [
      microCore.onMessage('GLOBAL_UPDATE', handleMessage),
      microCore.onMessage('USER_ACTION', handleMessage),
      microCore.onMessage('SYSTEM_NOTIFICATION', handleMessage)
    ];

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe?.());
    };
  }, [microCore, handleMessage]);

  const handleIncrement = () => {
    const newCount = localState.count + 1;
    setLocalState(prev => ({ ...prev, count: newCount }));
    
    // 发送消息给其他应用
    microCore?.broadcast({
      type: 'COUNTER_UPDATE',
      data: { count: newCount, from: 'react-app' }
    });
    
    // 更新全局状态
    microCore?.setState('reactCounter', newCount);
  };

  const sendTestMessage = () => {
    microCore?.broadcast({
      type: 'USER_ACTION',
      data: { 
        action: 'test_message',
        message: 'Hello from React!',
        timestamp: Date.now()
      }
    });
  };

  return (
    <div className="react-app">
      <header className="app-header">
        <h1>⚛️ React 微应用</h1>
        <p>独立运行的 React 应用，集成在微前端架构中</p>
      </header>

      <main className="app-main">
        <section className="feature-section">
          <h2>📊 状态管理</h2>
          <div className="counter-container">
            <div className="counter-display">{localState.count}</div>
            <button onClick={handleIncrement} className="btn btn-primary">
              增加计数
            </button>
          </div>
          
          <div className="global-state">
            <h3>全局状态:</h3>
            <pre>{JSON.stringify(globalState, null, 2)}</pre>
          </div>
        </section>

        <section className="feature-section">
          <h2>💬 应用通信</h2>
          <button onClick={sendTestMessage} className="btn btn-secondary">
            发送测试消息
          </button>
          
          <div className="message-log">
            <h3>消息日志:</h3>
            <div className="messages">
              {localState.messages.length === 0 ? (
                <p className="no-messages">暂无消息</p>
              ) : (
                localState.messages.map((msg, index) => (
                  <div key={index} className="message-item">
                    {msg}
                  </div>
                ))
              )}
            </div>
          </div>
        </section>

        <section className="feature-section">
          <h2>🔧 应用信息</h2>
          <div className="app-info">
            <p><strong>应用名称:</strong> React 微应用</p>
            <p><strong>React 版本:</strong> {React.version}</p>
            <p><strong>运行模式:</strong> {process.env.NODE_ENV}</p>
            <p><strong>传入属性:</strong></p>
            <pre>{JSON.stringify(otherProps, null, 2)}</pre>
          </div>
        </section>
      </main>
    </div>
  );
};

export default App;
```

### 4. 自定义 Hook

创建 `src/hooks/useMicroCore.ts`：

```typescript
import { useEffect, useState } from 'react';

interface MicroCoreAPI {
  sendMessage: (target: string, message: any) => void;
  broadcast: (message: any) => void;
  onMessage: (type: string, handler: (data: any) => void) => () => void;
  setState: (key: string, value: any) => void;
  getState: (key: string) => any;
  subscribe: (key: string, handler: (newValue: any, oldValue: any) => void) => () => void;
}

declare global {
  interface Window {
    microCore?: MicroCoreAPI;
  }
}

export const useMicroCore = (): MicroCoreAPI | null => {
  const [microCore, setMicroCore] = useState<MicroCoreAPI | null>(null);

  useEffect(() => {
    // 检查 microCore 是否可用
    if (window.microCore) {
      setMicroCore(window.microCore);
    } else {
      // 如果不可用，可能需要等待初始化
      const checkMicroCore = () => {
        if (window.microCore) {
          setMicroCore(window.microCore);
        } else {
          setTimeout(checkMicroCore, 100);
        }
      };
      checkMicroCore();
    }
  }, []);

  return microCore;
};

// 状态管理 Hook
export const useMicroState = <T>(key: string, defaultValue?: T) => {
  const microCore = useMicroCore();
  const [value, setValue] = useState<T>(defaultValue as T);

  useEffect(() => {
    if (!microCore) return;

    // 获取初始值
    const initialValue = microCore.getState(key);
    if (initialValue !== undefined) {
      setValue(initialValue);
    }

    // 订阅状态变化
    const unsubscribe = microCore.subscribe(key, (newValue: T) => {
      setValue(newValue);
    });

    return unsubscribe;
  }, [microCore, key]);

  const updateValue = (newValue: T) => {
    microCore?.setState(key, newValue);
  };

  return [value, updateValue] as const;
};

// 消息通信 Hook
export const useMicroMessage = (messageType: string, handler: (data: any) => void) => {
  const microCore = useMicroCore();

  useEffect(() => {
    if (!microCore) return;

    const unsubscribe = microCore.onMessage(messageType, handler);
    return unsubscribe;
  }, [microCore, messageType, handler]);

  const sendMessage = (target: string, data: any) => {
    microCore?.sendMessage(target, { type: messageType, data });
  };

  const broadcast = (data: any) => {
    microCore?.broadcast({ type: messageType, data });
  };

  return { sendMessage, broadcast };
};
```

### 5. 样式文件

创建 `src/App.css`：

```css
.react-app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.app-main {
  display: grid;
  gap: 2rem;
}

.feature-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.feature-section h2 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.5rem;
}

.counter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.counter-display {
  font-size: 3rem;
  font-weight: bold;
  color: #667eea;
  min-width: 80px;
  text-align: center;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #48bb78;
  color: white;
}

.btn-secondary:hover {
  background: #38a169;
  transform: translateY(-1px);
}

.global-state {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.global-state h3 {
  margin: 0 0 0.5rem 0;
  color: #4a5568;
}

.global-state pre {
  margin: 0;
  font-size: 0.875rem;
  color: #2d3748;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-log {
  margin-top: 1.5rem;
}

.message-log h3 {
  margin: 0 0 1rem 0;
  color: #4a5568;
}

.messages {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  background: #f7fafc;
}

.no-messages {
  color: #a0aec0;
  font-style: italic;
  text-align: center;
  margin: 0;
}

.message-item {
  background: white;
  padding: 0.5rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  font-size: 0.875rem;
  border-left: 3px solid #667eea;
}

.app-info {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.app-info p {
  margin: 0.5rem 0;
  color: #4a5568;
}

.app-info strong {
  color: #2d3748;
}

.app-info pre {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  color: #2d3748;
  background: white;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .react-app {
    padding: 1rem;
  }
  
  .app-header {
    padding: 1.5rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
  
  .counter-container {
    flex-direction: column;
    text-align: center;
  }
  
  .counter-display {
    font-size: 2.5rem;
  }
}
```

### 6. 应用入口

创建 `src/main.tsx`：

```tsx
import microApp from './micro-app';

// 导出微前端接口
(window as any)['react-app'] = microApp;

// 标记微前端环境
(window as any).__MICRO_CORE__ = true;

// 独立运行时的处理
if (process.env.NODE_ENV === 'development' && !window.microCore) {
  import('./standalone').then(({ startStandalone }) => {
    startStandalone();
  });
}
```

### 7. 独立运行模式

创建 `src/standalone.tsx`：

```tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './App.css';

// 模拟 microCore API
const mockMicroCore = {
  sendMessage: (target: string, message: any) => {
    console.log(`[Mock] 发送消息给 ${target}:`, message);
  },
  
  broadcast: (message: any) => {
    console.log('[Mock] 广播消息:', message);
  },
  
  onMessage: (type: string, handler: (data: any) => void) => {
    console.log(`[Mock] 监听消息类型: ${type}`);
    return () => console.log(`[Mock] 取消监听: ${type}`);
  },
  
  setState: (key: string, value: any) => {
    console.log(`[Mock] 设置状态 ${key}:`, value);
  },
  
  getState: (key: string) => {
    console.log(`[Mock] 获取状态: ${key}`);
    return null;
  },
  
  subscribe: (key: string, handler: (newValue: any, oldValue: any) => void) => {
    console.log(`[Mock] 订阅状态: ${key}`);
    return () => console.log(`[Mock] 取消订阅: ${key}`);
  }
};

export const startStandalone = () => {
  console.log('[React App] 独立模式启动');
  
  // 设置模拟的 microCore
  (window as any).microCore = mockMicroCore;
  
  const container = document.getElementById('root');
  if (!container) {
    document.body.innerHTML = '<div id="root"></div>';
  }
  
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(
    <React.StrictMode>
      <App globalState={{ mode: 'standalone' }} />
    </React.StrictMode>
  );
};
```

### 8. 构建配置

创建 `vite.config.ts`：

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  },
  
  build: {
    // 微前端模式构建配置
    lib: {
      entry: 'src/main.tsx',
      name: 'ReactMicroApp',
      fileName: 'index',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    },
    
    // 确保构建输出适合微前端加载
    cssCodeSplit: false,
    sourcemap: true
  },
  
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  }
});
```

### 9. TypeScript 配置

创建 `tsconfig.json`：

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    /* 微前端相关 */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 10. 包配置

更新 `package.json`：

```json
{
  "name": "react-micro-app",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@vitejs/plugin-react": "^4.2.1",
    "eslint": "^8.55.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5",
    "typescript": "^5.2.2",
    "vite": "^5.0.8"
  }
}
```

## 🔧 高级特性

### 1. 错误边界

创建 `src/components/ErrorBoundary.tsx`：

```tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[React App] 错误边界捕获错误:', error, errorInfo);
    
    // 调用外部错误处理器
    this.props.onError?.(error, errorInfo);
    
    // 向主应用报告错误
    if (window.microCore) {
      window.microCore.broadcast({
        type: 'APP_ERROR',
        data: {
          appName: 'react-app',
          error: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack
        }
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>🚨 应用出现错误</h2>
          <p>React 应用遇到了一个错误，请刷新页面重试。</p>
          <details>
            <summary>错误详情</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
          <button 
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="btn btn-primary"
          >
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### 2. 路由集成

创建 `src/components/MicroRouter.tsx`：

```tsx
import React, { createContext, useContext, useEffect, useState } from 'react';

interface RouteInfo {
  path: string;
  query: Record<string, string>;
  params: Record<string, string>;
}

interface MicroRouterContextType {
  currentRoute: RouteInfo;
  navigate: (path: string) => void;
  goBack: () => void;
}

const MicroRouterContext = createContext<MicroRouterContextType | null>(null);

export const useMicroRouter = () => {
  const context = useContext(MicroRouterContext);
  if (!context) {
    throw new Error('useMicroRouter must be used within MicroRouterProvider');
  }
  return context;
};

interface MicroRouterProviderProps {
  children: React.ReactNode;
}

export const MicroRouterProvider: React.FC<MicroRouterProviderProps> = ({ children }) => {
  const [currentRoute, setCurrentRoute] = useState<RouteInfo>({
    path: '/',
    query: {},
    params: {}
  });

  useEffect(() => {
    // 监听路由变化
    const handleRouteChange = () => {
      const path = window.location.pathname;
      const search = window.location.search;
      const query: Record<string, string> = {};
      
      // 解析查询参数
      if (search) {
        const params = new URLSearchParams(search);
        params.forEach((value, key) => {
          query[key] = value;
        });
      }
      
      setCurrentRoute({
        path,
        query,
        params: {} // 这里可以根据路由规则解析参数
      });
    };

    // 初始化路由
    handleRouteChange();
    
    // 监听路由变化
    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  const navigate = (path: string) => {
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  const goBack = () => {
    window.history.back();
  };

  const value: MicroRouterContextType = {
    currentRoute,
    navigate,
    goBack
  };

  return (
    <MicroRouterContext.Provider value={value}>
      {children}
    </MicroRouterContext.Provider>
  );
};
```

### 3. 性能优化

创建 `src/hooks/usePerformance.ts`：

```typescript
import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  mountTime: number;
  renderTime: number;
  updateCount: number;
}

export const usePerformance = (componentName: string) => {
  const metricsRef = useRef<PerformanceMetrics>({
    mountTime: 0,
    renderTime: 0,
    updateCount: 0
  });
  
  const mountTimeRef = useRef<number>(0);
  const renderStartRef = useRef<number>(0);

  // 组件挂载时间
  useEffect(() => {
    mountTimeRef.current = performance.now();
    
    return () => {
      const unmountTime = performance.now();
      const totalMountTime = unmountTime - mountTimeRef.current;
      
      console.log(`[Performance] ${componentName} 总生命周期时间: ${totalMountTime.toFixed(2)}ms`);
      
      // 向主应用报告性能数据
      if (window.microCore) {
        window.microCore.broadcast({
          type: 'PERFORMANCE_METRICS',
          data: {
            appName: 'react-app',
            componentName,
            metrics: {
              ...metricsRef.current,
              totalLifetime: totalMountTime
            }
          }
        });
      }
    };
  }, [componentName]);

  // 渲染性能监控
  useEffect(() => {
    renderStartRef.current = performance.now();
    metricsRef.current.updateCount++;
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartRef.current;
    metricsRef.current.renderTime = renderTime;
    
    if (renderTime > 16) { // 超过一帧的时间
      console.warn(`[Performance] ${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms`);
    }
  });

  return metricsRef.current;
};

// 懒加载组件 Hook
export const useLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const [Component, setComponent] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);

  useEffect(() => {
    importFunc()
      .then(module => {
        setComponent(() => module.default);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, []);

  return { Component, loading, error, Fallback: fallback };
};
```

## 🚀 部署和集成

### 1. 构建生产版本

```bash
# 构建微前端版本
npm run build

# 构建产物会生成在 dist 目录
# - index.umd.js: 微前端入口文件
# - style.css: 样式文件
```

### 2. 在主应用中注册

```typescript
// 在主应用中注册 React 微应用
microCore.registerApp({
  name: 'react-app',
  entry: 'https://your-cdn.com/react-app/index.umd.js',
  container: '#react-app-container',
  activeWhen: '/react',
  framework: 'react',
  
  // 传递给应用的属性
  props: {
    apiBaseUrl: 'https://api.example.com',
    theme: 'light',
    userRole: 'admin'
  },
  
  // 生命周期钩子
  beforeMount: async (app) => {
    console.log('React 应用即将挂载');
    // 可以在这里预加载数据
  },
  
  afterMount: (app) => {
    console.log('React 应用挂载完成');
    // 发送初始化消息
    microCore.sendMessage('react-app', {
      type: 'INIT',
      data: { timestamp: Date.now() }
    });
  }
});
```

### 3. CDN 部署配置

```javascript
// webpack.config.js 或 vite.config.ts 中配置 CDN
export default {
  build: {
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        },
        // 配置 CDN 路径
        assetFileNames: 'assets/[name].[hash][extname]',
        chunkFileNames: 'assets/[name].[hash].js',
        entryFileNames: 'assets/[name].[hash].js'
      }
    }
  }
};
```

## 🔍 调试和测试

### 1. 开发调试

```typescript
// 开发环境调试工具
if (process.env.NODE_ENV === 'development') {
  // 添加全局调试方法
  (window as any).__REACT_APP_DEBUG__ = {
    getState: () => {
      // 返回应用状态
    },
    triggerError: () => {
      throw new Error('测试错误边界');
    },
    sendTestMessage: () => {
      window.microCore?.broadcast({
        type: 'DEBUG_MESSAGE',
        data: { from: 'react-app', timestamp: Date.now() }
      });
    }
  };
}
```

### 2. 单元测试

```typescript
// src/__tests__/App.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import App from '../App';

// 模拟 microCore
const mockMicroCore = {
  sendMessage: jest.fn(),
  broadcast: jest.fn(),
  onMessage: jest.fn(() => () => {}),
  setState: jest.fn(),
  getState: jest.fn(),
  subscribe: jest.fn(() => () => {})
};

beforeEach(() => {
  (window as any).microCore = mockMicroCore;
});

test('应用正常渲染', () => {
  render(<App />);
  expect(screen.getByText('React 微应用')).toBeInTheDocument();
});

test('计数器功能正常', () => {
  render(<App />);
  const button = screen.getByText('增加计数');
  fireEvent.click(button);
  
  expect(mockMicroCore.broadcast).toHaveBeenCalledWith({
    type: 'COUNTER_UPDATE',
    data: { count: 1, from: 'react-app' }
  });
});
```

## 📚 最佳实践

### 1. 状态管理

- 使用 `useMicroState` Hook 管理全局状态
- 本地状态使用 React 内置的 `useState`
- 避免在微应用间直接共享复杂对象

### 2. 性能优化

- 使用 `React.memo` 优化组件渲染
- 合理使用 `useCallback` 和 `useMemo`
- 实现懒加载减少初始包大小

### 3. 错误处理

- 使用错误边界捕获组件错误
- 向主应用报告错误信息
- 提供友好的错误恢复机制

### 4. 样式隔离

- 使用 CSS Modules 或 styled-components
- 避免全局样式污染
- 使用 CSS 变量支持主题切换

## 🔗 相关资源

- [React 官方文档](https://react.dev/)
- [micro-core 核心 API](../../api/core.md)
- [微前端最佳实践](../advanced/performance.md)
- [错误处理指南](../advanced/error-handling.md)

---

通过本指南，你应该能够成功创建和集成 React 微应用到 micro-core 微前端架构中。记住要遵循最佳实践，确保应用的稳定性和性能。
