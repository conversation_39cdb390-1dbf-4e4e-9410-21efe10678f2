import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'Micro-Core',
  description: '下一代微前端架构解决方案 - 高性能、高扩展性、高可靠性',
  lang: 'zh-CN',
  base: '/',
  cleanUrls: true,
  ignoreDeadLinks: true,

  // 主题配置
  themeConfig: {
    // 网站标题和Logo
    siteTitle: 'Micro-Core',
    logo: '/logo.svg',

    // 导航栏
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/introduction' },
      { text: 'API参考', link: '/api/core' },
      { text: '示例', link: '/examples/' },
      { text: '演练场', link: '/playground/' },
      {
        text: '微前端迁移',
        items: [
          { text: 'qiankun 迁移指南', link: '/migration/qiankun' },
          { text: 'wujie 迁移指南', link: '/migration/wujie' },
          { text: '通用迁移策略', link: '/migration/general' }
        ]
      },
      {
        text: '生态系统',
        items: [
          { text: '插件系统', link: '/ecosystem/plugins' },
          { text: '适配器', link: '/ecosystem/adapters' },
          { text: '构建工具', link: '/ecosystem/builders' },
          { text: '边车模式', link: '/ecosystem/sidecar' }
        ]
      },
      { text: '更新日志', link: '/changelog' },
      {
        text: '相关链接',
        items: [
          { text: 'GitHub', link: 'https://github.com/echo008/micro-core' },
          { text: 'NPM', link: 'https://www.npmjs.com/package/@micro-core/core' },
          { text: '问题反馈', link: 'https://github.com/echo008/micro-core/issues' }
        ]
      }
    ],

    // 侧边栏
    sidebar: {
      '/guide/': [
        {
          text: '开始使用',
          items: [
            { text: '介绍', link: '/guide/introduction' },
            { text: '快速开始', link: '/guide/getting-started' },
            { text: '安装配置', link: '/guide/installation' },
            { text: '基础概念', link: '/guide/concepts' }
          ]
        },
        {
          text: '核心功能',
          items: [
            { text: '应用管理', link: '/guide/features/app-management' },
            { text: '路由系统', link: '/guide/features/routing' },
            { text: '应用间通信', link: '/guide/features/communication' },
            { text: '状态管理', link: '/guide/features/state-management' },
            { text: '生命周期', link: '/guide/features/lifecycle' },
            { text: '沙箱隔离', link: '/guide/features/sandbox' }
          ]
        },
        {
          text: '高级特性',
          items: [
            { text: '插件系统', link: '/guide/advanced/plugins' },
            { text: '多框架适配', link: '/guide/advanced/adapters' },
            { text: '构建集成', link: '/guide/advanced/build-integration' },
            { text: '边车模式', link: '/guide/advanced/sidecar-mode' },
            { text: '高性能加载器', link: '/guide/advanced/loaders' },
            { text: '智能预加载', link: '/guide/advanced/prefetch' }
          ]
        },
        {
          text: '最佳实践',
          items: [
            { text: '架构设计', link: '/guide/best-practices/architecture' },
            { text: '性能优化', link: '/guide/best-practices/performance' },
            { text: '错误处理', link: '/guide/best-practices/error-handling' },
            { text: '测试策略', link: '/guide/best-practices/testing' },
            { text: '部署指南', link: '/guide/best-practices/deployment' }
          ]
        }
      ],
      '/api/': [
        {
          text: '核心API',
          items: [
            { text: 'MicroCore', link: '/api/core' },
            { text: '应用管理', link: '/api/app-management' },
            { text: '路由系统', link: '/api/routing' },
            { text: '通信系统', link: '/api/communication' },
            { text: '状态管理', link: '/api/state-management' }
          ]
        },
        {
          text: '插件API',
          items: [
            { text: '插件基类', link: '/api/plugins/base' },
            { text: '路由插件', link: '/api/plugins/router' },
            { text: '通信插件', link: '/api/plugins/communication' },
            { text: '认证插件', link: '/api/plugins/auth' }
          ]
        },
        {
          text: '适配器API',
          items: [
            { text: '适配器基类', link: '/api/adapters/base' },
            { text: 'React适配器', link: '/api/adapters/react' },
            { text: 'Vue适配器', link: '/api/adapters/vue' },
            { text: 'Angular适配器', link: '/api/adapters/angular' }
          ]
        }
      ],
      '/examples/': [
        {
          text: '基础示例',
          items: [
            { text: '概览', link: '/examples/' },
            { text: '基本使用', link: '/examples/basic/' },
            { text: 'React应用', link: '/examples/frameworks/react' },
            { text: 'Vue应用', link: '/examples/frameworks/vue' },
            { text: 'Angular应用', link: '/examples/frameworks/angular' },
            { text: 'Svelte应用', link: '/examples/frameworks/svelte' },
            { text: 'Solid应用', link: '/examples/frameworks/solid' }
          ]
        },
        {
          text: '进阶示例',
          items: [
            { text: '多应用协作', link: '/examples/advanced/multi-app' },
            { text: '应用间通信', link: '/examples/advanced/communication' },
            { text: '共享状态', link: '/examples/advanced/shared-state' },
            { text: '动态路由', link: '/examples/advanced/dynamic-routing' },
            { text: '高性能加载', link: '/examples/advanced/performance' }
          ]
        }
      ],
      '/migration/': [
        {
          text: 'qiankun 迁移',
          items: [
            { text: '迁移概述', link: '/migration/qiankun' },
            { text: 'API 对照表', link: '/migration/qiankun/api-mapping' },
            { text: '配置迁移', link: '/migration/qiankun/config-migration' },
            { text: '生命周期迁移', link: '/migration/qiankun/lifecycle-migration' },
            { text: '通信迁移', link: '/migration/qiankun/communication-migration' },
            { text: '完整示例', link: '/migration/qiankun/complete-example' }
          ]
        },
        {
          text: 'wujie 迁移',
          items: [
            { text: '迁移概述', link: '/migration/wujie' },
            { text: 'API 对照表', link: '/migration/wujie/api-mapping' },
            { text: '配置迁移', link: '/migration/wujie/config-migration' },
            { text: '沙箱迁移', link: '/migration/wujie/sandbox-migration' },
            { text: '通信迁移', link: '/migration/wujie/communication-migration' },
            { text: '完整示例', link: '/migration/wujie/complete-example' }
          ]
        },
        {
          text: '通用迁移策略',
          items: [
            { text: '迁移规划', link: '/migration/general' },
            { text: '渐进式迁移', link: '/migration/general/progressive' },
            { text: '兼容性处理', link: '/migration/general/compatibility' },
            { text: '测试策略', link: '/migration/general/testing' }
          ]
        }
      ],
      '/ecosystem/': [
        {
          text: '插件生态',
          items: [
            { text: '插件概览', link: '/ecosystem/plugins' },
            { text: '路由插件', link: '/ecosystem/plugins/router' },
            { text: '通信插件', link: '/ecosystem/plugins/communication' },
            { text: '认证插件', link: '/ecosystem/plugins/auth' }
          ]
        },
        {
          text: '框架适配器',
          items: [
            { text: '适配器概览', link: '/ecosystem/adapters' },
            { text: 'React适配器', link: '/ecosystem/adapters/react' },
            { text: 'Vue适配器', link: '/ecosystem/adapters/vue' },
            { text: 'Angular适配器', link: '/ecosystem/adapters/angular' }
          ]
        },
        {
          text: '构建工具',
          items: [
            { text: '构建工具概览', link: '/ecosystem/builders' },
            { text: 'Webpack集成', link: '/ecosystem/builders/webpack' },
            { text: 'Vite集成', link: '/ecosystem/builders/vite' },
            { text: 'Rollup集成', link: '/ecosystem/builders/rollup' }
          ]
        }
      ]
    },

    // 社交链接
    socialLinks: [
      { icon: 'github', link: 'https://github.com/micro-core/micro-core' }
    ],

    // 页脚
    footer: {
      message: '基于 MIT 许可发布',
      copyright: 'Copyright © 2024 Micro-Core'
    },

    // 搜索
    search: {
      provider: 'local',
      options: {
        locales: {
          zh: {
            translations: {
              button: {
                buttonText: '搜索文档',
                buttonAriaLabel: '搜索文档'
              },
              modal: {
                noResultsText: '无法找到相关结果',
                resetButtonTitle: '清除查询条件',
                footer: {
                  selectText: '选择',
                  navigateText: '切换'
                }
              }
            }
          }
        }
      }
    },

    // 编辑链接
    editLink: {
      pattern: 'https://github.com/micro-core/micro-core/edit/main/docs/:path',
      text: '在 GitHub 上编辑此页面'
    },

    // 最后更新时间
    lastUpdated: {
      text: '最后更新于',
      formatOptions: {
        dateStyle: 'short',
        timeStyle: 'medium'
      }
    },

    // 文档页脚导航
    docFooter: {
      prev: '上一页',
      next: '下一页'
    },

    // 大纲标题
    outline: {
      label: '页面导航'
    },

    // 返回顶部
    returnToTopLabel: '回到顶部',

    // 外部链接图标
    externalLinkIcon: true
  },

  // 头部配置
  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
    ['meta', { name: 'theme-color', content: '#1e40af' }],
    ['meta', { name: 'og:type', content: 'website' }],
    ['meta', { name: 'og:locale', content: 'zh-CN' }],
    ['meta', { name: 'og:title', content: 'Micro-Core | 现代化微前端架构解决方案' }],
    ['meta', { name: 'og:site_name', content: 'Micro-Core' }],
    ['meta', { name: 'og:image', content: '/og-image.png' }],
    ['meta', { name: 'og:url', content: 'https://micro-core.dev/' }],
    ['meta', { name: 'twitter:card', content: 'summary_large_image' }],
    ['meta', { name: 'twitter:image', content: '/og-image.png' }]
  ],

  // 构建配置
  build: {
    outDir: '../dist/docs'
  },

  // Markdown配置
  markdown: {
    theme: {
      light: 'github-light',
      dark: 'github-dark'
    },
    lineNumbers: true,
    config: (md) => {
      // 自定义markdown插件配置
    }
  },

  // Vue配置
  vue: {
    template: {
      compilerOptions: {
        isCustomElement: (tag) => tag.includes('-')
      }
    }
  }
})