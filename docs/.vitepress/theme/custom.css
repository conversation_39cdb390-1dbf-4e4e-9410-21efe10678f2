/**
 * 自定义主题样式
 * micro-core 文档系统专用样式
 */

/* 主色调配置 */
:root {
    /* 品牌色彩 */
    --vp-c-brand-1: #1e40af;
    --vp-c-brand-2: #3b82f6;
    --vp-c-brand-3: #60a5fa;
    --vp-c-brand-soft: rgba(30, 64, 175, 0.14);

    /* 强调色 */
    --vp-c-accent-1: #0ea5e9;
    --vp-c-accent-2: #38bdf8;
    --vp-c-accent-3: #7dd3fc;

    /* 背景色 */
    --vp-c-bg: #ffffff;
    --vp-c-bg-alt: #f8fafc;
    --vp-c-bg-elv: #ffffff;
    --vp-c-bg-soft: #f1f5f9;

    /* 文本色 */
    --vp-c-text-1: #1e293b;
    --vp-c-text-2: #475569;
    --vp-c-text-3: #64748b;

    /* 边框色 */
    --vp-c-border: #e2e8f0;
    --vp-c-divider: #e2e8f0;
    --vp-c-gutter: #e2e8f0;

    /* 代码块配色 */
    --vp-code-block-bg: #f8fafc;
    --vp-code-bg: #f1f5f9;

    /* 自定义变量 */
    --micro-core-primary: #1e40af;
    --micro-core-secondary: #0ea5e9;
    --micro-core-success: #10b981;
    --micro-core-warning: #f59e0b;
    --micro-core-error: #ef4444;
    --micro-core-info: #6366f1;
}

/* 暗色主题 */
.dark {
    --vp-c-brand-1: #3b82f6;
    --vp-c-brand-2: #60a5fa;
    --vp-c-brand-3: #93c5fd;
    --vp-c-brand-soft: rgba(59, 130, 246, 0.14);

    --vp-c-accent-1: #38bdf8;
    --vp-c-accent-2: #7dd3fc;
    --vp-c-accent-3: #bae6fd;

    --vp-c-bg: #0f172a;
    --vp-c-bg-alt: #1e293b;
    --vp-c-bg-elv: #1e293b;
    --vp-c-bg-soft: #334155;

    --vp-c-text-1: #f1f5f9;
    --vp-c-text-2: #cbd5e1;
    --vp-c-text-3: #94a3b8;

    --vp-c-border: #334155;
    --vp-c-divider: #334155;
    --vp-c-gutter: #334155;

    --vp-code-block-bg: #1e293b;
    --vp-code-bg: #334155;
}

/* 首页英雄区域样式 */
.VPHero {
    padding-top: 48px !important;
    padding-bottom: 48px !important;
}

.VPHero .name {
    background: linear-gradient(135deg, var(--micro-core-primary) 0%, var(--micro-core-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.VPHero .text {
    font-size: 32px;
    line-height: 1.2;
    font-weight: 600;
    color: var(--vp-c-text-1);
}

.VPHero .tagline {
    font-size: 18px;
    color: var(--vp-c-text-2);
    max-width: 600px;
    margin: 0 auto;
}

/* 功能特性卡片样式 */
.VPFeatures {
    padding: 48px 24px;
}

.VPFeature {
    border: 1px solid var(--vp-c-border);
    border-radius: 12px;
    padding: 24px;
    background: var(--vp-c-bg-soft);
    transition: all 0.3s ease;
    height: 100%;
}

.VPFeature:hover {
    border-color: var(--vp-c-brand-1);
    box-shadow: 0 8px 32px rgba(30, 64, 175, 0.12);
    transform: translateY(-2px);
}

.VPFeature .icon {
    font-size: 32px;
    margin-bottom: 16px;
    display: block;
}

.VPFeature .title {
    font-size: 18px;
    font-weight: 600;
    color: var(--vp-c-text-1);
    margin-bottom: 8px;
}

.VPFeature .details {
    font-size: 14px;
    color: var(--vp-c-text-2);
    line-height: 1.6;
}

/* 导航栏样式增强 */
.VPNav {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid var(--vp-c-divider);
}

.dark .VPNav {
    background: rgba(15, 23, 42, 0.85);
}

/* 侧边栏样式 */
.VPSidebar {
    background: var(--vp-c-bg-alt);
    border-right: 1px solid var(--vp-c-divider);
}

.VPSidebarItem.level-0>.item>.link {
    font-weight: 600;
    color: var(--vp-c-text-1);
}

.VPSidebarItem.is-active>.item>.link {
    color: var(--vp-c-brand-1);
    background: var(--vp-c-brand-soft);
}

/* 内容区域样式 */
.VPContent {
    padding-top: 32px;
}

.vp-doc h1 {
    border-bottom: 2px solid var(--vp-c-brand-1);
    padding-bottom: 16px;
    margin-bottom: 32px;
}

.vp-doc h2 {
    border-bottom: 1px solid var(--vp-c-divider);
    padding-bottom: 8px;
    margin-top: 48px;
    margin-bottom: 16px;
}

.vp-doc h3 {
    margin-top: 32px;
    margin-bottom: 16px;
    color: var(--vp-c-brand-1);
}

/* 代码块样式增强 */
.vp-code-group {
    margin: 24px 0;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--vp-c-border);
}

.vp-code-group .tabs {
    background: var(--vp-c-bg-soft);
    border-bottom: 1px solid var(--vp-c-border);
}

.vp-code-group .tabs input:checked+label {
    background: var(--vp-c-brand-1);
    color: white;
}

/* 自定义容器样式 */
.custom-block {
    margin: 24px 0;
    border-radius: 8px;
    padding: 16px 20px;
    border-left: 4px solid;
}

.custom-block.tip {
    border-color: var(--micro-core-info);
    background: rgba(99, 102, 241, 0.1);
}

.custom-block.warning {
    border-color: var(--micro-core-warning);
    background: rgba(245, 158, 11, 0.1);
}

.custom-block.danger {
    border-color: var(--micro-core-error);
    background: rgba(239, 68, 68, 0.1);
}

.custom-block.info {
    border-color: var(--micro-core-secondary);
    background: rgba(14, 165, 233, 0.1);
}

.custom-block-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--vp-c-text-1);
}

/* 表格样式 */
.vp-doc table {
    border-collapse: collapse;
    margin: 24px 0;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--vp-c-border);
}

.vp-doc th {
    background: var(--vp-c-bg-soft);
    font-weight: 600;
    text-align: left;
    padding: 12px 16px;
    border-bottom: 1px solid var(--vp-c-border);
}

.vp-doc td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--vp-c-border);
}

.vp-doc tr:last-child td {
    border-bottom: none;
}

/* 链接样式 */
.vp-doc a {
    color: var(--vp-c-brand-1);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.vp-doc a:hover {
    color: var(--vp-c-brand-2);
    text-decoration: underline;
}

/* 按钮样式 */
.vp-button {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.vp-button.brand {
    background: var(--vp-c-brand-1);
    color: white;
}

.vp-button.brand:hover {
    background: var(--vp-c-brand-2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.vp-button.alt {
    background: var(--vp-c-bg-soft);
    color: var(--vp-c-text-1);
    border: 1px solid var(--vp-c-border);
}

.vp-button.alt:hover {
    background: var(--vp-c-bg-elv);
    border-color: var(--vp-c-brand-1);
}

/* 徽章样式 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.tip {
    background: var(--micro-core-info);
    color: white;
}

.badge.warning {
    background: var(--micro-core-warning);
    color: white;
}

.badge.danger {
    background: var(--micro-core-error);
    color: white;
}

.badge.success {
    background: var(--micro-core-success);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .VPHero .name {
        font-size: 48px;
    }

    .VPHero .text {
        font-size: 24px;
    }

    .VPHero .tagline {
        font-size: 16px;
    }

    .VPFeatures {
        padding: 32px 16px;
    }

    .VPFeature {
        padding: 20px;
    }
}

/* 打印样式 */
@media print {

    .VPNav,
    .VPSidebar,
    .VPFooter {
        display: none !important;
    }

    .VPContent {
        margin: 0 !important;
        padding: 0 !important;
    }

    .vp-doc {
        max-width: none !important;
    }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --vp-c-text-1: #000000;
        --vp-c-text-2: #333333;
        --vp-c-bg: #ffffff;
        --vp-c-border: #000000;
    }

    .dark {
        --vp-c-text-1: #ffffff;
        --vp-c-text-2: #cccccc;
        --vp-c-bg: #000000;
        --vp-c-border: #ffffff;
    }
}