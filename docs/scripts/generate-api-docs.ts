/**
 * @fileoverview API文档自动生成脚本
 * <AUTHOR> <<EMAIL>>
 */

import * as fs from 'fs';
import * as path from 'path';
import { ClassDeclaration, FunctionDeclaration, InterfaceDeclaration, Project, SourceFile, TypeAliasDeclaration } from 'ts-morph';

interface ApiDocConfig {
    // 源码目录
    sourceDir: string;
    // 输出目录
    outputDir: string;
    // 包含的文件模式
    include: string[];
    // 排除的文件模式
    exclude: string[];
    // 文档模板
    template: string;
}

interface ApiItem {
    name: string;
    type: 'class' | 'interface' | 'type' | 'function';
    description: string;
    signature: string;
    parameters?: Parameter[];
    returnType?: string;
    examples?: string[];
    since?: string;
    deprecated?: boolean;
    category: string;
}

interface Parameter {
    name: string;
    type: string;
    description: string;
    optional: boolean;
    defaultValue?: string;
}

class ApiDocGenerator {
    private project: Project;
    private config: ApiDocConfig;

    constructor(config: ApiDocConfig) {
        this.config = config;
        this.project = new Project({
            tsConfigFilePath: path.join(process.cwd(), 'tsconfig.json')
        });
    }

    /**
     * 生成API文档
     */
    async generate(): Promise<void> {
        console.log('开始生成API文档...');

        // 加载源文件
        const sourceFiles = this.loadSourceFiles();

        // 提取API信息
        const apiItems = this.extractApiItems(sourceFiles);

        // 按类别分组
        const groupedApis = this.groupApisByCategory(apiItems);

        // 生成文档文件
        await this.generateDocFiles(groupedApis);

        // 生成索引文件
        await this.generateIndexFile(groupedApis);

        console.log('API文档生成完成！');
    }

    /**
     * 加载源文件
     */
    private loadSourceFiles(): SourceFile[] {
        const sourceFiles: SourceFile[] = [];

        this.config.include.forEach(pattern => {
            const files = this.project.addSourceFilesAtPaths(
                path.join(this.config.sourceDir, pattern)
            );
            sourceFiles.push(...files);
        });

        return sourceFiles.filter(file => {
            const filePath = file.getFilePath();
            return !this.config.exclude.some(pattern =>
                filePath.includes(pattern)
            );
        });
    }

    /**
     * 提取API信息
     */
    private extractApiItems(sourceFiles: SourceFile[]): ApiItem[] {
        const apiItems: ApiItem[] = [];

        sourceFiles.forEach(sourceFile => {
            // 提取类
            sourceFile.getClasses().forEach(cls => {
                if (this.isExported(cls)) {
                    apiItems.push(this.extractClassInfo(cls));
                }
            });

            // 提取接口
            sourceFile.getInterfaces().forEach(iface => {
                if (this.isExported(iface)) {
                    apiItems.push(this.extractInterfaceInfo(iface));
                }
            });

            // 提取类型别名
            sourceFile.getTypeAliases().forEach(typeAlias => {
                if (this.isExported(typeAlias)) {
                    apiItems.push(this.extractTypeAliasInfo(typeAlias));
                }
            });

            // 提取函数
            sourceFile.getFunctions().forEach(func => {
                if (this.isExported(func)) {
                    apiItems.push(this.extractFunctionInfo(func));
                }
            });
        });

        return apiItems;
    }

    /**
     * 检查是否导出
     */
    private isExported(node: any): boolean {
        return node.isExported() || node.hasExportKeyword();
    }

    /**
     * 提取类信息
     */
    private extractClassInfo(cls: ClassDeclaration): ApiItem {
        const jsDoc = cls.getJsDocs()[0];
        const description = jsDoc?.getDescription() || '';

        return {
            name: cls.getName() || '',
            type: 'class',
            description: this.cleanDescription(description),
            signature: cls.getText(),
            category: this.getCategoryFromPath(cls.getSourceFile().getFilePath()),
            examples: this.extractExamples(jsDoc),
            since: this.extractTag(jsDoc, 'since'),
            deprecated: this.hasTag(jsDoc, 'deprecated')
        };
    }

    /**
     * 提取接口信息
     */
    private extractInterfaceInfo(iface: InterfaceDeclaration): ApiItem {
        const jsDoc = iface.getJsDocs()[0];
        const description = jsDoc?.getDescription() || '';

        return {
            name: iface.getName(),
            type: 'interface',
            description: this.cleanDescription(description),
            signature: iface.getText(),
            category: this.getCategoryFromPath(iface.getSourceFile().getFilePath()),
            examples: this.extractExamples(jsDoc),
            since: this.extractTag(jsDoc, 'since'),
            deprecated: this.hasTag(jsDoc, 'deprecated')
        };
    }

    /**
     * 提取类型别名信息
     */
    private extractTypeAliasInfo(typeAlias: TypeAliasDeclaration): ApiItem {
        const jsDoc = typeAlias.getJsDocs()[0];
        const description = jsDoc?.getDescription() || '';

        return {
            name: typeAlias.getName(),
            type: 'type',
            description: this.cleanDescription(description),
            signature: typeAlias.getText(),
            category: this.getCategoryFromPath(typeAlias.getSourceFile().getFilePath()),
            examples: this.extractExamples(jsDoc),
            since: this.extractTag(jsDoc, 'since'),
            deprecated: this.hasTag(jsDoc, 'deprecated')
        };
    }

    /**
     * 提取函数信息
     */
    private extractFunctionInfo(func: FunctionDeclaration): ApiItem {
        const jsDoc = func.getJsDocs()[0];
        const description = jsDoc?.getDescription() || '';

        return {
            name: func.getName() || '',
            type: 'function',
            description: this.cleanDescription(description),
            signature: func.getText(),
            parameters: this.extractParameters(func),
            returnType: func.getReturnTypeNode()?.getText(),
            category: this.getCategoryFromPath(func.getSourceFile().getFilePath()),
            examples: this.extractExamples(jsDoc),
            since: this.extractTag(jsDoc, 'since'),
            deprecated: this.hasTag(jsDoc, 'deprecated')
        };
    }

    /**
     * 提取参数信息
     */
    private extractParameters(func: FunctionDeclaration): Parameter[] {
        return func.getParameters().map(param => ({
            name: param.getName(),
            type: param.getTypeNode()?.getText() || 'any',
            description: '', // 从JSDoc中提取
            optional: param.hasQuestionToken(),
            defaultValue: param.getInitializer()?.getText()
        }));
    }

    /**
     * 清理描述文本
     */
    private cleanDescription(description: string): string {
        return description
            .replace(/\n\s*/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * 从路径获取类别
     */
    private getCategoryFromPath(filePath: string): string {
        if (filePath.includes('/core/')) return 'core';
        if (filePath.includes('/plugins/')) return 'plugins';
        if (filePath.includes('/adapters/')) return 'adapters';
        if (filePath.includes('/builders/')) return 'builders';
        if (filePath.includes('/shared/')) return 'shared';
        if (filePath.includes('/sidecar/')) return 'sidecar';
        return 'other';
    }

    /**
     * 提取示例代码
     */
    private extractExamples(jsDoc: any): string[] {
        if (!jsDoc) return [];

        const examples: string[] = [];
        const tags = jsDoc.getTags();

        tags.forEach((tag: any) => {
            if (tag.getTagName() === 'example') {
                examples.push(tag.getComment() || '');
            }
        });

        return examples;
    }

    /**
     * 提取标签值
     */
    private extractTag(jsDoc: any, tagName: string): string | undefined {
        if (!jsDoc) return undefined;

        const tag = jsDoc.getTags().find((t: any) => t.getTagName() === tagName);
        return tag?.getComment();
    }

    /**
     * 检查是否有标签
     */
    private hasTag(jsDoc: any, tagName: string): boolean {
        if (!jsDoc) return false;

        return jsDoc.getTags().some((t: any) => t.getTagName() === tagName);
    }

    /**
     * 按类别分组API
     */
    private groupApisByCategory(apiItems: ApiItem[]): Record<string, ApiItem[]> {
        const grouped: Record<string, ApiItem[]> = {};

        apiItems.forEach(item => {
            if (!grouped[item.category]) {
                grouped[item.category] = [];
            }
            grouped[item.category].push(item);
        });

        // 排序
        Object.keys(grouped).forEach(category => {
            grouped[category].sort((a, b) => a.name.localeCompare(b.name));
        });

        return grouped;
    }

    /**
     * 生成文档文件
     */
    private async generateDocFiles(groupedApis: Record<string, ApiItem[]>): Promise<void> {
        for (const [category, items] of Object.entries(groupedApis)) {
            const content = this.generateCategoryDoc(category, items);
            const filePath = path.join(this.config.outputDir, `${category}.md`);

            await fs.promises.mkdir(path.dirname(filePath), { recursive: true });
            await fs.promises.writeFile(filePath, content, 'utf-8');

            console.log(`生成 ${category} API文档: ${filePath}`);
        }
    }

    /**
     * 生成类别文档
     */
    private generateCategoryDoc(category: string, items: ApiItem[]): string {
        const categoryNames: Record<string, string> = {
            core: '核心API',
            plugins: '插件API',
            adapters: '适配器API',
            builders: '构建工具API',
            shared: '共享工具API',
            sidecar: '边车模式API'
        };

        let content = `# ${categoryNames[category] || category}\n\n`;

        // 按类型分组
        const typeGroups: Record<string, ApiItem[]> = {};
        items.forEach(item => {
            if (!typeGroups[item.type]) {
                typeGroups[item.type] = [];
            }
            typeGroups[item.type].push(item);
        });

        // 生成各类型的文档
        const typeOrder = ['class', 'interface', 'type', 'function'];
        const typeNames = {
            class: '类',
            interface: '接口',
            type: '类型',
            function: '函数'
        };

        typeOrder.forEach(type => {
            if (typeGroups[type]) {
                content += `## ${typeNames[type as keyof typeof typeNames]}\n\n`;

                typeGroups[type].forEach(item => {
                    content += this.generateItemDoc(item);
                });
            }
        });

        return content;
    }

    /**
     * 生成单个API项目文档
     */
    private generateItemDoc(item: ApiItem): string {
        let content = `### ${item.name}\n\n`;

        // 废弃警告
        if (item.deprecated) {
            content += `:::warning 已废弃\n此API已废弃，请使用替代方案。\n:::\n\n`;
        }

        // 描述
        if (item.description) {
            content += `${item.description}\n\n`;
        }

        // 签名
        content += `**类型签名:**\n\n`;
        content += `\`\`\`typescript\n${item.signature}\n\`\`\`\n\n`;

        // 参数
        if (item.parameters && item.parameters.length > 0) {
            content += `**参数:**\n\n`;
            content += `| 参数名 | 类型 | 必填 | 默认值 | 描述 |\n`;
            content += `|--------|------|------|--------|------|\n`;

            item.parameters.forEach(param => {
                const required = param.optional ? '否' : '是';
                const defaultValue = param.defaultValue || '-';
                content += `| ${param.name} | \`${param.type}\` | ${required} | \`${defaultValue}\` | ${param.description} |\n`;
            });

            content += '\n';
        }

        // 返回值
        if (item.returnType) {
            content += `**返回值:**\n\n`;
            content += `\`${item.returnType}\`\n\n`;
        }

        // 示例
        if (item.examples && item.examples.length > 0) {
            content += `**示例:**\n\n`;
            item.examples.forEach((example, index) => {
                content += `\`\`\`typescript\n${example}\n\`\`\`\n\n`;
            });
        }

        // 版本信息
        if (item.since) {
            content += `**添加于:** v${item.since}\n\n`;
        }

        content += '---\n\n';

        return content;
    }

    /**
     * 生成索引文件
     */
    private async generateIndexFile(groupedApis: Record<string, ApiItem[]>): Promise<void> {
        let content = `# API 参考\n\n`;
        content += `micro-core 提供了丰富的API接口，涵盖核心功能、插件系统、适配器、构建工具等各个方面。\n\n`;

        // 生成目录
        content += `## 目录\n\n`;

        const categoryNames: Record<string, string> = {
            core: '核心API',
            plugins: '插件API',
            adapters: '适配器API',
            builders: '构建工具API',
            shared: '共享工具API',
            sidecar: '边车模式API'
        };

        Object.keys(groupedApis).forEach(category => {
            const categoryName = categoryNames[category] || category;
            const itemCount = groupedApis[category].length;
            content += `- [${categoryName}](./${category}.md) (${itemCount} 个API)\n`;
        });

        content += '\n';

        // 快速索引
        content += `## 快速索引\n\n`;

        const allItems = Object.values(groupedApis).flat();
        const popularApis = allItems.filter(item =>
            ['MicroCore', 'registerApp', 'startApp', 'RouterPlugin', 'ReactAdapter'].includes(item.name)
        );

        if (popularApis.length > 0) {
            content += `### 常用API\n\n`;
            popularApis.forEach(item => {
                content += `- [${item.name}](./${item.category}.md#${item.name.toLowerCase()}) - ${item.description}\n`;
            });
            content += '\n';
        }

        // 按字母顺序的完整索引
        content += `### 完整索引\n\n`;
        const sortedItems = allItems.sort((a, b) => a.name.localeCompare(b.name));

        let currentLetter = '';
        sortedItems.forEach(item => {
            const firstLetter = item.name[0].toUpperCase();
            if (firstLetter !== currentLetter) {
                currentLetter = firstLetter;
                content += `\n**${currentLetter}**\n\n`;
            }

            content += `- [${item.name}](./${item.category}.md#${item.name.toLowerCase()}) (${item.type})\n`;
        });

        const indexPath = path.join(this.config.outputDir, 'index.md');
        await fs.promises.writeFile(indexPath, content, 'utf-8');

        console.log(`生成API索引文件: ${indexPath}`);
    }
}

// 配置
const config: ApiDocConfig = {
    sourceDir: path.join(process.cwd(), 'packages'),
    outputDir: path.join(process.cwd(), 'docs/api'),
    include: [
        '*/src/**/*.ts',
        '*/src/index.ts'
    ],
    exclude: [
        'test',
        'tests',
        '__tests__',
        '*.test.ts',
        '*.spec.ts',
        'node_modules'
    ],
    template: 'default'
};

// 执行生成
async function main() {
    try {
        const generator = new ApiDocGenerator(config);
        await generator.generate();
    } catch (error) {
        console.error('API文档生成失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

export { ApiDocConfig, ApiDocGenerator };
