<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圆环 -->
  <circle cx="32" cy="32" r="30" stroke="url(#gradient1)" stroke-width="4" fill="none"/>
  
  <!-- 内部几何图形 -->
  <path d="M20 20 L44 20 L44 32 L32 32 L32 44 L20 44 Z" fill="url(#gradient2)" opacity="0.8"/>
  <path d="M32 20 L44 20 L44 44 L32 44 Z" fill="url(#gradient3)" opacity="0.6"/>
  <circle cx="26" cy="26" r="4" fill="url(#gradient4)"/>
  <circle cx="38" cy="38" r="4" fill="url(#gradient5)"/>
  
  <!-- 连接线 -->
  <line x1="26" y1="30" x2="26" y2="38" stroke="url(#gradient6)" stroke-width="2" stroke-linecap="round"/>
  <line x1="30" y1="26" x2="38" y2="26" stroke="url(#gradient6)" stroke-width="2" stroke-linecap="round"/>
  <line x1="30" y1="30" x2="34" y2="34" stroke="url(#gradient6)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#38bdf8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7dd3fc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38bdf8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient6" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:0.8" />
    </linearGradient>
  </defs>
</svg>