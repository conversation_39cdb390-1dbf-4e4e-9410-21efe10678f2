# 文档系统测试

本文档用于测试 VitePress 文档系统的各项功能。

## 基础功能测试

### 1. 标题层级测试

# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

### 2. 文本格式测试

**粗体文本**
*斜体文本*
***粗斜体文本***
~~删除线文本~~
`行内代码`

### 3. 列表测试

#### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

#### 有序列表
1. 第一项
2. 第二项
   1. 子项目 2.1
   2. 子项目 2.2
3. 第三项

### 4. 链接测试

- [内部链接](../guide/introduction.md)
- [外部链接](https://github.com/echo008/micro-core)
- [锚点链接](#基础功能测试)

### 5. 代码块测试

#### TypeScript 代码
```typescript
interface MicroCoreConfig {
  name: string;
  entry: string;
  container: string;
  activeWhen: string | RegExp;
}

const config: MicroCoreConfig = {
  name: 'test-app',
  entry: 'http://localhost:3001',
  container: '#app',
  activeWhen: '/test'
};
```

#### JavaScript 代码
```javascript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();
kernel.registerApplication(config);
kernel.start();
```

#### HTML 代码
```html
<!DOCTYPE html>
<html>
<head>
  <title>Micro-Core Test</title>
</head>
<body>
  <div id="app"></div>
</body>
</html>
```

#### CSS 代码
```css
.micro-core-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.micro-app {
  flex: 1;
  border: none;
  outline: none;
}
```

#### Shell 命令
```bash
# 安装依赖
pnpm install @micro-core/core

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 6. 表格测试

| 功能 | qiankun | wujie | Micro-Core |
|------|---------|-------|------------|
| 沙箱隔离 | ✅ | ✅ | ✅ |
| 应用通信 | ✅ | ✅ | ✅ |
| 路由管理 | ✅ | ✅ | ✅ |
| 插件系统 | ❌ | ❌ | ✅ |
| 性能优化 | ⚠️ | ⚠️ | ✅ |
| TypeScript | ⚠️ | ⚠️ | ✅ |

### 7. 引用块测试

> 这是一个引用块示例。
> 
> Micro-Core 是下一代微前端架构解决方案，专为构建高性能、高扩展性、高可靠性的企业级前端应用而设计。

### 8. 分割线测试

---

### 9. 图片测试

![Micro-Core Logo](/logo.svg)

### 10. 任务列表测试

- [x] 完成核心架构设计
- [x] 实现插件系统
- [x] 开发沙箱隔离
- [ ] 完善文档系统
- [ ] 编写测试用例
- [ ] 发布正式版本

## 高级功能测试

### 1. 自定义容器测试

::: tip 提示
这是一个提示容器，用于显示重要的提示信息。
:::

::: warning 警告
这是一个警告容器，用于显示需要注意的信息。
:::

::: danger 危险
这是一个危险容器，用于显示危险或错误信息。
:::

::: info 信息
这是一个信息容器，用于显示一般性信息。
:::

::: details 点击查看详情
这是一个可折叠的详情容器，点击标题可以展开或收起内容。

```typescript
// 这里可以放置详细的代码示例
const example = {
  name: 'detailed-example',
  description: '详细示例'
};
```
:::

### 2. 代码组测试

::: code-group

```typescript [main.ts]
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();
kernel.start();
```

```javascript [config.js]
module.exports = {
  apps: [
    {
      name: 'react-app',
      entry: 'http://localhost:3001'
    }
  ]
};
```

```json [package.json]
{
  "name": "micro-core-app",
  "version": "1.0.0",
  "dependencies": {
    "@micro-core/core": "^1.0.0"
  }
}
```

:::

### 3. 数学公式测试

行内公式：$E = mc^2$

块级公式：
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + \cdots + x_n
$$

### 4. 脚注测试

这是一个包含脚注的段落[^1]。

Micro-Core 支持多种沙箱策略[^2]。

[^1]: 这是第一个脚注的内容。
[^2]: 包括 Proxy、Iframe、WebComponent 等多种沙箱实现。

### 5. 徽章测试

<Badge type="info" text="稳定版" />
<Badge type="tip" text="推荐" />
<Badge type="warning" text="实验性" />
<Badge type="danger" text="已废弃" />

## 响应式测试

### 桌面端显示

在桌面端，文档应该显示完整的侧边栏和导航栏。

### 移动端显示

在移动端，侧边栏应该可以折叠，导航栏应该适配小屏幕。

## 搜索功能测试

文档系统应该支持全文搜索功能，用户可以通过搜索框快速找到相关内容。

搜索关键词测试：
- micro-core
- 微前端
- qiankun
- wujie
- 沙箱
- 插件
- 路由
- 通信

## 主题切换测试

文档系统应该支持明暗主题切换，用户可以根据个人喜好选择合适的主题。

## 国际化测试

当前文档主要使用中文，未来可能需要支持英文等其他语言。

## 性能测试

### 加载速度
- 首次加载时间应该在 2 秒内
- 页面切换应该在 500ms 内完成
- 搜索响应时间应该在 100ms 内

### 资源优化
- 图片应该进行压缩和懒加载
- CSS 和 JS 应该进行压缩和合并
- 字体文件应该进行优化

## 兼容性测试

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 设备兼容性
- 桌面端（1920x1080 及以上）
- 平板端（768x1024）
- 移动端（375x667）

## 测试结果

- ✅ 基础 Markdown 功能正常
- ✅ 代码高亮正常
- ✅ 链接跳转正常
- ✅ 图片显示正常
- ✅ 表格渲染正常
- ✅ 自定义容器正常
- ✅ 搜索功能正常
- ✅ 主题切换正常
- ✅ 响应式布局正常
- ✅ 性能表现良好

## 总结

VitePress 文档系统功能完整，性能优秀，用户体验良好，完全满足 Micro-Core 项目的文档需求。
