# 应用管理 API

Micro-Core 提供了完整的应用管理 API，支持应用的注册、加载、挂载、卸载等操作。

## MicroCoreKernel

### 构造函数

```typescript
constructor(options?: MicroCoreKernelOptions)
```

创建 Micro-Core 内核实例。

**参数:**

```typescript
interface MicroCoreKernelOptions {
  /** 全局容器选择器或元素 */
  container?: string | HTMLElement;
  /** 是否启用开发模式 */
  devMode?: boolean;
  /** 全局错误处理器 */
  errorHandler?: (error: Error, app?: ApplicationInfo) => void;
  /** 性能监控配置 */
  performance?: PerformanceConfig;
  /** 预加载策略 */
  prefetch?: PrefetchStrategy;
}
```

**示例:**

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel({
  devMode: process.env.NODE_ENV === 'development',
  errorHandler: (error, app) => {
    console.error(`应用 ${app?.name} 发生错误:`, error);
  },
  performance: {
    enableMetrics: true,
    reportInterval: 5000
  }
});
```

## 应用注册

### registerApplication()

```typescript
registerApplication(config: ApplicationConfig): void
```

注册微应用到内核中。

**参数:**

```typescript
interface ApplicationConfig {
  /** 应用唯一标识 */
  name: string;
  /** 应用入口地址 */
  entry: string | EntryConfig;
  /** 应用容器 */
  container: string | HTMLElement | (() => HTMLElement);
  /** 激活条件 */
  activeWhen: string | RegExp | ((location: Location) => boolean);
  /** 沙箱策略 */
  sandbox?: SandboxType | SandboxConfig;
  /** 传递给应用的属性 */
  props?: Record<string, any> | (() => Record<string, any>);
  /** 应用生命周期钩子 */
  lifecycle?: LifecycleHooks;
  /** 预加载配置 */
  prefetch?: boolean | PrefetchConfig;
  /** 应用级别的插件 */
  plugins?: Plugin[];
  /** 应用优先级 */
  priority?: number;
  /** 应用分组 */
  group?: string;
  /** 应用标签 */
  tags?: string[];
}
```

**示例:**

```typescript
// 基础注册
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user'
});

// 高级注册
kernel.registerApplication({
  name: 'dashboard-app',
  entry: {
    html: 'http://localhost:3002/index.html',
    scripts: ['http://localhost:3002/static/js/main.js'],
    styles: ['http://localhost:3002/static/css/main.css']
  },
  container: () => document.querySelector('#dashboard-container'),
  activeWhen: (location) => location.pathname.startsWith('/dashboard'),
  sandbox: {
    type: 'proxy',
    strict: true,
    whitelist: ['console', 'fetch']
  },
  props: () => ({
    theme: getTheme(),
    userInfo: getCurrentUser(),
    apiConfig: getApiConfig()
  }),
  lifecycle: {
    beforeLoad: async (app) => {
      console.log(`${app.name} 开始加载`);
    },
    afterMount: async (app) => {
      console.log(`${app.name} 挂载完成`);
    }
  },
  prefetch: {
    strategy: 'idle',
    priority: 'high'
  },
  priority: 1,
  group: 'business',
  tags: ['dashboard', 'analytics']
});
```

### registerApplications()

```typescript
registerApplications(configs: ApplicationConfig[]): void
```

批量注册多个微应用。

**示例:**

```typescript
kernel.registerApplications([
  {
    name: 'app1',
    entry: 'http://localhost:3001',
    container: '#app1',
    activeWhen: '/app1'
  },
  {
    name: 'app2',
    entry: 'http://localhost:3002',
    container: '#app2',
    activeWhen: '/app2'
  }
]);
```

### unregisterApplication()

```typescript
unregisterApplication(name: string): Promise<void>
```

注销指定的微应用。

**示例:**

```typescript
await kernel.unregisterApplication('user-app');
```

## 应用控制

### start()

```typescript
start(options?: StartOptions): Promise<void>
```

启动微前端系统。

**参数:**

```typescript
interface StartOptions {
  /** 是否预加载所有应用 */
  prefetchAll?: boolean;
  /** 启动后的回调 */
  onStarted?: () => void;
  /** 是否启用路由监听 */
  enableRouting?: boolean;
  /** 初始路由 */
  initialRoute?: string;
}
```

**示例:**

```typescript
await kernel.start({
  prefetchAll: true,
  enableRouting: true,
  onStarted: () => {
    console.log('微前端系统启动完成');
  }
});
```

### loadApplication()

```typescript
loadApplication(name: string, props?: Record<string, any>): Promise<ApplicationInstance>
```

动态加载指定的微应用。

**返回值:**

```typescript
interface ApplicationInstance {
  name: string;
  status: ApplicationStatus;
  config: ApplicationConfig;
  instance?: any;
  sandbox?: Sandbox;
  container?: HTMLElement;
  props?: Record<string, any>;
  loadTime?: number;
  mountTime?: number;
}
```

**示例:**

```typescript
const app = await kernel.loadApplication('user-app', {
  theme: 'dark',
  locale: 'zh-CN'
});

console.log(`应用 ${app.name} 加载完成，状态: ${app.status}`);
```

### mountApplication()

```typescript
mountApplication(name: string, props?: Record<string, any>): Promise<void>
```

挂载指定的微应用。

**示例:**

```typescript
await kernel.mountApplication('user-app', {
  containerStyle: { height: '100vh' }
});
```

### unmountApplication()

```typescript
unmountApplication(name: string): Promise<void>
```

卸载指定的微应用。

**示例:**

```typescript
await kernel.unmountApplication('user-app');
```

### remountApplication()

```typescript
remountApplication(name: string, props?: Record<string, any>): Promise<void>
```

重新挂载指定的微应用。

**示例:**

```typescript
await kernel.remountApplication('user-app', {
  forceReload: true
});
```

## 应用查询

### getApplication()

```typescript
getApplication(name: string): ApplicationInstance | undefined
```

获取指定名称的应用实例。

**示例:**

```typescript
const app = kernel.getApplication('user-app');
if (app) {
  console.log(`应用状态: ${app.status}`);
}
```

### getApplications()

```typescript
getApplications(filter?: ApplicationFilter): ApplicationInstance[]
```

获取所有应用实例，支持过滤条件。

**参数:**

```typescript
interface ApplicationFilter {
  status?: ApplicationStatus | ApplicationStatus[];
  group?: string;
  tags?: string[];
  priority?: number;
}
```

**示例:**

```typescript
// 获取所有应用
const allApps = kernel.getApplications();

// 获取已挂载的应用
const mountedApps = kernel.getApplications({
  status: ApplicationStatus.MOUNTED
});

// 获取指定分组的应用
const businessApps = kernel.getApplications({
  group: 'business'
});

// 获取包含特定标签的应用
const dashboardApps = kernel.getApplications({
  tags: ['dashboard']
});
```

### getActiveApplications()

```typescript
getActiveApplications(): ApplicationInstance[]
```

获取当前激活的应用实例。

**示例:**

```typescript
const activeApps = kernel.getActiveApplications();
console.log(`当前激活的应用数量: ${activeApps.length}`);
```

### getApplicationsByStatus()

```typescript
getApplicationsByStatus(status: ApplicationStatus): ApplicationInstance[]
```

根据状态获取应用实例。

**示例:**

```typescript
const loadingApps = kernel.getApplicationsByStatus(ApplicationStatus.LOADING);
const errorApps = kernel.getApplicationsByStatus(ApplicationStatus.ERROR);
```

## 应用状态管理

### ApplicationStatus 枚举

```typescript
enum ApplicationStatus {
  NOT_LOADED = 'NOT_LOADED',     // 未加载
  LOADING = 'LOADING',           // 加载中
  LOADED = 'LOADED',             // 已加载
  MOUNTING = 'MOUNTING',         // 挂载中
  MOUNTED = 'MOUNTED',           // 已挂载
  UNMOUNTING = 'UNMOUNTING',     // 卸载中
  UNMOUNTED = 'UNMOUNTED',       // 已卸载
  ERROR = 'ERROR'                // 错误状态
}
```

### getApplicationStatus()

```typescript
getApplicationStatus(name: string): ApplicationStatus | undefined
```

获取指定应用的状态。

**示例:**

```typescript
const status = kernel.getApplicationStatus('user-app');
if (status === ApplicationStatus.ERROR) {
  console.error('应用处于错误状态');
}
```

### isApplicationActive()

```typescript
isApplicationActive(name: string): boolean
```

检查应用是否处于激活状态。

**示例:**

```typescript
if (kernel.isApplicationActive('user-app')) {
  console.log('用户应用当前处于激活状态');
}
```

### waitForApplication()

```typescript
waitForApplication(name: string, status: ApplicationStatus, timeout?: number): Promise<ApplicationInstance>
```

等待应用达到指定状态。

**示例:**

```typescript
try {
  const app = await kernel.waitForApplication('user-app', ApplicationStatus.MOUNTED, 5000);
  console.log('应用挂载完成');
} catch (error) {
  console.error('等待应用挂载超时');
}
```

## 应用配置管理

### updateApplicationConfig()

```typescript
updateApplicationConfig(name: string, config: Partial<ApplicationConfig>): void
```

更新应用配置。

**示例:**

```typescript
kernel.updateApplicationConfig('user-app', {
  props: { theme: 'dark' },
  priority: 2
});
```

### getApplicationConfig()

```typescript
getApplicationConfig(name: string): ApplicationConfig | undefined
```

获取应用配置。

**示例:**

```typescript
const config = kernel.getApplicationConfig('user-app');
if (config) {
  console.log('应用入口:', config.entry);
}
```

### setApplicationProps()

```typescript
setApplicationProps(name: string, props: Record<string, any>): void
```

设置应用属性。

**示例:**

```typescript
kernel.setApplicationProps('user-app', {
  theme: 'dark',
  locale: 'en-US',
  userInfo: { id: 123, name: 'John' }
});
```

### getApplicationProps()

```typescript
getApplicationProps(name: string): Record<string, any> | undefined
```

获取应用属性。

**示例:**

```typescript
const props = kernel.getApplicationProps('user-app');
console.log('应用属性:', props);
```

## 应用分组管理

### createApplicationGroup()

```typescript
createApplicationGroup(name: string, options?: GroupOptions): ApplicationGroup
```

创建应用分组。

**参数:**

```typescript
interface GroupOptions {
  priority?: number;
  loadStrategy?: 'parallel' | 'sequential';
  errorHandling?: 'continue' | 'stop';
}

interface ApplicationGroup {
  name: string;
  applications: string[];
  options: GroupOptions;
  load(): Promise<void>;
  mount(): Promise<void>;
  unmount(): Promise<void>;
}
```

**示例:**

```typescript
const businessGroup = kernel.createApplicationGroup('business', {
  loadStrategy: 'parallel',
  errorHandling: 'continue'
});

// 添加应用到分组
businessGroup.addApplication('dashboard-app');
businessGroup.addApplication('analytics-app');

// 批量操作分组应用
await businessGroup.load();
await businessGroup.mount();
```

### getApplicationGroup()

```typescript
getApplicationGroup(name: string): ApplicationGroup | undefined
```

获取应用分组。

**示例:**

```typescript
const group = kernel.getApplicationGroup('business');
if (group) {
  console.log(`分组包含 ${group.applications.length} 个应用`);
}
```

## 应用预加载

### prefetchApplication()

```typescript
prefetchApplication(name: string, options?: PrefetchOptions): Promise<void>
```

预加载指定应用。

**参数:**

```typescript
interface PrefetchOptions {
  strategy?: 'immediate' | 'idle' | 'visible';
  priority?: 'low' | 'normal' | 'high';
  timeout?: number;
}
```

**示例:**

```typescript
// 立即预加载
await kernel.prefetchApplication('user-app', {
  strategy: 'immediate',
  priority: 'high'
});

// 空闲时预加载
await kernel.prefetchApplication('dashboard-app', {
  strategy: 'idle',
  priority: 'normal'
});
```

### prefetchApplications()

```typescript
prefetchApplications(names: string[], options?: PrefetchOptions): Promise<void>
```

批量预加载应用。

**示例:**

```typescript
await kernel.prefetchApplications(['app1', 'app2', 'app3'], {
  strategy: 'idle',
  priority: 'low'
});
```

### cancelPrefetch()

```typescript
cancelPrefetch(name: string): void
```

取消应用预加载。

**示例:**

```typescript
kernel.cancelPrefetch('user-app');
```

## 应用性能监控

### getApplicationMetrics()

```typescript
getApplicationMetrics(name: string): ApplicationMetrics | undefined
```

获取应用性能指标。

**返回值:**

```typescript
interface ApplicationMetrics {
  loadTime: number;
  mountTime: number;
  unmountTime: number;
  memoryUsage: number;
  errorCount: number;
  activationCount: number;
  lastActivation: number;
}
```

**示例:**

```typescript
const metrics = kernel.getApplicationMetrics('user-app');
if (metrics) {
  console.log(`加载时间: ${metrics.loadTime}ms`);
  console.log(`挂载时间: ${metrics.mountTime}ms`);
  console.log(`内存使用: ${metrics.memoryUsage}MB`);
}
```

### getAllMetrics()

```typescript
getAllMetrics(): Record<string, ApplicationMetrics>
```

获取所有应用的性能指标。

**示例:**

```typescript
const allMetrics = kernel.getAllMetrics();
Object.entries(allMetrics).forEach(([name, metrics]) => {
  console.log(`${name}: 加载时间 ${metrics.loadTime}ms`);
});
```

## 错误处理

### getApplicationErrors()

```typescript
getApplicationErrors(name: string): ApplicationError[]
```

获取应用错误记录。

**返回值:**

```typescript
interface ApplicationError {
  timestamp: number;
  phase: string;
  error: Error;
  context?: any;
}
```

**示例:**

```typescript
const errors = kernel.getApplicationErrors('user-app');
errors.forEach(error => {
  console.error(`[${error.phase}] ${error.error.message}`);
});
```

### clearApplicationErrors()

```typescript
clearApplicationErrors(name: string): void
```

清除应用错误记录。

**示例:**

```typescript
kernel.clearApplicationErrors('user-app');
```

### setErrorHandler()

```typescript
setErrorHandler(handler: (error: Error, app?: ApplicationInfo) => void): void
```

设置全局错误处理器。

**示例:**

```typescript
kernel.setErrorHandler((error, app) => {
  console.error(`应用 ${app?.name} 发生错误:`, error);
  
  // 发送错误报告
  sendErrorReport({
    appName: app?.name,
    error: error.message,
    stack: error.stack,
    timestamp: Date.now()
  });
});
```

## 事件系统

### on()

```typescript
on(event: string, handler: EventHandler): void
```

监听应用管理事件。

**内置事件:**

- `application:registered` - 应用注册
- `application:unregistered` - 应用注销
- `application:before-load` - 应用加载前
- `application:after-load` - 应用加载后
- `application:before-mount` - 应用挂载前
- `application:after-mount` - 应用挂载后
- `application:before-unmount` - 应用卸载前
- `application:after-unmount` - 应用卸载后
- `application:error` - 应用错误
- `application:status-change` - 应用状态变化

**示例:**

```typescript
kernel.on('application:after-mount', (app) => {
  console.log(`应用 ${app.name} 挂载完成`);
  
  // 发送分析数据
  analytics.track('app-mounted', {
    appName: app.name,
    mountTime: app.mountTime
  });
});

kernel.on('application:error', (error, app) => {
  console.error(`应用 ${app.name} 发生错误:`, error);
  
  // 尝试恢复应用
  if (error.recoverable) {
    setTimeout(() => {
      kernel.remountApplication(app.name);
    }, 1000);
  }
});
```

### emit()

```typescript
emit(event: string, ...args: any[]): void
```

触发应用管理事件。

**示例:**

```typescript
kernel.emit('custom:app-ready', { appName: 'user-app' });
```

### off()

```typescript
off(event: string, handler?: EventHandler): void
```

移除事件监听器。

**示例:**

```typescript
const handler = (app) => console.log('应用挂载:', app.name);

kernel.on('application:after-mount', handler);
// ... 稍后移除监听器
kernel.off('application:after-mount', handler);
```
