# 通信系统 API

Micro-Core 提供了完整的应用间通信 API，支持事件总线、全局状态管理、直接通信等多种通信方式。

## EventBus

全局事件总线，基于发布-订阅模式实现应用间通信。

### 基础方法

#### emit()

```typescript
emit(event: string, ...args: any[]): boolean
```

发送事件。

**参数:**
- `event` - 事件名称
- `args` - 事件参数

**返回值:**
- `boolean` - 是否有监听器处理了该事件

**示例:**

```typescript
import { EventBus } from '@micro-core/core';

// 发送简单事件
EventBus.emit('user-login', { userId: '123', username: 'john' });

// 发送复杂数据
EventBus.emit('cart-update', {
  action: 'add',
  item: { id: 1, name: 'Product 1', price: 100 },
  total: 150,
  timestamp: Date.now()
});

// 检查是否有监听器
const hasListeners = EventBus.emit('app-ready');
if (!hasListeners) {
  console.log('没有应用监听 app-ready 事件');
}
```

#### on()

```typescript
on(event: string, listener: EventListener, options?: ListenerOptions): () => void
```

监听事件。

**参数:**

```typescript
type EventListener = (...args: any[]) => void;

interface ListenerOptions {
  once?: boolean;           // 是否只监听一次
  priority?: number;        // 监听器优先级 (数字越大优先级越高)
  context?: any;           // 监听器上下文
  filter?: (...args: any[]) => boolean; // 事件过滤器
}
```

**返回值:**
- `Function` - 取消监听的函数

**示例:**

```typescript
// 基础监听
const unsubscribe = EventBus.on('user-login', (userData) => {
  console.log('用户登录:', userData);
});

// 高优先级监听器
EventBus.on('critical-error', handleCriticalError, {
  priority: 100
});

// 一次性监听器
EventBus.on('app-initialized', () => {
  console.log('应用初始化完成');
}, { once: true });

// 带过滤器的监听器
EventBus.on('user-action', (action) => {
  console.log('重要用户操作:', action);
}, {
  filter: (action) => action.importance === 'high'
});

// 取消监听
unsubscribe();
```

#### once()

```typescript
once(event: string, listener: EventListener): () => void
```

监听事件一次。

**示例:**

```typescript
EventBus.once('app-ready', () => {
  console.log('应用准备就绪，此消息只会显示一次');
});
```

#### off()

```typescript
off(event: string, listener?: EventListener): void
```

移除事件监听器。

**示例:**

```typescript
const handler = (data) => console.log('处理数据:', data);

EventBus.on('data-update', handler);

// 移除特定监听器
EventBus.off('data-update', handler);

// 移除所有监听器
EventBus.off('data-update');
```

### 高级方法

#### onAny()

```typescript
onAny(listener: (event: string, ...args: any[]) => void): () => void
```

监听所有事件。

**示例:**

```typescript
const unsubscribe = EventBus.onAny((event, ...args) => {
  console.log(`事件 ${event} 被触发:`, args);
});

// 取消监听所有事件
unsubscribe();
```

#### offAny()

```typescript
offAny(listener?: (event: string, ...args: any[]) => void): void
```

移除全局事件监听器。

**示例:**

```typescript
EventBus.offAny(); // 移除所有全局监听器
```

#### listenerCount()

```typescript
listenerCount(event: string): number
```

获取事件监听器数量。

**示例:**

```typescript
const count = EventBus.listenerCount('user-login');
console.log(`user-login 事件有 ${count} 个监听器`);
```

#### eventNames()

```typescript
eventNames(): string[]
```

获取所有事件名称。

**示例:**

```typescript
const events = EventBus.eventNames();
console.log('当前注册的事件:', events);
```

#### removeAllListeners()

```typescript
removeAllListeners(event?: string): void
```

移除所有监听器。

**示例:**

```typescript
// 移除特定事件的所有监听器
EventBus.removeAllListeners('user-login');

// 移除所有事件的所有监听器
EventBus.removeAllListeners();
```

### 命名空间事件

#### namespace()

```typescript
namespace(name: string): EventBusNamespace
```

创建命名空间事件总线。

**示例:**

```typescript
// 创建用户模块的命名空间
const userEvents = EventBus.namespace('user');

// 在命名空间中发送事件
userEvents.emit('login', { userId: '123' });
userEvents.emit('logout', { userId: '123' });

// 监听命名空间事件
userEvents.on('login', (userData) => {
  console.log('用户登录:', userData);
});

// 也可以通过完整事件名监听
EventBus.on('user:login', (userData) => {
  console.log('用户登录:', userData);
});
```

### 事件中间件

#### use()

```typescript
use(middleware: EventMiddleware): () => void
```

添加事件中间件。

**参数:**

```typescript
type EventMiddleware = (
  event: string,
  args: any[],
  next: () => void
) => void;
```

**示例:**

```typescript
// 日志中间件
const removeLogMiddleware = EventBus.use((event, args, next) => {
  console.log(`[EventBus] ${event}:`, args);
  next();
});

// 性能监控中间件
EventBus.use((event, args, next) => {
  const startTime = performance.now();
  next();
  const endTime = performance.now();
  
  if (endTime - startTime > 10) {
    console.warn(`事件 ${event} 处理耗时 ${endTime - startTime}ms`);
  }
});

// 事件过滤中间件
EventBus.use((event, args, next) => {
  // 过滤敏感事件
  if (event.includes('password') || event.includes('secret')) {
    console.warn(`敏感事件 ${event} 被过滤`);
    return;
  }
  next();
});

// 移除中间件
removeLogMiddleware();
```

## GlobalState

全局状态管理系统，支持响应式状态更新和持久化。

### 基础操作

#### set()

```typescript
set(key: string, value: any): void
```

设置全局状态。

**示例:**

```typescript
import { GlobalState } from '@micro-core/core';

// 设置简单值
GlobalState.set('theme', 'dark');
GlobalState.set('language', 'zh-CN');

// 设置对象
GlobalState.set('user', {
  id: '123',
  name: 'John Doe',
  email: '<EMAIL>'
});

// 设置嵌套值
GlobalState.set('app.settings.notifications', true);
GlobalState.set('user.profile.avatar', '/avatars/john.jpg');
```

#### get()

```typescript
get<T>(key: string, defaultValue?: T): T
```

获取全局状态。

**示例:**

```typescript
// 获取简单值
const theme = GlobalState.get('theme');
const language = GlobalState.get('language', 'en-US'); // 带默认值

// 获取对象
const user = GlobalState.get('user');

// 获取嵌套值
const notifications = GlobalState.get('app.settings.notifications');
const avatar = GlobalState.get('user.profile.avatar');

// 类型安全的获取
interface User {
  id: string;
  name: string;
  email: string;
}

const user = GlobalState.get<User>('user');
```

#### has()

```typescript
has(key: string): boolean
```

检查状态是否存在。

**示例:**

```typescript
if (GlobalState.has('user')) {
  console.log('用户已登录');
}

if (GlobalState.has('app.settings.theme')) {
  console.log('主题设置存在');
}
```

#### remove()

```typescript
remove(key: string): boolean
```

删除状态。

**示例:**

```typescript
// 删除用户信息
GlobalState.remove('user');

// 删除嵌套状态
GlobalState.remove('app.settings.notifications');
```

#### clear()

```typescript
clear(): void
```

清空所有状态。

**示例:**

```typescript
GlobalState.clear();
```

### 高级操作

#### merge()

```typescript
merge(key: string, value: any): void
```

合并状态对象。

**示例:**

```typescript
// 设置初始用户信息
GlobalState.set('user', {
  id: '123',
  name: 'John',
  email: '<EMAIL>'
});

// 合并新的属性
GlobalState.merge('user', {
  avatar: '/avatars/john.jpg',
  lastLogin: Date.now()
});

// 结果: { id: '123', name: 'John', email: '<EMAIL>', avatar: '/avatars/john.jpg', lastLogin: 1234567890 }
```

#### push()

```typescript
push(key: string, value: any): void
```

向数组状态添加元素。

**示例:**

```typescript
// 初始化数组
GlobalState.set('cart.items', []);

// 添加商品
GlobalState.push('cart.items', { id: 1, name: 'Product 1', price: 100 });
GlobalState.push('cart.items', { id: 2, name: 'Product 2', price: 200 });
```

#### splice()

```typescript
splice(key: string, start: number, deleteCount?: number, ...items: any[]): any[]
```

修改数组状态。

**示例:**

```typescript
// 删除第一个商品
GlobalState.splice('cart.items', 0, 1);

// 在指定位置插入商品
GlobalState.splice('cart.items', 1, 0, { id: 3, name: 'Product 3', price: 150 });

// 替换商品
GlobalState.splice('cart.items', 0, 1, { id: 4, name: 'Product 4', price: 250 });
```

### 状态监听

#### watch()

```typescript
watch<T>(key: string, callback: (newValue: T, oldValue: T) => void): () => void
```

监听状态变化。

**示例:**

```typescript
// 监听主题变化
const unwatch = GlobalState.watch('theme', (newTheme, oldTheme) => {
  console.log(`主题从 ${oldTheme} 变更为 ${newTheme}`);
  document.body.className = `theme-${newTheme}`;
});

// 监听用户信息变化
GlobalState.watch('user', (newUser, oldUser) => {
  if (newUser && !oldUser) {
    console.log('用户登录:', newUser);
  } else if (!newUser && oldUser) {
    console.log('用户登出:', oldUser);
  } else if (newUser && oldUser) {
    console.log('用户信息更新:', newUser);
  }
});

// 监听嵌套状态变化
GlobalState.watch('user.profile', (newProfile, oldProfile) => {
  console.log('用户资料更新:', newProfile);
});

// 取消监听
unwatch();
```

#### watchAll()

```typescript
watchAll(callback: (key: string, newValue: any, oldValue: any) => void): () => void
```

监听所有状态变化。

**示例:**

```typescript
const unwatch = GlobalState.watchAll((key, newValue, oldValue) => {
  console.log(`状态 ${key} 发生变化:`, { newValue, oldValue });
  
  // 发送状态变化事件
  EventBus.emit('state:change', { key, newValue, oldValue });
});

// 取消监听
unwatch();
```

### 计算属性

#### computed()

```typescript
computed<T>(key: string, dependencies: string[], computeFn: (...deps: any[]) => T): () => void
```

定义计算属性。

**示例:**

```typescript
// 计算购物车总价
const uncompute = GlobalState.computed('cart.total', ['cart.items'], (items) => {
  return items.reduce((total, item) => total + item.price * item.quantity, 0);
});

// 计算用户显示名称
GlobalState.computed('user.displayName', ['user.profile'], (profile) => {
  if (!profile) return '未登录';
  return `${profile.firstName} ${profile.lastName}`;
});

// 使用计算属性
const total = GlobalState.get('cart.total');
const displayName = GlobalState.get('user.displayName');

// 监听计算属性变化
GlobalState.watch('cart.total', (newTotal) => {
  console.log(`购物车总价: ¥${newTotal}`);
});

// 移除计算属性
uncompute();
```

### 状态持久化

#### enablePersistence()

```typescript
enablePersistence(storage: 'localStorage' | 'sessionStorage' | Storage, options?: PersistenceOptions): void
```

启用状态持久化。

**参数:**

```typescript
interface PersistenceOptions {
  include?: string[];       // 包含的状态键
  exclude?: string[];       // 排除的状态键
  prefix?: string;          // 存储前缀
  serialize?: (value: any) => string;    // 序列化函数
  deserialize?: (value: string) => any;  // 反序列化函数
  throttle?: number;        // 节流时间 (ms)
}
```

**示例:**

```typescript
// 基础持久化
GlobalState.enablePersistence('localStorage');

// 高级持久化配置
GlobalState.enablePersistence('localStorage', {
  include: ['user', 'app.settings'],  // 只持久化指定状态
  exclude: ['tempData'],              // 排除临时数据
  prefix: 'micro-core-',              // 存储前缀
  throttle: 1000,                     // 1秒节流
  serialize: JSON.stringify,
  deserialize: JSON.parse
});

// 使用自定义存储
class CustomStorage implements Storage {
  // 实现 Storage 接口
}

GlobalState.enablePersistence(new CustomStorage());
```

#### save()

```typescript
save(): Promise<void>
```

手动保存状态。

**示例:**

```typescript
await GlobalState.save();
```

#### restore()

```typescript
restore(): Promise<void>
```

手动恢复状态。

**示例:**

```typescript
await GlobalState.restore();
```

### 状态快照

#### createSnapshot()

```typescript
createSnapshot(keys?: string[]): StateSnapshot
```

创建状态快照。

**示例:**

```typescript
// 创建全量快照
const fullSnapshot = GlobalState.createSnapshot();

// 创建部分快照
const userSnapshot = GlobalState.createSnapshot(['user', 'app.settings']);
```

#### restoreSnapshot()

```typescript
restoreSnapshot(snapshot: StateSnapshot): void
```

恢复状态快照。

**示例:**

```typescript
GlobalState.restoreSnapshot(fullSnapshot);
```

#### compareSnapshots()

```typescript
compareSnapshots(snapshot1: StateSnapshot, snapshot2: StateSnapshot): StateDiff
```

比较状态快照。

**示例:**

```typescript
const diff = GlobalState.compareSnapshots(snapshot1, snapshot2);
console.log('状态差异:', diff);
```

## 直接通信

### ApplicationCommunicator

应用间直接通信接口。

#### sendMessage()

```typescript
sendMessage(targetApp: string, message: any, options?: MessageOptions): Promise<any>
```

向指定应用发送消息。

**参数:**

```typescript
interface MessageOptions {
  timeout?: number;         // 超时时间
  retry?: number;          // 重试次数
  priority?: 'low' | 'normal' | 'high'; // 消息优先级
}
```

**示例:**

```typescript
import { ApplicationCommunicator } from '@micro-core/core';

// 发送消息给用户应用
const response = await ApplicationCommunicator.sendMessage('user-app', {
  type: 'GET_USER_INFO',
  userId: '123'
}, {
  timeout: 5000,
  retry: 3
});

console.log('用户信息:', response);
```

#### onMessage()

```typescript
onMessage(handler: MessageHandler): () => void
```

监听来自其他应用的消息。

**参数:**

```typescript
type MessageHandler = (message: any, sender: string, reply: (response: any) => void) => void;
```

**示例:**

```typescript
// 在用户应用中监听消息
const unsubscribe = ApplicationCommunicator.onMessage((message, sender, reply) => {
  console.log(`收到来自 ${sender} 的消息:`, message);
  
  if (message.type === 'GET_USER_INFO') {
    const userInfo = getUserInfo(message.userId);
    reply(userInfo);
  }
});

// 取消监听
unsubscribe();
```

### 通信中间件

#### addMiddleware()

```typescript
addMiddleware(middleware: CommunicationMiddleware): () => void
```

添加通信中间件。

**参数:**

```typescript
type CommunicationMiddleware = (
  message: any,
  sender: string,
  target: string,
  next: () => void
) => void;
```

**示例:**

```typescript
// 消息日志中间件
ApplicationCommunicator.addMiddleware((message, sender, target, next) => {
  console.log(`[Communication] ${sender} -> ${target}:`, message);
  next();
});

// 消息验证中间件
ApplicationCommunicator.addMiddleware((message, sender, target, next) => {
  if (!message.type) {
    console.error('消息缺少 type 字段');
    return;
  }
  next();
});

// 权限检查中间件
ApplicationCommunicator.addMiddleware((message, sender, target, next) => {
  if (!hasPermission(sender, target, message.type)) {
    console.error(`${sender} 没有权限向 ${target} 发送 ${message.type} 消息`);
    return;
  }
  next();
});
```

## 通信调试

### 启用调试模式

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

kernel.use(CommunicationPlugin, {
  debug: process.env.NODE_ENV === 'development',
  logLevel: 'verbose',      // 'silent' | 'error' | 'warn' | 'info' | 'verbose'
  maxLogSize: 1000         // 最大日志条数
});
```

### 调试工具

```typescript
// 获取通信统计
const stats = ApplicationCommunicator.getStats();
console.log('通信统计:', stats);

// 导出通信日志
const logs = ApplicationCommunicator.exportLogs();
console.log('通信日志:', logs);

// 清除日志
ApplicationCommunicator.clearLogs();
```

## 最佳实践

### 1. 事件命名规范

```typescript
// 推荐的事件命名规范
EventBus.emit('user:login');           // 模块:动作
EventBus.emit('cart:item-added');      // 模块:动作-对象
EventBus.emit('app:theme-changed');    // 应用:属性-变化
EventBus.emit('route:navigation-start'); // 路由:导航-开始

// 避免的命名方式
EventBus.emit('login');                // 太简单
EventBus.emit('userLoginSuccess');     // 驼峰不够清晰
EventBus.emit('USER_LOGIN');           // 全大写不友好
```

### 2. 状态结构设计

```typescript
// 推荐的状态结构
const stateStructure = {
  // 用户相关
  user: {
    profile: { id: '', name: '', email: '' },
    preferences: { theme: 'light', language: 'zh-CN' },
    permissions: []
  },
  
  // 应用相关
  app: {
    loading: false,
    error: null,
    settings: { apiUrl: '', timeout: 5000 }
  },
  
  // 业务相关
  business: {
    cart: { items: [], total: 0 },
    orders: { list: [], current: null }
  }
};
```

### 3. 错误处理

```typescript
// 统一的错误处理
EventBus.on('error', (error, context) => {
  console.error('通信错误:', error);
  
  // 错误上报
  reportError({
    type: 'communication-error',
    error: error.message,
    context,
    timestamp: Date.now()
  });
});

// 状态操作错误处理
try {
  GlobalState.set('user.profile', profileData);
} catch (error) {
  console.error('状态更新失败:', error);
  EventBus.emit('app:error', { type: 'state-error', error });
}
```
