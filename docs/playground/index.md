# 在线演练场

欢迎来到 Micro-Core 在线演练场！这里提供了丰富的交互式示例，让你能够直接在浏览器中体验 Micro-Core 的强大功能。

## 🚀 快速体验

### 基础示例

<div class="playground-grid">
  <div class="playground-card">
    <h3>🎯 Hello World</h3>
    <p>最简单的微前端应用示例</p>
    <a href="./basic/hello-world" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>🔄 应用切换</h3>
    <p>演示多个微应用之间的切换</p>
    <a href="./basic/app-switching" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>💬 应用通信</h3>
    <p>展示应用间的通信机制</p>
    <a href="./basic/communication" class="playground-link">立即体验</a>
  </div>
</div>

### 框架示例

<div class="playground-grid">
  <div class="playground-card">
    <h3>⚛️ React 应用</h3>
    <p>React 微前端应用完整示例</p>
    <a href="./frameworks/react" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>🟢 Vue 应用</h3>
    <p>Vue 微前端应用完整示例</p>
    <a href="./frameworks/vue" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>🅰️ Angular 应用</h3>
    <p>Angular 微前端应用完整示例</p>
    <a href="./frameworks/angular" class="playground-link">立即体验</a>
  </div>
</div>

### 高级特性

<div class="playground-grid">
  <div class="playground-card">
    <h3>🔌 插件系统</h3>
    <p>自定义插件开发和使用</p>
    <a href="./advanced/plugins" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>🛡️ 沙箱隔离</h3>
    <p>多种沙箱策略对比演示</p>
    <a href="./advanced/sandbox" class="playground-link">立即体验</a>
  </div>
  
  <div class="playground-card">
    <h3>⚡ 性能优化</h3>
    <p>预加载和缓存策略演示</p>
    <a href="./advanced/performance" class="playground-link">立即体验</a>
  </div>
</div>

## 🔄 迁移演练

### qiankun 迁移

<div class="playground-grid">
  <div class="playground-card">
    <h3>📦 qiankun 原版</h3>
    <p>原始 qiankun 应用示例</p>
    <a href="./migration/qiankun-original" class="playground-link">查看原版</a>
  </div>
  
  <div class="playground-card">
    <h3>🔄 迁移后版本</h3>
    <p>迁移到 Micro-Core 后的版本</p>
    <a href="./migration/qiankun-migrated" class="playground-link">查看迁移版</a>
  </div>
  
  <div class="playground-card">
    <h3>📊 对比分析</h3>
    <p>性能和功能对比分析</p>
    <a href="./migration/qiankun-comparison" class="playground-link">查看对比</a>
  </div>
</div>

### wujie 迁移

<div class="playground-grid">
  <div class="playground-card">
    <h3>📦 wujie 原版</h3>
    <p>原始 wujie 应用示例</p>
    <a href="./migration/wujie-original" class="playground-link">查看原版</a>
  </div>
  
  <div class="playground-card">
    <h3>🔄 迁移后版本</h3>
    <p>迁移到 Micro-Core 后的版本</p>
    <a href="./migration/wujie-migrated" class="playground-link">查看迁移版</a>
  </div>
  
  <div class="playground-card">
    <h3>📊 对比分析</h3>
    <p>性能和功能对比分析</p>
    <a href="./migration/wujie-comparison" class="playground-link">查看对比</a>
  </div>
</div>

## 🛠️ 自定义演练

### 配置生成器

<div class="config-generator">
  <h3>⚙️ 配置生成器</h3>
  <p>根据你的需求生成定制化的 Micro-Core 配置</p>
  
  <div class="generator-form">
    <div class="form-group">
      <label>主框架:</label>
      <select id="main-framework">
        <option value="vanilla">Vanilla JS</option>
        <option value="react">React</option>
        <option value="vue">Vue</option>
        <option value="angular">Angular</option>
      </select>
    </div>
    
    <div class="form-group">
      <label>子应用框架:</label>
      <div class="checkbox-group">
        <label><input type="checkbox" value="react"> React</label>
        <label><input type="checkbox" value="vue"> Vue</label>
        <label><input type="checkbox" value="angular"> Angular</label>
        <label><input type="checkbox" value="svelte"> Svelte</label>
      </div>
    </div>
    
    <div class="form-group">
      <label>沙箱策略:</label>
      <select id="sandbox-strategy">
        <option value="proxy">Proxy 沙箱</option>
        <option value="iframe">Iframe 沙箱</option>
        <option value="webcomponent">WebComponent 沙箱</option>
        <option value="namespace">命名空间沙箱</option>
      </select>
    </div>
    
    <div class="form-group">
      <label>构建工具:</label>
      <select id="build-tool">
        <option value="vite">Vite</option>
        <option value="webpack">Webpack</option>
        <option value="rollup">Rollup</option>
        <option value="esbuild">ESBuild</option>
      </select>
    </div>
    
    <button id="generate-config" class="generate-btn">生成配置</button>
  </div>
  
  <div id="generated-config" class="config-output" style="display: none;">
    <h4>生成的配置:</h4>
    <pre><code id="config-code"></code></pre>
    <button id="copy-config" class="copy-btn">复制配置</button>
    <button id="run-playground" class="run-btn">在演练场运行</button>
  </div>
</div>

## 📊 性能测试

### 基准测试

<div class="benchmark-section">
  <h3>⚡ 性能基准测试</h3>
  <p>对比不同微前端方案的性能表现</p>
  
  <div class="benchmark-controls">
    <button id="run-benchmark" class="benchmark-btn">运行基准测试</button>
    <select id="benchmark-scenario">
      <option value="startup">启动性能</option>
      <option value="switching">切换性能</option>
      <option value="memory">内存占用</option>
      <option value="bundle-size">包体积</option>
    </select>
  </div>
  
  <div id="benchmark-results" class="benchmark-results" style="display: none;">
    <canvas id="benchmark-chart"></canvas>
    <div class="benchmark-data">
      <table id="benchmark-table">
        <thead>
          <tr>
            <th>方案</th>
            <th>性能指标</th>
            <th>相对性能</th>
          </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>

## 🎮 交互式教程

### 步骤式学习

<div class="tutorial-section">
  <h3>📚 交互式教程</h3>
  <p>通过实际操作学习 Micro-Core 的各项功能</p>
  
  <div class="tutorial-list">
    <div class="tutorial-item">
      <h4>🎯 第一步：创建主应用</h4>
      <p>学习如何创建和配置主应用</p>
      <a href="./tutorial/step1" class="tutorial-link">开始学习</a>
    </div>
    
    <div class="tutorial-item">
      <h4>🔧 第二步：注册微应用</h4>
      <p>学习如何注册和管理微应用</p>
      <a href="./tutorial/step2" class="tutorial-link">开始学习</a>
    </div>
    
    <div class="tutorial-item">
      <h4>💬 第三步：应用间通信</h4>
      <p>学习如何实现应用间通信</p>
      <a href="./tutorial/step3" class="tutorial-link">开始学习</a>
    </div>
    
    <div class="tutorial-item">
      <h4>🛡️ 第四步：沙箱配置</h4>
      <p>学习如何配置和使用沙箱</p>
      <a href="./tutorial/step4" class="tutorial-link">开始学习</a>
    </div>
    
    <div class="tutorial-item">
      <h4>🔌 第五步：插件开发</h4>
      <p>学习如何开发自定义插件</p>
      <a href="./tutorial/step5" class="tutorial-link">开始学习</a>
    </div>
  </div>
</div>

## 🔧 开发工具

### 调试面板

<div class="devtools-section">
  <h3>🛠️ 开发者工具</h3>
  <p>强大的调试和监控工具</p>
  
  <div class="devtools-features">
    <div class="devtools-feature">
      <h4>📊 应用监控</h4>
      <p>实时监控应用状态和性能</p>
      <button class="devtools-btn">打开监控面板</button>
    </div>
    
    <div class="devtools-feature">
      <h4>🔍 沙箱检查器</h4>
      <p>检查沙箱隔离状态</p>
      <button class="devtools-btn">打开检查器</button>
    </div>
    
    <div class="devtools-feature">
      <h4>📡 通信调试</h4>
      <p>调试应用间通信</p>
      <button class="devtools-btn">打开调试器</button>
    </div>
    
    <div class="devtools-feature">
      <h4>⚡ 性能分析</h4>
      <p>分析应用性能瓶颈</p>
      <button class="devtools-btn">打开分析器</button>
    </div>
  </div>
</div>

<style>
.playground-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.playground-card {
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.playground-card:hover {
  border-color: var(--vp-c-brand);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.playground-card h3 {
  margin: 0 0 0.5rem 0;
  color: var(--vp-c-brand);
}

.playground-card p {
  margin: 0 0 1rem 0;
  color: var(--vp-c-text-2);
}

.playground-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: var(--vp-c-brand);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.playground-link:hover {
  background: var(--vp-c-brand-dark);
}

.config-generator {
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem 0;
}

.generator-form {
  display: grid;
  gap: 1rem;
  margin: 1rem 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
}

.checkbox-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: normal;
}

.generate-btn,
.copy-btn,
.run-btn,
.benchmark-btn,
.devtools-btn {
  padding: 0.75rem 1.5rem;
  background: var(--vp-c-brand);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.generate-btn:hover,
.copy-btn:hover,
.run-btn:hover,
.benchmark-btn:hover,
.devtools-btn:hover {
  background: var(--vp-c-brand-dark);
}

.config-output {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--vp-c-bg-soft);
  border-radius: 4px;
}

.config-output pre {
  background: var(--vp-code-bg);
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
}

.benchmark-section,
.tutorial-section,
.devtools-section {
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem 0;
}

.benchmark-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin: 1rem 0;
}

.benchmark-results {
  margin-top: 2rem;
}

.benchmark-data {
  margin-top: 1rem;
}

.benchmark-table {
  width: 100%;
  border-collapse: collapse;
}

.benchmark-table th,
.benchmark-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--vp-c-border);
}

.tutorial-list {
  display: grid;
  gap: 1rem;
  margin: 1rem 0;
}

.tutorial-item {
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  padding: 1rem;
}

.tutorial-item h4 {
  margin: 0 0 0.5rem 0;
}

.tutorial-item p {
  margin: 0 0 1rem 0;
  color: var(--vp-c-text-2);
}

.tutorial-link {
  color: var(--vp-c-brand);
  text-decoration: none;
}

.tutorial-link:hover {
  text-decoration: underline;
}

.devtools-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.devtools-feature {
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  padding: 1rem;
}

.devtools-feature h4 {
  margin: 0 0 0.5rem 0;
}

.devtools-feature p {
  margin: 0 0 1rem 0;
  color: var(--vp-c-text-2);
}
</style>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
  // 配置生成器逻辑
  const generateBtn = document.getElementById('generate-config');
  const configOutput = document.getElementById('generated-config');
  const configCode = document.getElementById('config-code');
  
  generateBtn?.addEventListener('click', () => {
    const mainFramework = document.getElementById('main-framework').value;
    const buildTool = document.getElementById('build-tool').value;
    const sandboxStrategy = document.getElementById('sandbox-strategy').value;
    
    const config = generateConfig(mainFramework, buildTool, sandboxStrategy);
    configCode.textContent = config;
    configOutput.style.display = 'block';
  });
  
  // 复制配置
  const copyBtn = document.getElementById('copy-config');
  copyBtn?.addEventListener('click', () => {
    navigator.clipboard.writeText(configCode.textContent);
    copyBtn.textContent = '已复制!';
    setTimeout(() => {
      copyBtn.textContent = '复制配置';
    }, 2000);
  });
});

function generateConfig(framework, buildTool, sandbox) {
  return `// Micro-Core 配置
import { MicroCoreKernel } from '@micro-core/core';
import { ${sandbox.charAt(0).toUpperCase() + sandbox.slice(1)}SandboxPlugin } from '@micro-core/plugin-sandbox-${sandbox}';

const kernel = new MicroCoreKernel();
kernel.use(${sandbox.charAt(0).toUpperCase() + sandbox.slice(1)}SandboxPlugin);

kernel.registerApplication({
  name: 'my-app',
  entry: 'http://localhost:3001',
  container: '#app-container',
  activeWhen: '/my-app',
  sandbox: '${sandbox}'
});

kernel.start();`;
}
</script>
