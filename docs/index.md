---
layout: home

hero:
  name: "micro-core"
  text: "现代化微前端架构"
  tagline: "轻量级、高性能、易扩展的微前端解决方案"
  image:
    src: /logo.svg
    alt: micro-core
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/getting-started
    - theme: alt
      text: 查看示例
      link: /examples/basic
    - theme: alt
      text: GitHub
      link: https://github.com/micro-core/micro-core

features:
  - icon: 🚀
    title: 轻量高效
    details: 核心库仅几KB，启动速度快，运行时开销小，为生产环境优化
  - icon: 🔧
    title: 插件化架构
    details: 基于插件的可扩展架构，支持路由、通信、认证等核心功能插件
  - icon: 🌐
    title: 多框架支持
    details: 原生支持React、Vue、Angular等主流框架，统一的适配器接口
  - icon: 📦
    title: 构建工具集成
    details: 深度集成Webpack、Vite、Rollup等构建工具，零配置开箱即用
  - icon: 🛡️
    title: 沙箱隔离
    details: 完善的JavaScript和CSS沙箱隔离，确保应用间互不干扰
  - icon: 🔄
    title: 应用间通信
    details: 高效的应用间通信机制，支持消息传递、状态共享、事件总线
  - icon: 📊
    title: 状态管理
    details: 内置全局状态管理，支持跨应用状态共享和响应式更新
  - icon: 🎯
    title: 路由系统
    details: 智能路由匹配和应用切换，支持嵌套路由和动态路由
  - icon: 🔍
    title: 开发调试
    details: 丰富的开发工具和调试功能，提供可视化的应用管理界面
---

## 为什么选择 micro-core？

micro-core 是一个现代化的微前端架构解决方案，专为大型前端应用而设计。它提供了完整的微前端基础设施，让你能够轻松构建可扩展、可维护的前端应用。

### 🎯 核心优势

- **轻量级设计**：核心库压缩后仅几KB，对现有项目影响最小
- **零配置启动**：开箱即用的配置，5分钟内完成微前端改造
- **生产就绪**：经过大规模生产环境验证，稳定可靠
- **完整生态**：提供插件、适配器、构建工具等完整生态系统

### 🚀 快速体验

```bash
# 安装核心包
npm install @micro-core/core

# 创建微前端实例
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore()

// 注册微前端应用
microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001/index.js',
  container: '#app-container',
  activeWhen: '/my-app'
})

// 启动应用
microCore.start()
```

### 📈 适用场景

- **大型企业应用**：多团队协作开发的复杂前端应用
- **遗留系统改造**：渐进式微前端改造，降低技术债务
- **多技术栈融合**：React、Vue、Angular等不同技术栈的统一管理
- **独立部署需求**：各个业务模块需要独立开发和部署

### 🏢 谁在使用

micro-core 已经在多个大型企业和项目中得到应用：

- 电商平台的商品管理、订单系统、用户中心
- 企业管理后台的各个业务模块
- 内容管理系统的编辑器、媒体库、设置面板
- 数据可视化平台的图表组件、仪表板

### 🤝 社区支持

- [GitHub 仓库](https://github.com/micro-core/micro-core) - 源码、问题反馈
- [示例项目](https://github.com/micro-core/examples) - 完整示例代码
- [更新日志](/changelog) - 版本更新记录
- [贡献指南](https://github.com/micro-core/micro-core/blob/main/CONTRIBUTING.md) - 参与贡献

---

<div class="tip custom-block" style="padding-top: 8px">

只是想尝试一下？跳到[快速开始](/guide/getting-started)。

</div>