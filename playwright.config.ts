import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
    testDir: './test/e2e',
    /* 并行运行测试 */
    fullyParallel: true,
    /* 在 CI 上禁止重试失败的测试 */
    forbidOnly: !!process.env.CI,
    /* 在 CI 上重试失败的测试 */
    retries: process.env.CI ? 2 : 0,
    /* 在 CI 上选择较少的工作进程 */
    workers: process.env.CI ? 1 : undefined,
    /* 报告器配置 */
    reporter: [
        ['html'],
        ['json', { outputFile: 'playwright-report/results.json' }],
        ['junit', { outputFile: 'playwright-report/results.xml' }]
    ],
    /* 全局测试配置 */
    use: {
        /* 基础 URL */
        baseURL: process.env.BASE_URL || 'http://localhost:3000',
        /* 收集失败测试的跟踪信息 */
        trace: 'on-first-retry',
        /* 截图配置 */
        screenshot: 'only-on-failure',
        /* 视频录制 */
        video: 'retain-on-failure'
    },

    /* 配置不同浏览器的测试项目 */
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] }
        },

        {
            name: 'firefox',
            use: { ...devices['Desktop Firefox'] }
        },

        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] }
        },

        /* 移动端浏览器测试 */
        {
            name: 'Mobile Chrome',
            use: { ...devices['Pixel 5'] }
        },
        {
            name: 'Mobile Safari',
            use: { ...devices['iPhone 12'] }
        },

        /* 品牌浏览器测试 */
        {
            name: 'Microsoft Edge',
            use: { ...devices['Desktop Edge'], channel: 'msedge' }
        },
        {
            name: 'Google Chrome',
            use: { ...devices['Desktop Chrome'], channel: 'chrome' }
        }
    ],

    /* 在测试开始前启动本地开发服务器 */
    webServer: process.env.CI
        ? undefined
        : {
            command: 'pnpm run dev',
            url: 'http://localhost:3000',
            reuseExistingServer: !process.env.CI,
            timeout: 120 * 1000
        },

    /* 测试超时配置 */
    timeout: 30 * 1000,
    expect: {
        timeout: 5 * 1000
    },

    /* 输出目录 */
    outputDir: 'test-results/'
});