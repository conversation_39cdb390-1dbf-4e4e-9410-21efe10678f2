version: '3.8'

services:
  # 开发环境
  dev:
    build:
      context: .
      target: development
    ports:
      - "3000:3000"  # 主应用
      - "3001:3001"  # React 子应用
      - "3002:3002"  # Vue 子应用
      - "3003:3003"  # Angular 子应用
      - "5173:5173"  # Vite 开发服务器
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - MICRO_CORE_DEBUG=true
    command: pnpm run dev

  # 生产环境
  production:
    build:
      context: .
      target: production
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # 文档服务
  docs:
    build:
      context: .
      target: docs
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # CDN 服务
  cdn:
    build:
      context: .
      target: cdn
    ports:
      - "8081:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # 测试环境
  test:
    build:
      context: .
      target: test
    environment:
      - NODE_ENV=test
      - CI=true
    command: pnpm run test:all

  # E2E 测试
  e2e:
    build:
      context: .
      target: e2e
    environment:
      - NODE_ENV=test
      - CI=true
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    volumes:
      - playwright-cache:/ms-playwright
    depends_on:
      - dev
    command: pnpm run test:e2e

  # Redis (用于缓存和会话)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

  # PostgreSQL (用于数据存储)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=microcore
      - POSTGRES_USER=microcore
      - POSTGRES_PASSWORD=microcore123
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - production
      - docs
      - cdn
    restart: unless-stopped

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    restart: unless-stopped

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  playwright-cache:
  redis-data:
  postgres-data:
  prometheus-data:
  grafana-data:

networks:
  default:
    driver: bridge